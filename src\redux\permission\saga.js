import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { fetchPermissionReq, getPermissionList } from "@/redux/permission/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* GetAllPermission(role_code) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `module-permission/role-permissions/${role_code?.payload}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data;
            return response;
        })
        .catch((err) => {
            var errMsg = [];
            return errMsg;
        });

    if (data) {
        yield put(getPermissionList(data));
    } else {
        yield put(getPermissionList([]));
    }
}

function* updateModulePermit(permissionList) {
    const data = yield axios
        .post(CONSTANTS.getAPI() + `module-permission/add-permission`, permissionList.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response;
            return errMsg;
        });

    if (data) {
        yield put(fetchPermissionReq(data?.data?.role_code));
        yield put(AlertSnackInfo({ message: data.message, result: data.success }));
    }
}

export function* PermissionWatcher() {
    yield takeEvery(actions.FETCH_PERMISSION_REQ, GetAllPermission);
    yield takeEvery(actions.UPDATE_MODULE_PERMISSION, updateModulePermit);
}

export default function* PermissionSaga() {
    yield all([fork(PermissionWatcher)]);
}
