import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { Clock, FilePenLine, Languages, ShieldCheck, Users } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const UserGroups = () => {
    const navigate = useNavigate();
    const [DataList, setDataList] = useState([]);
    const [languages, setLanguages] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [filterState, setFilterState] = useState({
        search: "",
        language_id: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = DataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.name?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const language = filterState?.language_id ? item?.language_id == filterState?.language_id : true; // Allow all items if no subCategory filter

            return matchesSearch && language; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(DataList);
        setFilterState({
            search: "",
            language_id: "",
        });
    };

    useEffect(() => {
        getUserGroupData();
        getLanguages();
    }, []);

    useEffect(() => {
        setFilteredData(DataList);
    }, [DataList]);

    const getUserGroupData = async () => {
        await tanstackApi
            .post("user-groups/list")
            .then((res) => {
                setDataList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const getLanguages = async () => {
        await tanstackApi
            .get("language/list")
            .then((res) => {
                setLanguages(res?.data?.data);
            })
            .catch((err) => {
                setLanguages([]);
            });
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-people-group"></i> User Groups
                </h4>
                <div className="tw-flex tw-justify-between tw-gap-2">
                    <Button
                        variant="outline"
                        className="tw-px-2 tw-py-1"
                        onClick={() => navigate(`/dashboard/user-group-trainers`)}
                    >
                        <i className="fa-solid fa-user-tie"></i> Group - Trainer Mapping
                    </Button>
                    <Button className="tw-px-2 tw-py-1" onClick={() => navigate(`/dashboard/user-group-create`)}>
                        <i className="fa-solid fa-plus"></i> Create Group
                    </Button>
                </div>
            </div>
            {/* <br /> */}
            <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label htmlFor="">Group Name</label>
                    <input
                        value={filterState?.search}
                        name="search"
                        onChange={onFilterChange}
                        style={{ minWidth: "300px" }}
                        type="text"
                        placeholder="Search by name ..."
                    />
                </div>
                <div className="input_group">
                    <label htmlFor="">Language</label>
                    <select value={filterState?.language_id} name="language_id" onChange={onFilterChange}>
                        <option value="">- All -</option>
                        {languages?.map((lang) => (
                            <option value={lang?.id} key={lang?.id}>
                                {lang?.name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-5 tw-p-0">
                {filteredData?.map((group, index) => (
                    <div key={index} className="tw-w-[390px] tw-rounded-xl tw-border-[1px] tw-p-1">
                        <div className="tw-p-2">
                            <div className="tw-flex tw-items-center tw-gap-2">
                                <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                    {group?.name}
                                </h2>
                            </div>
                            <p className="tw-mt-2 tw-line-clamp-3">{group?.description}</p>
                            <div className="tw-mt-1 tw-space-y-2">
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Users size={18} /> {group?.user_count}/{group?.user_restriction} Users
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Languages size={18} />{" "}
                                    <Badge variant={"outline"}>
                                        {languages?.find((dt) => dt?.id == group?.language_id)?.name}
                                    </Badge>
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Clock size={18} /> Created on {moment(group?.createdAt).format("LL")}
                                </span>{" "}
                                <span className="tw-grid tw-grid-cols-[20px_1fr] tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <ShieldCheck size={18} />{" "}
                                    <div className="tw-flex tw-flex-wrap tw-gap-1">
                                        {group?.lms_trainer_group_mappings?.length > 0 ? (
                                            group?.lms_trainer_group_mappings?.map((trainer, idx) => (
                                                <>
                                                    <a className="tw-font-semibold tw-underline">
                                                        {trainer?.lms_user?.first_name} {trainer?.lms_user?.last_name},
                                                    </a>
                                                    {"  "}
                                                </>
                                            ))
                                        ) : (
                                            <a className="tw-text-sm tw-text-slate-400">No trainers assigned Yet!</a>
                                        )}
                                    </div>
                                </span>{" "}
                            </div>
                            <div className="tw-mt-3 tw-grid tw-grid-cols-2 tw-gap-2">
                                <div
                                    onClick={() =>
                                        navigate(`/dashboard/user-subgroup-master/${group?.id}/${group?.name}`)
                                    }
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <i className="fa-solid fa-users-line tw-text-md"></i>
                                    View Teams
                                </div>
                                <div
                                    onClick={() => navigate(`/dashboard/user-group-create/${group?.id}`)}
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <FilePenLine size={16} strokeWidth={2} aria-hidden="true" />
                                    View Group
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default UserGroups;
