import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import MultipleSelector from "@/components/ui/multiselect";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const defaultValue = {
    scorm_course: "Combined",
    user_id: "555",
    individual_course: "Trainers",
    is_public: false,
    assign_learner_on_first_signup: true,
    re_enroll_enabled: true,
    trainers: [],
};

const CreateCourse = ({ open, setOpen, getCourses }) => {
    const navigate = useNavigate();
    const [trainersList, setTrainersList] = useState([]);
    const [selectedTrainers, setSelectedTrainers] = useState([]);
    const [optionsArray, setOptionsArray] = useState([]);

    const [courseCreate, setCourseCreate] = useState(defaultValue);

    const handleClose = () => {
        setCourseCreate(defaultValue);
        setOpen(false);
    };

    const onHandleChange = (name, value) => {
        if (name == "assign_learner_on_first_signup") {
            setCourseCreate({ ...courseCreate, [name]: !courseCreate?.assign_learner_on_first_signup });
        } else if (name == "re_enroll_enabled") {
            setCourseCreate({ ...courseCreate, [name]: !courseCreate?.re_enroll_enabled });
        } else {
            setCourseCreate({ ...courseCreate, [name]: value });
        }
    };

    useEffect(() => {
        let options = trainersList?.map((data) => {
            return {
                label: `${data?.first_name} ${data?.last_name}`,
                value: data?.id,
            };
        });
        setOptionsArray(options);
    }, [trainersList]);

    useEffect(() => {
        getTrainers();
    }, []);

    const getTrainers = async () => {
        await tanstackApi
            .get("users/get-users")
            .then((res) => {
                setTrainersList(res?.data?.data?.filter((dt) => dt?.is_trainer == true));
            })
            .catch((err) => {
                setTrainersList([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();
        const payload = {
            is_scorm: courseCreate?.scorm_course == "SCORM" ? true : false,
            user_id: localStorage.getItem("parent_user_id") || localStorage.getItem("userId"),
            is_individual_course_creation: courseCreate?.individual_course == "Individually" ? true : false,
            is_public: localStorage.getItem("level") == "levelOne" ? true : false,
            assign_learner_on_first_signup: courseCreate?.assign_learner_on_first_signup,
            re_enroll_enabled: courseCreate?.re_enroll_enabled,
            trainers: selectedTrainers?.map((dt) => dt?.value),
        };

        await tanstackApi
            .post("course/creation/init", { ...payload })
            .then((res) => {
                navigate(`/dashboard/edit-course/${res?.data?.data?.id}/yes`);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-lg">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">How do you want to create this course</h1>
                        </DialogTitle>
                        <DialogDescription>
                            Select below details according to your course convenience. You can create course as
                            individually or with trainers.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-flex-col-2 tw-mt-3 tw-flex tw-gap-10">
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox
                                    id="assign_learner_on_first_signup"
                                    checked={courseCreate?.assign_learner_on_first_signup}
                                    onCheckedChange={(e) => onHandleChange("assign_learner_on_first_signup", e)}
                                    name="assign_learner_on_first_signup"
                                />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="assign_learner_on_first_signup"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Enable Auto Enroll
                                    </label>
                                </div>
                            </div>
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox
                                    id="re_enroll_enabled"
                                    checked={courseCreate?.re_enroll_enabled}
                                    onCheckedChange={(e) => onHandleChange("re_enroll_enabled", e)}
                                />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="re_enroll_enabled"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Enable Re-Enroll
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Course Type</Label>
                                <Select
                                    value={courseCreate.scorm_course}
                                    onValueChange={(e) => onHandleChange("scorm_course", e)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose course type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Combined">Combined Course</SelectItem>
                                        <SelectItem value="SCORM">SCORM Course</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Individually / Trainers</Label>
                                <Select
                                    onValueChange={(e) => onHandleChange("individual_course", e)}
                                    value={courseCreate?.individual_course}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Define Ownership" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Individually">Individually</SelectItem>
                                        <SelectItem value="Trainers">Trainers</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        {courseCreate?.individual_course == "Trainers" && (
                            <div className="space-y-2">
                                <Label>Trainers</Label>
                                <MultipleSelector
                                    commandProps={{
                                        label: "Select Trainers",
                                    }}
                                    // value={trainersList.slice(0, 2)}
                                    defaultOptions={optionsArray}
                                    onChange={(e) => setSelectedTrainers(e)}
                                    placeholder="Select Trainers"
                                    hideClearAllButton
                                    hidePlaceholderWhenSelected
                                    emptyIndicator={<p className="text-center text-sm">No results found</p>}
                                />
                            </div>
                        )}
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary" onClick={handleClose}>
                                Close
                            </Button>
                        </DialogClose>
                        <Button onClick={onDataSubmit}>Create Course</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default CreateCourse;
