import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import SlideHeader, { AddContentFunction } from "@/pages/dashboard/createcourse/chaptercontent/slides/common";
import TipTapEditor from "@/pages/dashboard/createcourse/chaptercontent/slides/editor";
import HeadingExtends from "@/pages/dashboard/createcourse/chaptercontent/slides/editor/extensions/heading";
import { tanstackApiFormdata } from "@/react-query/api";
import BulletList from "@tiptap/extension-bullet-list";
import { Color } from "@tiptap/extension-color";
import Document from "@tiptap/extension-document";
import Dropcursor from "@tiptap/extension-dropcursor";
import FontFamily from "@tiptap/extension-font-family";
import Image from "@tiptap/extension-image";
import ListItem from "@tiptap/extension-list-item";
import ListKeymap from "@tiptap/extension-list-keymap";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Typography from "@tiptap/extension-typography";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { MediaPlayer, MediaProvider } from "@vidstack/react";
import { PlyrLayout, plyrLayoutIcons } from "@vidstack/react/player/layouts/plyr";
import "@vidstack/react/player/styles/base.css";
import "@vidstack/react/player/styles/plyr/theme.css";
import { useInView } from "framer-motion";
import { CircleFadingPlus, Upload, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

export default function HTML({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    setContentArray,
    contentArray,
    template,
    onSetDelete,
    setCurrentId,
    setOpenIconDialog,
}) {
    const containerRef = useRef(null);
    const content_mapping = {
        TEXT: HTMLEditor,
        IMAGE: ImageUpload,
        AUDIO: AudioUpload,
        VIDEO: VideoUpload,
    };

    const inView = useInView(containerRef, { amount: 0.8 });
    useEffect(() => {
        if (inView) setCurrentId(`#slide_${index}`);
    }, [inView]);

    return (
        <div
            ref={containerRef}
            className="tw-grid tw-min-h-[60vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${content?.background || template?.background})` }}
        >
            <SlideHeader
                index={index}
                content={content}
                contentArray={contentArray}
                onSlideEdit={onSlideEdit}
                onSetDelete={onSetDelete}
                onRemoveSlide={onRemoveSlide}
                setOpenIconDialog={setOpenIconDialog}
                setContentArray={setContentArray}
            />
            {content?.lecture_data == null ? (
                <AddContentFunction onSlideEdit={onSlideEdit} content={content} index={index} />
            ) : (
                <div style={content?.lecture_data?.layout} className={`tw-w-full tw-gap-5 tw-rounded-md tw-p-3`}>
                    {content?.lecture_data?.layout_box?.map((box, idx) => {
                        const Component = content_mapping[box?.content_type];
                        return (
                            <div
                                key={idx}
                                className="tw-h-full tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[2px] tw-border-dashed tw-border-slate-700 tw-p-3 tw-shadow-sm"
                            >
                                {Component ? (
                                    <Component
                                        idx={idx}
                                        index={index}
                                        content={content}
                                        setContentArray={setContentArray}
                                        contentArray={contentArray}
                                    />
                                ) : null}
                            </div>
                        );
                    })}
                </div>
            )}
        </div>
    );
}

const VideoUpload = ({ idx, index, content, contentArray, setContentArray }) => {
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [fileUrl, setFileUrl] = useState(content?.lecture_data?.layout_box[idx]?.content || "");

    const onFileChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileUpload(data);
        }
    };

    useEffect(() => {
        let options = [...contentArray];
        options[index].lecture_data.layout_box[idx].content = fileUrl;
        setContentArray(options);
    }, [fileUrl]);

    const onRemove = () => {
        setFileUrl("");
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setFileUrl(res.data.fileUrl);
                } else {
                    setLoad(false);
                    setFileUrl("");
                }
            })
            .catch((err) => {
                setLoad(false);
                setFileUrl("");
            });
    };
    return (
        <div className="tw-mt-2 tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-gap-2">
            {fileUrl !== "" ? (
                <div className="tw-flex tw-aspect-video tw-h-[100%] tw-items-center tw-justify-center tw-rounded-xl tw-border-[1px] tw-border-dashed tw-p-2">
                    <MediaPlayer
                        className="!tw-aspect-video tw-w-fit tw-border-[3px] tw-border-black tw-bg-black [--plyr-border-radius:_8px]"
                        src={fileUrl}
                        playsInline
                    >
                        <MediaProvider />
                        <PlyrLayout icons={plyrLayoutIcons} />
                    </MediaPlayer>
                </div>
            ) : (
                <div className="tw-flex tw-aspect-video tw-h-[100%] tw-items-center tw-justify-center tw-rounded-xl tw-border-[1px] tw-border-dashed tw-p-2">
                    <img
                        className="tw-aspect-[1/1] tw-w-[70px] tw-rounded-md tw-object-contain"
                        src="/assets/video.png"
                    />
                </div>
            )}
            <p className="tw-mb-2 tw-text-sm tw-text-slate-400">
                Please select an Image , File supported .mp4, .mkv & .mpeg{" "}
            </p>
            <div className="tw-flex tw-flex-wrap tw-items-center tw-justify-center tw-gap-2">
                <Label
                    htmlFor={`layout_video_url_${index}_${idx}`}
                    className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                >
                    <Upload className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                    <input
                        onChange={onFileChange}
                        type="file"
                        style={{ display: "none" }}
                        id={`layout_video_url_${index}_${idx}`}
                        accept="video/*"
                    />
                    <div className="max-sm:sr-only">
                        {load ? "Uploading" : "Upload Video Content"} {load ? `${uploaded}%` : null}
                    </div>
                </Label>
                {fileUrl && (
                    <Button variant="outline" className="aspect-square max-sm:p-0" onClick={onRemove}>
                        <X className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                        <Label className="max-sm:sr-only">Remove</Label>
                    </Button>
                )}
            </div>
        </div>
    );
};

const ImageUpload = ({ idx, index, content, contentArray, setContentArray }) => {
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [fileUrl, setFileUrl] = useState(content?.lecture_data?.layout_box[idx]?.content || "");

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileUpload(data);
        }
    };

    useEffect(() => {
        let options = [...contentArray];
        options[index].lecture_data.layout_box[idx].content = fileUrl;
        setContentArray(options);
    }, [fileUrl]);

    const onRemove = () => {
        setFileUrl("");
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setFileUrl(res.data.fileUrl);
                } else {
                    setLoad(false);
                    setFileUrl("");
                }
            })
            .catch((err) => {
                setLoad(false);
                setFileUrl("");
            });
    };

    return (
        <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-gap-2">
            {fileUrl !== "" ? (
                <div className="tw-aspect-video tw-h-full tw-max-h-96 tw-rounded-xl tw-border-[1px] tw-border-dashed tw-p-2">
                    <img className="tw-aspect-[2/1] tw-h-full tw-rounded-md tw-object-contain" src={fileUrl} />
                </div>
            ) : (
                <div className="tw-flex tw-aspect-video tw-h-full tw-items-center tw-justify-center tw-rounded-xl tw-border-[1px] tw-border-dashed tw-p-2">
                    <img
                        className="tw-aspect-[1/1] tw-w-[70px] tw-rounded-md tw-object-contain"
                        src="/assets/image.png"
                    />
                </div>
            )}
            <p className="tw-mb-2 tw-text-sm tw-text-slate-400">
                Please select an Image , File supported .png, .jpg & .webp{" "}
            </p>
            <div className="tw-flex tw-flex-wrap tw-items-center tw-justify-center tw-gap-2">
                <Label
                    htmlFor={`layout_image_url_${index}_${idx}`}
                    className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                >
                    <Upload className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                    <input
                        onChange={onImageChange}
                        type="file"
                        style={{ display: "none" }}
                        id={`layout_image_url_${index}_${idx}`}
                        accept="image/*"
                    />
                    <p className="max-sm:sr-only">
                        {load ? "Uploading" : "Upload Image Content"} {load ? `${uploaded}%` : null}
                    </p>
                </Label>
                {fileUrl && (
                    <Button variant="outline" className="aspect-square max-sm:p-0" onClick={onRemove}>
                        <X className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                        <Label className="max-sm:sr-only">Remove</Label>
                    </Button>
                )}
            </div>
        </div>
    );
};

const AudioUpload = ({ idx, index, content, contentArray, setContentArray }) => {
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [fileUrl, setFileUrl] = useState(content?.lecture_data?.layout_box[idx]?.content || "");

    const onFileChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileUpload(data);
        }
    };

    useEffect(() => {
        let options = [...contentArray];
        options[index].lecture_data.layout_box[idx].content = fileUrl;
        setContentArray(options);
    }, [fileUrl]);

    const onRemove = () => setFileUrl("");

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setFileUrl(res.data.fileUrl);
                } else {
                    setLoad(false);
                    setFileUrl("");
                }
            })
            .catch((err) => {
                setLoad(false);
                setFileUrl("");
            });
    };
    return (
        <div className="tw-mt-2 tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-gap-2">
            {fileUrl !== "" ? (
                <div className="tw-flex tw-aspect-video tw-w-full tw-items-center tw-justify-center tw-rounded-xl tw-border-[1px] tw-border-dashed tw-p-2">
                    <MediaPlayer
                        className="!tw-aspect-video tw-w-full tw-border-[3px] tw-border-black tw-bg-black [--plyr-border-radius:_8px]"
                        src={fileUrl}
                        playsInline
                    >
                        <MediaProvider />
                        <PlyrLayout icons={plyrLayoutIcons} />
                    </MediaPlayer>
                </div>
            ) : (
                <div className="tw-flex tw-aspect-video tw-w-full tw-items-center tw-justify-center tw-rounded-xl tw-border-[1px] tw-border-dashed tw-p-2">
                    <img
                        className="tw-aspect-[1/1] tw-w-[70px] tw-rounded-md tw-object-contain"
                        src="/assets/audio.png"
                    />
                </div>
            )}
            <p className="tw-mb-2 tw-text-sm tw-text-slate-400">
                Please select an Image , File supported .mp3, .ogg & .wav{" "}
            </p>
            <div className="tw-flex tw-flex-wrap tw-items-center tw-justify-center tw-gap-2">
                <Label
                    htmlFor={`layout_audio_url_${index}_${idx}`}
                    className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                >
                    <Upload className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                    <input
                        onChange={onFileChange}
                        type="file"
                        style={{ display: "none" }}
                        id={`layout_audio_url_${index}_${idx}`}
                        accept="audio/*"
                    />
                    <div className="max-sm:sr-only">
                        {load ? "Uploading" : "Upload Audio Content"} {load ? `${uploaded}%` : null}
                    </div>
                </Label>
                {fileUrl && (
                    <Button variant="outline" className="aspect-square max-sm:p-0" onClick={onRemove}>
                        <X className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                        <Label className="max-sm:sr-only">Remove</Label>
                    </Button>
                )}
            </div>
        </div>
    );
};

const HTMLEditor = ({ idx, index, content, contentArray, setContentArray }) => {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                text: {
                    HTMLAttributes: {
                        class: "tw-text-sm",
                    },
                },
            }),
            Color,
            Document,
            HeadingExtends.configure({
                levels: [1, 2, 3, 4, 5, 6],
            }),
            Paragraph,
            TextStyle.configure({
                HTMLAttributes: {},
            }),
            BulletList,
            ListItem,
            FontFamily.configure({
                types: ["textStyle"],
            }),
            Dropcursor,
            ListKeymap,
            Placeholder.configure({
                placeholder: "Write something …",
            }),
            Image,
            TextAlign.configure({
                types: ["heading", "paragraph"],
            }),
            Typography,
        ],
        content: content?.lecture_data?.layout_box[idx]?.content ?? "",
        editorProps: {
            attributes: {
                spellcheck: "false",
            },
        },
        onUpdate: ({ editor }) => {
            let options = [...contentArray];
            options[index].lecture_data.layout_box[idx].content = editor.getHTML();
            setContentArray(options);
        },
    });

    return <TipTapEditor editor={editor} />;
};
