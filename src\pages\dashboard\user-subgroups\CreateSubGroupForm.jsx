import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>read<PERSON>rumb<PERSON><PERSON>,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import AddAssignmentsToSubGroup from "./AddAssignmentsToSubGroup";
import AddHomeworksToSubGroup from "./AddHomeworksToSubGroup";
import AddUsersToSubGroup from "./AddUsersToSubGroup";
import Details from "./Details";
import { useSearchParams } from "react-router-dom";

const CreateSubGroupForm = () => {
    const params = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const router = useNavigate();
    const [groupTeamData, setGroupTeamData] = useState(null);

    useEffect(() => {
        if (params?.subgroup_id !== undefined) {
            getGroupTeams(params?.subgroup_id);
        }
    }, [params]);

    const getGroupTeams = async (subgroup_id) => {
        await tanstackApi
            .post(`user-subgroup/list`)
            .then((res) => {
                setGroupTeamData(res?.data?.data?.find((dt) => dt?.id == Number(subgroup_id)));
            })
            .catch((err) => {
                setGroupTeamData(null);
            });
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/user-group-master">User Groups</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink
                                href={`/dashboard/user-subgroup-master/${params?.group_id}/${params?.group_name}`}
                            >
                                {params?.group_name}
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>
                                {params.subgroup_id ? groupTeamData?.name : "Create new team"}
                            </BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <Button
                    className="tw-px-2 tw-py-1"
                    onClick={() => router(`/dashboard/user-subgroup-master/${params?.group_id}/${params?.group_name}`)}
                >
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
                {/* </Link> */}
            </div>
            <Tabs
                defaultValue={searchParams.get("tab") ?? "basic_details"}
                onValueChange={(v) => setSearchParams({ tab: v })}
            >
                {params?.subgroup_id !== undefined && (
                    <TabsList className="tw-grid tw-w-[570px] tw-grid-cols-4">
                        <TabsTrigger value="basic_details" className="tw-gap-2">
                            <i className="fa-solid fa-circle-info"></i> Basic Details
                        </TabsTrigger>
                        <TabsTrigger value="members" className="tw-gap-2">
                            <i className="fa-solid fa-user"></i> Members
                        </TabsTrigger>
                        <TabsTrigger value="homeworks" className="tw-gap-2">
                            <i className="fa-solid fa-chalkboard"></i> Homeworks
                        </TabsTrigger>
                        <TabsTrigger value="assignments" className="tw-gap-2">
                            <i className="fa-solid fa-laptop-file"></i> Assignments
                        </TabsTrigger>
                    </TabsList>
                )}
                <TabsContent value="basic_details">
                    <Details groupTeamData={groupTeamData} getGroupTeams={getGroupTeams} />
                </TabsContent>
                <TabsContent value="members">
                    <AddUsersToSubGroup groupTeamData={groupTeamData} getGroupTeams={getGroupTeams} />
                </TabsContent>
                <TabsContent value="homeworks">
                    <AddHomeworksToSubGroup groupTeamData={groupTeamData} getGroupTeams={getGroupTeams} />
                </TabsContent>
                <TabsContent value="assignments">
                    <AddAssignmentsToSubGroup groupTeamData={groupTeamData} getGroupTeams={getGroupTeams} />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default CreateSubGroupForm;
