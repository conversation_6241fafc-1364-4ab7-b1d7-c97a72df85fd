import React from "react";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
const statuses = [
    { value: "PENDING", label: "Pending" },
    { value: "PAID", label: "Paid" },
    // { value: "CANCELLED", label: "Cancelled" },
    // { value: "FAILED", label: "Failed" },
    // { value: "PROCESSING", label: "Processing" },
];

const DomainOrderDetails = () => {
    const navigate = useNavigate();
    const params = useParams();
    const [openAlert, setOpenAlert] = useState(false);
    const [status, setStatus] = useState("");

    const [invoiceDetails, setInvoiceDetails] = useState(null);

    useEffect(() => {
        if (params?.order_id !== undefined) {
            getInvoiceList(params?.order_id);
        }
    }, [params]);

    const getInvoiceList = async (payload) => {
        await tanstackApi
            .get("invoice/list")
            .then((res) => {
                setInvoiceDetails(res?.data?.data?.find((dt) => dt?.id == Number(payload)) || null);
            })
            .catch((err) => {
                setInvoiceDetails(null);
            });
    };

    const onStatusUpdate = (status) => {
        setOpenAlert(true);
        setStatus(status);
    };

    const onDataSubmit = async () => {
        if (status == "PAID") {
            let payload = {
                invoiceId: invoiceDetails?.id,
                sessionId: invoiceDetails?.session_id,
            };

            await tanstackApi
                .post("invoice/update-checkout", payload)
                .then((res) => {
                    getInvoiceList(params?.order_id);
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                })
                .catch((err) => {
                    toast.success("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This is not reversible, the order will be completed when you confirm.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Yes! Confirm</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/domain-orders">My orders</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>View order</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button className="tw-px-2 tw-py-1" onClick={() => navigate(`/dashboard/domain-orders`)}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
            </div>
            <br />
            <div className="tw-mx-5 tw-flex tw-justify-between">
                <div>
                    <h1 className="tw-text-md tw-font-lexend tw-font-medium">Customer :-</h1>
                    <div>
                        <p>
                            <span className="tw-text-sm tw-font-semibold tw-text-gray-500">Name :</span>{" "}
                            {`${invoiceDetails?.customer?.first_name} ${invoiceDetails?.customer?.last_name}`}
                        </p>
                        <p>
                            <span className="tw-text-sm tw-font-semibold tw-text-gray-500">Email :</span>{" "}
                            {invoiceDetails?.customer?.email}
                        </p>
                        <p>
                            <span className="tw-text-sm tw-font-semibold tw-text-gray-500">Domain :</span>{" "}
                            {invoiceDetails?.customer?.domain_name}
                        </p>
                    </div>
                </div>
                <div>
                    <h1 className="tw-text-md tw-font-lexend tw-font-medium">Invoice :-</h1>
                    <div>
                        <p>
                            <span className="tw-text-sm tw-font-semibold tw-text-gray-500">Invoice ID :</span>{" "}
                            {invoiceDetails?.invoice_number}
                        </p>
                        <p>
                            <span className="tw-text-sm tw-font-semibold tw-text-gray-500">Date / Time :</span>{" "}
                            {moment(invoiceDetails?.createdAt).format("LLL")}
                        </p>
                        {/* <p><span className='tw-text-gray-500 tw-text-sm tw-font-semibold'>Transaction ID :</span> {invoiceDetails?.stripe_payment_id}</p> */}
                    </div>
                </div>
            </div>
            <div className="tw-m-5">
                <h1 className="tw-text-md tw-font-lexend tw-font-medium">Invoice Summary :-</h1>
                <Table className="tw-mt-2">
                    <TableHeader>
                        <TableRow>
                            <TableHead>#.</TableHead>
                            <TableHead>Item Name</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Subscription</TableHead>
                            <TableHead className="tw-text-center">Quantity</TableHead>
                            <TableHead className="tw-text-right">Amount</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {invoiceDetails?.invoice_items.map((item, idx) => (
                            <TableRow key={item?.invoice}>
                                <TableCell className="font-medium">{idx + 1}</TableCell>
                                <TableCell>{item?.course?.course_title || item?.bundle?.name}</TableCell>
                                <TableCell>
                                    {item?.course ? <Badge variant={"outline"}>Course</Badge> : <Badge>Bundle</Badge>}
                                </TableCell>
                                <TableCell>
                                    {item?.subscription_type && (
                                        <Badge className={"tw-capitalize"} variant={"outline"}>
                                            {item?.subscription_type}
                                        </Badge>
                                    )}
                                </TableCell>
                                <TableCell className="tw-text-center">
                                    {item?.bulkPriceDetails
                                        ? `${item?.bulkPriceDetails?.lower_limit} - ${
                                              item?.bulkPriceDetails?.upper_limit
                                          } users`
                                        : "One Unit"}
                                </TableCell>
                                <TableCell className="tw-text-right">${item.total_price}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
            <div className="tw-m-5 tw-grid tw-grid-cols-[0.7fr_0.3fr] tw-gap-5">
                <div className="tw-space-y-2">
                    <p>
                        <span className="tw-text-sm tw-font-semibold tw-text-gray-500">Transaction ID :</span>{" "}
                        {invoiceDetails?.stripe_payment_id}
                    </p>
                    <Select
                        value={invoiceDetails?.status}
                        onValueChange={onStatusUpdate}
                        disabled={invoiceDetails?.status == "PAID"}
                    >
                        <SelectTrigger className="!tw-w-[180px]">
                            <SelectValue placeholder="One Unit" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                {statuses?.map((data, index) => (
                                    <SelectItem value={data?.value} key={index}>
                                        {data?.label}
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div className="tw-flex tw-flex-col tw-gap-1">
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <h1 className="tw-text-md tw-text-gray-400">Sub Total</h1>
                        <p className="text-right">${invoiceDetails?.total_amount}</p>
                    </div>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <h1 className="tw-text-md tw-text-gray-400">Discount</h1>
                        <p className="text-right">- ${invoiceDetails?.discount}</p>
                    </div>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <h1 className="tw-text-lg tw-font-medium tw-text-gray-400">Total</h1>
                        <p className="text-right tw-text-lg tw-font-medium">${invoiceDetails?.total_amount}</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DomainOrderDetails;
