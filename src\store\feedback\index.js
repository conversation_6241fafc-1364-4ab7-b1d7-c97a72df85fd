import { create } from "zustand";

export const useFeedbackStore = create((set) => ({
    isOpen: false,
    data: {
        feedback: null,
        type: "warning", // error, success, warning
    },
    action: (data) => set((state) => ({ ...state, isOpen: data })),
    open: () => set((state) => ({ ...state, isOpen: true })),
    close: () => set((state) => ({ ...state, isOpen: false })),
    setData: (data) => set((state) => ({ ...state, data })),
}));
