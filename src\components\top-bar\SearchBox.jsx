import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const trainer = [
    {
        key: "",
        label: "Dashboard",
        icon: "fa-solid fa-chart-simple tw-size-4",
        isActive: true,
    },
    {
        key: "trainer-zoom-classes",
        label: "Zoom Classes",
        icon: "fa-solid fa-video tw-size-4",
        isActive: true,
    },
    {
        key: "assigned-course",
        label: "Assigned Course",
        icon: "fa-solid fa-user-graduate tw-size-4",
        isActive: true,
    },
    {
        key: "assigned-quizzes",
        label: "Assigned Quizzes",
        icon: "fa-solid fa-gamepad tw-size-4",
        isActive: true,
    },
    {
        key: "user-groups",
        label: "My Groups",
        icon: "fa-solid fa-people-group tw-size-4",
        isActive: true,
    },
    {
        key: "homework",
        label: "Homeworks",
        icon: "fa-solid fa-chalkboard",
        isActive: true,
    },
    {
        key: "project-assignment",
        label: "Project Assignments",
        icon: "fa-solid fa-laptop-file",
        isActive: true,
    },
    {
        key: "assigned-data",
        label: "Assigned Work",
        icon: "fa-solid fa-spell-check tw-size-4",
        isActive: true,
    },
    {
        key: "submissions",
        label: "Submissions",
        icon: "fa-solid fa-clipboard-check tw-size-4",
        isActive: true,
    },
    {
        key: "my-profile",
        label: "My Profile",
        icon: "fa-solid fa-address-card tw-size-4",
        isActive: true,
    },
];

const learner = [
    {
        key: "",
        label: "Dashboard",
        icon: "fa-solid fa-chart-simple tw-size-4",
        isActive: true,
    },
    {
        key: "learner-zoom-classes",
        label: "Zoom Classes",
        icon: "fa-solid fa-video tw-size-4",
        isActive: true,
    },
    {
        key: "shop-learner  ",
        label: "Shop",
        icon: "fa-solid fa-shop",
        isActive: true,
    },
    {
        key: "course",
        label: "Courses",
        icon: "fa-solid fa-book tw-size-4",
        isActive: true,
    },

    {
        key: "learning-path-learner",
        label: "Learning Path",
        icon: "fa-solid fa-graduation-cap tw-size-4",
        isActive: true,
    },

    {
        key: "user-assignment",
        label: "Assignments",
        icon: "fa-solid fa-diagram-project tw-size-4",
        isActive: true,
    },
    {
        key: "user-homework",
        label: "Homework",
        icon: "fa-solid fa-sheet-plastic tw-size-4",
        isActive: true,
    },
    {
        key: "user-groups",
        label: "My Groups",
        icon: "fa-solid fa-people-group tw-size-4",
        isActive: true,
    },

    {
        key: "submissions-learner",
        label: "My Submissions",
        icon: "fa-solid fa-clipboard-check tw-size-4",
        isActive: true,
    },

    {
        key: "my-profile",
        label: "My Profile",
        icon: "fa-solid fa-address-card tw-size-4",
        isActive: true,
    },
];

const administrator = [
    {
        key: "",
        label: "Dashboard",
        icon: "fa-solid fa-chart-simple tw-size-4",
        isActive: true,
    },
    {
        key: "domain-types",
        label: "Domain Types",
        icon: "fa-solid fa-building",
        isActive: true,
    },
    {
        key: "normal-pricing",
        label: "Pricing",
        icon: "fa-solid fa-tags",
        isActive: true,
        children: [
            {
                key: "normal-pricing",
                label: "Normal Price",
                isActive: true,
            },
            {
                key: "bulk-pricing",
                label: "Bulk Price",
                isActive: true,
            },
        ],
    },
    {
        key: "domain-orders",
        label: "Orders",
        icon: "fa-solid fa-cart-plus",
        isActive: true,
    },
    {
        key: "domain-users",
        label: "Domains",
        icon: "fa-solid fa-earth-americas",
        isActive: true,
    },

    {
        key: "modules",
        label: "Modules",
        icon: "fa-solid fa-cubes tw-size-4",
        isActive: true,
    },
    {
        key: "category",
        label: "Categories",
        icon: "fa-solid fa-shapes tw-size-4",
        isActive: true,
    },
    {
        key: "my-courses",
        label: "Courses",
        icon: "fa-solid fa-book",
        isActive: true,
    },
    {
        key: "course-bundle",
        label: "Course Bundles",
        icon: "fa-solid fa-box-open tw-size-4",
        isActive: true,
    },
    {
        key: "question-pool",
        label: "Question Pool",
        icon: "fa-solid fa-smog",
        isActive: true,
    },
    {
        key: "quiz-tests",
        label: "Quizzes / Tests",
        icon: "fa-solid fa-gamepad",
        isActive: true,
    },
    {
        key: "interactions",
        label: "Interactions",
        icon: "fa-solid fa-hand-sparkles",
        isActive: true,
    },
    {
        key: "templates",
        label: "Templates",
        icon: "fa-solid fa-wand-magic-sparkles",
        isActive: true,
    },
    {
        key: "homework",
        label: "Homeworks",
        icon: "fa-solid fa-chalkboard",
        isActive: true,
    },
    {
        key: "project-assignment",
        label: "Project Assignments",
        icon: "fa-solid fa-laptop-file",
        isActive: true,
    },
    {
        key: "badges",
        label: "Badges",
        icon: "fa-solid fa-star tw-size-4",
        isActive: true,
    },
    {
        key: "question-types",
        label: "Question Types",
        icon: "fa-solid fa-asterisk",
        isActive: true,
    },
    {
        key: "question-bank",
        label: "Question Bank",
        icon: "fa-solid fa-server",
        isActive: true,
    },
    {
        key: "tag-master",
        label: "Tags",
        icon: "fa-solid fa-tags",
        isActive: true,
    },
    {
        key: "certificate",
        label: "Certificate",
        icon: "fa-solid fa-certificate",
        isActive: true,
    },

    {
        key: "terms-conditions",
        label: "Terms & Conditions",
        icon: "fa-solid fa-pen-to-square tw-size-4",
        isActive: true,
    },
];

const user = [
    {
        key: "",
        label: "Dashboard",
        icon: "fa-solid fa-chart-simple tw-size-4",
        isActive: true,
    },
    {
        key: "roles",
        label: "Roles",
        icon: "fa-solid fa-briefcase tw-size-4",
        isActive: true,
    },
    {
        key: "normal-pricing-domain",
        label: "Pricing",
        icon: "fa-solid fa-tags tw-size-4",
        isActive: true,
    },
    {
        key: "orders",
        label: "Orders",
        icon: "fa-solid fa-cart-plus tw-size-4",
        isActive: true,
    },
    {
        key: "users",
        label: "Users",
        icon: "fa-solid fa-user tw-size-4",
        isActive: true,
    },
    {
        key: "shop-domain",
        label: "Shop",
        icon: "fa-solid fa-shop",
        isActive: true,
    },
    {
        key: "my-courses",
        label: "Courses",
        icon: "fa-solid fa-book",
        isActive: true,
    },
    {
        key: "learning-path",
        label: "Learning Path",
        icon: "fa-solid fa-signs-post tw-size-4",
        isActive: true,
    },
    {
        key: "course-bundle",
        label: "Course Bundles",
        icon: "fa-solid fa-box-open tw-size-4",
        isActive: true,
    },
    {
        key: "user-group-master",
        label: "User Groups",
        icon: "fa-solid fa-people-group tw-size-4",
        isActive: true,
    },
    {
        key: "zoom-schedules",
        label: "Zoom Schedules",
        icon: "fa-solid fa-calendar-check tw-size-4",
        isActive: true,
    },
    {
        key: "question-pool",
        label: "Question Pool",
        icon: "fa-solid fa-smog",
        isActive: true,
    },
    {
        key: "quiz-tests",
        label: "Quizzes / Tests",
        icon: "fa-solid fa-gamepad",
        isActive: true,
    },
    {
        key: "interactions",
        label: "Interactions",
        icon: "fa-solid fa-hand-sparkles",
        isActive: true,
    },
    {
        key: "interactive-video",
        label: "Interactive Video",
        icon: "fa-solid fa-video",
        isActive: true,
    },
    {
        key: "templates",
        label: "Templates",
        icon: "fa-solid fa-wand-magic-sparkles",
        isActive: true,
    },
    {
        key: "homework",
        label: "Homeworks",
        icon: "fa-solid fa-chalkboard",
        isActive: true,
    },
    {
        key: "project-assignment",
        label: "Project Assignments",
        icon: "fa-solid fa-laptop-file",
        isActive: true,
    },

    // {
    //     key: "gamification",
    //     label: "Gamification",
    //     icon: "fa-solid fa-trophy tw-size-4",
    //     isActive: true,
    //     children: [],
    // },
    {
        key: "submissions-admin",
        label: "Submissions",
        icon: "fa-solid fa-clipboard-check tw-size-4",
        isActive: true,
    },
    {
        key: "attendance-reports",
        label: "Reports",
        icon: "fa-solid fa-file tw-size-4",
        isActive: true,
        children: [
            {
                key: "attendance-reports",
                label: "Attendance Reports",
                isActive: true,
            },
            {
                key: "timeline-logs",
                label: "Timeline Logs",
                isActive: true,
            },
            {
                key: "course-reports",
                label: "Course Reports",
                isActive: true,
            },
            {
                key: "learner-reports",
                label: "Learner Reports",
                isActive: true,
            },
            {
                key: "certificate-reports",
                label: "Certificate Report",
                isActive: true,
            },
        ],
    },
    {
        key: "my-account",
        label: "Settings",
        icon: "fa-solid fa-gears tw-size-4",
        isActive: true,
        children: [
            {
                key: "my-account",
                label: "My Account",
                isActive: true,
            },
            {
                key: "certificate",
                label: "Certificate",
                isActive: true,
            },
        ],
    },
    {
        key: "terms-conditions",
        label: "Terms & Conditions",
        icon: "fa-solid fa-pen-to-square tw-size-4",
        isActive: true,
    },
];

const SIDEBAR_KEYBOARD_SHORTCUT = "k";

const SearchBox = () => {
    const navigate = useNavigate();
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [open, setOpen] = useState(false);

    const onSearch = (search) => {
        setFilteredData(
            search ? dataList.filter((dt) => dt.label.toLowerCase().includes(search.toLowerCase())) : dataList,
        );
    };

    useEffect(() => {
        if (localStorage.getItem("level") == "levelThree") {
            if (localStorage.getItem("is_trainer") == "true") {
                setDataList(trainer);
            } else {
                setDataList(learner);
            }
        } else if (localStorage.getItem("level") == "levelOne") {
            setDataList(administrator);
        } else if (localStorage.getItem("level") == "levelTwo") {
            setDataList(user);
        }
    }, [trainer, localStorage, learner, administrator, user]);

    useEffect(() => {
        setFilteredData(dataList?.filter((dt) => dt?.isActive == true));
    }, [dataList]);

    const onRedirect = (data) => {
        navigate(data?.key);
        setOpen(false);
    };

    const toggleSearchMenu = () => {
        setOpen((open) => !open);
    };

    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
                event.preventDefault();
                toggleSearchMenu();
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [toggleSearchMenu]);

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button
                    variant="reset"
                    className="tw-relative tw-w-[150px] tw-items-center !tw-rounded-[50px] tw-border"
                >
                    <Input
                        className="tw-border-none tw-bg-transparent tw-p-0 tw-outline-none focus-visible:tw-ring-0 focus-visible:tw-ring-offset-0"
                        placeholder="Ctrl + K"
                        readOnly
                        tabIndex={-1}
                    />
                    <Search size={20} className="tw-text-slate-500" />
                </Button>
            </DialogTrigger>
            <DialogContent className="tw-w-[500px] tw-p-4">
                <div className="tw-font-lexend">
                    <p className="tw-mb-2 tw-text-sm">Enter a query to navigate to a page or resource.</p>
                    <Input
                        onChange={(e) => onSearch(e.target.value)}
                        className="!tw-rounded-xl"
                        placeholder="Search here to navigate..."
                    />
                </div>
                <div className="tw-flex tw-flex-col tw-gap-1">
                    {filteredData.slice(0, 5).map((data, idx) => (
                        <div
                            key={idx}
                            onClick={() => onRedirect(data)}
                            className="tw-flex tw-cursor-pointer tw-items-center tw-gap-4 tw-rounded-md tw-p-1 tw-px-2 tw-font-lexend tw-text-sm tw-text-slate-600 hover:tw-bg-slate-100"
                        >
                            <i className={data.icon} />
                            {data.label}
                        </div>
                    ))}
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default SearchBox;
