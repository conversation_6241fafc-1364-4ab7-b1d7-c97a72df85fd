import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { tanstackApi } from "@/react-query/api";
import { useGetTemplate } from "@/react-query/quizz/template";
import { Pencil } from "lucide-react";
import { useState } from "react";
import { Link, useParams } from "react-router-dom";
import { toast } from "sonner";

const TemplateSettings = ({ CourseData, getCourses }) => {
    const params = useParams();
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const templates = useGetTemplate({ limit: 20, offset: 0 });
    const loginToken = localStorage.getItem("login_token");
    const handleClear = () => setSelectedTemplate(null);

    const onDataSubmit = async (e) => {
        if (!selectedTemplate) {
            toast.warning("Selecte Template", {
                description: "Please select an template to proceed!",
            });
            return false;
        }
        e.preventDefault();
        const payload = {
            course_id: params?.course_id,
            step: "details",
            data: {
                course_title: CourseData.course_title,
                category_id: CourseData.category_id,
                course_description: CourseData.course_description,
                sub_category_id: CourseData.sub_category_id,
                course_banner_url: CourseData.course_banner_url,
                keywords: CourseData.keywords,
                audience_type: CourseData.audience_type,
                template_id: selectedTemplate,
            },
        };

        await tanstackApi
            .post("course/creation/update-course-details", { ...payload })
            .then((res) => {
                toast.success("Template Updated Successfully", {
                    description: res?.data?.message,
                });

                getCourses({ course_id: params?.course_id });
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    return (
        <Card>
            <CardHeader>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-space-y-2">
                        <CardTitle>Course template</CardTitle>
                        <CardDescription>
                            Choose UI UX template for the course player. Click save when you&apos;re done.
                        </CardDescription>
                    </div>
                    <div>
                        <Button className="tw-rounded-xl" variant="outline" asChild>
                            <Link
                                to={`https://lms-course-builder.vercel.app/dashboard/templates/create?type=course&token=${loginToken}`}
                                target="_blank"
                            >
                                <i className="fa-solid fa-plus"></i> Create Template
                            </Link>
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[1.5fr_2fr] tw-gap-8">
                    <div
                        className="tw-aspect-[1.53] tw-overflow-hidden tw-rounded-[1.2vw] tw-bg-cover tw-bg-center tw-bg-no-repeat tw-shadow-md"
                        style={{
                            backgroundImage: `url(${templates.data?.data?.find((dt) => dt?.id == selectedTemplate)?.font_family?.thumbnail || "/assets/thumbnail.png"})`,
                        }}
                    ></div>
                    <div className="tw-grid tw-grid-cols-3 tw-gap-3">
                        {templates.data?.data
                            ?.filter((dt) => dt?.template_type == "course")
                            .slice(0, 6)
                            .map((template, idx) => (
                                <div
                                    key={idx}
                                    onClick={() => setSelectedTemplate(template?.id)}
                                    className={`${selectedTemplate == template?.id && "active_template"} tw-flex tw-h-[10.3vw] tw-cursor-pointer tw-flex-col tw-items-center tw-justify-between tw-gap-2 tw-rounded-2xl tw-border-[1px] tw-p-1 tw-shadow-sm hover:tw-bg-slate-100`}
                                >
                                    <div className="tw-aspect-[1.65] tw-overflow-hidden tw-rounded-xl">
                                        <img
                                            src={template?.font_family?.thumbnail || "/assets/thumbnail.png"}
                                            alt=""
                                            className="tw-h-full tw-w-full tw-object-cover"
                                        />
                                    </div>
                                    <div className="tw-flex tw-w-full tw-items-center tw-justify-between tw-gap-2 tw-px-2">
                                        <Label className="tw-pb-1">{template?.template_title}</Label>
                                        <Link
                                            to={`https://lms-course-builder.vercel.app/dashboard/templates/${template?.id}?token=${loginToken}`}
                                            target="_blank"
                                        >
                                            <Pencil size={15} />
                                        </Link>
                                    </div>
                                </div>
                            ))}
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" variant="outline" onClick={handleClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> Save
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default TemplateSettings;
