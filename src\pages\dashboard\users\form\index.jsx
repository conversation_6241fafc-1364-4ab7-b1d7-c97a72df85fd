import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { useAddTimelineLog } from "@/react-query/common/timeline";
import { useCreateUser, useUpdateUser } from "@/react-query/users";
import { AxiosError } from "axios";
import { Eye, EyeOff } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

const defaultValue = {
    first_name: "",
    last_name: "",
    email: "",
    password: "",
    confirm_password: "",
    bio_data: "bio_data",
    user_type_code: "learner_lms2246",
    user_lang: "en",
    status: "Active",
};

const userSchema = z
    .object({
        first_name: z
            .string()
            .min(1, "Please enter your first name.")
            .regex(/^[A-Za-z\s]+$/, `First name should contain only alphabets`)
            .refine((val) => !/\s/.test(val), {
                message: `First name cannot contain empty spaces.`,
            }),
        last_name: z
            .string()
            .min(1, "Please enter your last name.")
            .regex(/^[A-Za-z\s]+$/, `Last name should contain only alphabets`)
            .refine((val) => !/\s/.test(val), {
                message: `Last name cannot contain empty spaces.`,
            }),
        email: z
            .string({ required_error: "Email id cannot be empty" })
            .trim()
            .min(1, "Email id cannot be empty")
            .email("Invalid email address."),
        password: z
            .string()
            .trim()
            .min(1, "Password cannot be empty")
            .min(8, "Password must be at least 8 characters long."),
        confirm_password: z
            .string()
            .trim()
            .min(1, "Confirm password cannot be empty")
            .min(8, "Confirm password must be at least 8 characters long."),
        user_type_code: z.string().min(1, "Please select a user type."),
        user_lang: z.string().default("en"),
        status: z.string().default("Active"),
    })
    .refine((val) => val.password === val.confirm_password, {
        message: "Passwords do not match",
        path: ["confirm_password"],
    });

const updateUserSchema = z.object({
    id: z.number(),
    first_name: z
        .string()
        .trim()
        .min(1, "Please enter your first name.")
        .regex(/^[A-Za-z\s]+$/, `First name should contain only alphabets`)
        .refine((val) => val.trim(), {
            message: `First name cannot contain empty spaces.`,
        }),
    last_name: z
        .string()
        .trim()
        .regex(/^[A-Za-z\s]+$/, `Last name should contain only alphabets`)
        .refine((val) => val.trim(), {
            message: `Last name cannot contain empty spaces.`,
        })
        .optional()
        .or(z.literal("").or(z.undefined())),
    email: z
        .string({ required_error: "Email id cannot be empty" })
        .trim()
        .min(1, "Email id cannot be empty")
        .email("Invalid email address."),
    password: z
        .string()
        .trim()
        .min(1, "Password cannot be empty")
        .min(8, "Password must be at least 8 characters long.")
        .optional()
        .or(z.literal("").or(z.undefined())),
    confirm_password: z
        .string()
        .trim()
        .min(1, "Confirm password cannot be empty")
        .min(8, "Confirm password must be at least 8 characters long.")
        .optional()
        .or(z.literal("").or(z.undefined())),
    user_type_code: z.string().optional().or(z.literal("").or(z.undefined())),
    user_lang: z.string().default("en"),
    status: z.string().default("Active"),
});

export default function UserForm({ open, setOpen, selected, roles }) {
    const createUser = useCreateUser();
    const updateUser = useUpdateUser();
    const addTimelineLog = useAddTimelineLog();
    const [data, setData] = useState(defaultValue);
    const formRef = useRef();
    console.log(data);

    useEffect(() => {
        if (selected) {
            selected.password = "";
            const filteredData = Object.keys({ ...defaultValue, id: "" }).reduce((acc, key) => {
                if (key in selected) acc[key] = selected[key];
                return acc;
            }, {});
            filteredData.is_trainer = Boolean(filteredData.is_trainer);
            filteredData.user_type_code = selected.role;
            setData(filteredData);
        } else {
            setData(defaultValue);
        }
    }, [selected]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        let result = {};

        if (selected) {
            result = updateUserSchema.safeParse(data);
        } else {
            result = userSchema.safeParse(data);
        }

        if (!result.success) {
            return toast.error(result.error.errors[0].message);
        }
        const payload = result.data;
        if (payload.password && payload.password !== data.confirm_password) {
            return toast.error("Passwords do not match", {
                description: "Please make sure that your passwords match.",
            });
        }

        if (payload.user_type_code == "trainer") payload.is_trainer = true;
        try {
            delete payload.confirm_password;
            if (selected) {
                if (!payload.password) delete payload.password;
                payload.is_trainer = Boolean(payload.is_trainer);
                await updateUser.mutateAsync(payload, {
                    onSuccess: (data) => {
                        let payload = {
                            user_id: window.localStorage.getItem("userId"),
                            event: "user",
                            log: `${`${data?.data?.first_name} ${data?.data?.last_name}`} updated successfully`,
                        };
                        addTimelineLog.mutate(payload);
                        toast.success("User Updated Successfully");
                    },
                });
            } else {
                delete payload.id;
                await createUser.mutateAsync(payload, {
                    onSuccess: (data) => {
                        let payload = {
                            user_id: window.localStorage.getItem("userId"),
                            event: "user",
                            log: `${`${data?.data?.first_name} ${data?.data?.last_name}`} created successfully`,
                        };
                        addTimelineLog.mutate(payload);
                        toast.success("User Created Successfully");
                    },
                });
            }
            setData(defaultValue);
            setOpen(false);
        } catch (error) {
            return toast.error("Something went wrong", {
                description: error instanceof AxiosError ? error.response.data.message : error.message,
            });
        }
    };

    const onChangeHandle = (e) => {
        setData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    };

    const onCheckChangeHandle = (name, checked) => {
        setData((prev) => ({ ...prev, [name]: checked }));
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>{selected ? "Edit User" : "Create User"}</DialogTitle>
                </DialogHeader>
                <div className="tw-mt-3">
                    <form ref={formRef} onSubmit={handleSubmit} className="tw-space-y-5">
                        <div className="tw-grid tw-grid-cols-2 tw-gap-3">
                            <Input type="hidden" name="id" id="id" value={data?.id} />
                            <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                                <Label htmlFor="first_name">First Name</Label>
                                <Input
                                    type="text"
                                    name="first_name"
                                    value={data?.first_name}
                                    onChange={onChangeHandle}
                                    id="first_name"
                                    placeholder="John"
                                />
                            </div>
                            <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                                <Label htmlFor="last_name">Last Name</Label>
                                <Input
                                    type="text"
                                    name="last_name"
                                    value={data?.last_name}
                                    onChange={onChangeHandle}
                                    id="last_name"
                                    placeholder="Doe"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-2 tw-gap-3">
                            <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    type="email"
                                    name="email"
                                    value={data?.email}
                                    onChange={onChangeHandle}
                                    id="email"
                                    placeholder="<EMAIL>"
                                />
                            </div>
                            <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                                <Label htmlFor="user_type_code">User Type</Label>
                                <SelectNative
                                    name="user_type_code"
                                    id="user_type_code"
                                    value={data?.user_type_code}
                                    onChange={onChangeHandle}
                                >
                                    <option value="">Select</option>
                                    {roles.map((rl) => (
                                        <option key={rl.id} value={rl.role_code}>
                                            {rl.display_name}
                                        </option>
                                    ))}
                                </SelectNative>
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-2 tw-gap-3">
                            <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                                <Label htmlFor="password">Password</Label>
                                <PasswordInput
                                    name="password"
                                    value={data?.password}
                                    onChange={onChangeHandle}
                                    id="password"
                                    placeholder="********"
                                />
                            </div>
                            <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                                <Label htmlFor="confirm_password">Confirm Password</Label>
                                <PasswordInput
                                    name="confirm_password"
                                    value={data?.confirm_password}
                                    onChange={onChangeHandle}
                                    id="confirm_password"
                                    placeholder="********"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-w-full tw-grid-cols-2 tw-items-center tw-gap-1.5">
                            <div className="tw-grid tw-grid-cols-2 tw-gap-3">
                                {selected && (
                                    <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                                        <Label htmlFor="status">Status</Label>
                                        <SelectNative
                                            name="status"
                                            id="status"
                                            value={data?.status}
                                            onChange={onChangeHandle}
                                        >
                                            <option value="Active">Active</option>
                                            <option value="Inactive">Inactive</option>
                                            <option value="Pending">Pending</option>
                                            <option value="Delete">Delete</option>
                                        </SelectNative>
                                    </div>
                                )}
                            </div>
                            <div className="tw-flex tw-gap-2">
                                <Checkbox
                                    name="is_trainer"
                                    checked={data?.is_trainer}
                                    onCheckedChange={(checked) => onCheckChangeHandle("is_trainer", checked)}
                                    id="is_trainer"
                                />
                                <Label htmlFor="is_trainer">Is trainer</Label>
                            </div>
                        </div>
                    </form>
                </div>
                <DialogFooter>
                    <Button variant="secondary" onClick={() => setOpen(false)}>
                        Cancel
                    </Button>
                    <Button variant="success" onClick={() => formRef.current.requestSubmit()}>
                        {selected ? "Save" : "Create"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

function PasswordInput({ ...props }) {
    const [isVisible, setIsVisible] = useState(false);
    const toggleVisibility = () => setIsVisible((prevState) => !prevState);

    return (
        <div className="tw-relative">
            <Input {...props} className="tw-pe-9" type={isVisible ? "text" : "password"} />
            <button
                className="tw-absolute tw-inset-y-0 tw-end-0 tw-flex tw-h-full tw-w-9 tw-items-center tw-justify-center tw-rounded-e-lg tw-text-muted-foreground/80 tw-outline-offset-2 tw-transition-colors hover:tw-text-foreground focus:tw-z-10 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-ring/70 disabled:tw-pointer-events-none disabled:tw-cursor-not-allowed disabled:tw-opacity-50"
                type="button"
                onClick={toggleVisibility}
                aria-label={isVisible ? "Hide password" : "Show password"}
                aria-pressed={isVisible}
                aria-controls="password"
            >
                {!isVisible ? (
                    <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                ) : (
                    <Eye size={16} strokeWidth={2} aria-hidden="true" />
                )}
            </button>
        </div>
    );
}
