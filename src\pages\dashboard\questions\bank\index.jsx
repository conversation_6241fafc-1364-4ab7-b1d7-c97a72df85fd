import Pagination from "@/components/table/pagination";
import { But<PERSON> } from "@/components/ui/button";
import { usePagination } from "@/hooks/use-pagination";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const ITEMS_PER_PAGE = 7;

const QuestionBank = () => {
    const navigate = useNavigate();
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [questionTypes, setQuestionTypes] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(dataList.length / ITEMS_PER_PAGE);
    const { pages } = usePagination({
        currentPage,
        totalPages,
        paginationItemsToDisplay: ITEMS_PER_PAGE,
    });

    useEffect(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        let options = dataList.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, dataList]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const [filterState, setFilterState] = useState({
        search: "",
        type: "",
        difficulty: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.question?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const matchesTag = filterState?.type ? item?.question_type == filterState?.type : true; // Allow all items if no subCategory filter
            const difficulty = filterState?.difficulty ? item?.difficulty_level == filterState?.difficulty : true; // Allow all items if no subCategory filter

            return matchesSearch && matchesTag && difficulty; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            type: "",
            difficulty: "",
        });
    };

    useEffect(() => {
        getQuestions();
        getQuestionTypes();
    }, []);

    useEffect(() => {
        setFilteredData(dataList);
    }, [dataList]);

    useEffect(() => {
        setTableData(filteredData);
    }, [filteredData]);

    const getQuestions = async () => {
        await tanstackApi
            .post("questions/list", {})
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const getQuestionTypes = async () => {
        await tanstackApi
            .get("question-type/list")
            .then((res) => {
                setQuestionTypes(res?.data?.data.filter((type) => type?.displayName.trim()));
            })
            .catch((err) => {
                setQuestionTypes([]);
            });
    };

    const onAddNewType = () => {
        navigate(`/dashboard/question-create`);
    };

    const onEditType = (data) => {
        navigate(`/dashboard/question-create/${data?.id}`);
    };

    return (
        <>
            <div>
                <div className="tw-flex tw-justify-between">
                    <h4 className="tw-text-xl tw-font-semibold">
                        <i className="fa-solid fa-server"></i> Question Banks
                    </h4>
                    <Button className="tw-px-2 tw-py-1" onClick={onAddNewType}>
                        <i className="fa-solid fa-plus"></i> New Question
                    </Button>
                </div>
                <br />
                <div className="page_filters tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Question
                        </label>
                        <input
                            onChange={onFilterChange}
                            value={filterState?.search}
                            name="search"
                            className="tw-text-sm"
                            style={{ minWidth: "300px" }}
                            type="text"
                            placeholder="Search by display Name ..."
                        />
                    </div>
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Type
                        </label>
                        <select
                            className="tw-text-sm"
                            onChange={onFilterChange}
                            value={filterState?.type}
                            name="type"
                            id=""
                        >
                            <option value=""> - Choose Type - </option>
                            {questionTypes?.map((type, idx) => (
                                <option value={type?.type} key={idx}>
                                    {type?.displayName}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Difficulty
                        </label>
                        <select
                            className="tw-text-sm"
                            onChange={onFilterChange}
                            value={filterState?.difficulty}
                            name="difficulty"
                            id=""
                        >
                            <option value=""> - All - </option>
                            <option value="easy">Easy</option>
                            <option value="hard">Hard</option>
                            <option value="medium">Medium</option>
                        </select>
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>Question</th>
                                <th>Type</th>
                                <th>Difficulty Level</th>
                                <th>Author</th>
                                <th>Creation On</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>{row?.question}</td>
                                    <td>{row?.questionTypeDetails?.displayName}</td>
                                    <td>{row?.difficulty_level}</td>
                                    <td>
                                        {row?.lms_user?.first_name
                                            ? `${row?.lms_user?.first_name} ${row?.lms_user?.last_name}`
                                            : "-"}
                                    </td>
                                    <td>{moment(row?.createdAt).format("LL")}</td>
                                    <td>
                                        <button className="selected_btn" onClick={() => onEditType(row)}>
                                            <i className="fa-solid fa-edit"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">7</p>
                    </div>
                    <div>
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            handlePageChange={handlePageChange}
                            itemsPerPage={ITEMS_PER_PAGE}
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export default QuestionBank;
