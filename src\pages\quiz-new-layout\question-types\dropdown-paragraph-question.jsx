"use client";

import { useState, useEffect } from "react";
import { Check, X } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default function DropdownParagraphQuestion({ question, onAnswerSubmit, showCorrectAnswer, savedAnswer }) {
    const [selectedOptions, setSelectedOptions] = useState(Array(question.dropdownOptions.length).fill(null));
    const [submitted, setSubmitted] = useState(false);

    useEffect(() => {
        if (savedAnswer) {
            setSelectedOptions(savedAnswer);
            setSubmitted(true);
        }
    }, [savedAnswer]);

    const handleOptionChange = (index, value) => {
        const newSelectedOptions = [...selectedOptions];
        newSelectedOptions[index] = Number.parseInt(value);
        setSelectedOptions(newSelectedOptions);
    };

    const handleSubmit = () => {
        // Check if all dropdowns have a selection
        if (selectedOptions.every((option) => option !== null)) {
            setSubmitted(true);

            // Convert to non-null array since we've checked all are selected
            const answers = selectedOptions;

            // Check if the answers match the correct answers
            const correct = JSON.stringify(answers) === JSON.stringify(question.correctAnswers);
            onAnswerSubmit(answers, correct);
        }
    };

    const renderParagraph = () => {
        return (
            <div className="tw-text-lg tw-leading-relaxed">
                {question.paragraphSegments.map((segment, index) => (
                    <span key={index}>
                        {segment}
                        {index < question.paragraphSegments.length - 1 && renderDropdown(index)}
                    </span>
                ))}
            </div>
        );
    };

    const renderDropdown = (index) => {
        const options = question.dropdownOptions[index];
        const selectedOption = selectedOptions[index];
        const correctOption = question.correctAnswers[index];
        const isCorrect = selectedOption === correctOption;

        return (
            <span className="tw-mx-1 tw-inline-block">
                <Select
                    disabled={submitted}
                    value={selectedOption !== null ? selectedOption.toString() : undefined}
                    onValueChange={(value) => handleOptionChange(index, value)}
                >
                    <SelectTrigger
                        className={cn(
                            "tw-h-8 tw-min-w-32 tw-border-0 !tw-bg-background/50 tw-font-medium tw-text-black",
                            submitted
                                ? isCorrect
                                    ? "tw-border-green-500 tw-bg-green-700/30"
                                    : "tw-border-red-500 tw-bg-red-700/30"
                                : "tw-border-orange-400",
                        )}
                    >
                        <SelectValue placeholder="Select..." />
                    </SelectTrigger>
                    <SelectContent>
                        {options.map((option, optionIndex) => (
                            <SelectItem key={optionIndex} value={optionIndex.toString()}>
                                {option}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                {submitted && (
                    <span className="tw-ml-1">
                        {isCorrect ? (
                            <Check className="tw-inline tw-text-green-400" />
                        ) : (
                            <X className="tw-inline tw-text-red-400" />
                        )}
                    </span>
                )}
                {showCorrectAnswer && !isCorrect && (
                    <span className="tw-ml-1 tw-text-sm tw-text-green-400">({options[correctOption]})</span>
                )}
            </span>
        );
    };

    return (
        <div className="tw-space-y-4">
            <p className="tw-mb-2 tw-text-sm tw-text-orange-300">
                Select the correct option for each blank in the paragraph
            </p>

            {renderParagraph()}

            {!submitted && selectedOptions.every((option) => option !== null) && (
                <Button onClick={handleSubmit} className="tw-mt-4 tw-bg-teal-700 tw-text-white hover:tw-bg-teal-600">
                    Submit Answer
                </Button>
            )}
        </div>
    );
}
