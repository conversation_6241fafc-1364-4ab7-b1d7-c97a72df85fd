import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const AddBundlesToDomainType = ({ userGroupData, getUserGroups }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [bundleList, setBundleList] = useState([]);
    const [domainBundles, setDomainBundles] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);

    const [selectedBundle, setSelectedBundle] = useState(null);

    useEffect(() => {
        getBundles();
        if (params?.domain_type_id !== undefined) {
            getDomainBundles(params?.domain_type_id);
        }
    }, [params]);

    const getBundles = async (payload) => {
        await tanstackApi
            .get("course-bundle/get-course-bundles")
            .then((res) => {
                setBundleList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setBundleList([]);
            });
    };

    const getDomainBundles = async () => {
        await tanstackApi
            .post("domain-course/list-assigned-courses-and-bundles", {})
            .then((res) => {
                setDomainBundles(
                    res?.data?.data
                        ?.filter(
                            (dt) =>
                                dt?.course_bundle_id !== null && dt.domain_type_id == Number(params?.domain_type_id),
                        )
                        ?.map((dt) => dt?.course_bundle_id),
                );
            })
            .catch((err) => {
                setDomainBundles([]);
            });
    };

    const onBundleSelection = (item) => {
        setSelectedBundle(item);
        setOpenAlert(true);
    };

    const onDataSubmit = async () => {
        if (selectedBundle !== null) {
            const payload = {
                domain_type: params?.domain_type_id,
                course_bundle_id: selectedBundle,
            };

            await tanstackApi
                .post("domain-course/assign-course-bundle-to-domain-type", { ...payload })
                .then((res) => {
                    toast.success("Assigned Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    getDomainBundles();
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected bundles to all the domains which having &quot;
                            {params?.domain_type_id}&quot; domain type.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Assign Course bundles to domain type - {params?.domain_type_id}</CardTitle>
                            <CardDescription>
                                Click on select button to select any bundle for {params?.domain_type_id}. Click on Add
                                Bundles button when you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{domainBundles?.length} Bundle Added</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Logo</th>
                                    <th>Bundle Name</th>
                                    <th>Course</th>
                                    <th>Created On</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {bundleList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td
                                            style={{ width: "130px" }}
                                            onClick={() => navigate(`/dashboard/course-bundle-create/${row?.id}`)}
                                        >
                                            <div className="table_image_alpha">
                                                <img
                                                    src={row?.logo_image_url || "/assets/course-placeholder.png"}
                                                    alt=""
                                                />
                                            </div>
                                        </td>
                                        <td onClick={() => navigate(`/dashboard/course-bundle-create/${row?.id}`)}>
                                            {row?.name}
                                        </td>
                                        <td>{row?.is_courses_added} Courses</td>
                                        <td>{moment(row?.createdAt).format("LL")}</td>
                                        <td>
                                            {domainBundles?.includes(row?.id) ? (
                                                <Button variant="">
                                                    <i className="fa-regular fa-circle-check"></i> Bundle Added
                                                </Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onBundleSelection(row?.id)}>
                                                    <i className="fa-regular fa-plus"></i> Add Bundle
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
            </Card>
        </>
    );
};

export default AddBundlesToDomainType;
