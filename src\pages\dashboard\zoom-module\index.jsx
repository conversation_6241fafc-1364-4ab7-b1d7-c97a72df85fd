import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import AddCredentails from "./AddCredentails";
import ScheduleClassAddEdit from "./ScheduleClassAddEdit";
import { useZoomCredCheck } from "@/react-query/zoom";

const ITEMS_PER_PAGE = 7;

const ZoomModule = () => {
    const navigate = useNavigate();
    const zoomCredCheck = useZoomCredCheck();
    const [editData, setEditData] = useState(null);
    const [open, setOpen] = useState(false);
    const [openClass, setOpenClass] = useState(false);
    const isRegistered = zoomCredCheck.data?.success;
    const [openAlert, setOpenAlert] = useState(false);

    const [courseList, setCourseList] = useState([]);
    const [chapterList, setChapterList] = useState([]);

    const [dataList, setDataList] = useState([]);
    const [tableData, setTableData] = useState([]);

    const [filteredData, setFilteredData] = useState([]);
    const [filterState, setFilterState] = useState({
        search: "",
        date: "",
        course_id: "",
        chapter_id: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
        if (name == "course_id") {
            getChapters({ course_id: value });
        }
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.topic?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const chapter = filterState?.chapter_id ? item?.chapter_id == filterState?.chapter_id : true; // Allow all items if no subCategory filter
            const course = filterState?.course_id ? item?.chapter?.lms_course?.id == filterState?.course_id : true; // Allow all items if no subCategory filter
            const date = filterState?.date ? item?.date?.slice(0, 10) == filterState?.date : true; // Allow all items if no subCategory filter

            return matchesSearch && chapter && course && date; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            date: "",
            course_id: "",
            chapter_id: "",
        });
    };

    useEffect(() => {
        // checkCreds();
    }, []);

    const checkCreds = async () => {
        await tanstackApi
            .get("creds/check")
            .then((res) => {
                // setIsRegistered(res?.data?.success);
            })
            .catch((err) => {});
    };

    const onAddClassSchedule = () => {
        setEditData(null);
        setOpenClass(true);
    };

    const onEditClassSchedule = (data) => {
        setEditData(data);
        setOpenClass(true);
    };

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(dataList.length / ITEMS_PER_PAGE);

    useEffect(() => {
        if (dataList?.length > 0) {
            const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
            const endIndex = startIndex + ITEMS_PER_PAGE;
            let options = dataList.slice(startIndex, endIndex);
            setTableData(options);
        }
    }, [currentPage, dataList]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        getSchdedules();
        getCourses();
    }, []);

    useEffect(() => {
        setFilteredData(dataList);
    }, [dataList]);

    useEffect(() => {
        setTableData(filteredData);
    }, [filteredData]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getChapters = async (payload) => {
        await tanstackApi
            .post("course/view-course", { ...payload })
            .then((res) => {
                setChapterList(res?.data?.data.courseChapters);
            })
            .catch((err) => {
                setChapterList([]);
            });
    };

    const getSchdedules = async () => {
        await tanstackApi
            .get("chapter-class/list")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const onDelete = (data) => {
        setEditData(data);
        setOpenAlert(true);
    };

    const onDeleteSchedule = async () => {
        await tanstackApi
            .delete(`chapter-class/delete/${editData?.id}`)
            .then((res) => {
                getSchdedules();
                toast.success("Schedule Deleted", {
                    description: res?.data?.message,
                });
            })
            .catch((err) => {
                setDataList([]);
                toast.error("Something went wrong!", {
                    description: err?.response?.data?.message,
                });
            });
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Schedule, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible. Selected chapter schedule will be deleted permanantly.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDeleteSchedule}>Yes Delete!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <AddCredentails
                open={open}
                setOpen={setOpen}
                editData={editData}
                isRegistered={isRegistered}
                checkCreds={checkCreds}
            />
            <ScheduleClassAddEdit
                open={openClass}
                setOpen={setOpenClass}
                editData={editData}
                isRegistered={isRegistered}
                getSchdedules={getSchdedules}
            />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Zoom Schedules</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <div className="tw-space-x-2">
                    {isRegistered && (
                        <Button variant="success" className="tw-rounded-xl">
                            Integrated <i className="fa-solid fa-circle-check"></i>
                        </Button>
                    )}
                    <Button variant="outline" onClick={() => setOpen(true)}>
                        <i className="fa-solid fa-shield-halved"></i> {isRegistered ? "Update" : "Add"} Credentials
                    </Button>
                </div>
                <div className="tw-space-x-2">
                    <Button onClick={onAddClassSchedule}>
                        <i className="fa-regular fa-clock"></i> Schedule Class
                    </Button>
                </div>
            </div>
            <div className="page_filters tw-mt-5 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Class topic
                    </label>
                    <input
                        value={filterState?.search}
                        name="search"
                        onChange={onFilterChange}
                        className="tw-text-sm"
                        style={{ minWidth: "250px" }}
                        type="text"
                        placeholder="Search by class topic ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Schedule Date
                    </label>
                    <input
                        value={filterState?.date}
                        name="date"
                        onChange={onFilterChange}
                        className="tw-text-sm"
                        type="date"
                    />
                </div>

                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table tw-mt-4">
                <table>
                    <thead>
                        <tr>
                            <th>Meet</th>
                            <th>Type</th>
                            <th>Topic</th>
                            <th>Scheduled On</th>
                            <th>Time</th>
                            <th>Duration</th>
                            <th>Location</th>
                            <th>Creation On</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>
                                    <div className="tw-flex tw-items-center tw-gap-2">
                                        <img
                                            className="tw-h-8"
                                            src={
                                                row?.meet_type == "zoom"
                                                    ? "/assets/zoom.png"
                                                    : row?.meet_type == "google-meet"
                                                      ? "/assets/google.png"
                                                      : ""
                                            }
                                            alt=""
                                        />
                                        <Label>
                                            {" "}
                                            {row?.meet_type == "zoom"
                                                ? "Zoom"
                                                : row?.meet_type == "google-meet"
                                                  ? "Google"
                                                  : ""}
                                        </Label>
                                    </div>
                                </td>
                                <td>
                                    {row?.type == "online" ? (
                                        <Badge className={"tw-font-lexend"}>Online</Badge>
                                    ) : (
                                        <Badge variant={"outline"} className={"tw-font-lexend"}>
                                            Offline
                                        </Badge>
                                    )}
                                </td>
                                <td>{row?.topic}</td>
                                <td>{moment(row?.date).format("LL")}</td>
                                <td>{`${row?.start_time} - ${row?.end_time}`}</td>
                                <td>{`${row?.duration} mins`}</td>
                                <td>
                                    {row?.type == "online" && (
                                        <Button variant="outline" onClick={() => window.open(row?.location, "_blank")}>
                                            <i className="fa-solid fa-video"></i> Join
                                        </Button>
                                    )}
                                </td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td className="tw-space-x-2">
                                    <button className="selected_btn" onClick={() => onEditClassSchedule(row)}>
                                        <i className="fa-solid fa-edit"></i> Edit
                                    </button>
                                    <button className="selected_btn" onClick={() => onDelete(row)}>
                                        <i className="fa-solid fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">7</p>
                </div>
                <div>
                    <Pagination className="tw-mx-0 tw-w-[auto]">
                        <PaginationContent>
                            {/* Previous Button */}
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page Numbers */}
                            {[...Array(totalPages)].map((_, index) => (
                                <PaginationItem key={index}>
                                    <PaginationLink
                                        href="#"
                                        isActive={index + 1 === currentPage}
                                        onClick={() => handlePageChange(index + 1)}
                                    >
                                        {index + 1}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {/* Ellipsis */}
                            {totalPages > 5 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            {/* Next Button */}
                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </div>
    );
};

export default ZoomModule;
