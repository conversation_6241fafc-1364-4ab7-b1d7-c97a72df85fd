import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import S<PERSON><PERSON>eader, { AddContentFunction } from "@/pages/dashboard/createcourse/chaptercontent/slides/common";
import { iconsList } from "@/pages/dashboard/createcourse/chaptercontent/slides/icon-list";
import { useInView } from "framer-motion";
import {
    BookOpenCheck,
    CircleFadingPlus,
    CircleHelp,
    Clock,
    Cog,
    Package,
    Presentation,
    Tags,
    Trash2,
    UserRoundPen,
    X,
} from "lucide-react";
import moment from "moment";
import { useEffect, useRef, useState } from "react";

const Quiz = ({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    onSetDelete,
    setCurrentId,
    setContentArray,
    contentArray,
    setOpenIconDialog,
}) => {
    const containerRef = useRef(null);
    const inView = useInView(containerRef, { amount: 0.8 });
    useEffect(() => {
        if (inView) setCurrentId(`#slide_${index}`);
    }, [inView]);
    return (
        <div
            ref={containerRef}
            className="tw-grid tw-min-h-[35vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${template?.background || content?.background})` }}
        >
            <SlideHeader
                index={index}
                content={content}
                contentArray={contentArray}
                onSlideEdit={onSlideEdit}
                onSetDelete={onSetDelete}
                onRemoveSlide={onRemoveSlide}
                setOpenIconDialog={setOpenIconDialog}
                setContentArray={setContentArray}
            />
            {content?.quiz == null ? (
                <AddContentFunction onSlideEdit={onSlideEdit} content={content} index={index} />
            ) : (
                <div className="tw-space-y-5 tw-px-10 tw-py-7">
                    <div className="tw-grid tw-grid-cols-[.4fr_.60fr] tw-gap-5">
                        <div className="tw-w-full tw-rounded-xl">
                            <img
                                className="tw-w-full tw-rounded-xl tw-object-cover"
                                src={content?.quiz?.thumbnail_img || "/assets/thumbnail.jpg"}
                            />
                        </div>
                        <div>
                            <h1 className="tw-text-3xl tw-font-bold">{content?.quiz?.title}</h1>
                            <p className="my-2 tw-text-lg">{content?.quiz?.description}</p>

                            <div className="tw-mt-5 tw-grid tw-grid-cols-2 tw-gap-2 tw-space-y-1">
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-[16px] tw-font-normal">
                                    <Package size={22} /> {content?.quiz?.max_points} Points
                                </span>
                                <span className="tw-flex tw-items-center tw-gap-2 tw-text-[16px] tw-font-normal">
                                    <BookOpenCheck size={22} /> {content?.quiz?.passing_marks} Passing Marks
                                </span>
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-[16px] tw-font-normal">
                                    <UserRoundPen size={22} /> {content?.quiz?.trainer_name}
                                </span>
                                <span className="tw-flex tw-items-center tw-gap-2 tw-text-[16px] tw-font-normal">
                                    <Clock size={22} />{" "}
                                    {calculateDuration(content?.quiz?.start_time, content?.quiz?.end_time)}
                                </span>
                                <span className="tw-flex tw-items-center tw-gap-2 tw-text-[16px] tw-font-normal">
                                    <Tags size={22} /> {content?.quiz?.is_graded ? "Graded" : "Not Graded"}
                                </span>
                                <span className="tw-flex tw-items-center tw-gap-2 tw-text-[16px] tw-font-normal">
                                    <CircleHelp size={22} /> {content?.quiz?.components?.length} Questions
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Quiz;

function calculateDuration(startTime, endTime) {
    const start = moment(startTime, "HH:mm:ss");
    const end = moment(endTime, "HH:mm:ss");

    const duration = moment.duration(end.diff(start));

    const hours = Math.floor(duration.asHours());
    const minutes = duration.minutes();
    const seconds = duration.seconds();

    // Return the duration as a formatted string
    return `${hours} hours, ${minutes} minutes, ${seconds} seconds`;
}
