import { Badge } from "@/components/ui/badge";
import {
    <PERSON><PERSON><PERSON><PERSON>b,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { BookCheck, Box, Clock } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

function calculateTotalPoints(questionsArray, key) {
    // Check if input is an array
    if (!Array.isArray(questionsArray)) {
        throw new Error("Input must be an array of question objects");
    }

    // Use reduce to accumulate total points
    const totalPoints = questionsArray.reduce((sum, question) => {
        // Validate if the current object has 'points' and it's a number
        if (question && typeof question[key] === "number") {
            return sum + question[key];
        } else {
            console.warn(`Skipping invalid question at index ${questionsArray.indexOf(question)}`);
            return sum; // Skip invalid objects
        }
    }, 0);

    return totalPoints;
}

const ViewSubmittedQuiz = () => {
    const params = useParams();
    const navigate = useNavigate();
    const [quizData, setQuizData] = useState(null);

    useEffect(() => {
        if (params?.id !== undefined) {
            getSubmissionsData(params?.id);
        }
    }, [params]);

    const getSubmissionsData = async (payload) => {
        if (localStorage.getItem("is_trainer") == "true") {
            await tanstackApi
                .post("quiz/list-submissions", { trainer_id: localStorage.getItem("userId") })
                .then((res) => {
                    setQuizData(res?.data?.data?.find((dt) => dt?.id == Number(payload)) || null);
                })
                .catch((err) => {
                    setQuizData(null);
                });
        } else {
            await tanstackApi
                .post("quiz/list-submissions", { user_id: localStorage.getItem("userId") })
                .then((res) => {
                    setQuizData(res?.data?.data?.find((dt) => dt?.id == Number(payload)) || null);
                })
                .catch((err) => {
                    setQuizData(null);
                });
        }
    };

    const onBack = () => {
        if (localStorage.getItem("is_trainer") == "true") {
            navigate(`/dashboard/submissions`);
        } else {
            navigate(`/dashboard/submissions-learner`);
        }
    };

    return (
        <div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            {localStorage.getItem("is_trainer") == "true" ? (
                                <BreadcrumbLink href="/dashboard/submissions">Submissions by learners</BreadcrumbLink>
                            ) : (
                                <BreadcrumbLink href="/dashboard/submissions-learner">All Submission</BreadcrumbLink>
                            )}
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Submitted Quiz</BreadcrumbPage>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>View Quiz</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <div>
                    <Button onClick={onBack}>
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                </div>
            </div>
            <div className="tw-grid tw-w-full tw-grid-cols-[250px_1fr] tw-gap-7 tw-border-secondary tw-p-2">
                <div className="tw-space-y-5">
                    <div className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-border-[1px]">
                        {quizData?.quiz?.thumbnail_img ? (
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-object-cover tw-shadow-sm"
                                src={quizData?.quiz?.thumbnail_img}
                            />
                        ) : (
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-object-cover tw-shadow-sm"
                                src={"/assets/thumbnail.png"}
                            />
                        )}
                    </div>
                </div>
                <div>
                    <h1 className="tw-text-3xl tw-font-bold">{quizData?.quiz?.title}</h1>
                    <p className="tw-mt-4 tw-line-clamp-2 tw-font-mono tw-text-[16px] tw-font-semibold tw-text-slate-500">
                        {quizData?.quiz?.description}
                    </p>
                    <div className="tw-mt-4 tw-grid tw-grid-cols-2 tw-gap-3">
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <Box size={18} />{" "}
                            <div>
                                Total Marks{" "}
                                {calculateTotalPoints(
                                    quizData?.components?.length > 0 ? quizData?.components : [],
                                    "earnedPoints",
                                )}
                                {" / "}
                                {calculateTotalPoints(
                                    quizData?.components?.length > 0 ? quizData?.components : [],
                                    "points",
                                )}
                            </div>
                        </span>{" "}
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <Box size={18} />{" "}
                            <div>
                                Passing Marks{" "}
                                {quizData?.quiz?.passing_marks &&
                                    `${quizData?.quiz?.passing_marks} - ${quizData?.quiz?.max_points}`}
                            </div>
                        </span>{" "}
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <BookCheck size={18} /> <Badge>{quizData?.result}</Badge>
                        </span>{" "}
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <Clock size={18} /> Submitted On{" "}
                            {quizData?.createdAt ? (
                                moment(quizData?.createdAt).format("LL")
                            ) : (
                                <span className="tw-text-xs tw-text-slate-400"> _ _ / _ _ / _ _ _ _</span>
                            )}
                        </span>{" "}
                    </div>
                </div>
            </div>
            <div className="tw-mt-5">
                <h1 className="tw-mb-3 tw-font-lexend tw-font-medium">#. Submission Summary :-</h1>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>SN.</th>
                                <th>Question</th>
                                <th>Attempted</th>
                                <th>Max Points</th>
                                <th>Earned Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            {quizData?.components?.map((question, index) => (
                                <tr key={index}>
                                    <td>{index + 1}.</td>
                                    <td>{question?.name}</td>
                                    <td>{question?.attempted ? "Yes" : "No"}</td>
                                    <td>{question?.points}</td>
                                    <td>{question?.earnedPoints}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default ViewSubmittedQuiz;
