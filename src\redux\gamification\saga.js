import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import {
    FetchBadgesList,
    GetBadgesData,
    GetHomeworkData,
    GetMyGroups,
    GetMySubGroups,
    GetQuizData,
    GetUserHomeworkData,
} from "@/redux/gamification/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* fetchBadges() {
    const data = yield axios
        .get(`${CONSTANTS.getAPI()}badges/list`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data?.data)
        .catch((err) => err.response.data);
    if (data) {
        yield put(GetBadgesData(data));
    }
}

function* addNewBadge(data) {
    const addData = yield axios
        .post(`${CONSTANTS.getAPI()}badges/create`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (addData) {
        yield put(fetchBadges());
    }
}

function* updateBadges(Data) {
    const data = yield axios
        .put(`${CONSTANTS.getAPI()}badges/update`, Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response?.data?.message;
            return errMsg;
        });
    if (data) {
        yield put(FetchBadgesList());
        // yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
    } else {
        // yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
        yield null;
    }
}

function* fetchQuizList(params) {
    const data = yield axios
        .post(`${CONSTANTS.getAPI()}quiz/list`, params?.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data?.data)
        .catch((err) => err.response.data);
    if (data) {
        yield put(GetQuizData(data));
    }
}

function* submitQuiz(data) {
    const addData = yield axios
        .post(`${CONSTANTS.getAPI()}quiz/submit`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
}

function* fetchHomeworkList(params) {
    const data = yield axios
        .post(`${CONSTANTS.getAPI()}homework/list`, params?.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data?.data)
        .catch((err) => err.response.data);
    if (data) {
        yield put(GetHomeworkData(data));
    }
}

function* submitHomework(data) {
    const addData = yield axios
        .post(`${CONSTANTS.getAPI()}homework/submission/submit`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
}

function* fetchUsersHomeworkList(params) {
    const data = yield axios
        .post(`${CONSTANTS.getAPI()}homework/list-homeworks-user`, params?.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data?.data;
            // console.log("response", response, res);
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });
    if (data) {
        yield put(GetUserHomeworkData(data));
    }
}

function* fetchMyGroupsData(params) {
    const data = yield axios
        .post(`${CONSTANTS.getAPI()}user-groups/get-my-groups`, params?.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data?.data;
            // console.log("response", response, res);
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });
    if (data) {
        yield put(GetMyGroups(data));
    }
}

function* fetchMySubGroupsData(params) {
    const data = yield axios
        .post(`${CONSTANTS.getAPI()}user-subgroup/get-my-subgroups`, params?.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data?.data;
            // console.log("response", response, res);
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });
    if (data) {
        yield put(GetMySubGroups(data));
    }
}

export function* gamificationWatcher() {
    yield takeEvery(actions.FETCH_BADGES_REQ, fetchBadges);
    yield takeEvery(actions.FETCH_QUIZ_REQ, fetchQuizList);
    yield takeEvery(actions.ADD_NEW_BADGE, addNewBadge);
    yield takeEvery(actions.UPDATE_BADGE, updateBadges);
    yield takeEvery(actions.UPDATE_BADGE, fetchBadges);
    yield takeEvery(actions.SUBMIT_QUIZ, submitQuiz);
    yield takeEvery(actions.SUBMIT_HOMEWORK, submitHomework);
    yield takeEvery(actions.FETCH_HOMEWORK_REQ, fetchHomeworkList);
    yield takeEvery(actions.FETCH_USERS_HOMEWORK_REQ, fetchUsersHomeworkList);

    yield takeEvery(actions.FETCH_MY_GROUPS_REQ, fetchMyGroupsData);
    yield takeEvery(actions.FETCH_MY_SUB_GROUPS_REQ, fetchMySubGroupsData);
}

export default function* GamificationSaga() {
    yield all([fork(gamificationWatcher)]);
}
