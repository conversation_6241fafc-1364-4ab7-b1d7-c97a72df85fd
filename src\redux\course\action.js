import {
    COURSE_BOOKMARKING,
    COURSE_BOOKMARKING_REQ,
    COURSE_DETAILS,
    COURSE_DETAILS_REQ,
    COURSE_PREREQUISITE_LIST,
    COURSE_PREREQUISITE_REQ,
    DELETE_COURSE,
    FETCH_ALL_COURSES_LIST,
    FETCH_COURSE_LIST_REQ,
    GET_ALL_COURSES_LIST,
    GET_UPDATED_BOOKMARKING,
    INIT_COURSE,
    SAVE_CHAPTERS,
    UPDATE_BOOKMARKING_DATA,
    UPDATE_CHAPTERS,
    UPDATE_COURSE,
    UPDTAE_COURSE_STATUS,
} from "@/redux-types";

export const fetchCourseDetailsReq = (course_id) => {
    return {
        type: COURSE_DETAILS_REQ,
        payload: course_id,
    };
};

export const getCourseDetails = (data) => {
    return {
        type: COURSE_DETAILS,
        payload: data,
    };
};

export const fetchBookmarkingReq = (course_id) => {
    return {
        type: COURSE_BOOKMARKING_REQ,
        payload: course_id,
    };
};

export const getBookmarking = (data) => {
    return {
        type: COURSE_BOOKMARKING,
        payload: data,
    };
};

export const UpdateBookmarkingData = (bookmarking) => {
    return {
        type: UPDATE_BOOKMARKING_DATA,
        payload: bookmarking,
    };
};

export const GetUpdatedBookmarking = (bookmarking) => {
    return {
        type: GET_UPDATED_BOOKMARKING,
        payload: bookmarking,
    };
};

export const fetchCOURSEREQ = (initData) => {
    return {
        type: FETCH_COURSE_LIST_REQ,
        payload: initData,
    };
};

export const fetchCoursesList = () => {
    return {
        type: FETCH_ALL_COURSES_LIST,
    };
};

export const deleteCourse = (courseId) => {
    return {
        type: DELETE_COURSE,
        payload: courseId,
    };
};

export const getAllCourrsesList = (coursesList) => {
    return {
        type: GET_ALL_COURSES_LIST,
        payload: coursesList,
    };
};
export const updateCourse = (data) => {
    return {
        type: UPDATE_COURSE,
        payload: data,
    };
};
export const updateCourseStatus = (data) => {
    return {
        type: UPDTAE_COURSE_STATUS,
        payload: data,
    };
};
export const saveChapters = (data) => {
    return {
        type: SAVE_CHAPTERS,
        payload: data,
    };
};

export const updateChapters = (data) => {
    return {
        type: UPDATE_CHAPTERS,
        payload: data,
    };
};

export const setCourse = (initData) => {
    return {
        type: INIT_COURSE,
        payload: initData,
    };
};

export const coursePresequireReq = () => {
    return {
        type: COURSE_PREREQUISITE_REQ,
    };
};

export const presequireList = (data) => {
    return {
        type: COURSE_PREREQUISITE_LIST,
        payload: data,
    };
};
