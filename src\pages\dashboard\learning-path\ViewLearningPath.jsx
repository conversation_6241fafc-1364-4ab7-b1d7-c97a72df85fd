import { Badge } from "@/components/ui/badge";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { Clock, FilePenLine, ShieldCheck } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

// import CourseSettings from "./CourseSettings";
// import TemplateSettings from "./TemplateSettings";
function formatNumber(num) {
    return num?.toString()?.padStart(2, "0");
}

const ViewLearningPath = () => {
    const params = useParams();
    const router = useNavigate();
    const [pathData, setPathData] = useState(null);

    useEffect(() => {
        if (params?.path_id !== undefined) {
            getLearningPath(params?.path_id);
        }
    }, [params]);

    const getLearningPath = async (payload) => {
        await tanstackApi
            .get(`learning-path/view-learning-path-details/${payload}`)
            .then((res) => {
                setPathData(res?.data?.data);
            })
            .catch((err) => {
                setPathData(null);
            });
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/learning-path">Learning Paths</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>View Learning Path</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <div className="tw-flex tw-gap-2">
                    <Button
                        className="tw-px-2 tw-py-1"
                        variant="outline"
                        onClick={() => router(`/dashboard/learning-path`)}
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button
                        className="tw-px-2 tw-py-1"
                        onClick={() => router(`/dashboard/learning-path-create/${params?.path_id}`)}
                    >
                        <i className="fa-solid fa-edit"></i> Edit Learning Path
                    </Button>
                </div>
                {/* </Link> */}
            </div>
            <div className="tw-grid tw-w-full tw-grid-cols-[400px_1fr] tw-gap-7 tw-border-secondary tw-p-2">
                <div className="tw-space-y-5">
                    <div className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-border-[1px]">
                        {pathData?.learning_path_logo_url ? (
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-object-cover tw-shadow-sm"
                                src={pathData?.learning_path_logo_url}
                            />
                        ) : (
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-object-cover tw-shadow-sm"
                                src={"/assets/thumbnail.png"}
                            />
                        )}
                    </div>
                </div>
                <div>
                    <h1 className="tw-text-3xl tw-font-bold">{pathData?.name}</h1>
                    <p className="tw-mt-4 tw-line-clamp-2 tw-font-mono tw-text-[16px] tw-font-semibold tw-text-slate-500">
                        {pathData?.description}
                    </p>
                    <div className="tw-mt-4 tw-space-y-3">
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <ShieldCheck size={18} />{" "}
                            <Badge variant={"outline"}>{pathData?.learning_path_status}</Badge>
                        </span>{" "}
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <FilePenLine size={18} /> Last Updated {moment(pathData?.updatedAt).format("LL")}
                        </span>{" "}
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <Clock size={18} /> Created On{" "}
                            {pathData?.createdAt ? (
                                moment(pathData?.createdAt).format("LL")
                            ) : (
                                <span className="tw-text-xs tw-text-slate-400"> _ _ / _ _ / _ _ _ _</span>
                            )}
                        </span>{" "}
                    </div>
                </div>
            </div>
            <br />
            <div className="tw-flex tw-flex-wrap tw-items-center tw-gap-[65px] tw-p-4">
                {pathData?.lms_learning_path_steps
                    ?.sort((a, b) => a?.order - b?.order)
                    ?.map((step, idx) => (
                        <div className="tw-relative" key={idx}>
                            <div
                                className="tw-w-[220px] tw-cursor-pointer tw-space-y-2 tw-rounded-xl tw-border-[1px] tw-p-2 hover:tw-bg-slate-50"
                                onClick={() => router(`/dashboard/view-course/${step?.course_id}`)}
                            >
                                <div>
                                    <h1>
                                        <Badge variant={"outline"}>Step {formatNumber(step?.order)}</Badge>
                                    </h1>
                                </div>
                                <div className="tw-w-full">
                                    <img
                                        className="tw-h-[120px] tw-w-full tw-rounded-lg tw-object-cover tw-shadow-sm"
                                        src={step?.step_image_url || "/assets/thumbnail.png"}
                                    />
                                </div>
                                <div>
                                    <h1 className="tw-text-md tw-font-mono tw-font-semibold">{step?.step_name}</h1>
                                    <p className="tw-mt-1 tw-line-clamp-2 tw-text-sm tw-font-normal tw-text-slate-500">
                                        {step?.description}
                                    </p>
                                </div>
                            </div>
                            {pathData?.lms_learning_path_steps?.length !== idx + 1 && (
                                <img
                                    src="/assets/right-arrow.png"
                                    className="tw-absolute tw-bottom-0 tw-right-[-55px] tw-top-0 tw-z-10 tw-my-[auto] tw-h-12"
                                    alt=""
                                />
                            )}
                        </div>
                    ))}
            </div>
        </div>
    );
};

export default ViewLearningPath;
