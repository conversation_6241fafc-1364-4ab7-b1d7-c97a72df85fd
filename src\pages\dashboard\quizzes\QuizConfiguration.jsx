import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    B<PERSON><PERSON><PERSON>bLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { useSidebar } from "@/components/ui/sidebar";
import DragDropMedia from "@/pages/dashboard/quizzes/components/DragDropMedia";
import FillinTheBlanks from "@/pages/dashboard/quizzes/components/FillinTheBlanks";
import HotspotDragDrop from "@/pages/dashboard/quizzes/components/HotspotDragDrop";
import HotspotDragDropMedia from "@/pages/dashboard/quizzes/components/HotspotDragDropMedia";
import LongAnswer from "@/pages/dashboard/quizzes/components/LongAnswer";
import MatchTheFollowing from "@/pages/dashboard/quizzes/components/MatchTheFollowing";
import SequenceArrange from "@/pages/dashboard/quizzes/components/SequenceArrange";
import Singlechoice from "@/pages/dashboard/quizzes/components/Singlechoice";
import SinglechoiceMedia from "@/pages/dashboard/quizzes/components/SinglechoiceMedia";
import StringDropdown from "@/pages/dashboard/quizzes/components/StringDropdown";
import MicroQuizPreview from "@/pages/dashboard/quizzes/preview";
import QuestionSettings from "@/pages/dashboard/quizzes/QuestionSettings";
import QuiestionTypes from "@/pages/dashboard/quizzes/QuiestionTypes";
import { tanstackApi } from "@/react-query/api";
import { useGetQuizDetails } from "@/react-query/quizz";
import { ArrowBigLeft, CloudFog, File, PackagePlus, Plus, Presentation, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import QuestionPoolForQuiz from "./QuestionPoolForQuiz";
import { useCallback } from "react";
import { memo } from "react";
import { useMemo } from "react";
import { ComparisonUtil } from "@/lib/helpers/utility";

function formatNumber(num) {
    return num?.toString()?.padStart(2, "0");
}

const MemoizedMicroQuizPreview = memo(MicroQuizPreview);

const preview_mapping = {
    singlechoice: {
        component: MemoizedMicroQuizPreview,
        image: "singlechoice.png",
        title: "Singlechoice",
    },
    singlechoice_media: {
        component: MemoizedMicroQuizPreview,
        image: "singlechoice_media.png",
        title: "Singlechoice Media",
    },
    fillin_the_blank: {
        component: MemoizedMicroQuizPreview,
        image: "fillin_the_blanks.png",
        title: "Fill in The Blanks",
    },
    dnd_image_box: {
        component: MemoizedMicroQuizPreview,
        image: "drag_drop_media.png",
        title: "Drag & Drop Image",
    },
    sequence_arrange: {
        component: MemoizedMicroQuizPreview,
        image: "sequence_arrange.png",
        title: "Sequence Arrange",
    },
    string_dropdown: {
        component: MemoizedMicroQuizPreview,
        image: "dropdown.png",
        title: "String Dropdown",
    },
    match_the_following: {
        component: MemoizedMicroQuizPreview,
        image: "match_the_following.png",
        title: "Match the following",
    },
    hotspot_dnd: {
        component: MemoizedMicroQuizPreview,
        image: "hotspot_dnd.png",
        title: "Hotspot Drag & Drop",
    },
    hotspot_dnd_image: {
        component: MemoizedMicroQuizPreview,
        image: "hotspot_dnd_media.png",
        title: "Hotspot Drag & Drop Media",
    },
    long_answer: {
        component: MemoizedMicroQuizPreview,
        image: "long_answer.png",
        title: "Long Answer",
    },
};

const content_mapping = {
    singlechoice: {
        comp: Singlechoice,
    },
    singlechoice_media: {
        comp: SinglechoiceMedia,
    },
    fillin_the_blank: {
        comp: FillinTheBlanks,
    },
    dnd_image_box: {
        comp: DragDropMedia,
    },
    sequence_arrange: {
        comp: SequenceArrange,
    },
    string_dropdown: {
        comp: StringDropdown,
    },
    match_the_following: {
        comp: MatchTheFollowing,
    },
    hotspot_dnd: {
        comp: HotspotDragDrop,
    },
    hotspot_dnd_image: {
        comp: HotspotDragDropMedia,
    },
    long_answer: {
        comp: LongAnswer,
    },
};

const QuizConfiguration = () => {
    const params = useParams();
    const { setOpen: sideBarOpen } = useSidebar();
    const [open, setOpen] = useState(false);
    const [openSheet, setOpenSheet] = useState(false);
    const [openPool, setOpenPool] = useState(false);
    const [quizData, setQuizData] = useState(null);
    const [selectedContent, setSelectedContent] = useState(null);
    const [INDEX, setINDEX] = useState(null);
    const [template, setTemplate] = useState(null);
    const [componentsArray, setComponentsArray] = useState([]);
    const [currentId, setCurrentId] = useState(null);
    const navigate = useNavigate();
    const particularQuizData = useGetQuizDetails(params?.quiz_id);
    const [draggedItemIndex, setDraggedItemIndex] = useState(null);

    const navigateToQuizTests = () => navigate("/dashboard/quiz-tests");
    const onAddContent = useCallback(() => setOpen(true), []);
    useEffect(() => {
        if (particularQuizData.isSuccess) {
            const data = particularQuizData.data?.data?.[0];
            setQuizData(data);
            setTemplate(data?.lms_template);
            if (data?.components?.length === 0) setOpen(true);

            const hasDataChanged = !ComparisonUtil.compare(data?.components, componentsArray);
            if (hasDataChanged) setComponentsArray(data?.components || []);
        }
    }, [particularQuizData.isSuccess, particularQuizData.data, template]);

    const onSlideEdit = useCallback((data, c_index) => {
        setOpenSheet(true);
        setINDEX(c_index);
        setSelectedContent(data);
    }, []);

    const onRemoveSlide = useCallback((index) => {
        setComponentsArray((prev) => {
            const updatedOptions = [...prev];
            updatedOptions.splice(index, 1);
            return updatedOptions;
        });
    }, []);

    const formattedComponents = useMemo(() => componentsArray.map((comp) => ({ ...comp })), [componentsArray]);

    useEffect(() => {
        sideBarOpen(false);
    }, []);

    const onMountSlide = (slide_id) => {
        window.location.href = `#slide_${slide_id}`;
    };

    const onUpdateQuiz = async (type) => {
        try {
            const payload = {
                id: quizData?.id,
                title: quizData?.title,
                type: quizData?.type,
                schedule_date: quizData?.schedule_date || undefined,
                start_time: quizData?.start_time || undefined,
                end_time: quizData?.end_time || undefined,
                trainer_id: quizData?.trainer_id || undefined,
                trainer_name: quizData?.trainer_name || undefined,
                thumbnail_img: quizData?.thumbnail_img,
                description: quizData?.description,
                max_points: quizData?.max_points || undefined,
                course_id: quizData?.course_id || undefined,
                chapter_id: quizData?.chapter_id || undefined,
                background_image: quizData?.background_image,
                is_graded: quizData?.is_graded,
                on_next_answer_show: quizData?.on_next_answer_show,
                quiz_duration: quizData?.quiz_duration || undefined,
                show_answer_type: quizData?.show_answer_type || undefined,
                passing_marks: quizData?.passing_marks || undefined,
                components: componentsArray,
                is_draft: type == "update" ? false : true,
            };
            const response = await tanstackApi.put("quiz/update", { ...payload });
            punchTimelineLog({
                user_id: localStorage.getItem("userId"),
                event: "quiz",
                log: `${componentsArray?.length} question added in ${quizData?.title} successfully.`,
            });

            toast.success(type === "update" ? "Updated Successfully" : "Saved Draft Successfully", {
                description: response?.data?.message,
            });
        } catch (err) {
            console.error("Quiz Update Error:", err);
            toast.error("Something went wrong", {
                description: err?.response?.data?.message || "An unexpected error occurred",
            });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    const handleDragStart = useCallback((index) => {
        setDraggedItemIndex(index);
    }, []);

    const handleDragOver = useCallback(
        (index) => {
            if (draggedItemIndex === index) return;
            const updatedItems = [...componentsArray];
            const [draggedItem] = updatedItems.splice(draggedItemIndex, 1);
            updatedItems.splice(index, 0, draggedItem);
            setComponentsArray(updatedItems);
            setDraggedItemIndex(index);
        },
        [draggedItemIndex, componentsArray],
    );

    const handleDragEnd = useCallback(() => {
        setDraggedItemIndex(null);
    }, []);

    return (
        <>
            <QuiestionTypes
                open={open}
                setOpen={setOpen}
                setComponentsArray={setComponentsArray}
                componentsArray={componentsArray}
                QuizData={quizData}
                INDEX={INDEX}
                onSlideEdit={onSlideEdit}
            />
            <QuestionPoolForQuiz
                open={openPool}
                setOpen={setOpenPool}
                setComponentsArray={setComponentsArray}
                componentsArray={componentsArray}
            />
            <div className="tw-grid tw-h-full tw-max-h-dvh tw-grid-cols-12 tw-grid-rows-[50px_1fr] tw-gap-4">
                <div className="tw-col-span-12 tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-5">
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <Breadcrumb>
                            <BreadcrumbList>
                                <BreadcrumbItem>
                                    <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbSeparator />
                                <BreadcrumbItem>
                                    <BreadcrumbLink href="/dashboard/quiz-tests">Quizzes</BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbSeparator />
                                <BreadcrumbItem>
                                    <BreadcrumbPage>{quizData?.title}</BreadcrumbPage>
                                </BreadcrumbItem>
                            </BreadcrumbList>
                        </Breadcrumb>
                        <div className="tw-flex tw-items-center tw-gap-2">
                            <Button onClick={navigateToQuizTests} variant="outline">
                                <ArrowBigLeft /> Back
                            </Button>
                            <Button variant="outline" onClick={() => setOpenPool(true)}>
                                <CloudFog /> Quesion Pool
                            </Button>
                            <Button asChild variant="outline">
                                <Link to={`/quiz-preview/${params?.quiz_id}`}>
                                    <Presentation /> Preview
                                </Link>
                            </Button>
                            <Button type="button" variant="secondary" onClick={() => onUpdateQuiz("draft")}>
                                <File /> Save Draft
                            </Button>
                            <Button type="button" onClick={() => onUpdateQuiz("update")}>
                                <Save /> Save Quiz
                            </Button>
                        </div>
                    </div>
                </div>
                <div className="tw-col-span-12 tw-grid tw-h-full tw-w-full tw-grid-cols-[8rem_1fr] tw-gap-3 tw-overflow-hidden">
                    <div className="tw-grid tw-h-full tw-w-full tw-grid-rows-[1fr_5rem] tw-gap-3 tw-overflow-hidden">
                        <div className="custom_scrollbar tw-flex tw-w-full tw-flex-col tw-gap-3 tw-overflow-y-auto">
                            {formattedComponents?.map((content, index) => {
                                const compData = preview_mapping[content?.componentTypeId];
                                if (!compData) return null;
                                return (
                                    <div
                                        key={`${content.componentTypeId}-${index}`}
                                        className="tw-w-full tw-cursor-pointer"
                                    >
                                        <compData.component
                                            onMountSlide={onMountSlide}
                                            formatNumber={formatNumber}
                                            index={index}
                                            title={compData.title}
                                            image={compData.image}
                                            currentId={currentId}
                                            handleDragStart={handleDragStart}
                                            handleDragOver={handleDragOver}
                                            handleDragEnd={handleDragEnd}
                                        />
                                    </div>
                                );
                            })}
                        </div>
                        <div className="tw-h-full tw-w-full">
                            <Button
                                variant="reset"
                                className="tw-flex tw-h-full tw-w-full tw-flex-col tw-border-2 tw-border-dashed"
                                onClick={onAddContent}
                            >
                                <PackagePlus className="tw-text-gray-400" size={40} />
                                <h1 className="tw-leading-0 tw-text-xs tw-font-semibold tw-text-gray-400">
                                    Add <br /> Question
                                </h1>
                            </Button>
                        </div>
                    </div>
                    <div className="tw-grid tw-size-full tw-grid-cols-[10fr_4fr] tw-grid-rows-1 tw-gap-5 tw-overflow-hidden">
                        <div className="custom_scrollbar tw-h-full tw-space-y-4 tw-overflow-y-auto tw-pr-1">
                            {formattedComponents.length === 0 ? (
                                <div className="tw-flex tw-h-[60vh] tw-items-center tw-justify-center tw-rounded-lg tw-border-2 tw-border-dashed">
                                    <Button variant="secondary" onClick={onAddContent}>
                                        <Plus size={16} strokeWidth={2} aria-hidden="true" />
                                        <p>Add Question</p>
                                    </Button>
                                </div>
                            ) : (
                                formattedComponents?.map((content, index) => {
                                    const Component = content_mapping[content?.componentTypeId]?.comp;
                                    return Component ? (
                                        <Component
                                            key={`${content.componentTypeId}-${index}`}
                                            onRemoveSlide={onRemoveSlide}
                                            onSlideEdit={onSlideEdit}
                                            index={index}
                                            content={content}
                                            setCurrentId={setCurrentId}
                                            setSelectedContent={setSelectedContent}
                                            setComponentsArray={setComponentsArray}
                                            componentsArray={componentsArray}
                                            template={template}
                                        />
                                    ) : null;
                                })
                            )}
                        </div>
                        <div className="custom_scrollbar tw-h-full tw-overflow-hidden tw-pr-2">
                            <QuestionSettings
                                open={openSheet}
                                setOpen={setOpenSheet}
                                setComponentsArray={setComponentsArray}
                                componentsArray={componentsArray}
                                selectedContent={selectedContent}
                                setSelectedContent={setSelectedContent}
                                INDEX={INDEX}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default QuizConfiguration;
