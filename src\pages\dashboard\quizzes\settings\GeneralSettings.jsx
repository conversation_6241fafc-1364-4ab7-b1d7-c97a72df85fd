import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

const GeneralSettings = ({ setContentData, contentData }) => {
    const onHandleChange = (value, name) => {
        setContentData({ ...contentData, [name]: value });
    };

    const showQuestionBody = ["fillin_the_blank", "string_dropdown", "long_answer"];

    return (
        <div>
            <Label>General Details :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">Please define basic outlines of question here.</p>
            <div className="tw-mt-3 tw-space-y-3">
                <div className="tw-space-y-1">
                    <Label htmlFor="name">Question Title</Label>
                    <Input
                        onChange={(e) => onHandleChange(e.target.value, "name")}
                        value={contentData?.name}
                        name="name"
                        id="name"
                        placeholder="Enter component title here"
                    />
                </div>
                <div className="tw-space-y-1">
                    <Label htmlFor="specialInstruction">Special Instructions</Label>
                    <Input
                        onChange={(e) => onHandleChange(e.target.value, "specialInstruction")}
                        value={contentData?.specialInstruction}
                        name="specialInstruction"
                        id="specialInstruction"
                        placeholder="Define Special Instructions here"
                    />
                </div>
                <div className="tw-grid tw-grid-cols-4">
                    <div className="tw-space-y-1">
                        <Label htmlFor="points">Points</Label>
                        <Input
                            onChange={(e) => onHandleChange(e.target.value, "points")}
                            value={contentData?.points}
                            name="points"
                            id="points"
                            placeholder="Define points"
                            type="number"
                        />
                    </div>
                </div>
                {showQuestionBody.includes(contentData?.componentTypeId) && (
                    <div className="tw-space-y-1">
                        <Label htmlFor="question">Question Body</Label>
                        <Textarea
                            onChange={(e) => onHandleChange(e.target.value, "question")}
                            value={contentData?.question}
                            name="question"
                            id="question"
                            placeholder="Define question body here"
                            rows="5"
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default GeneralSettings;
