import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { Clock, FilePenLine, Hash } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const LearningPaths = () => {
    const navigate = useNavigate();
    const [DataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [filterState, setFilterState] = useState({
        search: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = DataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.name?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            return matchesSearch; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(DataList);
        setFilterState({
            search: "",
        });
    };

    useEffect(() => {
        getPathData();
    }, []);

    useEffect(() => {
        setFilteredData(DataList);
    }, [DataList]);

    const getPathData = async () => {
        await tanstackApi
            .get("learning-path/listing")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-signs-post"></i> Learning Path
                </h4>
                <Button className="tw-px-2 tw-py-1" onClick={() => navigate(`/dashboard/learning-path-create`)}>
                    <i className="fa-solid fa-plus"></i> New Path
                </Button>
            </div>
            {/* <br /> */}
            <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label htmlFor="">Path Name</label>
                    <input
                        style={{ minWidth: "300px" }}
                        value={filterState?.search}
                        name="search"
                        onChange={onFilterChange}
                        type="text"
                        placeholder="Search by name ..."
                    />
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-5 tw-p-0">
                {filteredData?.map((path, index) => (
                    <div key={index} className="tw-w-[300px] tw-rounded-xl tw-border-[1px] tw-p-1">
                        <div
                            className="tw-h-[160px] tw-w-full tw-cursor-pointer tw-overflow-hidden tw-rounded-lg"
                            onClick={() => navigate(`/dashboard/learning-path-view/${path?.id}`)}
                        >
                            <img
                                className="tw-h-full tw-w-full tw-object-cover"
                                src={path?.learning_path_logo_url || "/assets/thumbnail.png"}
                                alt=""
                            />
                        </div>
                        <div className="tw-p-2">
                            <div
                                className="tw-flex tw-items-center tw-gap-2"
                                onClick={() => navigate(`/dashboard/learning-path-view/${path?.id}`)}
                            >
                                <h2 className="tw-leading-2 tw-text-md tw-line-clamp-1 tw-font-semibold">
                                    {path?.name}
                                </h2>
                            </div>
                            <div
                                className="tw-h-[40px]"
                                onClick={() => navigate(`/dashboard/learning-path-view/${path?.id}`)}
                            >
                                <p className="tw-mt-2 tw-line-clamp-2 tw-text-sm tw-text-slate-500">
                                    {path?.description}
                                </p>
                            </div>
                            <div className="tw-mt-3 tw-space-y-2">
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Hash size={18} /> {path?.code}
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Clock size={18} /> Created on {moment(path?.createdAt).format("LL")}
                                </span>{" "}
                            </div>
                            <div className="tw-mt-3 tw-grid tw-grid-cols-1 tw-gap-2">
                                <div
                                    onClick={() => navigate(`/dashboard/learning-path-create/${path?.id}`)}
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <FilePenLine size={16} strokeWidth={2} aria-hidden="true" />
                                    Edit Learning Path
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default LearningPaths;
