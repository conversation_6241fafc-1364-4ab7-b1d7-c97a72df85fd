[
    {
        "name": "In real life engineers need to make many alterations in order to end up with a final product. Thus, redesign and make the appropriate modifications and improvements on your model",
        "specialInstruction": "Complete the gaps using the appropriate words from the box.",
        "type": "long_answer",
        "points": 70,
        "question": "",
        "options": [],
        "fillOptions": [],
        "matchOptions": [],
        "draggable_options": [],
        "dropzone": [],
        "hotspots": [],
        "optionColumns": "1fr 1fr",
        "componentTypeId": "long_answer",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973000_personality_quiz.webp",
        "question_thumbnail": ""
    },
    {
        "name": "Complete the gaps using the appropriate words from the box",
        "specialInstruction": "Complete the gaps using the appropriate words from the box.",
        "type": "hotspot_dnd",
        "points": 70,
        "options": [],
        "fillOptions": [],
        "matchOptions": [],
        "draggable_options": [],
        "dropzone": [],
        "hotspotDropzone": [
            {
                "x": 10,
                "y": 0,
                "src": "",
                "correct": "One"
            },
            {
                "x": 10,
                "y": 0,
                "src": "",
                "correct": "three"
            },
            {
                "x": 10,
                "y": 0,
                "src": "",
                "correct": "two"
            },
            {
                "x": 10,
                "y": 0,
                "src": "",
                "correct": "five"
            },
            {
                "x": 10,
                "y": 0,
                "src": "",
                "correct": "four"
            }
        ],
        "hotspots": [
            {
                "x": 400,
                "y": 50,
                "src": "",
                "name": "One"
            },
            {
                "x": 400,
                "y": 100,
                "src": "",
                "name": "Two"
            },
            {
                "x": 400,
                "y": 150,
                "src": "",
                "name": "Three"
            },
            {
                "x": 400,
                "y": 200,
                "src": "",
                "name": "Four"
            },
            {
                "x": 400,
                "y": 250,
                "src": "",
                "name": "Five"
            }
        ],
        "optionColumns": "",
        "componentTypeId": "hotspot_dnd",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973000_personality_quiz.webp",
        "question_thumbnail": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS7092/course-images/1731398294_img_eacf32e87aa00f07c75189f7c83349fc2ea87718_1.png"
    },
    {
        "name": "Concept mapping",
        "specialInstruction": "Teachers can create concept maps with draggable slide objects to help students visualize and understand the relationships between different ideas.",
        "type": "dnd_hotspot_text",
        "points": 70,
        "options": [],
        "fillOptions": [],
        "matchOptions": [],
        "draggable_options": [],
        "dropzone": [],
        "hotspots": [
            {
                "x": 0,
                "y": 0,
                "name": "Open circuit"
            },
            {
                "x": 0,
                "y": 10,
                "name": "Cannot be used"
            },
            {
                "x": 0,
                "y": 20,
                "name": "Electrical behavior"
            },
            {
                "x": 0,
                "y": 30,
                "name": "Water"
            },
            {
                "x": 0,
                "y": 40,
                "name": "Metal"
            },
            {
                "x": 0,
                "y": 50,
                "name": "Rubber"
            },
            {
                "x": 0,
                "y": 60,
                "name": "Plastic"
            },
            {
                "x": 0,
                "y": 70,
                "name": "Wood"
            },
            {
                "x": 0,
                "y": 80,
                "name": "Glass"
            }
        ],
        "optionColumns": "",
        "componentTypeId": "dnd_hotspot_text",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973000_personality_quiz.webp",
        "question_thumbnail": "/assets/dnd-bg3.png"
    },
    {
        "name": "Matching & Labeling",
        "specialInstruction": "Matching and labeling activities can be a great way for students comprehend new concepts & vocabulary.",
        "type": "dnd_hotspot_text",
        "points": 70,
        "options": [],
        "fillOptions": [],
        "matchOptions": [],
        "draggable_options": [],
        "dropzone": [],
        "hotspots": [
            {
                "x": 711,
                "y": 45,
                "name": "Keyboard"
            },
            {
                "x": 401,
                "y": -7,
                "name": "Monitor"
            },
            {
                "x": 186,
                "y": -8,
                "name": "DVD Disk"
            },
            {
                "x": 177,
                "y": 166,
                "name": "Clock"
            },
            {
                "x": 119,
                "y": 58,
                "name": "Wi-Fi Router"
            },
            {
                "x": 381,
                "y": 207,
                "name": "Mouse"
            },
            {
                "x": 570,
                "y": 158,
                "name": "CPU"
            },
            {
                "x": 709,
                "y": -20,
                "name": "Mug"
            }
        ],
        "optionColumns": "",
        "componentTypeId": "dnd_hotspot_text",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973000_personality_quiz.webp",
        "question_thumbnail": "/assets/dnd-bg4.png"
    },
    {
        "name": "Which of these parts of the shuttle is the expandable external tank?",
        "specialInstruction": "Place a hotspot marker on the appropriate part.",
        "type": "hotspot_comp",
        "points": 70,
        "options": [],
        "fillOptions": [],
        "matchOptions": [],
        "draggable_options": [],
        "dropzone": [],
        "hotspots": [
            {
                "x": 212,
                "y": 23,
                "name": "C - A"
            },
            {
                "x": 268,
                "y": 52,
                "name": "C - B"
            },
            {
                "x": 210,
                "y": 64,
                "name": "C - C"
            }
        ],
        "optionColumns": "",
        "componentTypeId": "hotspot_comp",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973000_personality_quiz.webp",
        "question_thumbnail": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973022_360_f_61019696_oj0eo6owurnj7rvtr0wjcppdywcbgnmv.jpg"
    },
    {
        "name": "Categorize the components",
        "specialInstruction": "Drag & drop the parts to the right category box",
        "type": "dnd_comp",
        "points": 70,
        "options": [],
        "fillOptions": [],
        "matchOptions": [],
        "draggable_options": [
            {
                "id": "image1",
                "src": "/assets/pic1.png",
                "zone": "angles"
            },
            {
                "id": "image2",
                "src": "/assets/pic2.png",
                "zone": "rods"
            },
            {
                "id": "image3",
                "src": "/assets/pic2.png",
                "zone": "rods"
            },
            {
                "id": "image4",
                "src": "/assets/pic3.png",
                "zone": "wheels"
            },
            {
                "id": "image5",
                "src": "/assets/pic3.png",
                "zone": "wheels"
            },
            {
                "id": "image6",
                "src": "/assets/pic2.png",
                "zone": "rods"
            }
        ],
        "dropzone": [
            {
                "id": "angles",
                "zone": "angles"
            },
            {
                "id": "rods",
                "zone": "rods"
            },
            {
                "id": "wheels",
                "zone": "wheels"
            }
        ],
        "hotspots": [],
        "optionColumns": "1fr 1.5fr",
        "componentTypeId": "dnd_comp",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730972873_question_marks_background_78370_2896.jpg",
        "question_thumbnail": ""
    },
    {
        "name": "Complete the gaps using the appropriate words from the box",
        "specialInstruction": "Complete the gaps using the appropriate words from the box.",
        "type": "match_the_following",
        "points": 70,
        "options": [],
        "fillOptions": [],
        "matchOptions": [
            {
                "id": "1",
                "label": "Rose",
                "text": "Fruit",
                "imageLabel": "",
                "imageText": "",
                "correctIndex": 2
            },
            {
                "id": "2",
                "label": "Apple",
                "text": "Flower",
                "imageLabel": "",
                "imageText": "",
                "correctIndex": 1
            },
            {
                "id": "3",
                "label": "Shark",
                "text": "Animal",
                "imageLabel": "",
                "imageText": "",
                "correctIndex": 3
            },
            {
                "id": "4",
                "label": "Carrot",
                "text": "Vegetable",
                "imageLabel": "",
                "imageText": "",
                "correctIndex": 4
            }
        ],
        "draggable_options": [],
        "dropzone": [],
        "hotspots": [],
        "optionColumns": "1fr",
        "componentTypeId": "match_the_following",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973687_hand_drawn_question_mark_pattern_23_2149416617.jpg",
        "question_thumbnail": ""
    },
    {
        "name": "Read this abstract about Everest and choose one correct answer in each drop-down list.",
        "specialInstruction": "",
        "type": "string_dropdown_comp",
        "points": 70,
        "options": [],
        "fillOptions": [],
        "matchOptions": [],
        "draggable_options": [],
        "dropzone": [],
        "hotspots": [],
        "question": "The most {0} area of the mountain is often considered to be the Khumbu {1}, which is particularly dangerous due to the {2} movement of the icefall.",
        "dropdown_options": [
            {
                "id": "label_0",
                "correct": "beautiful",
                "options": ["dangerous", "beautiful", "safe"]
            },
            {
                "id": "label_1",
                "correct": "Glacier",
                "options": ["Ice Fall", "Glacier", "Peak"]
            },
            {
                "id": "label_2",
                "correct": "unstoppable",
                "options": ["frequent", "unpredictable", "unstoppable"]
            }
        ],
        "optionColumns": "1.5fr 1fr",
        "componentTypeId": "string_dropdown_comp",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730974045_pexels_mali_45987.jpg",
        "question_thumbnail": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730974142_glaciers_jakobshavn_glacier_greenland___kertu_adobe_stock_photo_229290457.jpg"
    },
    {
        "name": "Drag the words & drop them into appropriate places.",
        "specialInstruction": "",
        "type": "drag_the_words",
        "points": 70,
        "options": [],
        "question": "The mini car model is moving using a mechanism called [field]  and [field] gear. The [field] motion of the pinion gear is converted into [field] . In this case, the [field]  is used to change the [field] of motion.",
        "fillOptions": [
            {
                "label": "rack",
                "correctIndex": "1"
            },
            {
                "label": "gear",
                "correctIndex": 2
            },
            {
                "label": "linear",
                "correctIndex": "3"
            },
            {
                "label": "rotational",
                "correctIndex": "4"
            },
            {
                "label": "pinion",
                "correctIndex": "5"
            },
            {
                "label": "direction",
                "correctIndex": "6"
            }
        ],
        "matchOptions": [],
        "draggable_options": [],
        "dropzone": [],
        "hotspots": [],
        "dropdown_options": [],
        "optionColumns": "1.5fr 1fr",
        "componentTypeId": "drag_the_words",
        "question_background": "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS7092/course-images/1730974574_career_aptitude_test_1.jpg",
        "question_thumbnail": ""
    }
]

[
    {
        "title":"Interaction Titile",
        "description":"Interaction description",
        "instructions":"Interaction instructions",
        "type":"Flashcard / Reveal Hidden  -   / Steps / Timeline / Cyclic Process / Process  -  Labeled Graphics / Guided Image / Hotspot  -  Circle Diagram / Pyramid  -  Glossary / Media Catalogue / FAQ / Accordion  -  ",
        "category":"Process / Annotions / Hierarchy / Catalogue / Game",
        "template_id":4512,
        "structure":{
            "details":{},
            "database":[
                {
                    "label":"",
                    "description":"HTML",
                    "image_src":"s3 bucket",
                    "bg_sound":"s3 bucket",
                    "annotion_icon":"s3 bucket",
                    "x":451,
                    "y":212,
                },
                {
                    "label":"",
                    "description":"HTML",
                    "image_src":"s3 bucket",
                    "bg_sound":"s3 bucket",
                    "annotion_icon":"s3 bucket",
                    "x":451,
                    "y":212,
                }, 
                {
                    "label":"",
                    "description":"HTML",
                    "image_src":"s3 bucket",
                    "annotion_icon":"s3 bucket",
                    "bg_sound":"s3 bucket",
                    "x":451,
                    "y":212,
                }
            ]
        },
    }
]