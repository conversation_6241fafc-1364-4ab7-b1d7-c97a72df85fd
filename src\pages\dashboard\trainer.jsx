import { Label } from "@/components/ui/label";
import { tanstackApi } from "@/react-query/api";
import { Book, User, UsersRound, Video } from "lucide-react";
import { useEffect, useState } from "react";
import TrainerScheduleDash from "./user/trainerSchdeule";

const TrainerDashboard = () => {
    const [dashData, setDashData] = useState(null);
    const [dashboard, setDashboard] = useState([
        {
            name: "Assigned Courses",
            icon: Book,
            suffix: "courses",
            value: 0,
            api_key: "totalCourses",
        },
        {
            name: "Assigned Groups",
            icon: UsersRound,
            suffix: "groups",
            value: 0,
            api_key: "totalGroups",
        },
        {
            name: "Assigned Teams",
            icon: User,
            suffix: "groups",
            value: 0,
            api_key: "totalTeams",
        },
        {
            name: "Upcoming Classes",
            icon: Video,
            suffix: "Classes",
            value: 0,
            api_key: "totalUpcomingClasses",
        },
    ]);

    useEffect(() => {
        if (dashData !== null) {
            let dash = dashboard?.map((dash, idx) => {
                return {
                    ...dash,
                    value: dashData[dash?.api_key],
                };
            });
            setDashboard(dash);
        }
    }, [dashData]);

    const getDashData = async () => {
        await tanstackApi
            .get("dashboard")
            .then((res) => {
                setDashData(res?.data?.data);
            })
            .catch((err) => {
                setDashData(null);
            });
    };

    useEffect(() => {
        getDashData();
    }, []);

    return (
        <div>
            <div className="tw-flex tw-flex-col tw-justify-between tw-gap-3">
                <div>
                    <h1 className="tw-font-mono tw-text-xl tw-font-semibold">Welcome back, Trainer Dashboard 👋</h1>
                    <p className="tw-text-sm tw-text-slate-600">
                        There is the latest update for the of below mentioned modules. Check now
                    </p>
                </div>
            </div>
            <div className="tw-mt-5 tw-flex tw-flex-wrap tw-gap-3">
                {dashboard?.map((card, index) => (
                    <DashKPICard key={index} card={card} index={index} />
                ))}
            </div>
            <div>
                <div className="tw-mt-10">
                    <TrainerScheduleDash />
                </div>
            </div>
        </div>
    );
};

const DashKPICard = ({ card }) => {
    const ICON = card?.icon;
    return (
        <div className="p-3 tw-w-[280px] tw-cursor-pointer tw-rounded-2xl tw-border-[1px] hover:tw-bg-teal-50">
            <div className="tw-mb-2 tw-flex tw-justify-end">
                <Label className="tw-text-slate-600">{card?.name}</Label>
            </div>
            <div className="tw-grid tw-grid-cols-[50px_auto] tw-items-center tw-gap-2">
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="p-2 tw-rounded-xl tw-bg-gray-100">
                        <ICON className="tw-text-teal-500" size={30} />
                    </div>
                </div>
                <div className="tw-flex tw-items-baseline tw-gap-2">
                    <h1 className="tw-text-3xl tw-font-bold tw-text-slate-800">{card?.value}</h1>
                    <small className="tw-text-[16px] tw-font-normal tw-capitalize tw-text-slate-500">
                        {card?.suffix}
                    </small>
                </div>
            </div>
        </div>
    );
};

export default TrainerDashboard;
