import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { fetchRoleReq, getAllRoles } from "@/redux/roles/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* GetRole() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "role/listing-role", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response;
            return errMsg;
        });
    if (data) {
        yield put(getAllRoles(data));
    } else {
        yield null;
    }
}

function* addRole(roleData) {
    const roleDataPost = yield axios
        .post(CONSTANTS.getAPI() + "role/create-role", roleData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err;
            return errMsg;
        });
    if (roleDataPost?.success) {
        yield put(fetchRoleReq());
        yield put(
            AlertSnackInfo({
                message: roleDataPost?.message,
                result: roleDataPost?.success,
            }),
        );
    } else {
        yield put(
            AlertSnackInfo({
                message: roleDataPost?.message,
                result: roleDataPost?.success,
            }),
        );
    }
}

function* updateRole(roleData, onClose) {
    const roleUpdate = yield axios
        .put(CONSTANTS.getAPI() + "role/update-role", roleData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (roleUpdate) {
        yield put(fetchRoleReq());
        yield put(
            AlertSnackInfo({
                message: roleUpdate?.message,
                result: roleUpdate?.success,
            }),
        );
    } else {
        yield put(
            AlertSnackInfo({
                message: roleUpdate?.message,
                result: roleUpdate?.success,
            }),
        );
    }
}

function* deleteRole(delID) {
    try {
        const roleDelete = yield axios
            .post(CONSTANTS.getAPI() + "role/delete-role", delID.payload, {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("login_token")}`,
                },
            })
            .then((res) => {
                const response = res.data;
                return response;
            })
            .catch((err) => {
                const msg = err?.response?.data?.message;
                if (msg) {
                    return msg;
                }
                const errMsg = err.message;
                return errMsg;
            });
        if (roleDelete) {
            yield put(fetchRoleReq());

            if (roleDelete?.message) {
                yield put(
                    AlertSnackInfo({
                        message: roleDelete.message,
                        result: roleDelete?.success,
                    }),
                );
                return;
            }
            yield put(AlertSnackInfo({ message: roleDelete, result: roleDelete?.success }));
        } else {
            yield put(
                AlertSnackInfo({
                    message: roleDelete?.message,
                    result: roleDelete?.success,
                }),
            );
        }
    } catch (error) {
        // console.log("new we", error)
    }
}

export function* RoleWatcher() {
    yield takeEvery(actions.FETCH_ROLES_REQ, GetRole);
    yield takeEvery(actions.ADD_NEW_ROLE, addRole);
    yield takeEvery(actions.UPDATE_ROLE, updateRole);
    yield takeEvery(actions.DELETE_ROLE, deleteRole);
}

export default function* RoleSaga() {
    yield all([fork(RoleWatcher)]);
}
