import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { addCategory, updateCategory } from "@/redux/category/action";
import { useEffect, useId, useState } from "react";
import { useDispatch } from "react-redux";

export default function CategoryForm({ open, setOpen, selected }) {
    const dispatch = useDispatch();
    const formId = useId();
    const [data, setData] = useState({
        category_name: "",
        is_active: "true",
    });

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setData({ ...data, [name]: value });
    };

    useEffect(() => {
        if (selected !== null) {
            setData({
                category_name: selected.category_name,
                is_active: Bo<PERSON>an(selected.is_active),
                id: selected.id,
            });
        } else {
            setData({
                category_name: "",
                is_active: "true",
            });
        }
    }, [selected]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (selected !== null) {
            const payload = {
                category_name: data?.category_name,
                is_active: data?.is_active === "true" ? true : false,
                id: selected.id,
            };
            dispatch(updateCategory(data));
            setOpen(false);
        } else {
            const payload = {
                category_name: data?.category_name,
                is_active: data?.is_active === "true" ? true : false,
            };
            dispatch(addCategory(payload));
            setOpen(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="tw-z-[1000]">
                <DialogHeader>
                    <DialogTitle>{selected ? "Edit Category" : "Create Category"}</DialogTitle>
                </DialogHeader>
                <div>
                    <form id={formId} onSubmit={handleSubmit} className="tw-space-y-3">
                        <Input type="hidden" name="id" id="id" value={data?.id} />

                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="display_name">Category Name</Label>
                            <Input
                                type="text"
                                name="category_name"
                                value={data?.category_name}
                                onChange={onChangeHandle}
                                id="display_name"
                                placeholder="Category Name"
                            />
                        </div>
                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="is_active">Category Status</Label>
                            <SelectNative
                                name="is_active"
                                id="is_active"
                                value={data?.is_active}
                                onChange={onChangeHandle}
                                placeholder=""
                            >
                                <option value="true">Active</option>
                                <option value="false">In Active</option>
                            </SelectNative>
                        </div>
                    </form>
                </div>
                <DialogFooter>
                    <Button variant="secondary" onClick={() => setOpen(false)}>
                        Cancel
                    </Button>
                    <Button variant="success" form={formId} type="submit">
                        Save
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
