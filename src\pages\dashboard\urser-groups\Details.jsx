import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const defaultValue = {
    language_id: "",
    name: "",
    user_restriction: 0,
};

const Details = ({ userGroupData, getUserGroups }) => {
    const params = useParams();
    const navigate = useNavigate();

    const [languages, setLanguages] = useState([]);
    const [userGroupDetails, setUserGroupDetails] = useState(defaultValue);

    const handleClear = () => {
        if (userGroupData == null) {
            setUserGroupDetails(defaultValue);
        } else {
            setUserGroupDetails({
                language_id: userGroupData?.language_id,
                name: userGroupData?.name,
                user_restriction: userGroupData?.user_restriction,
            });
        }
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setUserGroupDetails({ ...userGroupDetails, [name]: value });
    };

    useEffect(() => {
        if (userGroupData !== null) {
            setUserGroupDetails({
                language_id: userGroupData?.language_id,
                name: userGroupData?.name,
                user_restriction: userGroupData?.user_restriction,
            });
        } else {
            setUserGroupDetails({
                language_id: null,
                name: "",
                user_restriction: null,
            });
        }
    }, [userGroupData]);

    useEffect(() => {
        getLanguages();
    }, []);

    const getLanguages = async () => {
        await tanstackApi
            .get("language/list")
            .then((res) => {
                setLanguages(res?.data?.data);
            })
            .catch((err) => {
                setLanguages([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!userGroupDetails?.name) {
            toast?.warning("Group Name", {
                description: "Group name is required",
            });
            return false;
        } else if (!userGroupDetails?.language_id) {
            toast?.warning("Language", {
                description: "Group Language selection is required",
            });
            return false;
        } else if (!userGroupDetails?.user_restriction) {
            toast?.warning("User restriction", {
                description: "Define user restriction in group",
            });
            return false;
        }

        if (params?.group_id !== undefined) {
            const payload = {
                group_id: params?.group_id,
                language_id: userGroupDetails?.language_id,
                name: userGroupDetails?.name,
                user_restriction: userGroupDetails?.user_restriction,
            };

            await tanstackApi
                .put("user-groups/update", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "user groups",
                        log: `${userGroupDetails?.name} updated successfully.`,
                    });
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });

                    getUserGroups(params?.group_id);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                language_id: userGroupDetails?.language_id,
                name: userGroupDetails?.name,
                user_restriction: userGroupDetails?.user_restriction,
            };

            await tanstackApi
                .post("user-groups/create", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "user groups",
                        log: `${userGroupDetails?.name} created successfully.`,
                    });
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                    navigate(`/dashboard/user-group-master`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = (payload) => tanstackApi.post("users/add-timeline-log", payload);

    return (
        <Card>
            <CardHeader>
                <CardTitle>Group basic details</CardTitle>
                <CardDescription>
                    Add bundle title, user restriction & language etc here. Click save when you&apos;re done.
                </CardDescription>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[700px_auto] tw-gap-0">
                    <div className="tw-grid tw-grid-cols-1 tw-gap-3">
                        <div className="tw-space-y-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Group title</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={userGroupDetails?.name}
                                    name="name"
                                    id="name"
                                    placeholder="Enter group name here"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-2 tw-gap-4">
                            <div className="tw-space-y-1">
                                <Label htmlFor="language_id">Language</Label>
                                <SelectNative
                                    onChange={onChangeHandle}
                                    value={userGroupDetails?.language_id}
                                    name="language_id"
                                >
                                    <option value=""> - Choose Language - </option>
                                    {languages?.map((lang, idx) => (
                                        <option value={lang?.id} key={idx}>
                                            {lang?.name}
                                        </option>
                                    ))}
                                </SelectNative>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="user_restriction">User Restriction</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={userGroupDetails?.user_restriction}
                                    name="user_restriction"
                                    id="user_restriction"
                                    type="number"
                                    placeholder="Max users limit here"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" variant="outline" onClick={handleClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> Save Details
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default Details;
