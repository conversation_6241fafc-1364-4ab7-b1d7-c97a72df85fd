import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { colorsList } from "@/data/colors";
import { cn, isUrl } from "@/lib/utils";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import { motion } from "framer-motion";
import parse from "html-react-parser";
import { ArrowRight } from "lucide-react";
import { useState } from "react";

export function Process({ interactions, template }) {
    const data = interactions?.structure?.database;
    const [currentTab, setCurrentTab] = useState(data[0]?.label);
    const { handleNext, handlePrev } = usePlayer();

    const handlePrevSlide = () => {
        const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (currentIndex === 0) return handlePrev();
        setCurrentTab(data[currentIndex - 1]?.label);
    };

    const handleNextSlide = () => {
        const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (data.length - 1 === currentIndex) return handleNext();
        setCurrentTab(data[currentIndex + 1]?.label);
    };

    return (
        <>
            <div className="tw-relative tw-w-full">
                <motion.div className="tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[40px] tw-text-black">
                    <Tabs
                        value={currentTab}
                        onValueChange={setCurrentTab}
                        className="tw-grid tw-h-full tw-min-w-[600px] tw-grid-rows-[50px_1fr]"
                    >
                        <div className="tw-flex tw-items-center tw-justify-center">
                            <TabsList className="tw-h-full tw-w-fit tw-bg-transparent tw-px-5 tw-py-0">
                                {data.map((step, idx) => {
                                    const color = colorsList[idx];
                                    return (
                                        <TabsTrigger
                                            key={idx}
                                            value={step.label}
                                            style={{
                                                background: `linear-gradient(-135deg,transparent 22px, ${color} 22px, ${color} 100% ) top right, linear-gradient(-45deg, transparent 22px,${color} 22px, ${color} 100% ) bottom right`,
                                                backgroundSize: "100% 50%",
                                                backgroundRepeat: "no-repeat",
                                                transform: `${idx !== 0 ? `translateX(-${2 * idx}rem)` : ""} ${idx === currentTab ? "scale(1)" : "scale(0.8)"}`,
                                                paddingLeft: idx !== 0 ? `2rem` : "1rem",
                                                zIndex: data.length - idx,
                                            }}
                                            className={cn(
                                                "tw-relative tw-flex tw-items-center tw-justify-center tw-bg-transparent tw-pr-12 tw-font-lazyDog !tw-text-[22px] tw-leading-none tw-text-[#3B3A3E] tw-shadow-none data-[state=active]:tw-bg-transparent data-[state=active]:tw-text-[#3B3A3E] data-[state=active]:tw-shadow-none 2xl:!tw-text-[40px]",
                                            )}
                                        >
                                            <span className="tw-relative tw-z-10">
                                                {idx + 1}. {step.label}
                                            </span>
                                        </TabsTrigger>
                                    );
                                })}
                            </TabsList>
                        </div>
                        {data.map((step, index) => (
                            <TabsContent key={index} value={step.label}>
                                <div className="2xl:tw-mt-5">
                                    <div className="tw-relative tw-h-96 tw-w-full">
                                        {isUrl(step.image_src) ? (
                                            <img
                                                src={step.image_src}
                                                className="tw-absolute tw-inset-0 tw-z-0 tw-size-full tw-rounded-xl tw-object-cover"
                                                alt={step.label}
                                            />
                                        ) : (
                                            <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/60" />
                                        )}
                                    </div>
                                    <div className="tw-mt-4 tw-font-lexend">
                                        <h2 className="tw-text-[24px] tw-font-medium 2xl:tw-text-[30px]">
                                            {index + 1}. {step.label}
                                        </h2>
                                        <div
                                            className="tw-mt-1 tw-text-lg"
                                            dangerouslySetInnerHTML={{ __html: step.description }}
                                        />
                                    </div>
                                </div>
                            </TabsContent>
                        ))}
                    </Tabs>
                </motion.div>
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    onClick={handlePrevSlide}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px]",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}

export function Steps({ interactions, template }) {
    const data = interactions?.structure?.database;
    const [currentTab, setCurrentTab] = useState(data[0]?.label);
    const { handlePrev, handleNext } = usePlayer();

    const handlePrevSlide = () => {
        const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (currentIndex === 0) return handlePrev();
        setCurrentTab(data[currentIndex - 1]?.label);
    };

    const handleNextSlide = () => {
        const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (data.length - 1 === currentIndex) return handleNext();
        setCurrentTab(data[currentIndex + 1]?.label);
    };

    return (
        <>
            <div className="tw-relative tw-w-full">
                <motion.div className="tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[40px] tw-text-black">
                    <Tabs
                        value={currentTab}
                        onValueChange={setCurrentTab}
                        className="tw-grid tw-h-full tw-w-full tw-grid-rows-[120px_1fr]"
                    >
                        <div className="tw-flex tw-items-center tw-justify-center">
                            <TabsList className="tw-h-full tw-w-fit tw-gap-8 tw-bg-transparent tw-px-5 tw-py-0">
                                {data.map((step, index) => (
                                    <div key={index} className="tw-flex tw-items-center tw-gap-8">
                                        <div className="tw-flex tw-flex-col tw-gap-0">
                                            <TabsTrigger
                                                value={step.label}
                                                className="tw-flex tw-aspect-square tw-items-center tw-justify-center !tw-rounded-full tw-bg-[#FFEBBA] !tw-p-2 tw-font-lazyDog !tw-text-[22px] tw-leading-none tw-text-[#3B3A3E] tw-shadow-none data-[state=active]:!tw-bg-[#F8CA5C] data-[state=active]:tw-text-[#3B3A3E] data-[state=active]:tw-shadow-none 2xl:!tw-text-[40px]"
                                            >
                                                <div className="w-flex tw-aspect-square tw-size-10 tw-items-center tw-justify-center tw-rounded-full tw-bg-[#f8ca5c] tw-p-2">
                                                    <span
                                                        style={{
                                                            fontFamily: template?.font_family?.body?.font_family,
                                                            // fontSize: template?.font_family?.body?.font_size ?? "32px",
                                                            lineHeight: "1",
                                                            fontWeight:
                                                                template?.font_family?.body?.font_weight ?? "700",
                                                            // color: template?.font_family?.body?.color ?? "#333333",
                                                        }}
                                                    >
                                                        {index + 1}
                                                    </span>
                                                </div>
                                            </TabsTrigger>
                                            <div
                                                className="tw-flex tw-items-center tw-justify-center tw-rounded-full tw-text-center tw-text-sm tw-text-black"
                                                style={{
                                                    fontFamily: template?.font_family?.body?.font_family,
                                                    fontSize: template?.font_family?.body?.font_size ?? "32px",
                                                    lineHeight: "1",
                                                    fontWeight: template?.font_family?.body?.font_weight ?? "700",
                                                    color: template?.font_family?.body?.color ?? "#333333",
                                                }}
                                            >
                                                {step.label}
                                            </div>
                                        </div>
                                        {data.length - 1 !== index && <ArrowRight className="pl-8 mb-3" />}
                                    </div>
                                ))}
                            </TabsList>
                        </div>

                        {data.map((step, index) => (
                            <TabsContent key={index} value={step.label}>
                                <div
                                    style={{
                                        background: step.image_src ? `url(${step.image_src})` : "",
                                    }}
                                    className="tw-relative tw-flex tw-h-full tw-min-h-80 tw-flex-col tw-overflow-hidden tw-rounded-xl tw-p-5 tw-text-white"
                                >
                                    <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/40"></div>
                                    <div className="tw-relative tw-z-10 tw-flex tw-h-full tw-flex-1 tw-flex-col tw-justify-end tw-font-lazyDog">
                                        <h2
                                            style={{
                                                fontFamily: template?.font_family?.subtitle?.font_family,
                                                fontSize: template?.font_family?.subtitle?.font_size ?? "32px",
                                                lineHeight: "1",
                                                fontWeight: template?.font_family?.subtitle?.font_weight ?? "700",
                                                color: template?.font_family?.subtitle?.color ?? "#333333",
                                            }}
                                            className="tw-mb-2 tw-mt-auto tw-text-[20px] tw-leading-none 2xl:tw-text-[30px]"
                                        >
                                            {index + 1}. {step.label}
                                        </h2>
                                        <div
                                            style={{
                                                fontFamily: template?.font_family?.body?.font_family,
                                                fontSize: template?.font_family?.body?.font_size ?? "32px",
                                                lineHeight: "1",
                                                fontWeight: template?.font_family?.body?.font_weight ?? "700",
                                                color: template?.font_family?.body?.color ?? "#333333",
                                            }}
                                            className="tw-text-base"
                                        >
                                            {parse(step.description)}
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                        ))}
                    </Tabs>
                </motion.div>
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    onClick={handlePrevSlide}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px]",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}
