import { animationVariants } from "@/pages/dashboard/course-player/content/type/animation";
import { MediaPlayer, MediaProvider } from "@vidstack/react";
import { PlyrLayout, plyrLayoutIcons } from "@vidstack/react/player/layouts/plyr";
import "@vidstack/react/player/styles/base.css";
import "@vidstack/react/player/styles/plyr/theme.css";
import { motion } from "framer-motion";
import parse, { domToReact } from "html-react-parser";
import { useEffect, useRef } from "react";

const ContentHTML = ({ content, template }) => {
    const component = {
        TEXT: ContentArea,
        IMAGE: ImageArea,
        VIDEO: VideoArea,
    };
    return (
        <div className="content_body" style={content?.lecture_data?.layout}>
            {content?.lecture_data?.layout_box?.map((box, idx) => {
                const Component = component[box?.content_type];
                if (!Component) return null;
                return <Component key={idx} template={template} idx={content?.id} box={box} />;
            })}
        </div>
    );
};

function ContentArea({ box, idx, template }) {
    const animation = animationVariants[box?.type || "Slide to Right"];

    const elementVariant = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 },
    };

    const splitTextIntoSentences = (text) => {
        return text.split(/(?<=[.?!])\s+/); // Split by sentence boundaries
    };

    const parseStyleString = (styleString) => {
        if (!styleString) return {};
        return styleString.split(";").reduce((styleObject, styleProperty) => {
            const [key, value] = styleProperty.split(":").map((str) => str.trim());
            if (key && value) {
                styleObject[camelCase(key)] = value; // Convert CSS properties to camelCase
            }
            return styleObject;
        }, {});
    };

    const camelCase = (str) => str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());

    const renderWithAnimation = (domNode, index) => {
        if (domNode.type === "tag") {
            if (domNode.name === "img") {
                return (
                    <motion.div
                        key={index}
                        initial="hidden"
                        animate="visible"
                        variants={elementVariant}
                        transition={{ duration: 0.5, delay: index * 0.5 }}
                    >
                        <img
                            src={domNode.attribs.src}
                            alt={domNode.attribs.alt || ""}
                            {...domNode.attribs}
                            style={{ maxWidth: "100%", height: "auto" }}
                        />
                    </motion.div>
                );
            }
            return (
                <motion.div
                    key={index}
                    initial="hidden"
                    animate="visible"
                    variants={elementVariant}
                    transition={{ duration: 0.5, delay: index * 0.5 }}
                    className={domNode.attribs.class}
                    style={parseStyleString(domNode.attribs.style)}
                >
                    {domToReact(domNode.children)}
                </motion.div>
            );
        }

        if (domNode.type === "text") {
            return domNode.data;
        }

        return null; // Skip unsupported node types
    };

    const content = parse(box?.content, {
        replace: (domNode, index) => renderWithAnimation(domNode, index),
    });

    return (
        <motion.div
            className="animate_HTML tw-space-y-4"
            key={idx}
            initial="initial"
            animate="animate"
            variants={animation}
            transition={{
                duration: animation?.duration ?? 0.5,
                delay: animation?.delay ?? 0,
            }}
        >
            {content}
            {/* {parse(box?.content)} */}
        </motion.div>
    );
}

function VideoArea({ box, idx }) {
    const animation = animationVariants[box?.type || "Slide to Right"];
    return (
        <motion.div
            className="tw-h-full"
            key={idx}
            initial="initial"
            animate="animate"
            variants={animation}
            transition={{
                duration: animation?.duration ?? 0.5,
                delay: animation?.delay ?? 0,
            }}
        >
            <MediaPlayer
                className="!tw-aspect-video !tw-h-[290px] tw-border-[3px] tw-border-black tw-bg-black [--plyr-border-radius:_8px]"
                src={box?.content}
                playsInline
            >
                <MediaProvider />
                <PlyrLayout icons={plyrLayoutIcons} />
            </MediaPlayer>
        </motion.div>
    );
}

function ImageArea({ box, idx }) {
    const ref = useRef(null);
    const animation = animationVariants[box?.type || "Grow"];

    const getCurrentScale = () => {
        const style = getComputedStyle(ref.current);
        const transform = style.transform;
        if (transform && transform.includes("matrix")) {
            const match = transform.match(/matrix\(([^)]+)\)/);
            if (match) {
                const values = match[1].split(", ");
                return parseFloat(values[0]);
            }
        }
        return 1;
    };

    // <p>The autumn breeze whispered through the golden leaves, carrying the faint scent of pine and the promise of a crisp evening.</p><p>In this hidden grove, time seemed to pause, inviting all who entered to simply breathe, to listen, to exist.</p><img src="https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1732883909_screenshot_2024_10_28_143329.png"></img>

    const zoomIn = () => {
        const currentScale = getCurrentScale();
        if (currentScale >= 3) return;
        ref.current.style.transform = `scale(${currentScale + 0.25})`;
    };

    const zoomOut = () => {
        const currentScale = getCurrentScale();
        if (currentScale <= 0.25) return;
        ref.current.style.transform = `scale(${currentScale - 0.25})`;
    };

    const toggleFullscreen = () => {
        if (document.fullscreenElement) return document.exitFullscreen();
        if (ref.current) ref.current.requestFullscreen();
    };

    useEffect(() => {
        if (ref.current) ref.current.style.transform = "scale(1)";
    }, [box.content]);

    return (
        <div className="animate_IMG">
            <div className="container">
                <motion.img
                    ref={ref}
                    src={box?.content}
                    key={idx}
                    initial="initial"
                    whileInView="animate"
                    variants={animation}
                    transition={{
                        duration: animation?.duration ?? 0.5,
                        delay: animation?.delay ?? 0.5,
                    }}
                />
                <motion.div className="zoom_opts" key={box?.content} initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
                    <button onClick={zoomIn}>
                        <i className="fa-solid fa-magnifying-glass-plus"></i>
                    </button>
                    <button onClick={zoomOut}>
                        <i className="fa-solid fa-magnifying-glass-minus"></i>
                    </button>
                    <button onClick={toggleFullscreen}>
                        <i className="fa-solid fa-expand"></i>
                    </button>
                </motion.div>
            </div>
        </div>
    );
}

export default ContentHTML;
