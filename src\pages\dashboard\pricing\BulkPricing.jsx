import {
    <PERSON><PERSON><PERSON>rumb,
    Breadc<PERSON>bItem,
    Bread<PERSON><PERSON>bLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import BulkPricingAddEdit from "./BulkPricingAddEdit";

const tabsData = [
    {
        name: "Course Pricing",
    },
    {
        name: "Bundle Pricing",
    },
];

const ITEMS_PER_PAGE = 9;

const BulkPricing = () => {
    const [activeTab, setActiveTab] = useState("Course Pricing");

    const [open, setOpen] = useState(false);
    const [courseList, setCourseList] = useState([]);
    const [editData, setEditData] = useState(null);

    const navigate = useNavigate();
    const [serverData, setServerData] = useState([]);
    const [dataList, setDataList] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [questionTypes, setQuestionTypes] = useState([]);

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(dataList.length / ITEMS_PER_PAGE);

    // Get data for the current page
    const getPaginatedData = () => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        return dataList.slice(startIndex, endIndex);
    };

    useEffect(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        let options = dataList.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, dataList]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const [filterState, setFilterState] = useState({
        search: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? (activeTab == "Course Pricing" ? item?.course?.course_title : item?.bundle?.name)
                      ?.toLowerCase()
                      ?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            return matchesSearch; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
        });
    };

    useEffect(() => {
        getPriceList();
        getCourses();
    }, []);

    useEffect(() => {
        if (activeTab == "Course Pricing") {
            setDataList(serverData?.filter((dt) => dt?.course_id !== null));
        } else {
            setDataList(serverData?.filter((dt) => dt?.bundle_id !== null));
        }
    }, [serverData, activeTab]);

    // useEffect(() => {
    //     setFilteredData(dataList);
    // }, [dataList]);

    useEffect(() => {
        setTableData(filteredData);
    }, [filteredData]);

    const getPriceList = async () => {
        await tanstackApi
            .get("course-bundle-bulk-price/all")
            .then((res) => {
                setServerData(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setServerData([]);
            });
    };

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const onAddNewType = () => {
        setEditData(null);
        setOpen(true);
    };

    const onEditData = (data) => {
        setEditData(data);
        setOpen(true);
    };

    return (
        <>
            {" "}
            <BulkPricingAddEdit
                open={open}
                setOpen={setOpen}
                editData={editData}
                activeTab={activeTab}
                getPriceList={getPriceList}
            />
            <div>
                <div className="tw-flex tw-justify-between">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Pricing</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Bulk pricing for domains</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <Button className="tw-px-2 tw-py-1" onClick={onAddNewType}>
                        <i className="fa-solid fa-plus"></i> New Bulk Pricing
                    </Button>
                </div>
                <div className="page_tabs">
                    {tabsData?.map((tab, idx) => (
                        <p
                            key={idx}
                            onClick={() => setActiveTab(tab?.name)}
                            className={activeTab == tab?.name ? "active" : ""}
                        >
                            {tab?.name}
                        </p>
                    ))}
                </div>
                <div className="page_filters tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Search
                        </label>
                        <input
                            value={filterState?.search}
                            name="search"
                            onChange={onFilterChange}
                            className="tw-text-sm"
                            type="text"
                            placeholder="Search by name ..."
                        />
                    </div>

                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>{activeTab == "Course Pricing" ? "Course Name" : "Bundle Name"}</th>
                                <th>Price</th>
                                <th>Currency</th>
                                <th>Lower Limit</th>
                                <th>Upper Limit</th>
                                <th>Creation On</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>
                                        {activeTab == "Course Pricing" ? row?.course?.course_title : row?.bundle?.name}
                                    </td>
                                    <td>{`${row?.currency?.currency_symbol} ${row?.unit_price}`}</td>
                                    <td>{row?.currency?.currency_code || "-"}</td>
                                    <td>
                                        {row?.lower_limit} <small>Users</small>
                                    </td>
                                    <td>
                                        {row?.upper_limit} <small>Users</small>
                                    </td>
                                    <td>{moment(row?.createdAt).format("LL")}</td>
                                    <td>
                                        <button className="selected_btn" onClick={() => onEditData(row)}>
                                            <i className="fa-solid fa-edit"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">7</p>
                    </div>
                    <div>
                        <Pagination className="tw-mx-0 tw-w-[auto]">
                            <PaginationContent>
                                {/* Previous Button */}
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {/* Page Numbers */}
                                {[...Array(totalPages)].map((_, index) => (
                                    <PaginationItem key={index}>
                                        <PaginationLink
                                            href="#"
                                            isActive={index + 1 === currentPage}
                                            onClick={() => handlePageChange(index + 1)}
                                        >
                                            {index + 1}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {/* Ellipsis */}
                                {totalPages > 5 && (
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                )}

                                {/* Next Button */}
                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </>
    );
};

export default BulkPricing;
