import { tanstack<PERSON>pi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetAllInteractions = ({ limit, offset }) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["interactions", { limit, offset, userId }],
        queryFn: async () => (await tanstackApi.get("/interaction/list", { params: { limit, offset } })).data,
    });
};

export const useGetInteraction = (id) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["interactions", { id, userId }],
        queryFn: async () => (await tanstackApi.get(`/interaction/get/${id}`)).data,
        enabled: !!id,
    });
};

export const useCreateInteraction = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("/interaction/create", data)).data,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["interactions"] });
        },
    });
};

export const useUpdateInteraction = () => {
    const userId = localStorage.getItem("userId");
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            const { createdAt, updatedAt, ...payload } = data;
            return (await tanstackApi.put("/interaction/update", payload)).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["interactions"] });
        },
    });
};

export const useDeleteInteraction = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.delete(`/interaction/delete/${data.id}`)).data,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["interactions"] });
        },
    });
};
