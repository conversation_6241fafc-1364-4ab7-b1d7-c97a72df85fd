import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
// import QuestionTypeForm from "./QuestionTypeForm";
import { Badge } from "@/components/ui/badge";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { utils, writeFileXLSX } from "xlsx";

const typeList = [
    {
        key: "online",
        label: "Online",
    },
    {
        key: "offline",
        label: "Offline",
    },
];

const AttendanceReport = () => {
    const params = useParams();
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(11);

    const [filterState, setFilterState] = useState({
        search: "",
        date: "",
        type: "",
        meet_type: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? `${item?.user?.first_name} ${item?.user?.last_name}`
                      ?.toLowerCase()
                      ?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const date = filterState?.date
                ? moment(item.class?.date).format("DD/MMM/YYYY") === moment(filterState?.date).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            const type = filterState?.type ? item?.class?.type === filterState?.type : true; // Allow all items if no subCategory filter
            const meetType = filterState?.meet_type ? item?.class?.meet_type === filterState?.meet_type : true; // Allow all items if no subCategory filter

            return matchesSearch && date && type && meetType; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            date: "",
            type: "",
            meet_type: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getReports();
    }, []);

    const getReports = async () => {
        await tanstackApi
            .get("reports/get-attendance-reports", { params: { domain_id: params?.domain_id } })
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    function excelConverter() {
        var finalData = filteredData?.map((row, index) => {
            return {
                Learner: `${row?.user?.first_name} ${row?.user?.last_name}`,
                Email: row?.user?.email,
                Topic: row?.class?.topic,
                Chapter: row?.class?.chapter?.chapter_title,
                Course: row?.class?.chapter?.lms_course?.course_title,
                Meet: row?.class?.meet_type,
                Type: row?.class?.type,
                Date: moment(row?.class?.date).format("LL"),
                "Attended On": moment(row?.created_at).format("LL"),
            };
        });
        const wb = utils.book_new();
        utils.book_append_sheet(wb, utils.json_to_sheet(finalData));
        writeFileXLSX(wb, "Attendance Reports.xlsx");
    }

    return (
        <>
            <div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                        <div className="input_group">
                            <label className="tw-text-sm" htmlFor="">
                                Search
                            </label>
                            <input
                                className="tw-text-sm"
                                type="text"
                                placeholder="Search by names here..."
                                onChange={onFilterChange}
                                value={filterState?.search}
                                name="search"
                            />
                        </div>

                        <div className="input_group">
                            <label className="tw-text-sm" htmlFor="">
                                Type
                            </label>
                            <select
                                className="tw-text-sm"
                                onChange={onFilterChange}
                                value={filterState?.type}
                                name="type"
                            >
                                <option value=""> - All - </option>
                                {typeList?.map((status, idx) => (
                                    <option key={idx} value={status?.key}>
                                        {status?.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="input_group">
                            <label className="tw-text-sm" htmlFor="">
                                Online Type
                            </label>
                            <select
                                className="tw-text-sm"
                                onChange={onFilterChange}
                                value={filterState?.meet_type}
                                name="meet_type"
                            >
                                <option value=""> - All - </option>
                                <option value="zoom">Zoom Meet</option>
                                <option value="google-meet">Google Meet</option>
                            </select>
                        </div>
                        <div className="input_group">
                            <label className="tw-text-sm" htmlFor="">
                                Date
                            </label>
                            <input
                                className="tw-text-sm"
                                type="date"
                                onChange={onFilterChange}
                                value={filterState?.date}
                                name="date"
                            />
                        </div>
                        <div className="filter_controls">
                            <button className="search_btn" onClick={onSearch}>
                                <i className="fa-solid fa-magnifying-glass"></i> Search
                            </button>
                            <button className="clear_btn" onClick={onClear}>
                                <i className="fa-solid fa-xmark"></i> Clear
                            </button>
                        </div>
                    </div>
                    <Button className="tw-px-2 tw-py-1" onClick={excelConverter}>
                        <i className="fa-solid fa-download"></i> Export Data
                    </Button>
                </div>

                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>Learner</th>
                                <th>Email</th>
                                <th>Topic</th>
                                <th>Chapter</th>
                                <th>Course</th>
                                <th>Meet</th>
                                <th>Type</th>
                                <th>Date</th>
                                <th>Attended on</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>{`${row?.user?.first_name} ${row?.user?.last_name}`}</td>
                                    <td>{row?.user?.email}</td>
                                    <td>{row?.class?.topic}</td>
                                    <td>{row?.class?.chapter?.chapter_title}</td>
                                    <td>{row?.class?.chapter?.lms_course?.course_title}</td>
                                    <td>{row?.class?.meet_type}</td>
                                    <td>
                                        {row?.class?.type == "online" ? (
                                            <Badge>Online</Badge>
                                        ) : (
                                            <Badge variant={"outline"}>Offline</Badge>
                                        )}
                                    </td>

                                    <td>{moment(row?.class?.date).format("LL")}</td>
                                    <td>{moment(row?.created_at).format("LL")}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination className="">
                            <PaginationContent>
                                {/* Previous Button */}
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {/* Page Numbers with Ellipses */}
                                {currentPage > 3 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                1
                                            </PaginationLink>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    </>
                                )}

                                {getVisiblePages().map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href="#"
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {currentPage < totalPages - 2 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                {totalPages}
                                            </PaginationLink>
                                        </PaginationItem>
                                    </>
                                )}

                                {/* Next Button */}
                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </>
    );
};

export default AttendanceReport;
