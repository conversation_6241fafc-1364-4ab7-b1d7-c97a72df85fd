import { colorsList } from "@/data/colors";
import { useEffect, useRef, useState } from "react";
import Draggable from "react-draggable";

const HotspotDragDrop = ({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    setContentData,
    template,
    // setComponentsArray,
}) => {
    const containerRef = useRef(null);
    const [circles, setCircles] = useState(content.hotspots);
    const [dropzones, setDropzones] = useState(content.hotspotDropzone);
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

    // Update container dimensions dynamically
    useEffect(() => {
        const updateContainerSize = () => {
            if (containerRef.current) {
                const rect = containerRef.current.getBoundingClientRect();
                setContainerSize({ width: rect.width, height: rect.height });
            }
        };

        updateContainerSize();
        window.addEventListener("resize", updateContainerSize);

        return () => {
            window.removeEventListener("resize", updateContainerSize);
        };
    }, []);

    const clampPosition = (x, y) => {
        return {
            x: Math.min(Math.max(x, 0), containerSize.width),
            y: Math.min(Math.max(y, 0), containerSize.height),
        };
    };

    // Convert relative positions to absolute positions
    const getAbsolutePosition = (relativeX, relativeY) => {
        if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
        return clampPosition(relativeX * containerSize.width, relativeY * containerSize.height);
    };

    // Convert absolute positions to relative positions
    const getRelativePosition = (data, absoluteX, absoluteY) => {
        if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
        return {
            ...data,
            x: absoluteX / containerSize.width,
            y: absoluteY / containerSize.height,
        };
    };

    // Initialize hotspots and dropzones from `data`

    // Handle draggable stop for hotspots
    const handleStop = (index, pos) => {
        setCircles((prevCircles) =>
            prevCircles.map((circle, i) => (i === index ? { ...getRelativePosition(circle, pos.x, pos.y) } : circle)),
        );
    };

    // Handle draggable stop for dropzones
    const handleStopDropzone = (index, pos) => {
        setDropzones((prevZones) =>
            prevZones.map((zone, i) => (i === index ? { ...getRelativePosition(zone, pos.x, pos.y) } : zone)),
        );
    };

    useEffect(() => {
        setContentData({ ...content, hotspotDropzone: dropzones, hotspots: circles });
    }, [circles, dropzones]);

    return (
        <div
            className="tw-grid tw-min-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            // style={{ backgroundImage: `url(${template?.background||content?.question_background})` }}
        >
            <div className="tw-flex tw-items-center tw-justify-center tw-px-10 tw-py-7">
                <div
                    className="tw-overflow-hidden tw-rounded-lg tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{
                        backgroundImage: `url(${content?.question_thumbnail})`,
                        position: "relative",
                        // aspectRatio: 12/7,
                        width: "600px",
                        height: "350px",
                    }}
                    ref={containerRef}
                >
                    {/* Render Hotspots */}
                    {circles.map((position, c_index) => {
                        const { x, y } = getAbsolutePosition(position.x, position.y);
                        return (
                            <Draggable
                                key={`circle-${c_index}`}
                                bounds="parent"
                                position={{ x, y }}
                                onStop={(e, data) => handleStop(c_index, data)}
                            >
                                <div
                                    className="tw-absolute tw-z-10 tw-cursor-grab tw-rounded-md tw-bg-gray-100 tw-px-2 tw-py-1 tw-text-sm"
                                    style={{ width: "max-content", background: colorsList[c_index] }}
                                >
                                    <p className="tw-font-mono tw-font-medium">{position.name}</p>
                                </div>
                            </Draggable>
                        );
                    })}

                    {/* Render Dropzones */}
                    {dropzones.map((position, d_index) => {
                        const { x, y } = getAbsolutePosition(position.x, position.y);
                        return (
                            <Draggable
                                key={`dropzone-${d_index}`}
                                bounds="parent"
                                position={{ x, y }}
                                onStop={(e, data) => handleStopDropzone(d_index, data)}
                            >
                                <div className="tw-absolute tw-h-[50px] tw-w-[50px] tw-cursor-grab tw-rounded-lg tw-border-[2px] tw-border-dashed tw-border-red-500 tw-bg-white/30"></div>
                            </Draggable>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default HotspotDragDrop;
