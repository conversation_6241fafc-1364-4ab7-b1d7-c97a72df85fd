import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { imageResponse } from "@/redux/image-upload/action";
import { fetchLearningPathReq, getAllLearningPaths, viewLearningPathData } from "@/redux/learning-path/action";
import { addTimelineLog } from "@/redux/reports/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* GetAllLearningPath() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "learning-path/listing", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data.data)
        .catch((err) => err.response.data);

    yield put(getAllLearningPaths(data ? data : []));
}

function* uploadReq(Data) {
    const imageUpload = yield axios
        .post(CONSTANTS.getAPI() + "common/upload-file", Data.payload, {
            headers: {
                "Content-Type": "multipart/form-data",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (imageUpload) {
        if (imageUpload.success) {
            yield put(imageResponse(imageUpload));
        }
    }
}

function* addNewLearningPath(Data) {
    const addLearningPath = yield axios
        .post(CONSTANTS.getAPI() + "learning-path/create", Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (addLearningPath) {
        yield put(
            addTimelineLog({
                user_id: localStorage.getItem("userId"),
                event: "learning path",
                log: `${addLearningPath?.data?.name} created successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
            }),
        );

        yield put(fetchLearningPathReq());
        yield put(AlertSnackInfo({ message: addLearningPath.message, result: addLearningPath.success }));
    }
}

function* updateLearningPath(Data) {
    const updateLearningPath = yield axios
        .put(CONSTANTS.getAPI() + "learning-path/update", Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (updateLearningPath) {
        yield put(
            addTimelineLog({
                user_id: localStorage.getItem("userId"),
                event: "learning path",
                log: `${updateLearningPath?.data?.name} updated successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
            }),
        );
        yield put(fetchLearningPathReq());
        yield put(AlertSnackInfo({ message: updateLearningPath.message, result: updateLearningPath.success }));
    }
}

function* assignCourseReq(Data) {
    const assignCourse = yield axios
        .post(CONSTANTS.getAPI() + "learning-path/assign-course", Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (assignCourse) {
        yield put(AlertSnackInfo({ message: assignCourse.message, result: assignCourse.success }));
    }
}

function* viewLearningPathSaga(learningID) {
    const viewLearningPath = yield axios
        .get(CONSTANTS.getAPI() + `learning-path/view-learning-path-details/${learningID.payload}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (viewLearningPath) {
        yield put(viewLearningPathData(viewLearningPath));
    }
}

function* viewLevel3LearningPathSaga(learningID) {
    const viewLearningPath = yield axios
        .get(CONSTANTS.getAPI() + `learning-path/view-learning-path/${learningID.payload}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (viewLearningPath) {
        yield put(viewLearningPathData(viewLearningPath));
    }
}

function* assignGroupORUserToPath(user) {
    const assignGroupUser = yield axios
        .post(CONSTANTS.getAPI() + `learning-path/assign-users`, user.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (assignGroupUser) {
        yield put(AlertSnackInfo({ message: assignGroupUser.message, result: assignGroupUser.success }));
    }
}

function* updateAssignGroupORUserToPath(user) {
    const UpdateAssignGroupUser = yield axios
        .put(CONSTANTS.getAPI() + `learning-path/assign-users`, user.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (UpdateAssignGroupUser) {
        yield put(AlertSnackInfo({ message: UpdateAssignGroupUser.message, result: UpdateAssignGroupUser.success }));
    }
}

export function* LearningPathWatcher() {
    yield takeEvery(actions.FETCH_LEARNING_PATH_REQ, GetAllLearningPath);
    yield takeEvery(actions.UPLOAD_LEARNING_PATH, uploadReq);
    yield takeEvery(actions.ADD_LEARNING_PATH, addNewLearningPath);
    yield takeEvery(actions.UPDATE_LEARNING_PATH, updateLearningPath);
    yield takeEvery(actions.ASSIGN_COURSE_TO_LEARNING_PATH, assignCourseReq);
    yield takeEvery(actions.VIEW_LEARNING_PATH_REQ, viewLearningPathSaga);
    yield takeEvery(actions.VIEW_LEVEL_THREEE_LEARNING_PATH_REQ, viewLevel3LearningPathSaga);
    yield takeEvery(actions.ASSIGN_COURSE_OR_GROUP_TO_LEARNING_PATH, assignGroupORUserToPath);
    yield takeEvery(actions.UPDATE_ASSIGNED_COURSE_OR_GROUP_TO_LEARNING_PATH, updateAssignGroupORUserToPath);
}

export default function* LearningPathSaga() {
    yield all([fork(LearningPathWatcher)]);
}
