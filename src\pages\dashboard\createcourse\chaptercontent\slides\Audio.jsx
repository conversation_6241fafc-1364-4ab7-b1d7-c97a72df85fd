import SlideHeader, { AddContentFunction } from "@/pages/dashboard/createcourse/chaptercontent/slides/common";
import { useInView } from "framer-motion";
import { CircleFadingPlus } from "lucide-react";
import { useEffect, useRef, useState } from "react";

const Audio = ({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    onSetDelete,
    setSelectedContent,
    setCurrentId,
    setOpenIconDialog,
    setContentArray,
    contentArray,
}) => {
    const [editable, setEditable] = useState(false);
    const headingRef = useRef(null);
    const containerRef = useRef(null);
    const inView = useInView(containerRef, { amount: 0.8 });
    useEffect(() => {
        if (inView) setCurrentId(`#slide_${index}`);
    }, [inView]);
    return (
        <div
            ref={containerRef}
            className="tw-grid tw-min-h-[60vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${template?.background || content?.background})` }}
        >
            <SlideHeader
                index={index}
                content={content}
                contentArray={contentArray}
                onSlideEdit={onSlideEdit}
                onSetDelete={onSetDelete}
                onRemoveSlide={onRemoveSlide}
                setOpenIconDialog={setOpenIconDialog}
                setContentArray={setContentArray}
            />
            {content?.content_url == null ? (
                <AddContentFunction onSlideEdit={onSlideEdit} content={content} index={index} />
            ) : (
                <div className={`tw-w-full tw-gap-5 tw-rounded-md tw-p-3`}>
                    <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-rounded-lg tw-border-[2px] tw-border-dashed tw-border-slate-700 tw-p-3 tw-shadow-sm">
                        <audio controls className="tw-w-[80%]">
                            <source src={content?.content_url} type="audio/mpeg" />
                        </audio>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Audio;
