import { cn, isUrl } from "@/lib/utils";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

export function Accordion({ interactions, template }) {
    const data = interactions?.structure?.database;
    const [currentTab, setCurrentTab] = useState(0);
    const { handleNext, handlePrev } = usePlayer();

    useEffect(() => {
        setCurrentTab(0);
    }, [data]);

    const handlePrevSlide = () => {
        // const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (currentTab === 0) return handlePrev();
        setCurrentTab((prev) => prev - 1);
    };

    const handleNextSlide = () => {
        // const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (data.length - 1 === currentTab) return handleNext();
        setCurrentTab((prev) => prev + 1);
    };

    const variants = {
        initial: {
            opacity: 0,
            scale: 0,
        },
        open: {
            opacity: 1,
            scale: 1,
        },
    };

    const circleVariants = {
        initial: {
            bottom: "2.5rem",
            left: "1.25rem",
        },
        open: {
            bottom: "4.5rem",
            left: "1rem",
        },
    };
    return (
        <>
            <div className="tw-flex tw-h-[650px] tw-w-full tw-gap-5">
                {data.map((item, index) => {
                    return (
                        <div
                            key={index}
                            className={cn(
                                "tw-relative tw-flex tw-w-[80px] tw-items-center tw-justify-center tw-overflow-hidden tw-rounded-2xl tw-transition-all",
                                index === currentTab && "tw-flex-grow",
                            )}
                            onClick={() => setCurrentTab(index)}
                        >
                            {isUrl(item.image_src) ? (
                                <img
                                    src={item.image_src}
                                    className="tw-absolute tw-inset-0 tw-z-0 tw-size-full tw-object-cover"
                                    alt=""
                                />
                            ) : null}
                            <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/60" />
                            <motion.div
                                variants={circleVariants}
                                initial="initial"
                                animate={currentTab === index ? "open" : "initial"}
                                className="tw-absolute tw-size-10 tw-shrink-0 tw-rounded-full tw-border tw-border-teal-600 tw-bg-white"
                            />
                            <motion.div
                                variants={variants}
                                initial="initial"
                                animate={currentTab === index ? "open" : "initial"}
                                className="tw-relative tw-z-10 tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center tw-pb-4"
                            >
                                <div className="tw-mt-auto tw-flex tw-w-full tw-items-start tw-gap-4 tw-pl-16 tw-text-white">
                                    <div className="tw-flex tw-h-24 tw-flex-col tw-gap-1 tw-text-base">
                                        <h1 className="tw-text-lg tw-font-medium">{item.label}</h1>
                                        <p className="tw-text-sm">{item.description}</p>
                                    </div>
                                </div>
                            </motion.div>
                        </div>
                    );
                })}
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    disabled={0 === currentTab}
                    onClick={handlePrevSlide}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px]",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}
