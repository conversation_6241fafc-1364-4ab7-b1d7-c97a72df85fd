import { CONSTANTS } from "@/constants";
import axios from "axios";

export const tanstackApi = axios.create({
    baseURL: CONSTANTS.getAPI(),
    headers: {
        "Content-Type": "application/json",
    },
});

tanstackApi.interceptors.request.use((config) => {
    const token = localStorage.getItem("login_token") ?? localStorage.getItem("auth_signup_token");
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

export const tanstackApiFormdata = axios.create({
    baseURL: CONSTANTS.getAPI(),
    headers: {
        "Content-Type": "multipart/form-data",
    },
});

tanstackApiFormdata.interceptors.request.use((config) => {
    const token = localStorage.getItem("login_token");
    if (token) config.headers.Authorization = `Bearer ${token}`;
    return config;
});
