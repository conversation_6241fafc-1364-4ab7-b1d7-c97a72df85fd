import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const ITEMS_PER_PAGE = 8;

const schedule_range = ["today", "this_week", "this_month", "this_year", "custom"];

function isDatePassed(date) {
    const inputDate = new Date(date);
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    inputDate.setHours(0, 0, 0, 0);
    return inputDate < currentDate;
}

const LearnerZoomClasses = () => {
    const navigate = useNavigate();
    const [openAlert, setOpenAlert] = useState(false);
    const [editData, setEditData] = useState(null);
    const [filterState, setFilterState] = useState({
        type: "",
        start_date: "",
        end_date: "",
    });
    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };
    const [dataList, setDataList] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);

    const totalPages = Math.ceil(dataList.length / ITEMS_PER_PAGE);

    const getPaginatedData = () => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        return dataList.slice(startIndex, endIndex);
    };

    useEffect(() => {
        if (dataList?.length >= 0) {
            setTableData(getPaginatedData());
        }
    }, [currentPage, dataList]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        getSchedules({});
    }, []);

    const getSchedules = async (payload) => {
        await tanstackApi
            .get("chapter-class/list-learner", { params: payload })
            .then((res) => {
                setDataList(res?.data?.data || []);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const onSearch = () => {
        let payload = {
            type: filterState?.type,
            start_date: filterState?.type === "custom" ? filterState?.start_date : undefined,
            end_date: filterState?.type === "custom" ? filterState?.end_date : undefined,
        };
        setCurrentPage(1); // Reset to the first page on search
        getSchedules(payload);
    };

    const onClear = () => {
        setFilterState({
            type: "",
            start_date: "",
            end_date: "",
        });
        setCurrentPage(1); // Reset to the first page on clear
        getSchedules({});
    };

    const onJoinClass = async () => {
        await tanstackApi
            .get(`class-attendance/mark-my-attendance/${editData?.id}`)
            .then((res) => {
                if (res?.data?.status) {
                    punchTimelineLog({
                        user_id: localStorage.getItem("parent_user_id"),
                        event: "sessions",
                        log: `${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} attended ${editData?.topic} successfully.`,
                    });

                    window.open(editData?.location, "_blank");
                } else {
                    toast.warning(res?.data?.message);
                }
            })
            .catch((err) => {
                toast.warning(err?.response?.data?.error);
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you ready to join the class?</AlertDialogTitle>
                        <AlertDialogDescription>Click on join button to continue. Thank you!</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onJoinClass}>Yes Join!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Zoom Classes</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
            </div>
            <div className="page_filters tw-mt-4 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Schedules
                    </label>
                    <select onChange={onFilterChange} value={filterState?.type} name="type" className="tw-text-sm">
                        <option value="">- All -</option>
                        {schedule_range?.map((data, idx) => (
                            <option key={idx} value={data} className="tw-capitalize">
                                {data?.replaceAll("_", " ")}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        From
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState?.start_date}
                        name="start_date"
                        className="tw-text-sm"
                        type="date"
                        disabled={filterState?.type !== "custom"}
                        style={{ cursor: filterState?.type !== "custom" ? "not-allowed" : "auto" }}
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        To
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState?.end_date}
                        name="end_date"
                        className="tw-text-sm"
                        type="date"
                        disabled={filterState?.type !== "custom"}
                        style={{ cursor: filterState?.type !== "custom" ? "not-allowed" : "auto" }}
                    />
                </div>

                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table tw-mt-4">
                <table>
                    <thead>
                        <tr>
                            <th>Meet</th>
                            <th>Type</th>
                            <th>Topic</th>
                            <th>Chapter</th>
                            <th>Course</th>
                            <th>Scheduled On</th>
                            <th>Time</th>
                            <th>Duration</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData.length == 0 ? (
                            <tr>
                                <td colSpan={9}>
                                    <div className="tw-flex tw-h-40 tw-items-center tw-justify-center tw-rounded-xl tw-border">
                                        <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                            No Schedules Found
                                        </h2>
                                    </div>
                                </td>
                            </tr>
                        ) : (
                            tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            <img
                                                className="tw-h-8"
                                                src={
                                                    row?.meet_type === "zoom"
                                                        ? "/assets/zoom.png"
                                                        : row?.meet_type === "google-meet"
                                                          ? "/assets/google.png"
                                                          : ""
                                                }
                                                alt=""
                                            />
                                            <Label>
                                                {row?.meet_type === "zoom"
                                                    ? "Zoom"
                                                    : row?.meet_type === "google-meet"
                                                      ? "Google"
                                                      : ""}
                                            </Label>
                                        </div>
                                    </td>
                                    <td>
                                        {row?.type === "online" ? (
                                            <Badge className={"tw-font-lexend"}>Online</Badge>
                                        ) : (
                                            <Badge variant={"outline"} className={"tw-font-lexend"}>
                                                Offline
                                            </Badge>
                                        )}
                                    </td>
                                    <td>{row?.topic}</td>
                                    <td>{row?.chapter?.chapter_title}</td>
                                    <td>{row?.chapter?.lms_course?.course_title}</td>
                                    <td>{moment(row?.date).format("LL")}</td>
                                    <td>{`${row?.start_time} - ${row?.end_time}`}</td>
                                    <td>{`${row?.duration} mins`}</td>
                                    <td>
                                        {row?.type === "online" ? (
                                            <>
                                                {isDatePassed(row?.date) ? (
                                                    <Button variant="delete">
                                                        <i className="fa-solid fa-clock-rotate-left"></i> Expired
                                                    </Button>
                                                ) : (
                                                    <Button
                                                        variant="success"
                                                        onClick={() => {
                                                            setEditData(row);
                                                            setOpenAlert(true);
                                                        }}
                                                    >
                                                        <i className="fa-solid fa-video"></i> Join now
                                                    </Button>
                                                )}
                                            </>
                                        ) : (
                                            <>-</>
                                        )}
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">
                        {ITEMS_PER_PAGE}
                    </p>
                </div>
                <div>
                    <Pagination className="tw-mx-0 tw-w-[auto]">
                        <PaginationContent>
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {[...Array(totalPages)].map((_, index) => (
                                <PaginationItem key={index}>
                                    <PaginationLink
                                        href="#"
                                        isActive={index + 1 === currentPage}
                                        onClick={() => handlePageChange(index + 1)}
                                    >
                                        {index + 1}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {totalPages > 5 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </div>
    );
};

export default LearnerZoomClasses;
