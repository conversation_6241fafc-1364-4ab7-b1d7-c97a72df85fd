import DashboardLayout from "@/layouts/dashbaord";
import MainLayout from "@/layouts/main";
import PrivateLayout from "@/layouts/private";
import { themes } from "@/lib/theme";
import { store } from "@/redux/providers";
import "@/styles/certificate.sass";
import "@/styles/courseplayer.sass";
import "@/styles/index.css";
import "@/styles/laptop.sass";
import "@/styles/main.sass";
import "@/styles/mobile.sass";
import "@/styles/primary.sass";
import "@/styles/stylegate.css";
import "@/styles/stylegate.sass";
import "@/styles/tab.sass";
import "@/tailwind.css";
import { createTheme, ThemeProvider as MuiTheme } from "@mui/material";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { Loader2 } from "lucide-react";
import { lazy, Suspense } from "react";
import { Provider, useSelector } from "react-redux";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { ThemeProvider } from "styled-components";

const ChapterZoomClasses = lazy(() => import("@/pages/dashboard/createcourse/ChapterZoomClasses"));
const DomainReports = lazy(() => import("@/pages/dashboard/domain-master/DomainReports"));
const DomainOrders = lazy(() => import("@/pages/dashboard/domain-orders/DomainOrders"));
const LearnerZoomClasses = lazy(() => import("@/pages/dashboard/learner-zoom-classes"));
const BulkPricing = lazy(() => import("@/pages/dashboard/pricing/BulkPricing"));
const NormalPricing = lazy(() => import("@/pages/dashboard/pricing/NormalPricing"));
const AttendanceReport = lazy(() => import("@/pages/dashboard/reports/AttendanceReport"));
const ShopDomain = lazy(() => import("@/pages/dashboard/shop-domain"));
const CreateOrderDomain = lazy(() => import("@/pages/dashboard/shop-domain/CreateOrderDomain"));
const ShopLearner = lazy(() => import("@/pages/dashboard/shop-learner"));
const CreateOrderLearner = lazy(() => import("@/pages/dashboard/shop-learner/CreateOrderLearner"));
const TermsForUser = lazy(() => import("@/pages/dashboard/terms-conditions/TermsForUser"));
const TrainerZoomClasses = lazy(() => import("@/pages/dashboard/trainer-zoom-classes"));
const ClassLearners = lazy(() => import("@/pages/dashboard/trainer-zoom-classes/ClassLearners"));
const ZoomModule = lazy(() => import("@/pages/dashboard/zoom-module"));
const Orders = lazy(() => import("@/pages/dashboard/orders/Orders"));
const NormalPricingDomain = lazy(() => import("@/pages/dashboard/pricing/NormalPricingDomain"));
const ViewOrderDetails = lazy(() => import("@/pages/dashboard/shop-domain/ViewOrderDetails"));
const DomainOrderDetails = lazy(() => import("@/pages/dashboard/domain-orders/DomainOrderDetails"));
const OrderDetails = lazy(() => import("@/pages/dashboard/orders/OrderDetails"));
const MyProfile = lazy(() => import("@/pages/dashboard/profile"));
const InteractiveVideoPreview = lazy(() => import("@/pages/dashboard/interactive-video/preview"));
const InteractiveVideo = lazy(() => import("@/pages/dashboard/interactive-video"));
const QuestionPool = lazy(() => import("@/pages/dashboard/questions-pool"));
const ImportQuestions = lazy(() => import("@/pages/dashboard/questions-pool/ImportQuestions"));
const SubmissionsAdmin = lazy(() => import("@/pages/dashboard/submissions-admin"));
const ViewSubmittedAdmin = lazy(() => import("@/pages/dashboard/submissions-admin/ViewSubmittedAdmin"));
const ViewSubmittedQuizAdmin = lazy(() => import("@/pages/dashboard/submissions-admin/ViewSubmittedQuizAdmin"));
const SignupSteps = lazy(() => import("@/pages/auth/signin/SignupSteps"));
const BadgeMaster = lazy(() => import("@/pages/dashboard/badges"));
const BadgesPage = lazy(() => import("@/pages/dashboard/badges"));
const CatgoryPage = lazy(() => import("@/pages/dashboard/category"));
const CourseBundles = lazy(() => import("@/pages/dashboard/course-bundles"));
const CreateBundleForm = lazy(() => import("@/pages/dashboard/course-bundles/CreateBundleForm"));
const ViewCourseBundle = lazy(() => import("@/pages/dashboard/course-bundles/ViewCourseBundle"));
const DomainMaster = lazy(() => import("@/pages/dashboard/domain-master"));
const DomainForm = lazy(() => import("@/pages/dashboard/domain-master/DomainForm"));
const DomainTypeMaster = lazy(() => import("@/pages/dashboard/domain-types"));
const DomainTypesForm = lazy(() => import("@/pages/dashboard/domain-types/DomainTypesForm"));
const GenerateCertificate = lazy(() => import("@/pages/dashboard/generate-certificate"));
const HomeworkMaster = lazy(() => import("@/pages/dashboard/homework-master"));
const LearningPathsLearner = lazy(() => import("@/pages/dashboard/learning-path-learner"));
const LearnerViewLearningPath = lazy(() => import("@/pages/dashboard/learning-path-learner/LearnerViewLearningPath"));
const LearningPathCreate = lazy(() => import("@/pages/dashboard/learning-path/LearningPathCreate"));
const ViewLearningPath = lazy(() => import("@/pages/dashboard/learning-path/ViewLearningPath"));
const ModulePage = lazy(() => import("@/pages/dashboard/module"));
const ModuleMaster = lazy(() => import("@/pages/dashboard/modules"));
const AssignmentMaster = lazy(() => import("@/pages/dashboard/project-assignments"));
const CertificateReport = lazy(() => import("@/pages/dashboard/reports/CertificateReport"));
const CourseReport = lazy(() => import("@/pages/dashboard/reports/CourseReport"));
const LearnerReport = lazy(() => import("@/pages/dashboard/reports/LearnerReport"));
const TimelineLogs = lazy(() => import("@/pages/dashboard/reports/TimelineLogs"));
const CertificateMaster = lazy(() => import("@/pages/dashboard/settings/certificates"));
const CertificateForm = lazy(() => import("@/pages/dashboard/settings/certificates/CertificateForm"));
const SubCategory = lazy(() => import("@/pages/dashboard/subcategory"));
const SubmissionsLearner = lazy(() => import("@/pages/dashboard/submission-learner"));
const ViewSubmitted = lazy(() => import("@/pages/dashboard/submission-learner/ViewSubmitted"));
const ViewSubmittedQuiz = lazy(() => import("@/pages/dashboard/submission-learner/ViewSubmittedQuiz"));
const TagMaster = lazy(() => import("@/pages/dashboard/tags"));
const TermsConditions = lazy(() => import("@/pages/dashboard/terms-conditions"));
const UserGroups = lazy(() => import("@/pages/dashboard/urser-groups"));
const CreateGroupForm = lazy(() => import("@/pages/dashboard/urser-groups/CreateGroupForm"));
const GroupsTrainers = lazy(() => import("@/pages/dashboard/urser-groups/GroupsTrainers"));
const UserSubGroups = lazy(() => import("@/pages/dashboard/user-subgroups"));
const CreateSubGroupForm = lazy(() => import("@/pages/dashboard/user-subgroups/CreateSubGroupForm"));
const AboutPage = lazy(() => import("@/pages/about"));
const DashboardPage = lazy(() => import("@/pages/dashboard"));
const CoursePage = lazy(() => import("@/pages/dashboard/courses"));
const SignInPage = lazy(() => import("@/pages/auth/signin"));
const SignUpPage = lazy(() => import("@/pages/auth/signup"));
const MyAccountPage = lazy(() => import("@/pages/dashboard/my-account"));
const LearningPaths = lazy(() => import("@/pages/dashboard/learning-path"));
const CoursePlayerPage = lazy(() => import("@/pages/dashboard/course-player"));
const AssignedCourses = lazy(() => import("@/pages/dashboard/assignedcourses"));
const AssignedQuizzes = lazy(() => import("@/pages/dashboard/assignedquizzes"));
const AssignedWork = lazy(() => import("@/pages/dashboard/assignedwork"));
const WorkDetails = lazy(() => import("@/pages/dashboard/assignedwork/WorkDetails"));
const UserAssignments = lazy(() => import("@/pages/dashboard/assignments"));
const AttemptAssignment = lazy(() => import("@/pages/dashboard/assignments/AttemptAssignment"));
const CreateCourseWrapper = lazy(() => import("@/pages/dashboard/createcourse"));
const ViewCourse = lazy(() => import("@/pages/dashboard/createcourse/ViewCourse"));
const ChapterContent = lazy(() => import("@/pages/dashboard/createcourse/chaptercontent"));
const UserHomeworks = lazy(() => import("@/pages/dashboard/homeworks"));
const AttemptHomework = lazy(() => import("@/pages/dashboard/homeworks/AttemptHomework"));
const MyCourses = lazy(() => import("@/pages/dashboard/mycourses"));
const MyGroups = lazy(() => import("@/pages/dashboard/mygroups"));
const MySubGroups = lazy(() => import("@/pages/dashboard/mysubgroups"));
const Submissions = lazy(() => import("@/pages/dashboard/submissions"));
const ReviewCheck = lazy(() => import("@/pages/dashboard/submissions/ReviewCheck"));
const Interactions = lazy(() => import("@/pages/dashboard/interactions"));
const CreateInteractions = lazy(() => import("@/pages/dashboard/interactions/create"));
const UpdateInteractions = lazy(() => import("@/pages/dashboard/interactions/create"));
const Templates = lazy(() => import("@/pages/dashboard/template"));
const TemplatesForm = lazy(() => import("@/pages/dashboard/template/form"));
const QuizMaster = lazy(() => import("@/pages/dashboard/quizzes"));
const QuizConfiguration = lazy(() => import("@/pages/dashboard/quizzes/QuizConfiguration"));
const CreateQuizWrapper = lazy(() => import("@/pages/dashboard/quizzes/createquiz"));
const QuestionTypes = lazy(() => import("@/pages/dashboard/questions/types"));
const QuestionBank = lazy(() => import("@/pages/dashboard/questions/bank"));
const QuuestionCreate = lazy(() => import("@/pages/dashboard/questions/bank/QuuestionCreate"));
const QuizPreview = lazy(() => import("@/pages/dashboard/quiz-preview"));
const RolesPage = lazy(() => import("@/pages/dashboard/roles"));
const RolePermissionPage = lazy(() => import("@/pages/dashboard/roles/permission"));
const UsersPage = lazy(() => import("@/pages/dashboard/users"));
const UserControlPanelPage = lazy(() => import("@/pages/dashboard/users/control-panel"));
const UserEnrollPage = lazy(() => import("@/pages/dashboard/users/enroll"));
const LearnerProfile = lazy(() => import("@/pages/auth/learner-porfile"));
const MyCertificates = lazy(() => import("@/pages/dashboard/my-certificates"));
const QuizNewLayout = lazy(() => import("@/pages/quiz-new-layout"));

const muiTheme = createTheme({
    palette: {
        mode: "light",
    },
});

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            staleTime: 1000 * 5,
            retry: 0,
        },
    },
});

function App() {
    return (
        <>
            <QueryClientProvider client={queryClient}>
                <Provider store={store}>
                    <Content />
                </Provider>
                {import.meta.env.MODE == "development" && <ReactQueryDevtools initialIsOpen={false} />}
            </QueryClientProvider>
        </>
    );
}

function Content() {
    const { themeName } = useSelector((state) => state.ThemeSwitcher?.changeThemes);
    return (
        <ThemeProvider theme={themes[themeName]}>
            <MuiTheme theme={muiTheme}>
                <BrowserRouter>
                    <Suspense
                        fallback={
                            <div className="tw-flex tw-min-h-dvh tw-items-center tw-justify-center tw-bg-white">
                                <Loader2 className="tw-size-10 tw-animate-spin tw-text-blue-600" />
                            </div>
                        }
                    >
                        <Routes>
                            <Route element={<MainLayout />}>
                                <Route index element={<SignInPage />} />
                                <Route path="signup" element={<SignUpPage />} />
                                <Route path="signup-steps" element={<SignupSteps />} />
                                <Route path="learner-profile" element={<LearnerProfile />} />
                                <Route path="about" element={<AboutPage />} />
                                <Route path="terms-conditions-user" element={<TermsForUser />} />
                                <Route path="quiz-test" element={<QuizNewLayout />} />

                                <Route element={<PrivateLayout />}>
                                    <Route
                                        path="course-details/view/:course_id/:chapter_id/:content_id"
                                        element={<CoursePlayerPage />}
                                    />
                                    <Route path="quiz-preview/:quiz_id" element={<QuizPreview />} />
                                    <Route path="dashboard" element={<DashboardLayout />}>
                                        <Route index element={<DashboardPage />} />
                                        <Route path="roles">
                                            <Route index element={<RolesPage />} />
                                            <Route path="permission/:role_code" element={<RolePermissionPage />} />
                                        </Route>
                                        <Route path="category">
                                            <Route index element={<CatgoryPage />} />
                                        </Route>
                                        <Route path="badges">
                                            <Route index element={<BadgesPage />} />
                                        </Route>
                                        <Route path="modules">
                                            <Route index element={<ModulePage />} />
                                        </Route>
                                        <Route path="sub-category/:id" element={<SubCategory />} />
                                        <Route path="users">
                                            <Route index element={<UsersPage />} />
                                            <Route path="control-panel/:user_code" element={<UserControlPanelPage />} />
                                            <Route path="enroll/:user_code" element={<UserEnrollPage />} />
                                        </Route>
                                        <Route path="course" element={<CoursePage />} />
                                        <Route path="interactive-video" element={<InteractiveVideo />} />
                                        <Route path="interactive-video/:id" element={<InteractiveVideoPreview />} />
                                        <Route path="learning-path" element={<LearningPaths />} />
                                        <Route path="learning-path-create/:path_id?" element={<LearningPathCreate />} />
                                        <Route path="learning-path-view/:path_id?" element={<ViewLearningPath />} />

                                        <Route path="learning-path-learner" element={<LearningPathsLearner />} />
                                        <Route
                                            path="learning-path-learner-view/:path_id?"
                                            element={<LearnerViewLearningPath />}
                                        />

                                        <Route path="my-account" element={<MyAccountPage />} />
                                        <Route path="my-certificates" element={<MyCertificates />} />
                                        <Route path="certificate" element={<CertificateMaster />} />
                                        <Route
                                            path="certificate-create/:certificate_type/:certificate_id?"
                                            element={<CertificateForm />}
                                        />

                                        <Route path="user-assignment" element={<UserAssignments />} />
                                        <Route path="attempt-assignment/:id?" element={<AttemptAssignment />} />

                                        <Route path="user-homework" element={<UserHomeworks />} />
                                        <Route path="attempt-homework/:id?" element={<AttemptHomework />} />

                                        <Route path="interactions">
                                            <Route index element={<Interactions />} />
                                            <Route path="create" element={<CreateInteractions />} />
                                            <Route path=":id" element={<UpdateInteractions />} />
                                        </Route>

                                        <Route path="templates">
                                            <Route index element={<Templates />} />
                                            <Route path="create" element={<TemplatesForm />} />
                                            <Route path=":id" element={<TemplatesForm />} />
                                        </Route>

                                        <Route path="my-courses" element={<MyCourses />} />
                                        <Route path="edit-course/:course_id/:init?" element={<CreateCourseWrapper />} />
                                        <Route path="view-course/:course_id?" element={<ViewCourse />} />
                                        <Route
                                            path="zoom-classes/:course_id/:chapter_id/:chapter_title"
                                            element={<ChapterZoomClasses />}
                                        />
                                        <Route
                                            path="chapter-content/:course_id/:chapter_id?"
                                            element={<ChapterContent />}
                                        />

                                        <Route path="assigned-course" element={<AssignedCourses />} />
                                        <Route path="assigned-quizzes" element={<AssignedQuizzes />} />

                                        <Route path="quiz-tests" element={<QuizMaster />} />
                                        <Route path="quiz-create/:quiz_id?" element={<CreateQuizWrapper />} />
                                        <Route path="quiz-config/:quiz_id" element={<QuizConfiguration />} />

                                        <Route path="user-groups" element={<MyGroups />} />
                                        <Route path="user-teams/:group_id/:group_name" element={<MySubGroups />} />

                                        <Route path="submissions">
                                            <Route index element={<Submissions />} />
                                            <Route path=":type/:id?" element={<ReviewCheck />} />
                                        </Route>

                                        <Route path="submissions-admin" element={<SubmissionsAdmin />} />
                                        <Route
                                            path="submissions-admin-view/:type/:id?"
                                            element={<ViewSubmittedAdmin />}
                                        />
                                        <Route
                                            path="submissions-admin-quiz-view/:type/:id?"
                                            element={<ViewSubmittedQuizAdmin />}
                                        />

                                        <Route path="submissions-learner" element={<SubmissionsLearner />} />
                                        <Route path="submissions-learner-view/:type/:id?" element={<ViewSubmitted />} />
                                        <Route
                                            path="submissions-learner-quiz-view/:type/:id?"
                                            element={<ViewSubmittedQuiz />}
                                        />

                                        <Route path="assigned-data">
                                            <Route index element={<AssignedWork />} />
                                            <Route path=":type/:id?" element={<WorkDetails />} />
                                        </Route>

                                        <Route path="question-types" element={<QuestionTypes />} />
                                        <Route path="question-bank" element={<QuestionBank />} />
                                        <Route path="question-create/:question_id?" element={<QuuestionCreate />} />

                                        <Route path="tag-master" element={<TagMaster />} />

                                        <Route path="homework" element={<HomeworkMaster />} />
                                        <Route path="project-assignment" element={<AssignmentMaster />} />

                                        <Route path="course-bundle" element={<CourseBundles />} />
                                        <Route path="course-bundle-create/:bundle_id?" element={<CreateBundleForm />} />
                                        <Route path="course-bundle-view/:bundle_id?" element={<ViewCourseBundle />} />

                                        <Route path="user-group-master" element={<UserGroups />} />
                                        <Route path="user-group-create/:group_id?" element={<CreateGroupForm />} />
                                        <Route path="user-group-trainers" element={<GroupsTrainers />} />

                                        <Route
                                            path="user-subgroup-master/:group_id/:group_name"
                                            element={<UserSubGroups />}
                                        />
                                        <Route
                                            path="user-subgroup-create/:group_id/:group_name/:subgroup_id?"
                                            element={<CreateSubGroupForm />}
                                        />

                                        <Route path="domain-users" element={<DomainMaster />} />
                                        <Route path="domain-create/:domain_id?" element={<DomainForm />} />
                                        <Route path="domain-reports/:domain_id?" element={<DomainReports />} />

                                        <Route path="domain-types" element={<DomainTypeMaster />} />
                                        <Route
                                            path="domain-type-create/:domain_type_id?"
                                            element={<DomainTypesForm />}
                                        />

                                        <Route path="attendance-reports" element={<AttendanceReport />} />
                                        <Route path="timeline-logs" element={<TimelineLogs />} />
                                        <Route path="course-reports" element={<CourseReport />} />
                                        <Route path="learner-reports" element={<LearnerReport />} />
                                        <Route path="learner-profile" element={<LearnerReport />} />
                                        <Route path="certificate-reports" element={<CertificateReport />} />
                                        <Route path="category" element={<CatgoryPage />} />
                                        <Route path="modules" element={<ModuleMaster />} />
                                        <Route path="badges" element={<BadgeMaster />} />
                                        <Route path="terms-conditions" element={<TermsConditions />} />

                                        <Route path="my-profile" element={<MyProfile />} />
                                        <Route path="question-pool" element={<QuestionPool />} />
                                        <Route
                                            path="question-import-pool/:pool_id/:pool_name"
                                            element={<ImportQuestions />}
                                        />

                                        <Route path="zoom-schedules" element={<ZoomModule />} />
                                        <Route path="trainer-zoom-classes" element={<TrainerZoomClasses />} />
                                        <Route
                                            path="trainer-classes-learners/:class_id/:topic_name"
                                            element={<ClassLearners />}
                                        />
                                        <Route path="learner-zoom-classes" element={<LearnerZoomClasses />} />

                                        <Route path="normal-pricing" element={<NormalPricing />} />
                                        <Route path="normal-pricing-domain" element={<NormalPricingDomain />} />

                                        <Route path="bulk-pricing" element={<BulkPricing />} />

                                        <Route path="shop-learner" element={<ShopLearner />} />
                                        <Route path="create-order-learner" element={<CreateOrderLearner />} />

                                        <Route path="shop-domain" element={<ShopDomain />} />
                                        <Route path="view-order/:order_id" element={<ViewOrderDetails />} />
                                        <Route path="create-order-domain" element={<CreateOrderDomain />} />
                                        <Route path="domain-orders" element={<DomainOrders />} />
                                        <Route path="view-domain-order/:order_id" element={<DomainOrderDetails />} />

                                        <Route path="orders" element={<Orders />} />
                                        <Route path="view-learner-order/:order_id" element={<OrderDetails />} />
                                    </Route>
                                    <Route
                                        path="generate-certificate/:course_id/:bookmarking_id"
                                        element={<GenerateCertificate />}
                                    />
                                </Route>
                                {/* <Route element={<EditorLayout />}>
                                    <Route path="editor/lexical" element={<LexicalEditor />} />
                                    <Route path="editor/tip-tap" element={<TipTapEditor />} />
                                </Route> */}

                                {/* <Route path="player" element={<PlayerLayout />}>
                                    <Route index element={<PlayerPage />} />
                                </Route> */}
                            </Route>
                        </Routes>
                    </Suspense>
                </BrowserRouter>
            </MuiTheme>
        </ThemeProvider>
    );
}

export default App;
