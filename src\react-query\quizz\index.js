import { tanstack<PERSON>pi } from "@/react-query/api";
import { timelineLog, useAddTimelineLog } from "@/react-query/common/timeline";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetQuizDetails = (id) => {
    const userId = localStorage.getItem("userId");
    const level = localStorage.getItem("level");
    const is_public = level == "levelOne" ? true : false;
    return useQuery({
        queryKey: ["quiz", { id, userId }],
        queryFn: async () => (await tanstackApi.post(`/quiz/list`, { is_public, id })).data,
        enabled: !!id,
    });
};

export const useGetQuizList = () => {
    const userId = localStorage.getItem("userId");
    const level = localStorage.getItem("level");
    const is_public = level == "levelOne" ? true : false;
    return useQuery({
        queryKey: ["quiz", { userId }],
        queryFn: async () => (await tanstackApi.post(`/quiz/list`, { is_public })).data,
    });
};

export const useQuizCreate = () => {
    const queryClient = useQueryClient();
    const userId = localStorage.getItem("userId");
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.post(`/quiz/create`, data)).data;
        },
        onSuccess: (data) => {
            timelineLog({
                user_id: userId,
                event: "quiz",
                log: `${data.data.title} has been created successfully.`,
            });
            queryClient.invalidateQueries({ queryKey: ["quiz"] });
        },
    });
};

export const useQuizUpdate = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.put(`/quiz/update`, data)).data;
        },
        onSuccess: (_, data) => {
            timelineLog({
                user_id: localStorage.getItem("userId"),
                event: "quiz",
                log: `${data.title} has been updated successfully.`,
            });
            queryClient.invalidateQueries({ queryKey: ["quiz"] });
        },
    });
};

export const useQuizDelete = () => {
    const query = useQueryClient();
    return useMutation({
        mutationFn: async ({ id }) => (await tanstackApi.delete(`/quiz/delete/${id}`)).data,
        onSuccess: (_, data) => {
            timelineLog({
                user_id: localStorage.getItem("userId"),
                event: "quiz",
                log: `${data.title} has been deleted successfully.`,
            });
            query.invalidateQueries({ queryKey: ["quiz"] });
        },
    });
};
