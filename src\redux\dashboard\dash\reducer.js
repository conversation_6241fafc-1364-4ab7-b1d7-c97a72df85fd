import {
    DASHBOARD_STATS_DATA,
    FETCH_DASHBOARD_STATS_REQ,
    FETCH_SCHEDULED_CHAPTERS_REQ,
    FETCH_USER_POINTS_REQ,
    SCHEDULED_CHAPTERS_DATA,
    USER_POINTS_DATA,
} from "@/redux-types";

const initialState = {
    dashData: {},
    userPoints: {},
    scheduledChapters: [],
};

const DashReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_DASHBOARD_STATS_REQ:
            return {
                ...state,
            };
        case DASHBOARD_STATS_DATA:
            return {
                ...state,
                dashData: action.payload,
            };
        case FETCH_USER_POINTS_REQ:
            return {
                ...state,
            };
        case USER_POINTS_DATA:
            return {
                ...state,
                userPoints: action.payload,
            };
        case FETCH_SCHEDULED_CHAPTERS_REQ:
            return {
                ...state,
            };
        case SCHEDULED_CHAPTERS_DATA:
            return {
                ...state,
                scheduledChapters: action.payload,
            };
        default:
            return state;
    }
};

export default DashReducer;
