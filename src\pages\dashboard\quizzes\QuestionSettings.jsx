import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>ooter } from "@/components/ui/sheet";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import DragDropSettings from "./settings/DragDropSettings";
import FeedbackSettings from "./settings/FeedbackSettings";
import GeneralSettings from "./settings/GeneralSettings";
import MediaSettings from "./settings/MediaSettings";
import OptionsSettings from "./settings/OptionsSettings";

const options_mapping = {
    singlechoice: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Options", icon: "fa-solid fa-list-check" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    singlechoice_media: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Options", icon: "fa-solid fa-list-check" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    fillin_the_blank: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Options", icon: "fa-solid fa-list-check" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    dnd_image_box: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Drag & Drops", icon: "fa-solid fa-grip-vertical" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    sequence_arrange: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Options", icon: "fa-solid fa-list-check" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    string_dropdown: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Options", icon: "fa-solid fa-list-check" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    match_the_following: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Options", icon: "fa-solid fa-list-check" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    hotspot_dnd: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Drag & Drops", icon: "fa-solid fa-grip-vertical" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    hotspot_dnd_image: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Drag & Drops", icon: "fa-solid fa-grip-vertical" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
    long_answer: [
        { label: "General", icon: "fa-solid fa-circle-info" },
        { label: "Media", icon: "fa-solid fa-images" },
        { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
    ],
};

let content_mapping = {
    singlechoice: "Options",
    singlechoice_media: "Options",
    fillin_the_blank: "Options",
    dnd_image_box: "Drag & Drops",
    sequence_arrange: "Options",
    string_dropdown: "Options",
    match_the_following: "Options",
    hotspot_dnd: "Drag & Drops",
    hotspot_dnd_image: "Drag & Drops",
    long_answer: "General",
};

const label_mapping = {
    singlechoice: "Singlechoice",
    singlechoice_media: "Singlechoice media",
    fillin_the_blank: "Fill in the blanks",
    dnd_image_box: "Drag drop media",
    sequence_arrange: "Sequence arrange",
    string_dropdown: "String dropdown",
    match_the_following: "Match the following",
    hotspot_dnd: "Hotspot drag & drop",
    hotspot_dnd_image: "Hotspot drag & drop media",
    long_answer: "Long answer",
};

const QuestionSettings = ({
    open,
    setOpen,
    setComponentsArray,
    componentsArray,
    selectedContent,
    INDEX,
    setSelectedContent,
}) => {
    const [defaultSlide, setDefaultSlide] = useState(content_mapping[selectedContent?.componentTypeId]);

    useEffect(() => {
        setDefaultSlide(content_mapping[selectedContent?.componentTypeId]);
    }, [selectedContent?.componentTypeId]);

    const onSaveChanges = (e) => {
        e.preventDefault();
        let options = [...componentsArray];
        options[INDEX] = selectedContent;
        setComponentsArray(options);
        setOpen(false);
    };

    return (
        <div className="tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-grid-rows-[1fr_3rem] tw-gap-4">
            <Tabs
                defaultValue={"General"}
                className="custom_scrollbar tw-overflow-y-auto tw-pr-1"
                onValueChange={(e) => setDefaultSlide(e)}
            >
                <TabsList className="tw-h-fit tw-w-full tw-flex-wrap">
                    {(
                        options_mapping[selectedContent?.componentTypeId] || [
                            { label: "General", icon: "fa-solid fa-circle-info" },
                            { label: "Options", icon: "fa-solid fa-list-check" },
                            { label: "Media", icon: "fa-solid fa-images" },
                            { label: "Feedbacks", icon: "fa-regular fa-comment-dots" },
                        ]
                    )?.map((data, idx) => (
                        <TabsTrigger disabled={INDEX == null} className="tw-gap-2" value={data.label} key={idx}>
                            <i className={data.icon}></i> {data.label}
                        </TabsTrigger>
                    ))}
                </TabsList>
                <TabsContent value="General">
                    <GeneralSettings setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
                <TabsContent value="Options">
                    <OptionsSettings setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
                <TabsContent value="Drag & Drops">
                    <DragDropSettings setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
                <TabsContent value="Media">
                    <MediaSettings setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
                <TabsContent value="Feedbacks">
                    <FeedbackSettings setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
            </Tabs>
            <SheetFooter>
                <Button variant="outline">
                    <i className="fa-solid fa-xmark"></i> Cancel
                </Button>
                <Button type="submit" onClick={onSaveChanges}>
                    <i className="fa-solid fa-save"></i> Save changes
                </Button>
            </SheetFooter>
        </div>
    );
};

export default QuestionSettings;
