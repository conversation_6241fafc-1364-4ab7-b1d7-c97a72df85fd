import {
    ADD_NEW_BADGE,
    FETCH_BADGES_REQ,
    FETCH_HOMEWORK_REQ,
    <PERSON>ETCH_MY_GROUPS_REQ,
    FETCH_MY_SUB_GROUPS_REQ,
    FETCH_QUIZ_REQ,
    <PERSON>ETCH_USERS_HOMEWORK_REQ,
    GET_BADGES_LIST,
    GET_HOMEWORK_LIST,
    GET_MY_GROUPS_LIST,
    GET_MY_SUB_GROUPS_LIST,
    GET_QUIZ_LIST,
    GET_USERS_HOMEWORK_LIST,
    SUBMIT_HOMEWORK,
    SUBMIT_QUIZ,
    UPDATE_BADGE,
} from "@/redux-types";

export const FetchBadgesList = () => {
    return {
        type: FETCH_BADGES_REQ,
    };
};

export const GetBadgesData = (data) => {
    return {
        type: GET_BADGES_LIST,
        payload: data,
    };
};

export const AddNewBadge = (data) => {
    return {
        type: ADD_NEW_BADGE,
        payload: data,
    };
};

export const UpdateBadge = (data) => {
    return {
        type: UPDATE_BADGE,
        payload: data,
    };
};

export const FetchQuizList = (data) => {
    return {
        type: FETCH_QUIZ_REQ,
        payload: data,
    };
};

export const GetQuizData = (data) => {
    return {
        type: GET_QUIZ_LIST,
        payload: data,
    };
};

export const SubmitQuizData = (data) => {
    return {
        type: SUBMIT_QUIZ,
        payload: data,
    };
};

export const FetchHomeworkList = (data) => {
    return {
        type: FETCH_HOMEWORK_REQ,
        payload: data,
    };
};

export const GetHomeworkData = (data) => {
    return {
        type: GET_HOMEWORK_LIST,
        payload: data,
    };
};

export const SubmitHomeworkData = (data) => {
    return {
        type: SUBMIT_HOMEWORK,
        payload: data,
    };
};

export const FetchUserHomeworkList = (data) => {
    return {
        type: FETCH_USERS_HOMEWORK_REQ,
        payload: data,
    };
};

export const GetUserHomeworkData = (data) => {
    return {
        type: GET_USERS_HOMEWORK_LIST,
        payload: data,
    };
};

export const FetchMyGroups = (data) => {
    return {
        type: FETCH_MY_GROUPS_REQ,
        payload: data,
    };
};

export const GetMyGroups = (data) => {
    return {
        type: GET_MY_GROUPS_LIST,
        payload: data,
    };
};

export const FetchMySubGroups = (data) => {
    return {
        type: FETCH_MY_SUB_GROUPS_REQ,
        payload: data,
    };
};

export const GetMySubGroups = (data) => {
    return {
        type: GET_MY_SUB_GROUPS_LIST,
        payload: data,
    };
};
