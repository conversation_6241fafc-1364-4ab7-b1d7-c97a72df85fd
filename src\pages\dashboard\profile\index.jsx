import { Badge } from "@/components/ui/badge";
import {
    <PERSON><PERSON><PERSON><PERSON>b,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import ChangePassword from "./ChangePassword";

const MyProfile = () => {
    const [open, setOpen] = useState(false);
    const [userProfile, setUserProfile] = useState("");
    const [userEmail, setUserEmail] = useState(localStorage.getItem("user_email"));

    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "USER-PROFILE");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setUserProfile(res.data.fileUrl);
                } else {
                    setLoad(false);
                    setUserProfile("");
                }
            })
            .catch((err) => {
                setLoad(false);
                setUserProfile("");
            });
    };

    const onUpdateProfile = async () => {
        let payload = {
            id: parseInt(localStorage.getItem("userId")),
            picture_url: userProfile,
        };
        await tanstackApi
            .put("users/update-user", payload)
            .then((res) => {
                if (res.data.success) {
                    toast.success("Profile updated successfully");
                    localStorage.setItem("user_image", userProfile);
                    window.location.reload();
                } else {
                    toast.warning("Something went wrong");
                }
            })
            .catch((err) => {
                toast.error("Something went wrong");
            });
    };

    const RemoveImage = () => {
        setUserProfile("");
    };

    const onUpdateEmail = async () => {
        let payload = {
            email: userEmail,
        };
        await tanstackApi
            .put("users/update-email", payload)
            .then((res) => {
                if (res.data.success) {
                    toast.success("Email updated successfully");
                    localStorage.setItem("user_email", userEmail);
                    window.location.reload();
                } else {
                    toast.warning("Something went wrong");
                }
            })
            .catch((err) => {
                toast.error("Something went wrong");
            });
    };

    return (
        <div>
            <ChangePassword open={open} setOpen={setOpen} />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>My Profile</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
            </div>
            <br />
            <div className="tw-flex tw-items-end tw-justify-between tw-gap-5 tw-border-b-[1px] tw-p-4">
                <div className="tw-flex tw-items-center tw-gap-5">
                    <div className="tw-h-[120px] tw-w-[120px] tw-overflow-hidden tw-rounded-2xl">
                        <img
                            className="tw-h-full tw-w-full tw-object-cover"
                            src={localStorage.getItem("user_image")}
                            alt=""
                        />
                    </div>
                    <div className="tw-space-y-1">
                        <h1 className="tw-text-2xl tw-font-medium">
                            {localStorage.getItem("lms_fName")} {localStorage.getItem("lms_lName")}
                        </h1>
                        <div className="tw-space-y-1">
                            <p className="tw-text-sm">
                                Role :{" "}
                                <span className="tw-text-md tw-ml-2">
                                    {localStorage.getItem("is_trainer") == "true" ? "Trainer" : "Learner"}
                                </span>
                            </p>
                            <p className="tw-text-sm">
                                E-mail :{" "}
                                <span className="tw-text-md tw-ml-2">{localStorage.getItem("user_email")}</span>
                            </p>
                            <p className="tw-text-sm">
                                Status : <Badge className="tw-text-md tw-ml-2">Active</Badge>
                            </p>
                        </div>
                    </div>
                </div>
                <div>
                    <Button variant="outline" onClick={() => setOpen(true)}>
                        <i className="fa-solid fa-key"></i> Change Password
                    </Button>
                </div>
            </div>
            <br />
            <div className="tw-m-3 tw-flex tw-justify-between tw-gap-5">
                <div>
                    <h1 className="tw-text-lg tw-font-medium tw-text-gray-600">
                        <i className="fa-solid fa-camera"></i> Update profile picture
                    </h1>
                    <p className="tw-ml-3 tw-text-sm tw-text-slate-400">
                        (Upload your profile picture here & click on Save Profile button to apply changes)
                    </p>
                </div>
                <div>
                    <Button variant="outline" onClick={onUpdateProfile}>
                        <i className="fa-regular fa-circle-check"></i> Save Profile
                    </Button>
                </div>
            </div>
            <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                <div className="tw-h-[200px] tw-w-[250px] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                    <img
                        className="tw-h-full tw-w-full tw-rounded-md tw-object-cover"
                        src={userProfile || "/assets/thumbnail.png"}
                    />
                </div>
                <div className="tw-flex tw-gap-2">
                    {!userProfile && (
                        <Label
                            htmlFor="course_banner_url"
                            className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                        >
                            <Upload
                                className="opacity-60 sm:-ms-1 sm:me-2"
                                size={16}
                                strokeWidth={2}
                                aria-hidden="true"
                            />
                            <input
                                onChange={onImageChange}
                                type="file"
                                style={{ display: "none" }}
                                id="course_banner_url"
                                accept="image/*"
                            />
                            <div className="max-sm:sr-only">
                                {load ? "Uploading" : "Upload Image *"} {load ? `${uploaded}%` : null}
                            </div>
                        </Label>
                    )}
                    {userProfile && (
                        <Button variant="outline" className="aspect-square max-sm:p-0" onClick={RemoveImage}>
                            <X className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                            <Label className="max-sm:sr-only">Remove</Label>
                        </Button>
                    )}
                </div>
            </div>
            <br />
            <div className="tw-m-3 tw-flex tw-justify-between tw-gap-5">
                <div>
                    <h1 className="tw-text-lg tw-font-medium tw-text-gray-600">
                        <i className="fa-solid fa-envelope-circle-check"></i> Update email address
                    </h1>
                    <p className="tw-ml-3 tw-text-sm tw-text-slate-400">
                        (enter email address below input field & click on Update Email button to apply changes)
                    </p>
                </div>
                <div>
                    <Button variant="outline" onClick={onUpdateEmail}>
                        <i className="fa-regular fa-circle-check"></i> Update Email
                    </Button>
                </div>
            </div>
            <div className="tw-m-5">
                <Input
                    onChange={(e) => setUserEmail(e.target.value)}
                    value={userEmail}
                    placeholder="eg: <EMAIL>"
                />
            </div>
        </div>
    );
};

export default MyProfile;
