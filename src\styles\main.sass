@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap')
@import url('https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap')

.hide_spin
    &::-webkit-inner-spin-button
        display: none
.varificationInput
    display: flex
    width: 100%
    justify-content: space-between
    div
        &:first-child
            width: 80%
    div
        &:last-child
            width: 16%
            button
                border: 1px solid #363636
                border-radius: 4px
                cursor: pointer
                transition: 200ms all ease-in
                &:hover
                    border: 1px solid #5896FF
                    color: #5896FF
                    background: #fff
.PrimaryContainer
    height: 100vh
    padding-top: 50px
.TermsConditions
    padding: 20px
    .heading
        text-align: center
        margin-bottom: 50px
.SecondaryHeader
    box-shadow: 0px 3px 5px #3636363b
    // border-bottom: 1px solid #363636
    z-index: 10000
    background: #fff
    height: 50px
    padding: 0vw 1vw
    display: flex
    align-items: center
    justify-content: space-between
    position: fixed
    top: 0
    width: 100%
    .Logo
        display: flex
        align-items: center
        gap: 10px
        img
            height: 40px
            width: 40px
        h1
            font-family: 'Poppins'
            font-weight: 600
            font-size: 35px
            line-height: 40px
            color: #363636

.otpInputField
    // border: 1px solid red
    display: flex
    justify-content: center
    margin-bottom: 10px
.inputStyle
    width: 3rem !important
    height: 3rem
    margin: 0 0.5rem
    font-size: 1.5rem
    border-radius: 4px
    border: 1px solid rgba(0, 0, 0, 0.3)

.Registration_Container
    display: grid
    grid-template-columns: 1fr
    margin: 20px
    &>div
        // border: 1px solid #363636
.reg_section
    border: 1px solid rgba(160, 160, 160, 0.54)
    border-radius: 20px
    box-shadow: 0px 4px 10px rgba(109, 109, 109, 0.25)
    margin-top: 4rem
    margin-bottom: 1rem
    padding: 20px
    h1
        margin: 10px 0px
        font-family: 'Josefin sans'
        text-transform: uppercase
        font-size: 20px
        font-weight: 700
        letter-spacing: 2px
        border-bottom: 1px solid #D5D5D5
    h3
        margin: 0 10px
        font-size: 18px
.imageUploadSec
    display: flex
    flex-direction: column
    small
        font-size: 15px
        margin: 5px
    .ProfileContent
        display: flex
        flex-direction: column
        gap: 20px
        .imageDiv
            display: flex
            flex-direction: column
            justify-content: center
            align-items: center
            gap: 20px
            img
                height: 300px
                border: 1px solid #3636363d
                border-radius: 20px
            input
                display: none
            label
                margin: 10px 10px
                cursor: pointer
                background: #ececec
                color: #000
                font-weight: 500
                outline: none
                border: 1px solid transparent
                border-radius: 5px
                padding: 5px 20px
                font-size: 14px
                &:hover
                    background: #7d73e633
                    color: var(--color-primary)
.input_field
    display: flex
    flex-direction: column
    // height: 60px
    label
        display: flex
        align-items: center
        color: var(--color-dark-gray)
        // margin-bottom: 3px
    input[type="text"], input[type="password"]
        outline: none
        border: 1px solid var(--color-dark-gray)
        border-radius: 3px
        padding: 5px
        width: 45%
        &::placeholder
            color: var(--color-light-gray)
        &:focus
            border: 1px solid var(--color-primary-light)

    textarea
        width: 70%
        outline: none
        border: 1px solid var(--color-dark-gray)
        border-radius: 3px
        padding: 5px
        &::placeholder
            color: var(--color-light-gray)
        &:focus
            border: 1px solid var(--color-primary-light)

    select
        outline: none
        border: 1px solid var(--color-dark-gray)
        border-radius: 3px
        padding: 7px
        width: 45%
        &:focus
            border: 1px solid var(--color-primary-light)

.input_field_two
    display: flex
    align-items: center
    gap: 20px
    label
        cursor: pointer
    input
        cursor: pointer
        accent-color: var(--color-primary-light)

.define_section
    // padding: 20px
    .defineTABS
        margin: 5px 10px
        display: flex
        gap: 20px
        p
            background: var(--color-light-gray)
            border-radius: 5px
            color: var(--color-dark-gray)
            cursor: pointer
            font-family: 'Open sans'
            padding: 3px 12px
            &:hover
                color: #000
        .categoryActive
            background: var(--color-primary)
            color: #fff
            &:hover
                color: #fff
    .defineForm
        display: grid
        grid-template-columns: 1fr 1fr
        gap: 10px
        padding: 10px
        padding-bottom: 0px
        width: 40%

.company_reg_form
    margin-top: 30px
    .hyperlink
        display: flex
        justify-content: space-between
        h4
            color: var(--color-primary)
            cursor: pointer
            &:hover
                color: var(--color-primary-light)
    .ImageInput
        display: flex
        flex-direction: column
        gap: 15px
        input
            display: none
        label
            background: var(--color-light-gray)
            border: 1px solid var(--color-dark-gray)
            border-radius: 5px
            color: #000
            cursor: pointer
            font-family: 'Open sans'
            padding: 3px 12px
            width: 10%
            text-align: center
            &:hover
                color: var(--color-dark-gray)
                background: transparent
        img
            height: 300px
            width: 45%
            border-radius: 5px
            overflow: hidden
            border: 1px solid var(--color-dark-gray)
    .about_input
        margin: 10px 10px 30px 10px
        width: 100%
        display: flex
        flex-direction: column
        gap: 15px
    p
        color: var(--color-dark-gray)
        margin: 5px 10px
    .goal_input
        margin: 10px 10px 30px 10px
        width: 55%
        display: grid
        grid-template-columns: 1fr 1fr
        gap: 15px
    &>.input_field
        margin: 5px 10px
        width: 100%
    .radio_InputGroup
        &> label
            color: var(--color-dark-gray)
            margin-bottom: 3px
            display: flex
            align-items: center
        &> div
            display: flex
            gap: 80px
            margin: 5px 0

.continueButton
    width: 100%
    // border: 1px solid var(--color-dark-gray)
    display: flex
    align-items: center
    justify-content: right
    button
        margin: 10px 10px
        cursor: pointer
        background: var(--color-primary)
        color: #fff
        font-weight: 500
        outline: none
        border: 2px solid var(--color-primary)
        border-radius: 3px
        padding: 3px 20px
        font-size: 16px
        &:hover
            background: #7d73e633
            color: var(--color-primary)

.footer-main
    background-color: #5896FF
    display: flex
    flex-direction: column
    align-items: center
    justify-content: center
    padding: 2rem
    box-sizing: border-boox
    margin-top: 2rem
    border-radius: 0 5rem 0 0
    h3
        color: white
        letter-spacing: 1.4px
        font-weight: bold
        font-size: 1.3rem
        margin-bottom: 1rem
    p:hover
        cursor: pointer
    .top
        display: flex
        gap: 1rem
        color: white
    .container
        margin-top: 2rem
        display: flex
        flex-wrap: wrap
        width: 80%
        justify-content: space-between
        div
            min-width: 10rem
            display: flex
            flex-direction: column
            ul
                li
                    line-height: 2.7rem
                    font-size: 1.1rem
                    color: white
                    letter-spacing: 1.3px
                li:hover
                    cursor: pointer
.form-error
    color: #d32f2f !important
    font-family: Roboto , Helvetica , Arial ,sans-serif !important
    font-weight: 400 !important
    font-size: 0.75rem !important
    line-height: 1.66 !important
    letter-spacing: 0.03333em !important
    margin: 4px 14px 0 14px !important

.form-modal
    display: flex
    position: fixed
    z-index: 100
    background-color: rgba(240,240,240,0.4)
    align-items: center
    justify-content: center
    width: 100vw
    height: 100vh
    top: 0
    left: 0
    .container
        background-color: white
        width: 30%
        box-shadow: 0 0 15px 0px rgba(120,120,120,0.5)
        height: fit-content
        border-radius: 10px
        padding: 1rem
        box-sizing: border-box
        .top
            display: flex
            align-items: center
            justify-content: space-between
            margin-bottom: 1rem
        form
            background-color: red
            gap: 1rem
        .field
            margin: 1rem 0
.pageHeader
    display: flex
    justify-content: space-between
    align-items: center
    h4
        color: rgba(38, 50, 56, 0.9)
        margin: 0
        padding: 0
    .Buttons
        display: flex
        align-items: center
        gap: 10px
    .grupHead
        display: flex
        gap: 40px
        .inputSearch
            display: flex
            align-items: center
            gap: 5px
            background: #FFFFFF
            border: 1px solid #E2E2E2
            width: 500px
            padding: 3px 10px
            cursor: pointer
            border-radius: 4px
            .fa-magnifying-glass
                color: #6f6f6fbe
            input
                outline: none
                border: none
                width: 80%
                font-size: 14px
                &::placeholder
                    color: #6f6f6fbe
.Notification_popOver
    display: grid
    grid-template-columns: 65px auto
    gap: 10px
    padding: 10px 20px
    outline: none
    cursor: pointer
    border-bottom: 1px solid #3233320a
    transition: 100ms all ease
    &:hover
        background: #32333218
    .notificationImage
        width: 100%
        height: 45px
        border-radius: 3px
        overflow: hidden

        img
            height: 100%
            width: 100%
            object-fit: cover
    h5
        font-size: 13px
        font-weight: 500
        color: #323332
        margin-top: 0
    p
        font-size: 12px
        font-weight: 400
        color: #979797
        // white-space: nowrap
        text-overflow: ellipsis
        overflow: hidden
    span
        font-size: 12px
        font-weight: 400
        color: #323332

.formHead
    display: flex
    justify-content: space-between
    align-items: center
    margin-bottom: 5px
.mainTable
    height: 650px
    margin-top: 20px
    width: 100%

.PermissionPage
    margin: 20px 0
    border: 1px solid #3636367e
    border-radius: 4px
    .headerComp
        display: grid
        grid-template-columns: 2fr 5fr
        border-bottom: 1px solid #3636367e
        .title
            // border: 1px solid red
            padding: 20px
            border-right: 1px solid #3636367e
            h3
                font-size: 18px
                color: #363636be
                text-align: center
        .options
            display: flex
            // border: 1px solid red
            justify-content: space-between
            align-items: center
            padding: 20px 40px
            p
                color: #363636be
                font-weight: 700
                font-size: 15px

    .moduleComp
        display: grid
        grid-template-columns: 2fr 5fr
        border-bottom: 1px solid #36363625
        &:last-child
            border-bottom: none
        .title
            // border: 1px solid red
            border-right: 1px solid #36363625
            padding: 20px
            display: flex
            justify-content: space-between
            align-content: center
            h2
                font-size: 1.1em
                color: var(--color-primary)
        .options
            display: flex
            // border: 1px solid red
            justify-content: space-between
            align-items: center
            padding: 20px 40px
            input
                width: 20px
                height: 20px
                cursor: pointer
                accent-color: var(--color-primary)
        .subTitle
            // border: 1px solid red
            border-right: 1px solid #3636367e
            padding: 20px
            h2
                font-size: 15px
                color: #66bfbf
        .subOptions
            display: flex
            // border: 1px solid red
            justify-content: space-between
            align-items: center
            padding: 20px 40px
            input
                width: 18px
                height: 18px
                cursor: pointer
                accent-color: #66bfbf

//  PAGE STYLING => LEVEL 2
.image_upload
    width: 100%
    height: 100%
    border-radius: 3px
    display: flex
    flex-direction: column
    align-items: center
    justify-content: center
    padding: 1.3rem
    box-sizing: border-box
    border-style: dotted
    border-color: #0056D2
    color: #0056D2
    p
        text-align: center

.image_upload:hover
    cursor: pointer

.setting_container
    width: 100%
    display: flex
    padding: 1rem 0rem
    box-sizing: border-box
    .setting_sidebar
        width: 14%
        min-width: 11rem
        display: flex
        flex-direction: column
        ul
            li
                color: #838383
                text-transform: capitalize
                font-size: 1.1rem
                letter-spacing: 0.6px
                margin-bottom: 1rem
                display: flex
                align-items: center
                &:hover
                    cursor: pointer
                    color: rgb(50,50,50)
                    transition: 0.6s
                div
                    width: 5px
                    height: 17px
                    margin-right: 8px
                .active div
                    background-color: #0056D2
                    border-radius: 5px
        .active
            color: #0056D2
            font-weight: 500
            &:hover
                color: #0056D2 !important
    .setting_body
        width: 86%
        padding-left: 0.7rem
        box-sizing: border-box
        border-left: 1px solid gray

.general_settings
    padding: 2rem
    width: 100%
    @media only screen and (max-width: 580px)
        padding: 0

    .container
        padding: 1rem 0
    .container header
        padding-bottom: 0.6rem
        border-bottom: 1px solid gray
        box-sizing: border-box
        margin-bottom: 1rem
        h5
            color: rgba(100,100,100,0.8)
            letter-spacing: 1px
    .container .flex
        display: flex
    .container .flex > div
        width: 45%
        padding-right: 0.6rem
        box-sizing: border-box
        .upload_img
            width: 70%
        .logo-sec
            height: fit-content
            label
                font-size: 1rem
                color: var(--color-dark-gray)
                margin-bottom: 0.4rem
    .container .single div
        margin-bottom: 1.1rem
        display: flex
        align-items: center
        width: 100%
        label
            font-size: 1rem
            color: var(--color-dark-gray)
            margin-left: 0.7rem
        label:hover
            cursor: pointer
    footer
        display: flex
        justify-content: end

.settings_home_page
    width: 100%
    display: flex
    flex-direction: column
    header
        display: flex
        justify-content: end
        gap: 1rem
        align-items: center
        color: #0056D2
        font-weight: 500
        @media only screen and (max-width: 580px)
            margin-top: 1rem
            flex-direction: column
            align-items: start
        p:hover
            cursor: pointer
        .reset-btn
            display: flex
            align-items: center
            gap: 1rem

    nav
        background-color: #0056D2
        // display: flex
        // justify-content: space-between
        color: white
        padding: 0.7rem 0.7rem 0.7rem 0.3rem
        // margin: 0.7rem 0 0
        // border-radius: 3px
        box-sizing: border-box
        .home_page_container_header
            max-width: 1200px
            margin: 0 auto
            display: flex
            justify-content: space-between
            div
                display: flex
                align-items: center
                h5
                    color: white
                    cursor: pointer
                ul
                    gap: 3rem
                    font-size: 1rem
                    display: flex
                    li:hover
                        cursor: pointer
    .banner
        display: flex
        align-items: center
        justify-content: center
        height: 15rem
        border-radius: 3px
        background-color: rgba(225,225,225,0.6)

.home_page_container_banner
    max-width: 1260px
    margin: 0 auto
    width: 100%
    padding: 0 30px

.home_page_container
    max-width: 1260px
    margin: 0 auto
    width: 100%
    padding: 0 30px

#icon
    color: #0056D2
#icon:hover
    cursor: pointer

.dash-mn-lms
    min-height: 75vh

.welcome_screen
    widget: 80%
    display: flex
    flex-direction: column
    padding: 0.3rem 0.9rem
    box-sizing: border-box
    header
        display: flex
        flex-direction: column
        margin-bottom: 2rem
        h4
            color: #0056D2
        span
            color: #737373
            margin-top: 0.5rem
            letter-spacing: 0.8px
    section
        display: flex
        align-items: center
        gap: 1rem
        box-shadow: 0px 4px 4px rgba(97, 97, 97, 0.25)
        cursor: pointer
        padding: 1rem
        padding-right: 4rem
        box-sizing: border-box
        background: white
        border-radius: 6px
        margin: 0.5rem 0
        p
            color: #656565
            font-weight: 400
            font-size: 1rem
        img
            width: 4rem
            height: 4rem
            border-radius: 50%
    section:hover
        box-shadow: 0px 4px 4px rgba(97, 97, 97, 0.55)
        transition: 0.6s
    footer
        display: flex
        align-items: center
        justify-content: center
        width: 100%
        gap: 0.5rem
        margin-top: 1.4rem

.loading
    z-index: 1000
    display: flex
    align-items: center
    justify-content: center
    width: 100%
    height: fit-content

.dashboard_screen
    width: 100%
    height: 100%
    padding: 0.6rem
    box-sizing: border-box
    .sectionOne
        justify-content: space-between
        display: flex
        height: 37%
        align-items: flex-start
        div
            height: 100%
            max-height: 100%
            h6
                color: #7E7E7E
                margin-bottom: 0.7rem
        div:nth-child(1)
            width: 70%
            .container
                display: flex
                flex-wrap: wrap
                gap: 1rem
                width: 100%
                height: 100%
                max-height: 100%
                overflow: auto
                div
                    width: 30%
                    height: 9rem
                    background: white
                    box-shadow: 0px 4px 4px rgba(149, 149, 149, 0.25)
                    border-radius: 5px
                    transition: transform 0.5s
                    header
                        height: 70%
                        img
                            border-radius: 5px 5px 0 0
                            width: 100%
                            height: 100%
                    footer
                        height: 30%
                        display: flex
                        justify-content: center
                        align-items: center
                        font-size: 1rem
                        border-radius: 0 0 5px 5px
                div:hover
                    cursor: pointer
                    box-shadow: 0px 4px 4px rgba(149, 149, 149, 0.6)
                    transform: scale(1.08)

    .sectionTwo
        margin-top: 1.5rem
        margin-bottom: 2rem
        h6
            color: #7E7E7E
            margin-bottom: 0.7rem
        .container-sec-2
            .card
                background: white
                min-width: 18rem
                max-width: 18rem
                box-shadow: 0px 4px 4px rgba(149, 149, 149, 0.25)
                padding: 0.7rem
                box-sizing: border-box
                border-radius: 5px
                display: flex
                flex-direction: column
                .top
                    display: flex
                    justify-content: space-between
                    align-items: center
                    div:nth-child(1)
                        display: flex
                        gap: 0.4rem
                        div
                            display: flex
                            flex-direction: column
                            p
                                color: #0056D2
                                font-size: 1.1rem
                                font-weight: 500
                            span
                                color: #4A4A4A
                .mid1
                    display: flex
                    align-items: center
                    gap: 0.7rem
                    margin: 0.8rem 0
                    p
                        display: flex
                        align-items: center
                footer
                    display: flex
                    justify-content: center
                    p
                        color: #0056D2
                        font-weight: 500
            .card:hover
                cursor: pointer
                box-shadow: 0px 4px 4px rgba(149, 149, 149, 0.6)
                transition: 0.6s
    .sectionThree
        display: flex
        flex-direction: column
        width: 100%
        h6
            color: #7E7E7E
            margin-bottom: 0.7rem
        .container-sec-3
            width: 100% !important
            display: flex
            flex-wrap: wrap
            gap: 0.8rem
            .card
                background: white
                min-width: 22rem
                max-width: 22rem
                box-shadow: 0px 4px 4px rgba(149, 149, 149, 0.25)
                margin-bottom: 0.7rem
                padding: 0.7rem 1rem
                border-radius: 5px
                display: flex
                flex-direction: column
                .top
                    display: flex
                    justify-content: space-between
                    div
                        display: flex
                        align-items: center
                    p
                        color: #0056D2
                        font-weight: 500
                    div > div
                        border-radius: 5px
                        margin-right: 0.4rem
                        width: 2rem
                        height: 2rem
                        background: #0056D2
                .mid
                    display: flex
                    flex-wrap: wrap
                    margin: 1rem 0
                    div
                        width: 50%
                        margin-bottom: 0.6rem
                        color: #6C6C6C
                        display: flex
                        align-items: center
                        gap: 0.5rem
                        div
                            width: 1.5rem
                            height: 1.5rem
                            background: #C4C4C4
                            border-radius: 50%
                footer
                    width: 100%
                    display: flex
                    justify-content: center
            .card:hover
                cursor: pointer
                box-shadow: 0px 4px 4px rgba(168, 168, 168, 0.55)
                transition: 0.6s
#selected_course
    background: rgba(225, 255, 226, 1)
    border: 1px solid #00BF1F

.courses_list_card
    display: flex
    min-height: 9rem
    max-height: 9rem
    justify-content: space-between
    background: white
    padding: 0.5rem
    box-sizing: border-box
    border: 0.2px solid white
    margin-bottom: 1.4rem
    border-radius: 10px
    box-shadow: 4px 4px 8px rgba(143, 143, 143, 0.25)
    .left
        display: flex
        align-items: center
        min-width: 70%
        img
            width: 240px
            height: 100%
            resize: auto
            border-radius: 6px
        .container
            display: flex
            flex-direction: column
            justify-content: space-between
            height: 100%
            .top_div > div:nth-child(1)
                display: flex
                align-items: center
                gap: 2rem
            section
                display: flex
                flex-wrap: wrap
                align-items: flex-start
                justify-content: space-between
                div
                    width: fit-content
                    box-sizing: border-box
                    margin-bottom: 0.4rem
    .right-bundle
        display: flex
        align-items: center
    .right
        margin-left: 0.6rem
        display: flex
        flex-direction: column
        justify-content: space-between
        width: fit-content
        div
            display: flex
            align-items: center
            color: rgba(138, 138, 138, 1)
.courses_list_card:hover
    cursor: pointer
    border: 0.2px solid rgba(0, 86, 210, 0.3)
    box-shadow: 0px 0px 8px rgba(143, 143, 143, 0.55)
    transition: 0.6s

.print_button
    // display: flex
    // justify-content: center
    position: absolute
    top: 42%
    right: -220px

.grid_container
    display: flex
    flex-wrap: wrap
    gap: 1.1rem
    .course_list_grid
        display: flex
        width: 18rem
        height: fit-content
        border-radius: 10px
        border: 0.5px solid white
        flex-direction: column
        box-shadow: 4px 4px 10px rgba(150, 150, 150, 0.25)
        .top
            width: 100%
            height: 10rem
            position: relative
            border-radius: 10px 10px 0 0
            overflow: hidden
            background-position: center
            background-repeat: no-repeat
            background-size: cover
            #menu_icon
                color: white
                font-size: 1.3rem
                position: absolute
                top: 0
                right: 0
                cursor: pointer
            p
                position: absolute
                bottom: 10px
                right: 10px
                color: white
                background: #0056D2
                padding: 0.3rem 0.6rem
                border-radius: 10px
        section
            display: flex
            flex-direction: column
            padding: 0.6rem
            box-sizing: border-box
            gap: 0.6rem
            .top_s
                display: flex
                justify-content: space-between
                align-items: center
                h5
                    color: rgba(74, 74, 74, 0.9)
                    font-size: 1.2rem
            span
                color: #0056D2
                font-weight: 500
            .sec_a
                display: flex
                justify-content: space-between
                align-items: center
                p
                    display: flex
                    align-items: center
                    gap: 0.3rem
                    color: rgba(132, 132, 132, 1)
            footer
                display: flex
                align-items: center
                justify-content: space-between
    .course_list_grid:hover
        cursor: pointer
        border: 0.2px solid rgba(0, 86, 210, 0.3)
        box-shadow: 0px 0px 8px rgba(143, 143, 143, 0.55)
        transition: 0.6s

// COUSR BUNDLE UI
.course_bundle_welcome
    width: 100%
    height: 45vh
    display: flex
    flex-direction: column
    background: white
    border: 1px solid #BABABA
    border-radius: 10px
    padding: 2rem
    box-sizing: border-box
    .container
        width: 100%
        height: 80%
        margin: 3rem 0
        display: flex
        flex-direction: column
        align-items: center
        justify-content: space-between
        h4
            text-align: center
            width: 70%
            color: #A9A9A9
            letter-spacing: 1.1px
            line-height: 2rem

.course_bundle_create
    display: flex
    height: 100%
    margin: 1rem 1.2rem
    .sidebar
        width: 20%
        min-width: 15rem
        display: flex
        flex-direction: column
        h5
            color: #0056D2
            margin-bottom: 0.7rem
            font-size: 1.25rem
            letter-spacing: 0.8px
        div
            display: flex
            margin-bottom: 0.3rem
            align-items: center
            label
                font-size: 1.06rem
                letter-spacing: 0.7px
                cursor: pointer
    .container
        width: 100%
        .section
            background: white
            border-radius: 10px
            width: 100%
            height: 85%
            box-shadow: 4px 4px 20px rgba(186, 186, 186, 0.25)
            padding: 2rem
            box-sizing: border-box
            display: flex
            flex-wrap: wrap
            .left
                width: 60%
                .flex
                    display: flex
                    gap: 1.3rem
                    align-items: center
            .right_img-sec
                width: 40%
                display: flex
                flex-direction: column
                justify-content: space-between
                div:nth-child(1)
                    height: 14rem
                    padding: 0 3rem
                    box-sizing: border-box
                    display: flex
                    flex-direction: column
                    align-items: center
                    justify-content: center
                    p
                        color: #0056D2
                        margin: 0.6rem
                        font-size: 1rem
                        cursor: pointer
                    img
                        width: 100%
                        height: 100%
                        margin-top: 2.4rem
                        border-radius: 5px
                        cursor: pointer
                        box-shadow: 4px 4px 10px rgba(120, 120, 120, 0.65)
                div:nth-child(2)
                    display: flex
                    justify-content: right

.course_bundle_create_2
    display: flex
    flex-direction: column
    height: fit-content
    padding: 1rem
    box-sizing: border-box
    margin: 1rem 1.2rem
    border-radius: 10px
    background: white
    box-shadow: 4px 4px 20px rgba(186, 186, 186, 0.25)
    nav
        margin-bottom: 1.2rem
        display: flex
        justify-content: space-between
        align-items: center
        box-sizing: border-box
        div
            display: flex
            align-items: center
            gap: 1rem
        div:nth-child(1)
            width: 60%
        .flex_div
            display: flex
            align-items: center
            p
                color: rgba(0, 187, 7, 1)

    section
        display: flex
        .sidebar
            width: 17%
            min-width: 15rem
            display: flex
            padding-right: 1rem
            box-sizing: border-box
            flex-direction: column
            h5
                color: #0056D2
                margin-bottom: 0.7rem
                font-size: 1.25rem
                letter-spacing: 0.8px
            div
                display: flex
                flex-direction: column
                margin: 0.4rem 0
                label
                    font-size: 1.06rem
                    letter-spacing: 0.7px
                    color: #0056D2
                    margin-bottom: 0.3rem
                    cursor: pointer
                .active
                    color: #0056D2
                    font-weight: 500
                ul > li
                    margin-left: 1rem
                    user-select: none
                    text-transform: capitalize
                    color: rgba(120,120,120,0.8)
                    margin-bottom: 0.7rem
                ul > li:hover
                    cursor: pointer
                    color: #0056D2
                    transition: 0.6s
            div:hover
                cursor: pointer
        .container-2
            padding-left: 0.7rem
            box-sizing: border-box
            width: 82%
            border-left: 1px solid #CECECE

.course_bundle_list
    width: 100%
    display: flex
    flex-direction: column
    .container_main
        display: flex
        flex-wrap: wrap
        gap: 2rem
        padding: 0 1rem
        box-sizing: border-box
        height: fit-content
        .course_bundle_card
            background: white
            border-radius: 10px
            width: 23%
            display: flex
            flex-direction: column
            justify-content: space-between
            min-width: 17.5rem
            box-shadow: 4px 4px 10px rgba(122, 122, 122, 0.25)
            height: fit-content
            height: 28rem
            .image
                position: relative
                background-position: center
                background-repeat: no-repeat
                object-fit: cover
                width: 100%
                border-radius: 10px 10px 0 0
                overflow: hidden
                height: 55%
                img
                    width: 100%
                    height: 100%
                p
                    position: absolute
                    top: 10px
                    left: 10px
                    color: white
                    padding: 0.1rem 0.6rem
                    border: 1px solid #F0AD0F
                    box-sizing: border-box
                    background: #F0AD0F
                    font-size: 0.8rem
                    border-radius: 100px
                    user-select: none
                    cursor: pointer
            .container
                display: flex
                flex-direction: column
                justify-content: space-between
                height: 13rem !important
                padding: 0.8rem
                gap: 0.6rem
                box-sizing: border-box
                h5
                    color: rgba(74, 74, 74, 1)
                    cursor: default
                h6
                    color: rgba(109, 109, 109, 0.75)
                    cursor: default
                    span
                        color: rgba(0, 86, 210, 1)
                section
                    display: flex
                    gap: 0.2rem
                    justify-content: space-between
                    div
                        display: flex
                        flex-direction: column
                        img
                            width: 60px
                            height: 60px
                            border-radius: 5px
                            cursor: pointer
                        span
                            text-align: center
                            font-size: 0.7rem
                            color: rgba(168, 168, 168, 1)
                            margin-top: 0.1rem
                    p
                        display: flex
                        font-size: 0.7rem
                        color: rgba(113, 113, 113, 1)
                        align-items: center
                        padding-bottom: 0.7rem
                        box-sizing: border-box
                footer
                    margin: 0.6rem 0
                    display: flex
                    justify-content: center

        .course_bundle_card:hover
            box-shadow: 4px 4px 10px rgba(122, 122, 122, 0.55)
            transition: 0.6s
.chapterOrder
    position: absolute
    right: 40px
    top: 10px
    display: flex
    align-items: center
    gap: 5px
    label
        font-weight: 700
        color: #0056D2
        text-transform: uppercase
    input
        width: 100px
        outline: none
        border: 1px solid #36363669
        border-radius: 4px
        font-size: 12px
        padding: 2px 5px
        &::placeholder
            font-size: 11px
.course_bundle_dt
    display: flex
    flex-direction: column
    width: 100%
    header
        background: rgba(19, 24, 52, 1)
        display: flex
        align-items: center
        gap: 1rem
        padding: 0.4rem
        border-radius: 5px
        box-sizing: border-box
        img
            border-radius: 5px
            width: 250px
            height: 100%
        div
            padding: 0.4rem 0
            box-sizing: border-box
            display: flex
            flex-direction: column
            height: 100%
            justify-content: space-between
            color: white
            h4, h5
                display: flex
                align-items: center
                gap: 1rem
                color: white
            h4 span
                font-size: 1rem
                color: rgba(255, 187, 0, 1)
    .container
        display: flex
        flex-wrap: wrap
        gap: 2rem
        width: 100%
        padding: 1rem 0
        box-sizing: border-box
        .course_list_grid-1
            display: flex
            width: 17rem
            min-height: 20rem
            height: fit-content
            border-radius: 10px
            border: 0.5px solid white
            flex-direction: column
            justify-content: space-between
            background: white
            box-shadow: 4px 4px 10px rgba(150, 150, 150, 0.25)
            .top
                width: 100%
                height: 10rem
                position: relative
                border-radius: 10px 10px 0 0
                overflow: hidden
                background-position: center
                background-repeat: no-repeat
                background-size: cover
                #menu_icon
                    color: white
                    font-size: 1.3rem
                    position: absolute
                    top: 0
                    right: 0
                    cursor: pointer
                p
                    position: absolute
                    bottom: 10px
                    right: 10px
                    color: white
                    background: #0056D2
                    padding: 0.3rem 0.6rem
                    border-radius: 10px
            section
                display: flex
                flex-direction: column
                padding: 0.6rem
                box-sizing: border-box
                gap: 0.6rem
                div
                    display: flex
                    align-items: center
                div img
                    width: 40px
                    height: 40px
                    border-radius: 50%
                    margin-right: 0.6rem
                .top_s
                    display: flex
                    justify-content: space-between
                    align-items: center
                    h5
                        color: rgba(74, 74, 74, 0.9)
                        font-size: 1.2rem
                p
                    color: rgba(139, 139, 139, 1)
                    display: flex
                    align-items: center
                    #star_icon
                        color: rgba(255, 183, 31, 1)
                        font-size: 1rem
                        // margin-right: 0.1rem
                span
                    color: #0056D2
                    font-weight: 500
                    font-size: 1.1rem
                    span
                        font-size: 0.9rem
                        color: rgba(143, 143, 143, 0.7)
                .sec_a
                    display: flex
                    justify-content: space-between
                    align-items: center
                    p
                        display: flex
                        align-items: center
                        gap: 0.3rem
                        color: rgba(132, 132, 132, 1)
                footer
                    display: flex
                    align-items: center
                    justify-content: space-between
        .course_list_grid-1:hover
            cursor: pointer
            border: 0.2px solid rgba(0, 86, 210, 0.3)
            box-shadow: 0px 0px 8px rgba(143, 143, 143, 0.55)
            transition: 0.6s

.setting_certificate
    display: flex
    flex-direction: column
    .container
        display: flex
        align-items: center
        flex-wrap: wrap
        width: 100%
        gap: 1.45rem
        margin-top: 0.95rem
        padding: 0
        flex-direction: row
        //justify-content: space-evenly
        .image_demo_container
            width: 100%
            height: 10rem
            max-height: 10rem
            margin-bottom: 2.5rem
            h6
                text-transform: capitalize
            .slick-list
                height: 10rem
            .slick-slide
                margin: 0 5px
                width: 100%
                height: 10rem

            .slider_img
                height: 10rem !important
                max-height: 10rem !important
                display: none
                border-radius: 10px !important
                cursor: pointer
        header
            margin: 1rem 0
            gap: 1rem
            display: flex
            flex-direction: column
        section
            gap: 1rem
            // width: 100%
            // min-height: 5rem
            display: flex
            flex-direction: column
            // padding-bottom: 1rem
            // box-sizing: border-box
            .main_cont
                width: 100%
                display: flex
                gap: 1rem
                flex-wrap: wrap
            .upload
                width: 200px
                height: 140px
                margin-bottom: 0.4rem
            .slider
                width: 100%
                display: flex
            .img_container
                width: 32rem
                max-width: 32rem
                height: 21rem
                overflow: auto
                cursor: pointer
    footer
        padding-top: 1rem
        display: flex
        align-items: center
        justify-content: space-between
        section
            display: flex
            flex-direction: column
            gap: 0.5rem
            span
                width: 200px
                border: 1px solid rgba(100,100,100,0.2)
                border-radius: 10px
                height: 30px
                margin-bottom: 0.5rem
    #selected
        padding: 0.1rem
        box-sizing: border-box
        border: 3px solid rgba(0, 86, 210, 0.8)
        box-shadow: 0px 0px 8px rgba(100, 100, 100, 1)
    h6
        // margin: 1rem 0
        margin-top: 0.7rem
    .template
        margin-top: 0.6rem
        display: flex
        flex-wrap: wrap
        justify-content: space-between
        .left
            display: flex
            flex-direction: column
            text-align: left
            width: 20%
            div
                display: flex
                flex-direction: column
                text-align: left
                h6
                    margin-bottom: 0.4rem
            div > div
                    display: flex
                    flex-direction: row
.dash-mn-heading-sml
    font-size: 1.1rem
    letter-spacing: 0.5px
    color: rgba(50,50,50,0.9)

.stats-card-mn
    display: flex
    align-items: center
    gap: 1.3rem
    padding: 1rem 1.1rem
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px
    box-sizing: border-box
    border-radius: 7px
    // box-shadow: 0px 0px 10px 2px rgba(0,0,0,0.6)
    border: 1px solid #E1E1E1
    cursor: pointer
    div
        display: flex
        flex-direction: column
        text-align: left
        gap: 0.15rem
        span
            font-size: 0.8rem
            color: gray
            white-space: nowrap
            letter-spacing: 0.5px
        h5
            letter-spacing: 0.5px
    #stats-card-icon
        height: 45px
        width: 45px
        background: #E4F8EE
        padding: 0.6rem
        border-radius: 50%
        color: var(--dash-primary-color)
.stats-card-mn:hover
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px
    transition: 0.5s

.header-dashboard-mn
    width: 100%
    display: grid
    grid-template-columns: 1fr
    grid-gap: 1rem
    .stats-section-mn
        display: grid
        grid-template-columns: repeat(4, 1fr)
        grid-gap: 1rem
    .recent-activity
        padding: 0.80rem 1.1rem
        box-sizing: border-box
        border-radius: 7px
        border: 1px solid #E1E1E1
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px
        height: 100%
        max-height: 17rem
        header
            height: 10%
        .activity-container
            height: 90%
            max-height: 90%
            overflow: auto
        .activity-container::-webkit-scrollbar
            display: none
.activity-card-mn
    display: flex
    gap: 1rem
    padding: 1rem 0
    box-sizing: border-box
    #act-glb-icon
        font-size: 1.4rem
        height: 30px
        width: 30px
        background: #E4F8EE
        padding: 0.35rem
        border-radius: 50%
        color: var(--dash-primary-color)
        display: flex
        position: relative
        align-items: center
        justify-content: center
        text-transform: uppercase
    #act-glb-icon::after
        position: absolute
        bottom: -45px
        content: ''
        width: 2px
        left: 45%
        border-radius: 5px
        height: 40px
        z-index: 1500
        background: rgba(150,150,150,0.5)
    .section
        display: flex
        flex-direction: column
        text-align: left
        p
            font-size: 0.9rem
            cursor: default
            margin-bottom: 0.3rem
            color: rgba(40,40,40,0.9)
        span
            color: rgba(150,150,150,0.8)
            font-size: 0.8rem
            cursor: default
            display: flex
            align-items: center
            gap: 0.5rem

.course-dashboard-mn
    margin: 1rem 0
    padding: 1rem 0
    padding-bottom: 1.2rem
    border-radius: 7px
    header
        display: flex
        align-items: center
        gap: 0.5rem
        margin-bottom: 0.6rem
        h5
            width: fit-content !important
            margin: 0 !important
            padding: 0 !important
        a
            white-space: nowrap
            color: var(--dash-primary-color)
        a:hover
            cursor: pointer
            color: rgba(40,40,40,0.7)
            user-select: none
    .card-cntr-dsh-mn
        width: 100%
        display: flex
        gap: 1.2rem
        flex-wrap: wrap
    .dashboard-table
        width: 100%
        padding: 1rem 1.1rem
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px
        box-sizing: border-box
        border-radius: 7px
        // max-height: 20rem
        // overflow: auto
        table
            width: 100%
            thead
                tr
                    th
                        text-align: center
                        padding-bottom: 0.4rem
                        box-sizing: border-box
                        cursor: default
                        border-bottom: 1px solid rgba(80,80,80,0.5)
            tbody
                tr
                    td
                        text-align: center
                        padding: 0.6rem
                        text-decoration: none
                        &:nth-last-child(1)
                            color: blue
                            text-align: center
                            justify-content: center
                            display: flex
                            font-size: 1.1rem
                            align-items: center
                            gap: 0.3rem
                            cursor: pointer

.dash-course-crd-mn
    display: flex
    flex-direction: column
    width: 14rem
    height: 18.7rem
    border-radius: var(--card-ui-border-radius)
    z-index: 5000
    padding: 0.5rem
    box-sizing: border-box
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px
    border: 1px solid rgba(150,150,150,0.3)
    .img
        height: 45%
        position: relative
        img
            width: 100%
            height: 100%
            object-fit: cover
            repeat: no-repeat
            border-radius: var(--card-ui-border-radius)
        span
            position: absolute
            bottom: 9px
            left: 9px
            margin: 0
            padding: 0.1rem 0.8rem
            border-radius: var(--card-ui-border-radius)
            background: white
            font-size: 0.7rem
            color: rgba(50,50,50,0.7)
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px
            display: flex
            align-items: center
            gap: 0.4rem
            #signal-icon
                color: red
                font-size: 1.2rem
                margin: 0
                padding: 0
    .dt-cntr
        height: 55%
        display: flex
        flex-direction: column
        padding: 0.4rem
        box-sizing: border-box
        justify-content: space-between
        .center
            margin: 0.7rem 0
            display: flex
            flex-direction: column
            .top
                display: flex
                flex-direction: row
                align-items: center
                justify-content: space-between
                div
                    display: flex
                    flex-direction: row
                    align-items: center
                    gap: 0.4rem
                    #cntrl-icon
                        font-size: 1.1rem
                        color: rgba(150,150,150,0.9)
            .bottom
                .bar
                    width: 100%
                    height: 7px
                    border-radius: 10px
                    background: rgba(200,200,200,0.5)
                    position: relative
                    .course-tooltip
                        position: absolute
                        display: none
                        top: -35px
                        right: 45%
                        background: white
                        color: var(--dash-primary-color)
                        box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px
                        font-size: 0.8rem
                        padding: 0.1rem 0.4rem
                        border-radius: 10px
                    .course-tooltip::after
                        content: ''
                        position: absolute
                        background: white
                        width: 8px
                        height: 8px
                        bottom: -4px
                        left: 35%
                        transform: rotate(45deg)
                    .status
                        position: absolute
                        left: 0
                        height: 7px
                        width: 50%
                        border-radius: 10px
                        background: var(--dash-primary-color)
                .bar:hover
                    .course-tooltip
                        display: flex
        div
            display: flex
            flex-direction: column
            text-align: left
            gap: 0.1rem
            h5
                color: rgba(50,50,50,0.8)
                font-size: 1.1rem
                margin-bottom: 0.3rem
                padding: 0.2rem
                border-radius: 5px
                width: fit-content
        span
            font-size: 0.75rem
            color: rgba(70,70,70,0.7)
            width: fit-content
            padding: 0.19rem 0.2rem
            border-radius: 5px

        footer
            display: flex
            align-items: center
            justify-content: space-between

.dash-course-crd-mn:hover
    border: 1px solid var(--dash-primary-color)
    transition: 0.5s
    cursor: pointer
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px

.user-permission-header
    display: flex
    gap: 1rem
    .img
        width: 6rem
        height: 6rem
        .img-mn
            cursor: pointer
            width: 100%
            background: orange
            display: flex
            color: white
            font-size: 3rem
            align-items: center
            justify-content: center
            text-transform: uppercase
            height: 100%
            object-fit: cover
            border-radius: 50%
    .info
        display: flex
        gap: 0.3rem
        flex-direction: column
        h4
            color: blue
            span
                font-size: 0.8rem
                margin-left: 0.4rem
                color: rgba(20,20,20,0.7)
        span:nth-child(4)
            width: fit-content
            padding: 0.1rem 0.8rem
            border-radius: 10px
            letter-spacing: 0.8px
            background: green
            color: white
            font-size: 0.8rem
            cursor: pointer
            user-select: none
            text-transform: capitalize

.user-prmns-tble
    display: flex
    gap: 0.3rem
    height: 93vh
    flex-direction: column
    .tb-main-bar
        width: 100%
        margin: 0.5rem 0 0.9rem 0
        display: flex
        justify-content: space-between

.lecture_content
    // border: 1px solid #36363669
    padding: 20px
    margin: 20px 0
    border-radius: 5px
    display: flex
    flex-direction: column
    gap: 20px
    position: relative
    box-shadow: inset 0px 0px 5px #0058d23b
    & > div
        display: flex
        flex-direction: column
        gap: 7px
    & > p
        position: absolute
        right: 0
        top: 0
        svg
            color: #0056D2
    .contentBOX
        background: #fff
        border: 1px dashed #0058d262
        display: grid
        grid-template-columns: repeat(7, 1fr)
        padding: 20px
        gap: 0.5vw
        .contentTypes
            display: flex
            flex-direction: column
            align-items: center
            gap: 7px
            border: 1px solid transparent
            padding: 20px 0
            border-radius: 5px

            p
                color: #36363680
                text-align: center
                // font-size: 
            img
                height: 3vw
                transition: 300ms all ease
            &:hover
                cursor: pointer
                background: #36363610
                border: 1px solid #36363618
                p
                    color: #000
    .fileUploadBox
        background: #fff
        border: 1px dashed #0058d262
        display: flex
        flex-direction: column
        justify-content: center
        align-items: center
        min-height: 308px
        padding-bottom: 20px
        gap: 10px
        position: relative
        .content_header
            position: absolute
            width: 100%
            display: flex
            justify-content: space-between
            top: 0
            padding: 10px
            p
                font-size: 14px
                color: #363636cc
            button
                outline: none
                border-radius: 3px
                padding: 1px 10px
                cursor: pointer
                background: #1976D2
                color: #fff
                text-transform: uppercase
                font-size: 12px
                border: 1px solid #1976D2
                transition: 300ms all ease
                &:hover
                    background: #fff
                    color: #1976D2
        img
            height: 10vw
        input
            display: none
        svg
            font-size: 4vw
            color: #1976D2
        p
            font-size: 0.9vw
        label
            background: #1976D2
            color: #fff
            border-radius: 4px
            padding: 5px 20px
            font-size: 15px
            font-weight: 500
            cursor: pointer
        .progressBar
            width: 90%
            margin-top: 10px
            // border: 1px solid #1976D2

.certificate_edit_modal
    width: 100vw
    height: 100vh
    display: flex
    align-items: center
    justify-content: center
    background: rgba(70,70,70,0.4)
    top: 0
    left: 0
    position: fixed
    z-index: 1000
    .container
        width: 96vw
        min-width: 96vw
        height: 97%
        min-height: 97%
        overflow: auto
        background: white
        border-radius: 10px
        display: flex
        flex-wrap: wrap
        justify-content: space-between
        padding: 1rem
        box-sizing: border-box
        .certificate-ui
            flex: 2.7
            overflow: hidden
            display: flex
            align-items: center
            justify-content: center
        .certificate-cntrs
            border-left: 2px solid rgba(45,45,45,0.3)
            flex: 1
            display: flex
            padding: 0.6rem
            align-items: center
            box-sizing: border-box
            gap: 1rem
            flex-direction: column
            max-height: 100%
            overflow: auto
            overflow-x: hidden
            .row
                display: flex
                justify-content: center
                width: 100%
                input, select
                    width: 100%
                    border: 1px solid rgba(35,35,35,0.6)
                    border-radius: 5px
                    padding: 0.4rem
                    box-sizing: border-box

.SCORM_Module
    // overflow-y: scroll
    border: 1px solid #36363644
    border-radius: 4px
    background: #fff
    padding: 20px
    margin-bottom: 20px
    p
        margin-bottom: 10px
        font-weight: 700
        color: #1976D2
        font-size: 16px
    .inputArea
        .submitBtn
            text-align: right
            margin-top: 20px
            button
                outline: none
                cursor: pointer
                padding: 3px 10px
                border: 1px solid #363636
                border-radius: 3px
                transition: 300ms all ease
                &:hover
                    background: #1565C0
                    color: #fff
                    border: 1px solid #1565C0

    .ScormDispaly
        // height: 400px
        display: flex
        flex-direction: column
        gap: 20px
        align-items: flex-end
        width: 100%
        button
            outline: none
            cursor: pointer
            padding: 3px 10px
            border: 1px solid #363636
            border-radius: 3px
            transition: 300ms all ease
            &:hover
                background: #0056D2
                color: #fff
                border: 1px solid #0056D2
    .inputSideBySide
        // margin-top: 15px
        display: flex
        width: 280px
        gap: 15px
        div
            display: flex
            border-radius: 5px
            background: #363636ab
            p

                height: 100%
                color: #fff
                display: flex
                align-items: center
                font-size: 14px
                padding: 0 10px

.ViewCourse
    border: 1px solid #d9dad7
    border-radius: 3px
    padding: 10px

.CourseHeader
    display: flex
    align-items: center
    justify-content: space-between
    margin-bottom: 20px
    h5
        display: flex
        gap: 5px
    .Buttons
        display: flex
        align-items: center
        justify-content: space-between
        gap: 10px
.courseBanner
    display: flex
    align-items: center
    gap: 20px
    border-bottom: 1px solid #d9dad7
    // border-radius:3px
    padding-bottom: 10px
    margin-bottom: 5px
    .banner_img
        width: 20%
        overflow: hidden
        border-radius: 4px
        height: 10vw
        img
            height: 100%
    .banner_details
        width: 70%
        display: flex
        flex-direction: column
        gap: 10px
        h6
            font-size: 20px
            line-height: 28px
        p
            font-size: 16px
            color: #757a79

.courseContent
    display: flex
    align-items: center
    flex-direction: column
    gap: 20px
    // border: 1px solid #d9dad7
    border-radius: 3px
    padding: 10px
    h5
        font-size: 16px
        svg
            // margin-right:5px
    .preRequisiteDiv
        border-bottom: 1px solid #d9dad7
        padding: 10px 0
        margin: 5px 0px 20px 0px
        // border-radius: 5px
        h5
            margin-bottom: 10px
            // font-size: 14px
            svg
                // font-size: 17px
        a
            margin: 20px
            svg
                margin-right: 5px
        .noLink
            color: #000
            text-decoration: none
    .contentBOX
        .contentRow
            h6
                background: #ececec
                padding: 10px
                border-radius: 3px
            div
                display: flex
                align-items: center
                flex-direction: column
                gap: 10px
                padding: 10px 10px
                p
                    font-size: 14px
                    cursor: pointer
                    &:hover
                        text-decoration: underline
                    svg
                        // font-size:20px
            .scorm_div
                border: 1px solid #d9dad7
                padding: 10px
                margin: 20px 20px 10px 20px
                span
                    // text-align: right
                    font-size: 12px
                    svg
                        font-size: 17px
                p
                    margin-top: 10px
                    &:hover
                        text-decoration: none
                .scormPlayBtn
                    outline: none
                    background: #262626
                    border: 1px solid #262626
                    color: #fff
                    margin-left: 20px
                    cursor: pointer
                    letter-spacing: 1px
                    border-radius: 5px
                    text-transform: uppercase
                    font-size: 12px
                    font-weight: 600
                    transition: 300ms all ease
                    &:hover
                        background: #fff
                        color: #262626

                .ScormDispaly
                    display: flex
                    flex-direction: column
                    align-items: flex-end
                    button
                        width: 70px
.ScormContent
    height: 100%
    // border: 1px solid red
    // display: grid
    // grid-template-columns: 220px auto
    margin-top: 20px
    .scoNavigations
        box-shadow: inset 0px 0px 5px #363636
        border-radius: 7px
        padding: 10px
        p
            color: #000
            font-size: 13px
            font-weight: 500
        ul
            margin-bottom: 10px
            li
                margin-left: 5px
                cursor: pointer
                font-size: 14px
                &:hover
                    color: #000
                    font-weight: 500

.Prerequisite
    min-height: 100px

.reports
    margin-top: 20px
    display: flex
    justify-content: space-between
    // border: 1px solid #b93e3e
    background-color: #59b2ee3b
    border-radius: 20px
    // gap: 2px
    .headData1
        margin: 3px 3px
        padding: 25px
        width: 20vw
        display: flex
        flex-direction: column
        justify-content: center
        background-color: #eff9fd
        align-items: center
        border-radius: 20px 0px 0px 20px
    .headData
        margin: 3px 2px
        padding: 25px
        width: 20vw
        display: flex
        flex-direction: column
        justify-content: center
        // border: 1px solid #df1010
        background-color: #eff9fd
        align-items: center
        // border-radius: 15px 0px 0px 15px
    .headData2
        margin: 3px 3px
        padding: 25px
        width: 20vw
        display: flex
        flex-direction: column
        justify-content: center
        background-color: #eff9fd
        align-items: center
        border-radius: 0px 20px 20px 0px

.Quill
    width: 100%
    // box-shadow: 1px solid red
    .quill
        height: 40vh
.react-quill-section
    margin-bottom: 2rem

.ql-container
    min-height: 10rem
    height: 100%

.courseValidity
    display: flex
    flex-direction: column
    gap: 10px
    width: 100%
.dual_field
    display: flex
    justify-content: space-between
        // border: 1px solid red
    .customInput
        // border: 1px solid red
        gap: 5px
        display: flex
        flex-direction: column
        justify-content: flex-start
        &:first-child
            width: 40%
        &:last-child
            width: 55%
        label
            font-size: 16px
        input,select
            border: 1px solid #3636365d
            border-radius: 4px
            outline: none
            padding: 3px 10px
            height: 40px
            background: rgb(246, 250, 255)
.enrollInput
    display: flex
    gap: 50px
    div
        display: flex
        gap: 10px
        align-items: center
        input
            height: 20px
            width: 20px
        label
            font-size: 16px
.Filters
    display: flex
    width: 100%
    justify-content: space-between
    align-items: center
    padding: 10px 0px
    // border: 1px solid #222
    .div1
        width: 100%
        display: flex
        justify-content: space-between
        .first
            border: 1px solid #363636b9
            padding: 5px 5px
            outline: none
            border-radius: 4px
        .second
            border: 1px solid #363636b9
            border-radius: 4px
            padding: 5px
            margin-right: 50px
            outline: none
    .button
        color: #fff
        background: #000
        border: none
        border-radius: 4px
        font-size: 15px
        letter-spacing: 1px
        padding: 4px 10px
        transition: 300ms all ease
        &:hover
            background: rgba(0, 0, 0, 0.651)
.org_logo
    // border: 1px solid red
    height: 100%
    display: flex
    justify-content: center
    align-items: center
    gap: 10px
    padding: 10px 0
    .textLogo
        border: 1px solid #fff
        height: 100%
        width: 24%
        display: flex
        justify-content: center
        align-items: center
        font-size: 25px
        color: #fff
        overflow: hidden
        border-radius: 3px
    .img
        // border: 1px solid #fff
        height: 100%
        // width: 24%
        overflow: hidden
        border-radius: 3px
        img
            height: 100%
            object-fit: cover
            width: 100%
    p
        color: #fff
        text-align: left
        font-size: 16px
        line-height: 22px

.table_checkBOX
    // border: 1px solid red
    width: 100%
    display: flex
    justify-content: center
    align-items: center
    text-align: center
    input
        height: 20px
        cursor: pointer
        width: 20px
.tableImgBox
    // padding: 10px
    // border: 1px solid red
    height: 40px
    width: 70px
    border-radius: 10px
    img
        height: 100%
        width: 100%
        object-fit: cover

.path-detail-bt
    font-size: 0.9rem
    display: flex
    justify-content: space-between
    align-items: center
    padding: 0.1rem 0.4rem
    background: rgba(150,150,150,0.3)
    color: rgba(70,70,70,0.8)
    width: fit-content
    user-select: none
    gap: 0.7rem
    border-radius: 4px
    #cls
        font-size: 0.8rem
        cursor: pointer

.assign-nav-toogle
    background: white
    width: fit-content
    overflow: hidden
    margin: 0.9rem 0
    box-sizing: border-box
    display: flex
    align-items: center
    border: 1px solid rgba(170,170,170,0.3)
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px
    gap: 0.2rem
    border-radius: 10px
    justify-content: space-between
    p
        margin: 0
        padding: 0
        font-size: 1rem
        user-select: none
    span
        text-transform: capitalize
        font-size: 1rem
        cursor: pointer
        padding: 0.2rem 0.7rem
        user-select: none
        border-radius: 10px
        color: rgba(20,20,20,0.7)
    .active
        background: rgba(25,118,210,1)
        color: white
        transition: 0.6s

.active-status
    background: green
    margin-left: -5px !important
    font-size: 0.8rem !important
    color: white !important
    opacity: 0.9
    padding: 0.05rem 0.7rem
    border-radius: 10px

.inactive-status
    background: red
    opacity: 0.9
    margin-left: -5px !important
    font-size: 0.8rem !important
    color: white
    padding: 0.05rem 0.7rem
    border-radius: 10px

.view-path-header
    display: flex
    flex-direction: column
    gap: 0.5rem
    margin: 0.8rem 0
    span
        display: flex
        font-size: 1rem
        gap: 1rem
        align-items: center
        color: rgba(40,40,40,0.9)
        b
            color: rgba(50,50,50,0.6)

.learning-path-step-container
    display: flex
    align-items: center
    justify-content: center
    padding: 1rem 0.6rem
    box-sizing: border-box
    // background: blue
    section
        display: flex
        align-items: center
        flex-direction: column
        padding-left: 3rem
        box-sizing: border-box
        gap: 4rem
        // background: orange
        width: 100%

.path-step-ui
    background: white
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px
    padding: 1rem
    box-sizing: border-box
    width: 25%
    height: fit-content
    cursor: pointer
    border-radius: 25px
    display: flex
    position: relative
    position: relative
    .img
        width: 40%
        img
            width: 100%
            background: orange
            border-radius: 5px
            object-fit: cover
    .path-card-container
        width: 60%
        display: flex
        gap: 0.35rem
        padding: 0.4rem
        box-sizing: border-box
        flex-direction: column
        span
            text-transform: capitalize
            font-size: 1rem
        span:nth-child(1)
            width: fit-content
            background: rgba(220,220,220,0.7)
            color: rgba(40,40,40,0.7)
            padding: 0.1rem 0.4rem
            font-size: 0.9rem
            border-radius: 5px
    #down-arrow
        color: rgba(67,67,67,0.6)
        position: absolute
        bottom: -2.7rem
        font-size: 1.5rem
        left: 48%
.path-step-ui:hover
    transition: 0.6s
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px
.path-step-ui.completed
    box-shadow: inset 0px 0px 5px 2px rgba(0,247,0,1)

.learning-upload-img
    padding: 0.8rem
    box-sizing: border-box
    margin-top: 0.3rem
    div
        position: relative
        width: fit-content
        img
            width: 100%
            min-width: 20rem
            max-height: 20rem
            resize: none
            object-fit: cover
            border-radius: 10px
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px
            cursor: default
        #close
            position: absolute
            top: -10px
            right: -10px
            color: rgba(25,118,210,1)
            background: white
            padding: 0.2rem
            border-radius: 50%
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px
        #close:hover
            cursor: pointer
            background: rgba(25,118,210,1)
            color: white
            transition: 0.6s

.butt
    width: 5vw
    color: #fff
    background: #000
    border: none
    border-radius: 4px
    font-size: 15px
    letter-spacing: 1px
    padding: 4px 10px
    transition: 300ms all ease
    &:hover
        background: rgba(0, 0, 0, 0.651)
        color: #fff
.loading-ui
    min-width: 80vw
    min-height: 80vh
    display: flex
    align-items: center
    justify-content: center

.certificate_wrapper
    // border: 1px solid red
    text-align: center
    padding: 30px 0
    embed
        width: 8.27in
        height: 11.69in

////////////////////////////////////////////// SIGN IN PAGE ///////////////////////////////////////////////

.sign_in_alpha
    // background: #FCF7EF
    background-image: url("https://www.pepsico.com/images/default-source/homepage/for_use_pepsico_dotcom_only_20240212_0112_d.png?sfvrsn=7b07341f_0")
    background-repeat: no-repeat
    background-size: cover
    width: 100vw
    height: 100vh
    overflow: auto
    .alpha_header
        display: flex
        align-items: center
        justify-content: space-between
        padding: 20px 60px
        .left
            img
                height: 100px
        .right
            display: flex
            align-items: center
            gap: 20px
            svg
                cursor: pointer
                color: #fff
            .action_cta
                display: flex
                align-items: center
                gap: 5px
                button
                    border: none
                    border-radius: 8px
                    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                    font-size: 14px
                    font-weight: 500
                    padding: 8px 20px
                    background: transparent
                    color: #fff
                .cta_active
                    background: #FEC887

    .middle_section
        display: flex
        align-items: center
        justify-content: center
        padding-top: 5px
        img
            height: 60vh
        .login_form
            background: #fff
            border-radius: 30px
            padding: 45px
            width: 450px
            box-shadow: 0px 0px 50px #3636360c
            h2
                text-align: center
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                font-weight: 700
                font-size: 30px
                margin-bottom: 10px
            p
                text-align: center
                // font-weight: 500
                font-size: 16px
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
            .form_wrapper
                display: flex
                flex-direction: column
                gap: 10px
                margin: 30px 0 20px 0
            .input_group
                display: flex
                gap: 10px
                .input_text
                    border: 1px solid #ececec
                    border-radius: 10px
                    overflow: hidden
                    display: flex
                    justify-content: space-between
                    align-items: center
                    // padding: 5px 10px
                    position: relative
                    input
                        width: 100%
                        border: none
                        padding: 10px 15px
                        outline: none
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                    small,i
                        cursor: pointer
                        position: absolute
                        right: 10px
            &>small
                font-size: 13px
                font-weight: 500
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                display: flex
                align-items: center
                gap: 5px
                a
                    color: #000 !important
                    &:hover
                        text-decoration: underline
                input
                    width: 16px
                    height: 16px

            &>button
                border: none
                border-radius: 8px
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                font-size: 14px
                font-weight: bold
                padding: 10px 30px
                background: #FEC887
                width: 100%
                margin-top: 20px
                transition: 200ms all ease
                &:hover
                    background: #fdbe70
            .other_sign_in
                display: flex
                align-items: center
                flex-direction: column
                margin: 30px 0
                gap: 20px
                small
                    font-size: 13px
                    font-weight: 500
                div
                    display: flex
                    align-items: center
                    gap: 7px
                    button
                        display: flex
                        align-items: center
                        gap: 7px
                        border: 1px solid #36363623
                        border-radius: 7px
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                        padding: 9px 17px
                        background: #fff
                        i
                            font-size: 18px
                        b
                            font-size: 13px
            .sign_up_link
                text-align: center
                font-size: 13px
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                a
                    color: #000
                    font-weight: 500
                    cursor: pointer

    .middle_section_signUP
        display: flex
        align-items: center
        justify-content: center
        padding-top: 20px
        img
            height: 60vh
        .login_form
            background: #fff
            border-radius: 30px
            padding: 30px 45px
            width: 450px
            box-shadow: 0px 0px 50px #3636360c
            h2
                text-align: center
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                font-weight: 700
                font-size: 28px
                margin-bottom: 5px
            p
                text-align: center
                // font-weight: 500
                font-size: 14px
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
            .form_wrapper
                display: flex
                flex-direction: column
                gap: 15px
                margin: 15px 0 20px 0
            .input_group
                display: flex
                gap: 10px
                .input_text
                    border: 1px solid #ececec
                    border-radius: 10px
                    // overflow: hidden
                    display: flex
                    justify-content: space-between
                    align-items: center
                    // padding: 5px 10px
                    position: relative
                    input
                        width: 100%
                        border: none
                        padding: 10px 15px
                        outline: none
                        border-radius: 10px
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                    small,i
                        cursor: pointer
                        position: absolute
                        right: 10px
                    p
                        position: absolute
                        bottom: -15px
            &>small
                font-size: 13px
                font-weight: 500
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                display: flex
                align-items: center
                gap: 5px
                input
                    width: 16px
                    height: 16px

            &>button
                border: none
                border-radius: 8px
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                font-size: 14px
                font-weight: bold
                padding: 10px 30px
                background: #FEC887
                width: 100%
                margin-top: 0px
                transition: 200ms all ease
                &:hover
                    background: #fdbe70
            .sign_up_link
                text-align: center
                font-size: 13px
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                a
                    color: #000
                    font-weight: 500
                    cursor: pointer
    .alpha_footer
        text-align: center
        margin-top: 30px
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
        font-weight: 500
        color: #fff

.verify_btn
    background: #ececec
    border-radius: 5px
    padding: 3px 7px
    transition: 150ms all ease
    &:hover
        background: #FEC887
        color: #000

.MyAccountAlpha
    .main_page
        margin-top: 30px
        .section_details
            margin-bottom: 25px
            border: 1px solid #ececec
            box-shadow: 2px 2px 10px #ececec
            padding: 20px
            border-radius: 15px
            .section_heading
                display: flex
                align-items: center
                justify-content: space-between
                margin-bottom: 15px
                button
                    border: none
                    border-radius: 8px
                    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                    font-size: 14px
                    font-weight: bold
                    padding: 7px 20px
                    background: #ececec
                    margin-top: 0px
                    transition: 200ms all ease
                    &:hover
                        background: #1976D2
                        color: #fff
            .section_wrapper
                display: grid
                grid-template-columns: auto 500px
                gap: 30px
                .left
                    display: flex
                    flex-direction: column
                    gap: 20px
                .right
                    display: flex
                    flex-direction: column
                    align-items: center
                    gap: 15px
                    .org_logo
                        border: 1px dashed #3636368f
                        border-radius: 10px
                        width: 90%
                        height: 250px
                        img
                            height: 100%
                            width: 100%
                            object-fit: contain
                    input
                        display: none
                    label
                        width: 50%
                        text-align: center
                        cursor: pointer
                        border: none
                        border-radius: 8px
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                        font-size: 14px
                        font-weight: bold
                        padding: 10px 20px
                        background: #ececec
                        margin-top: 0px
                        transition: 200ms all ease
                        &:hover
                            background: #1976D2
                            color: #fff
            .input_group
                display: flex
                gap: 10px
                margin: 0 10px
                .input_button
                    display: flex
                    align-items: flex-end
                    button
                        border: none
                        border-radius: 5px
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                        font-size: 16px
                        font-weight: normal
                        padding: 10px 10px
                        width: 100%
                        text-align: center
                        background: #ececec
                        margin-top: 0px
                        transition: 200ms all ease
                        &:hover
                            background: #1976D2
                            color: #fff
                .input_text
                    flex-direction: column
                    display: flex
                    justify-content: space-between
                    position: relative
                    label
                        margin-left: 5px
                        font-weight: 500
                    input
                        border: 1px solid #ececec
                        border-radius: 10px
                        width: 100%
                        font-size: 16px
                        padding: 10px 15px
                        outline: none
                        border-radius: 10px
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                .input_select
                    flex-direction: column
                    display: flex
                    justify-content: space-between
                    position: relative
                    label
                        margin-left: 5px
                        font-weight: 500
                    select
                        border: 1px solid #ececec
                        border-radius: 10px
                        width: 100%
                        font-size: 16px
                        padding: 10px 15px
                        outline: none
                        border-radius: 10px
                        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                .input_multiselect
                    flex-direction: column
                    display: flex
                    justify-content: space-between
                    position: relative
                    label
                        margin-left: 5px
                        font-weight: 500
                    .options_wrapper
                        display: flex
                        flex-wrap: wrap
                        gap: 10px
                        border: 1px solid #ececec
                        padding: 10px
                        border-radius: 10px
                        p
                            padding: 7px 15px
                            background: #ececec
                            font-size: 14px
                            font-weight: bold
                            border-radius: 5px
                            cursor: pointer
                            color: #3636364f
                        .active
                            background: #000
                            color: #fff
.input_otp
    width: 100%
    display: flex
    align-items: center
    justify-content: center
.newdesign_otpmodule
    width: 3rem !important
    text-align: "center"
    height: 3rem
    margin: 0 0.5rem
    font-size: 1.5rem
    border-radius: 4px
    border: 1px solid rgba(0, 0, 0, 0.3)
    color: #000
    border-radius: 10px
    width: 100%
    font-size: 16px
    padding: 10px 15px
    outline: none
    border-radius: 10px
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif

.scorm_completion_box
    display: flex
    flex-direction: column
    align-items: center
    justify-content: center
    gap: 20px
    min-height: 450px
    h5
        font-weight: normal !important
        width: 500px
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
        text-align: center
        line-height: 30px
    a
        button
            border: none
            border-radius: 5px
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
            font-size: 16px
            font-weight: 500
            padding: 7px 20px
            text-align: center
            background: #42b8832d
            color: #42B883
            margin-top: 0px
            transition: 200ms all ease
            &:hover
                background: #42B883
                color: #fff

.prerequisites_wrapper
    padding: 20px
    margin-top: 20px

.input_custom, .textarea_custom, .select_custom, .Quill
    position: relative
    small
        position: absolute
        right: -5px
        top: -20px
        font-size: 20px !important
        font-weight: 700
        color: #3775F2

.certificate_previewer
    padding: 20px
    margin-top: 20px
    display: flex
    align-items: center
    justify-content: center
    .landscape_design
        // width: 1133.858px !important
        // height: 799.748px !important
        .template
            scale: 1 !important

// Quiz Migration

.template_page
    display: grid
    grid-template-columns: 1.3fr 4fr 2.2fr
    margin-top: 40px
    gap: 30px
    font-family: 'Public Sans', sans-serif

.component_list
    display: flex
    flex-direction: column
    border: 1px solid #ececec
    border-radius: 5px
    li
        list-style: none
        display: flex
        align-items: center
        justify-content: space-between
        border-bottom: 1px solid #ececec
        padding: 10px 10px
        transition: 200ms all ease
        &:hover
            background: #EBF8F4
            cursor: pointer
        i
            flex: 1
            font-size: 20px
            color: #9ba6a5
        p
            flex: 6
            // color: #00A76F
            font-size: 16px
        svg
            flex: 1

.form_container
    margin-right: 40px
.form_screen
    display: flex
    flex-direction: column
    gap: 20px

.general_setting
    display: flex
    flex-direction: column
    margin-top: 20px
    gap: 20px
.comp_option
    display: flex
    flex-direction: column
    margin-top: 20px
    gap: 20px
.comp_conditions
    display: flex
    flex-direction: column
    margin-top: 20px
    gap: 20px
.input_group_cntrl
    display: flex
    align-items: center
    justify-content: space-between
    gap: 15px
    .text_input_v1
        display: flex
        flex-direction: column
        gap: 5px
        label
            font-size: 14px
            font-weight: 500
            color: #a2a8d3
            margin-left: 3px
        input
            padding: 7px 7px
            border-radius: 5px
            font-family: "Public Sans", sans-serif
            border: 1px solid #a2a8d37e
            outline-color: #a2a8d3
            color: #212B36
            font-size: 15px
            // margin-left: 10px
            &::placeholder
                color: #919EAB
                font-size: 14px
    .textarea_input_v1
        display: flex
        flex-direction: column
        gap: 5px
        label
            font-size: 14px
            font-weight: 500
            color: #a2a8d3
            margin-left: 3px
        textarea
            padding: 7px 7px
            border-radius: 5px
            font-family: "Public Sans", sans-serif
            border: 1px solid #a2a8d37e
            outline-color: #a2a8d3
            color: #212B36
            font-size: 15px
            // margin-left: 10px
            &::placeholder
                color: #919EAB
                font-size: 14px
    .number_input_v1
        display: flex
        flex-direction: column
        gap: 5px
        label
            font-size: 14px
            font-weight: 500
            color: #a2a8d3
            margin-left: 3px
        input
            padding: 7px 7px
            border-radius: 5px
            font-family: "Public Sans", sans-serif
            border: 1px solid #a2a8d37e
            outline-color: #a2a8d3
            color: #212B36
            font-size: 14px
            // margin-left: 10px
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button
                -webkit-appearance: none
            &::placeholder
                color: #919EAB
                font-size: 14px
    .select_input_v1
        display: flex
        flex-direction: column
        gap: 5px
        label
            font-size: 14px
            font-weight: 500
            color: #a2a8d3
            margin-left: 3px
        select
            padding: 7px 7px
            border-radius: 5px
            font-family: "Public Sans", sans-serif
            border: 1px solid #a2a8d37e
            outline-color: #a2a8d3
            color: #212B36
            cursor: pointer
            font-size: 14px

.comp_options_box
    td
        border: none
    .img_lbl
        height: 40px
        width: 60px
        cursor: pointer
        display: flex
        gap: 3px
        flex-direction: column
        align-items: center
        border: 1px dashed #ececec
        border-radius: 5px
        padding: 5px
        color: #9ba6a5
        i
            font-size: 15px
        p
            font-size: 12px
        input
            display: none
    .tab_img
        position: relative
        height: 50px
        width: 70px
        border: 1px dashed #ececec
        border-radius: 5px
        padding: 2px
        img
            height: 100%
            width: 100%
            object-fit: cover
        i
            position: absolute
            right: -7px
            top: -7px
            font-size: 20px
            color: #9ba6a5
            background: #fff
            border-radius: 50%
            cursor: pointer
            &:hover
                color: #5c5f5e
    textarea
        padding: 7px 7px
        border-radius: 5px
        font-family: "Public Sans", sans-serif
        border: 1px solid #a2a8d37e
        outline-color: #a2a8d3
        // color: #919EAB
        font-size: 14px

.comp_conditions_box
    display: flex
    flex-direction: column
    gap: 20px
    // height: 60vh
    // overflow-y: scroll
    .condition
        display: flex
        flex-direction: column
        gap: 10px
        border: 1px dashed #ececec
        padding: 10px
        border-radius: 7px
        .upper
            display: flex
            align-items: center
            justify-content: space-between
            gap: 10px
            label
                font-size: 14px
                font-weight: 600
            i
                font-size: 15px
                margin-right: 5px
        .lower
            display: flex
            flex-direction: column
            gap: 10px
            input
                padding: 7px 7px
                border-radius: 5px
                font-family: "Public Sans", sans-serif
                border: 1px solid #ececec
                // outline-color: #a2a8d3
                color: #000
                font-size: 14px
            textarea
                padding: 7px 7px
                border-radius: 5px
                font-family: "Public Sans", sans-serif
                border: 1px solid #ececec
                // outline-color: #a2a8d3
                color: #000
                font-size: 14px

.MainCard
    background-color: rgb(255, 255, 255)
    color: rgb(33, 43, 54)
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms
    background-image: none
    overflow: hidden
    position: relative
    box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px
    border-radius: 16px
    z-index: 0
    padding: 24px

.MainCard_secondary
    background-color: rgb(255, 255, 255)
    color: rgb(33, 43, 54)
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms
    background-image: none
    overflow: hidden
    position: relative
    box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px
    border-radius: 16px
    z-index: 0
    padding: 15px

.tabs
    font-family: 'Public Sans', sans-serif
    display: flex
    gap: 30px
    padding: 0px 20px
    border-bottom: 1px solid #ececec
    p
        display: flex
        flex-direction: row
        align-items: center
        gap: 3px
        padding: 10px 0
        line-height: 1.25
        font-size: 0.875rem
        font-weight: 600
        color: rgb(99, 115, 129)
        cursor: pointer
        border-bottom: 2px solid transparent
        b

            height: 24px
            min-width: 24px
            line-height: 0
            border-radius: 6px
            -webkit-box-align: center
            align-items: center
            white-space: nowrap
            display: inline-flex
            -webkit-box-pack: center
            justify-content: center
            text-transform: capitalize
            padding: 0px 2px
            font-size: 0.75rem
            font-weight: 700
            transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms
            color: rgb(33, 43, 54)
            background-color: rgba(33, 43, 54, 0.123)
        .type_1
            color: #00A76F
            background-color: #DBF6E5
        .type_2
            color: #C88D32
            background-color: #FFF1D6
        .type_3
            color: #C13833
            background-color: #FFE4DE
    .active
        border-bottom: 2px solid rgb(33, 43, 54)
        color: rgb(33, 43, 54)
        b
            color: #fff
            background: rgb(33, 43, 54)
.tab_second
    padding: 0px 0px
    gap: 17px
    p
        flex-direction: row-reverse

.button_group_two
    display: flex
    align-items: center
    justify-content: right
    gap: 10px
    // border-bottom: 1px  solid #ececec
    padding-bottom: 15px

.btn_one
    background-color: #EBF8F4
    border: 1px solid #44A76F
    border-radius: 8px
    // box-shadow: rgba(213, 217, 217, .5) 0 2px 5px 0
    font-family: "Public Sans", sans-serif
    box-sizing: border-box
    color: #44A76F
    cursor: pointer
    display: inline-block
    font-size: 13px
    font-weight: 600
    line-height: 29px
    padding: 0 10px 0 11px
    position: relative
    text-align: center
    text-decoration: none
    user-select: none
    -webkit-user-select: none
    touch-action: manipulation
    vertical-align: middle
    width: 100px

    &:hover
        background-color: #f7fafa

    &:focus
        border-color: #008296
        box-shadow: rgba(213, 217, 217, .5) 0 2px 5px 0
        outline: 0
.btn_two
    background-color: #fff1d6c2
    border: 1px solid #C88D32
    border-radius: 8px
    // box-shadow: rgba(213, 217, 217, .5) 0 2px 5px 0
    box-sizing: border-box
    font-family: "Public Sans", sans-serif
    color: #C88D32
    cursor: pointer
    display: inline-block
    font-size: 13px
    font-weight: 600
    line-height: 29px
    padding: 0 10px 0 11px
    position: relative
    text-align: center
    text-decoration: none
    user-select: none
    -webkit-user-select: none
    touch-action: manipulation
    vertical-align: middle
    width: 100px

    &:hover
        background-color: #FFF1D6

    &:focus
        border-color: #008296
        box-shadow: rgba(213, 217, 217, .5) 0 2px 5px 0
        outline: 0

.comp_control
    // background-color: #F5F5F7
    color: rgb(33, 43, 54)
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms
    background-image: none
    // overflow: hidden
    position: relative
    // box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px
    border-radius: 10px
    z-index: 0
    padding: 15px
    border: 2px solid transparent
    .controls
        display: none

.comp_active
    border: 1px dashed #1D1D1F
    .controls
        position: absolute
        right: -40px
        top: 50%
        transform: translateY(-50%)
        display: flex
        flex-direction: column
.comp_mounted
    border: 2px solid #29C383

.correctAnswer
    margin-top: 10px
    color: #919EAB
    display: flex
    align-items: center
    flex-wrap: wrap
    gap: 5px
    font-size: 14px
    background: #EBF8F4
    color: #15B579
    border-radius: 7px
    padding: 10px
.special_instruction
    margin-top: 1vw
    color: #919EAB
    display: flex
    align-items: baseline
    // flex-wrap: wrap
    gap: .7vw
    small
        font-size: .8vw
    i
        font-size: 1vw

.simple_text_input
    display: flex
    flex-direction: column
    gap: 10px
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 20px
        // line-height: 30px
        font-weight: 600
        letter-spacing: -0.005em
        margin-bottom: 10px
    input
        padding: 15px 12px
        border-radius: 7px
        font-family: "Lexend", sans-serif
        border: 1px solid rgba(145, 158, 171, 0.2)
        // color: #919EAB
        font-size: 20px
        outline: none
        // margin-left: 10px
        &::placeholder
            color: #919EAB
            font-size: 18px

.textarea_input
    display: flex
    flex-direction: column
    gap: 10px
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 20px
        // line-height: 30px
        font-weight: 600
        letter-spacing: -0.005em
        margin-bottom: 10px
    textarea
        padding: 15px 12px
        outline: none
        border-radius: 7px
        font-family: "Lexend", sans-serif
        border: 1px solid rgba(145, 158, 171, 0.2)
        // color: #919EAB
        font-size: 20px
        // margin-left: 10px
        &::placeholder
            color: #919EAB
            font-size: 18px
.date_picker_input
    display: flex
    flex-direction: column
    gap: 10px
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 20px
        // line-height: 30px
        font-weight: 600
        letter-spacing: -0.005em
        margin-bottom: 10px
    input
        padding: 15px 12px
        outline: none
        border-radius: 7px
        font-family: "Lexend", sans-serif
        border: 1px solid rgba(145, 158, 171, 0.2)
        // color: #919EAB
        font-size: 20px
        // margin-left: 10px
        &::placeholder
            color: #919EAB
            font-size: 18px
.select_dropdown_input
    display: flex
    flex-direction: column
    gap: 10px
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 20px
        // line-height: 30px
        font-weight: 600
        letter-spacing: -0.005em
        margin-bottom: 10px
    select
        padding: 15px 12px
        outline: none
        border-radius: 7px
        font-family: "Lexend", sans-serif
        border: 1px solid rgba(145, 158, 171, 0.2)
        // color: #919EAB
        font-size: 20px
        // margin-left: 10px
        &::placeholder
            color: #919EAB
            font-size: 18px
.single_choice_input
    display: flex
    flex-direction: column
    gap: 10px
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 20px
        // line-height: 30px
        font-weight: 600
        letter-spacing: -0.005em
    .option_box
        margin-top: 10px
        display: grid
        grid-template-columns: 1fr 1fr
        gap: 20px
        div
            display: flex
            align-items: center
            justify-content: center
            padding: 10px 7px
            border-radius: 7px
            cursor: pointer
            border: 1px dashed #86868B
            background: #ffff
            label
                cursor: pointer
                color: #86868B
                font-weight: 600
                font-size: 16px
                width: 100%
                height: 100%
                text-align: center
            &:hover
                border: 1px dashed #1D1D1F
                label
                    color: #1D1D1F
            .isCorrect
                color: #25C081
            input
                display: none
                cursor: pointer
                accent-color: #626fe6
                width: 18px
                height: 18px
        .isSelected
            border: 1px solid #1D1D1F
            background: #1D1D1F
            label
                color: #fff
            &:hover
                label
                    color: #fff

.multi_choice_input
    display: flex
    flex-direction: column
    gap: 10px
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 20px
        // line-height: 30px
        font-weight: 600
        letter-spacing: -0.005em
    .option_box
        margin-top: 10px
        display: grid
        grid-template-columns: 1fr 1fr
        gap: 20px
        div
            display: flex
            align-items: center
            justify-content: center
            padding: 10px 7px
            border-radius: 7px
            cursor: pointer
            border: 1px dashed #86868B
            background: #ffff
            label
                cursor: pointer
                color: #86868B
                font-weight: 600
                font-size: 16px
                width: 100%
                height: 100%
                text-align: center
            &:hover
                border: 1px dashed #1D1D1F
                label
                    color: #1D1D1F
            .isCorrect
                color: #25C081
            input
                display: none
                cursor: pointer
                accent-color: #626fe6
                width: 18px
                height: 18px
        .isSelected
            border: 1px solid #1D1D1F
            background: #1D1D1F
            label
                color: #fff
            &:hover
                label
                    color: #fff

.match_the_following
    display: flex
    flex-direction: column
    gap: 10px
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 1.5vw
        font-weight: 600
        letter-spacing: -0.005em
    .options_box
        margin-top: 10px
        display: grid
        grid-template-columns: 1fr 0.1fr 1fr
        gap: .5vw
        div
            display: flex
            flex-direction: column
            gap: .5vw
            padding: 10px 7px
            border-radius: 7px
            label
                cursor: pointer
                color: #86868B
                font-weight: 600
                font-size: 16px
                width: 100%
                height: 100%
                text-align: center
        .middle_group
            display: flex
            align-items: center
            justify-content: space-around
            flex-direction: column
            padding: 10px 2px

        .left_group
            div
                background: #fff
                border: 1px solid #ececf1
                cursor: default
                label
                    color: #363636
                    font-weight: normal
                    pointer-events: none
        .right_group
            div
                background: #fff
                border: 1px solid #ececf1
                position: relative
                cursor: grab
                label
                    color: #00AFF0
                    font-weight: normal
                    pointer-events: none
                i
                    position: absolute
                    right: 1vw
                    top: 50%
                    transform: translateY(-50%)

.fill_in_the_blank
    display: flex
    flex-direction: column
    gap: 10px
    .fillable_options
        display: flex
        align-items: center
        justify-content: center
        gap: 1vw
        margin-block: 1.3vw
        p
            font-family: "Lexend", sans-serif
            font-size: .8vw
            background: #000
            color: #fff
            border-radius: .5vw
            padding: .3vw 1vw
            cursor: pointer
            user-select: none
    label
        color: #1D1D1F
        margin-left: 3px
        font-family: "Lexend", sans-serif
        font-size: 20px
        // line-height: 30px
        font-weight: 600
        letter-spacing: -0.005em
        padding: 1vw
    .drop_container
        background-color: transparent
        padding: 1vw
        border-bottom: 1px solid #000
        &.dropped
            background-color: #d1ffd1
            padding: 0.2vw 1vw
            border-bottom: none
            min-width: fit-content !important
            margin-inline: 0.4vw
            border-radius: 0.4vw
        &.drop_active
            background-color: #efefef

.common_image_upload
    display: flex
    flex-direction: column
    align-items: center
    gap: 15px
    .org_logo
        border: 1px dashed #3636368f
        border-radius: 10px
        width: 50%
        height: 150px
        img
            height: 100%
            width: 100%
            object-fit: contain
    input
        display: none
    label
        width: 50%
        text-align: center
        cursor: pointer
        border: none
        border-radius: 8px
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
        font-size: 14px
        font-weight: bold
        padding: 10px 20px
        background: #ececec
        margin-top: 0px
        transition: 200ms all ease
        &:hover
            background: #1976D2
            color: #fff

///////////////////////////////////////// LEVEL 3 DAHSBOARD /////////////////////////////////////////////
.level_three_dash
    .main_dash
        display: flex
        flex-direction: column
        gap: 20px
        background: #fff
        border-radius: 27px
        padding: 20px
        .dash_head
            // background: linear-gradient(180deg, #0086F8 0%, #18519B 100%)
            width: 100%
            // padding: 30px 30px 30px 30px
            border-radius: 20px
            .head_title
                display: flex
                align-items: flex-start
                justify-content: space-between
                .left
                    // flex: .7
                    h1
                        color: #2D3446
                        font-size: 30px
                        line-height: 50px
                        font-weight: 800
                        font-family: Nunito
                        span
                            font-family: Nunito
                            font-size: 30px
                            font-weight: 500
                            line-height: 50px
                    p
                        font-family: Nunito
                        font-size: 20px
                        font-weight: 700
                        line-height: 30px
                        color: #2D3446
                        margin-top: 5px

                .right
                    // flex: .3
                    display: flex
                    align-items: center
                    justify-content: center
                    img
                        height: 180px
        .dash_cards
            display: flex
            flex-wrap: wrap
            gap: 15px
            margin-top: 0px
            .dash_card
                cursor: pointer
                display: flex
                justify-content: space-between
                align-items: center
                gap: 20px
                background: #ececec
                width: 250px
                padding: 15px 15px 15px 18px
                border-radius: 10px
                .left
                    h3
                        color: #2D3446
                        font-family: Nunito
                        font-size: 25px
                        font-weight: 800
                        line-height: 27px
                    p
                        color: #2D3446
                        font-family: Nunito
                        font-size: 14px
                        font-weight: 600
                        line-height: 19.1px
                .right
                    display: flex
                    align-items: center
                    justify-content: center
                    width: 70px
                    height: 70px
                    background: #2D3446
                    border-radius: 10px
                    img
                        height: 45px
.schedule_view
    padding: 20px
    border-radius: 20px
    border: 1px solid #EFEFEF
    .schedule_bottom
        display: flex
        align-items: center
        justify-content: right
        padding: 15px 15px 0 15px
    .schedule_header
        display: flex
        align-items: center
        gap: 10px
        img
            height: 52px
        div
            h1
                font-family: Nunito
                font-size: 22px
                font-weight: 700
                line-height: 30.01px
                color: #226EBD
            p
                font-family: Nunito
                font-size: 16px
                font-weight: 700
                line-height: 21.82px
                color: #717171
                margin-top: 5px
    .main_details
        display: grid
        grid-template-columns: 317px auto
        margin-top: 20px
        gap: 20px
        .calendar_info
            border: 1px solid #EFEFEF
            border-radius: 15px
        .scheduled_chapters
            display: flex
            flex-direction: column
            gap: 10px
            .chapter_card
                border: 1px solid #EFEFEF
                border-radius: 15px
                padding: 15px
                .card_head
                    display: flex
                    justify-content: space-between
                    h1
                        font-family: Nunito
                        font-size: 18px
                        font-weight: 800
                        line-height: 18px
                        letter-spacing: 0.01em
                    button
                        padding: 4px 15px 3px 15px
                        border-radius: 4px
                        background: linear-gradient(180deg, #0086F8 0%, #18519B 100%)
                        border: none
                        outline: none
                        color: #fff
                        font-family: Nunito
                        font-size: 13px
                        font-weight: 500
                        line-height: 20.46px
                        letter-spacing: 0.02em
                        &:hover
                            opacity: .8
            .card_details
                p
                    font-family: Nunito
                    font-size: 13px
                    font-weight: 500
                    line-height: 20px
                    letter-spacing: 0.01em
                    color: #8C8C8C
                    b
                        font-weight: 700
            .card_action
                display: flex
                align-items: center
                gap: 10px
                margin-top: 10px
                button
                    padding: 4px 15px 3px 15px
                    border-radius: 50px
                    background: #9F7DFF
                    border: none
                    outline: none
                    color: #fff
                    font-family: Nunito
                    font-size: 13px
                    font-weight: 500
                    line-height: 20.46px
                    letter-spacing: 0.02em
                    &:hover
                        opacity: .8
                    &:last-child
                        background: #00B9AE
    .table_data
        display: flex
        flex-direction: column
        gap: 10px
        margin-top: 20px
        .table_row
            display: grid
            border: 1px solid #EFEFEF
            border-radius: 15px
            padding: 10px
            .table_cell
                h6
                    font-family: Nunito
                    font-size: 15px
                    font-weight: 700
                    line-height: 19.1px
                p
                    font-family: Nunito
                    font-size: 14px
                    font-weight: 600
                    line-height: 18px
                    margin-top: 5px
                    color: #18519B
.user_courses
    padding: 20px
    border-radius: 20px
    border: 1px solid #EFEFEF
    .courses_header
        display: flex
        align-items: center
        justify-content: space-between
        gap: 10px
        button
            padding: 4px 15px 3px 15px
            border-radius: 4px
            background: linear-gradient(180deg, #0086F8 0%, #18519B 100%)
            border: none
            outline: none
            color: #fff
            font-family: Nunito
            font-size: 13px
            font-weight: 500
            line-height: 20.46px
            letter-spacing: 0.02em
            &:hover
                opacity: .8
        img
            height: 52px
        div
            h1
                font-family: Nunito
                font-size: 22px
                font-weight: 700
                line-height: 30.01px
                color: #226EBD
            p
                font-family: Nunito
                font-size: 16px
                font-weight: 700
                line-height: 21.82px
                color: #717171
                margin-top: 5px
    .courses_wrapper
        margin-top: 15px
        display: flex
        flex-wrap: wrap
        gap: 20px
        .dash_course_card
            width: 250px
            padding: 13px 10px 13px 10px
            border-radius: 10px
            border: 1px solid #EFEFEFEF
            transition: 200ms all ease
            .course_banner
                width: 100%
                height: 150px
                border-radius: 10px
                overflow: hidden
                img
                    height: 100%
                    width: 100%
                    object-fit: cover
            .dash_course_details
                margin-top: 15px
                display: flex
                flex-direction: column
                gap: 5px
                h1
                    font-family: Nunito
                    font-size: 16px
                    font-weight: 700
                    line-height: 21.82px
                small
                    font-family: Nunito
                    font-size: 13px
                    font-weight: 600
                    line-height: 21.82px
                    color: #616161
                p
                    font-family: Nunito
                    font-size: 15px
                    font-weight: 500
                    line-height: 21.82px
            &:hover
                background: #EEF6FF
                border: 1px solid #EEF6FF
                box-shadow: 0px 2px 4px 0px #0000001A
                cursor: pointer

.profile_dash
    background: #fff
    padding: 20px 0px 20px 0px
    gap: 30px
    border-radius: 30px
    // opacity: 0px
    display: flex
    flex-direction: column
    gap: 50px
    .details
        display: flex
        flex-direction: column
        align-items: center
        gap: 15px
        .profile_icon
            width: 133px
            height: 133px
            position: relative
            // border-radius: 50%
            &>img
                object-fit: cover
                height: 100%
                width: 100%
                border-radius: 50%
            div
                position: absolute
                right: 0
                bottom: -15px
                height: 60px
                img
                    height: 60px
        h5
            font-family: "Nunito", sans-serif
            color: #2D3446
            font-size: 28px
            font-weight: 700
            line-height: 38.19px
            text-align: center
            // margin: 7px 0
        .badge_level
            width: Hug (255px)px
            // height: Hug (51px)px
            padding: 5px 30px 5px 30px
            gap: 5px
            border-radius: 10px
            background: #ececec
            display: flex
            align-items: center
            img
                height: 50px
        .user_points
            width: 255px
            padding: 15px
            gap: 0px
            border-radius: 10px
            justify: space-between
            // opacity: 0px
            background: #FFFFFF66
            display: flex
            gap: 10px
            justify-content: space-around
            div
                text-align: center
                h6
                    font-family: Nunito
                    font-size: 22px
                    font-weight: 800
                    line-height: 27px
                    color: #2D3446
                p
                    color: #2D3446
                    font-family: Nunito
                    font-size: 13px
                    font-weight: 600
    .notifications
        display: flex
        flex-direction: column
        align-items: center
        gap: 15px
        h2
            font-family: Nunito
            font-size: 30px
            font-weight: 700
            line-height: 36px
            text-align: center
            color: #2D3446
        .noti_wrapper
            width: 284px
            display: flex
            flex-direction: column
            gap: 10px
            div
                padding: 10px
                gap: 10px
                border-radius: 10px
                // opacity: 0px
                border: 1px solid #ececec
                h4
                    font-family: Nunito
                    font-size: 14px
                    font-weight: 600
                    line-height: 16.99px
                    letter-spacing: 0.01em
                    color: #2d3446c7
                    display: flex
                    justify-content: space-between
                    font-family: "Lexend", sans-serif
                p
                    font-family: Nunito
                    font-size: 11px
                    font-weight: 500
                    line-height: 13.35px
                    letter-spacing: 0.01em
                    font-family: "Lexend", sans-serif
                    color: #2D3446
                    margin-top: 5px
    .achieved_badges
        display: flex
        flex-direction: column
        align-items: center
        gap: 15px
        h2
            font-family: Nunito
            font-size: 30px
            font-weight: 700
            line-height: 36px
            text-align: center
            color: #fff
        .badge_wrapper
            display: grid
            grid-template-columns: 1fr 1fr 1fr
            gap: 10px
            row-gap: 0
            div
                height: 90px
                img
                    height: 100%
                    object-fit: contain
    .performance
        display: flex
        flex-direction: column
        align-items: center
        gap: 15px
        h2
            font-family: Nunito
            font-size: 30px
            font-weight: 700
            line-height: 36px
            text-align: center
            color: #fff
        .perform_wrapper
            display: flex
            flex-direction: column
            gap: 15px
            width: 284px
            .perform_card
                display: flex
                justify-content: space-between
                padding: 15px 23px 15px 23px
                border-radius: 10px
                justify: space-between
                align-items: center
                background: #FFFFFF66
                .left
                    h3
                        font-family: Nunito
                        font-size: 25px
                        font-weight: 800
                        line-height: 27px
                        color: #fff
                    p
                        font-family: Nunito
                        font-size: 14px
                        font-weight: 600
                        line-height: 19.1px
                        color: #fff
                .right
                    img
                        height: 60px

.progress
    border: 1px solid #2A94FF
    background: #fff
    border-radius: 20px
    position: relative
    margin: 15px 0
    height: 15px
    width: 156px

.progress-done
    --wid: 0%
    background: linear-gradient(180deg, #2A94FF 0%, #18519B 100%)
    border-radius: 20px
    color: #fff
    display: flex
    align-items: center
    justify-content: center
    height: 100%
    width: var(--wid)
    opacity: 1
    transition: 1s ease 0.3s

.main_course_play
    width: 100%
    background: #fff
    &:fullscreen
        padding: 20px
.course_player_header
    display: flex
    align-items: center
    justify-content: space-between
    background: #f5f5f7
    border-radius: 20px
    padding: 25px
    // width: 100%
    .action
        display: flex
        align-items: center
        gap: 20px
        p
            font-size: 14px
            &:hover
                // text-decoration: underline
    h4
        font-family: Lexend, sans-serif
        font-size: 24px
        font-weight: 600
        letter-spacing: -.005em
        line-height: 1.0714285714
    button
        width: 35px
        height: 35px
        border-radius: 50%
        cursor: pointer
        background: #E6E6E9
        border: none
        transition: 150ms all ease
        display: flex
        align-items: center
        justify-content: center
        i
            color: #535354b4
            font-size: 18px
        &:hover
            background: #e6e6e9b7

.course_player
    // height: 80vh
    // border: 1px solid red
    margin: 20px 0
    gap: 20px
    display: grid
    grid-template-columns: 2fr 8fr
    transition: 200ms all ease
    .course_content
        height: 100%
        // border: 2px solid #9ba6a534
        // border-radius: 5px
        // padding: 10px
        display: flex
        flex-direction: column
        gap: 20px
        // justify-content: space-between
        .bottomDiv
            display: flex
            justify-content: space-between
            align-items: center
            .ins_controls
                width: 100%
                display: flex
                justify-content: space-between
                align-items: center
                .right
                    display: flex
                    align-items: center
                    gap: 15px

    .course_content_list
        background: #f5f5f7
        border-radius: 20px
        padding: 18px 18px
        display: flex
        flex-direction: column
        gap: 15px
        height: 600px
        transition: 200ms all ease
        .hamburger_menu
            display: flex
            align-items: center
            justify-content: right
            i
                font-size: 16px
                color: #363636d7
                cursor: pointer
                width: 30px
                height: 30px
                border-radius: 50%
                display: flex
                align-items: center
                transition: 200ms all ease
                justify-content: center
                &:hover
                    // background: #ececec
                    color: #000
        .contentRow
            h6
                color: #1d1d1f
                font-family: Lexend, sans-serif
                font-size: 16px
                font-weight: 600
                letter-spacing: -.005em
                margin-left: 2px
                i
                    margin-right: 3px
                    font-size: 15px
                    cursor: pointer
            div
                display: flex
                align-items: center
                flex-direction: column
                gap: 7px
                padding: 15px 5px 5px 25px
                .active_lecture
                    color: #000
                    font-weight: 500
                p
                    font-size: 0.72vw
                    font-family: Lexend, sans-serif
                    cursor: pointer
                    color: #363636de
                    transition: 300ms all ease
                    &:hover
                        color: #000
                        margin-left: 5px
    .minimize_class
        padding: auto 0px
        transition: 200ms all ease
        .contentRow
            display: none

.course_player_min
    grid-template-columns: 50px auto
    transition: 200ms all ease

.Course_content_player
    // background: #f5f5f7
    // border-radius: 20px
    // padding: 25px
    height: 600px

.nav_btns
    width: 60px
    height: 60px
    border-radius: 50%
    cursor: pointer
    background: #E6E6E9
    border: none
    transition: 250ms all ease
    i
        color: #535354b4
        font-size: 25px
    &:hover
        background: #e6e6e9b7

.nav_btns_2
    // width: 60px
    padding: 0 10px
    height: 40px
    border-radius: 50px
    display: flex
    align-items: center
    gap: 10px
    cursor: pointer
    background: #E6E6E9
    transition: 150ms all ease
    border: none
    i
        color: #535354b4
        font-size: 20px
    p
        font-size: 16px
        // line-height: 1.0714285714
        font-weight: 700
        letter-spacing: -0.005em
        font-family: "Lexend", sans-serif
        // font-family: "Public Sans", sans-serif
        color: #535354b4
        margin: 0
        // text-transform: uppercase
    &:hover
        background: #e6e6e9b7

.ContentIFRAME
    background: #fff
    margin-top: 20px
    border-radius: 15px
    padding: 10px
    height: 91%
    overflow-y: auto
    display: grid
    grid-template-columns: 1fr
    gap: 30px
    &::-webkit-scrollbar
        width: 6px
    &::-webkit-scrollbar-track
        background: transparent
    &::-webkit-scrollbar-thumb
        background: #F5F5F7
        border-radius: 5px
    &::-webkit-scrollbar-thumb:hover
        background: #dadae2
    &>div
        display: grid
        gap: 20px

.html_editor_head
    display: flex
    align-items: center
    justify-content: space-between

.lecture_data
    background: #fff
    // margin-top: 20px
    border-radius: 15px
    padding: 10px
    overflow-y: auto
    display: grid
    grid-template-columns: 1fr
    gap: 30px
    &::-webkit-scrollbar
        width: 6px
    &::-webkit-scrollbar-track
        background: transparent
    &::-webkit-scrollbar-thumb
        background: #F5F5F7
        border-radius: 5px
    &::-webkit-scrollbar-thumb:hover
        background: #dadae2
    .lecture_card_actions
        display: flex
        align-items: center
        justify-content: right
        padding: 10px
        button
            padding: 2px 10px
            color: #000
            border: 1px solid #ececec
            cursor: pointer
            border-radius: 10px
            font-size: 13px
            transition: 200ms all ease
            &:hover
                background: #363636
                color: #fff
    .card_grid
        display: grid
        gap: 20px
        &>div
            border: 1px solid #363636
            border-radius: 10px
            min-height: 400px
            overflow: hidden
            display: grid
            grid-template-rows: auto 40px 40px

            select
                position: relative
                cursor: pointer
            .IMG_DIV
                display: flex
                align-items: center
                justify-content: center
                position: relative
                img
                    object-fit: contain
                    height: 400px
                    width: 100%
                input
                    display: none
                label
                    cursor: pointer
                    background: #ececec
                    border-radius: 5px
                    padding: 5px 10px
                    display: flex
                    align-items: center
                    gap: 5px
                    color: #363636
                    transition: 200ms all ease
                    &:hover
                        background: #363636
                        color: #fff
                button
                    position: absolute
                    top: 0
                    right: 0
                    width: 27px
                    height: 27px
                    border-radius: 10px
                    z-index: 2
                    display: flex
                    align-items: center
                    justify-content: center
                    border: none
                    transition: 200ms all ease
                    &:hover
                        background: #363636
                        color: #fff

.editor_controls
    padding: 10px
    display: flex
    align-items: center
    justify-content: center
    gap: 10px
    margin-top: 10px
    p
        padding: 2px 10px
        color: #000
        border: 1px solid #ececec
        cursor: pointer
        border-radius: 10px
        transition: 300ms all ease
        &:hover
            background: #ececec

.layout_head
    position: absolute

.lecture_quill
    height: 100%

.nav_btn_start
    // width: 60px
    font-family: "Lexend", sans-serif
    height: 50px
    display: flex
    align-items: center
    font-size: 18px
    font-weight: 500
    text-transform: uppercase
    color: #535354b4
    gap: 5px
    border-radius: 50px
    padding: 0 20px
    cursor: pointer
    background: #E6E6E9
    border: none
    transition: 250ms all ease
    i
        color: #535354b4
        font-size: 25px
    &:hover
        background: #e6e6e9b7

.ContentQuizz
    // border: 1px solid red
    display: flex
    align-items: center
    justify-content: center
    // padding: 0 3vw
    overflow: auto
    height: 100%
    position: relative
    .msq-option-container
        display: grid
        grid-template-columns: 1fr 1fr
        gap: 1rem
        padding-top: 3rem

        .msq-option
            all: unset,
            color: white
            background-color: transparent
            font-size: 40px
            line-height: 40px
            padding: 1rem 2rem
            border-radius: 0.5rem
            border: 1px solid #9C9C9C
            &.selected
                color: #E08D67
                background-color: white

    .fitb-option-container
        display: flex
        align-items: center
        justify-content: center
        gap: 1rem
        .fitb-option
            all: unset,
            background-color: #fff
            color: #E08D67
            padding: 0.5rem 1rem
            font-size: 40px
            line-height: 40px
            border-radius: 1.5rem

    .match-option-container
        display: grid
        grid-template-rows: 0.8fr 0.2fr
        height: 295px
        padding-top: 1.5rem
        column-gap: 1rem
        .match-option
            width: 100%
            background-color: #fff
            border-radius: 1rem
            display: flex
            align-items: center
            justify-content: center
            font-size: 40px
        .match-option-placeholder
            border: 1px dashed #fff
            border-radius: 1rem

    .match-answer-container
        padding-top: 3rem
        display: flex
        align-items: center
        justify-content: center
        gap: 1rem
        & > div
            background-color: #fff
            border-radius: 0.5rem
            padding: 0.5rem 2rem
            font-size: 20px
            line-height: 20px
            mix-blend-mode: difference

    .img-option-container
        display: grid
        width: 100%
        padding-top: 3rem
        gap: 2rem

        .img-option
            width: 100%
            height: 306px
            position: relative
            border-radius: 1.5rem
            overflow: hidden
            &.selected
                img
                    border: 5px solid #fecc5c
                .checked
                    display: flex
            img
                width: 100%
                height: 100%
                object-fit: cover
                position: absolute
                border-radius: 1.5rem
                border: 5px solid transparent
                inset: 0
                z-index: 1
            .checked
                position: absolute
                top: 4px
                left: 4px
                width: 3rem
                aspect-ratio: 1 / 1
                background: #fecc5c
                color: white
                border-radius: 0 0 0.5rem
                display: none
                justify-content: center
                align-items: center
                font-size: 1.5rem
                z-index: 10

.navigation_control
    display: flex
    align-items: center
    justify-content: center
    gap: 20px
    margin-top: 15px
    width: 100%
    &>div
        display: flex
        align-items: center
        justify-content: center
        gap: 10px

.instructions_box
    // margin-top: 30px
    // border: 1px solid #000
    display: grid
    grid-auto-rows: 4fr 1fr
    // min-height: 550px
    // width: 70%
    img
        height: 100px
    .ins_cards
        // border: 1px solid #000
        // background: #F5F5F7
        padding: 25px
        border-radius: 20px
        .ins_card
            display: grid
            grid-template-columns: 3fr 1.5fr
            gap: 30px
            position: relative
            padding-top: 30px
            small
                position: absolute
                font-size: 20px
                font-weight: 800
                top: -10px
                color: #7F7F80
            .left
                // border: 1px solid #000
                h2
                    font-size: 40px
                    line-height: 1.0714285714
                    font-weight: 600
                    letter-spacing: -0.005em
                    font-family: "Lexend", sans-serif
                p
                    margin-top: 20px
                    font-size: 21px
                    line-height: 1.3334133333
                    font-weight: 400
                    letter-spacing: .009em
                    font-family: "Lexend", sans-serif
            .right
                // border: 1px solid #000
                display: flex
                // align-items: center
                justify-content: center
                img
                    height: 170px
    .ins_control
        display: flex
        align-items: center
        justify-content: center
        gap: 20px
        z-index: 1
        &>div
            display: flex
            align-items: center
            justify-content: center
            gap: 20px

.single_question
    // margin-top: 20px
    // border: 1px dashed #ececec
    display: flex
    flex-direction: column
    align-items: center
    justify-content: space-between
    width: 70%
    min-height: 400px
    // background-color: rgb(255, 255, 255)
    color: rgb(33, 43, 54)
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms
    background-image: none
    overflow: hidden
    position: relative
    // box-shadow: rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px
    // border-radius: 16px
    z-index: 0
    // padding: 24px
    .question_box
        width: 100%
        display: flex
        flex-direction: column
        gap: 20px

.result_screen
    z-index: 1
    display: grid
    grid-template-columns: 1fr 1fr
    .left
        display: flex
        align-items: center
        div
            overflow: hidden
            background: #fff
            border-radius: 1vw
            border: 1px solid #ececf1
            table
                th
                    border: 1px solid #fff
                    padding: .5vw 1vw
                    font-family: "Lexend", sans-serif
                    font-size: 1vw
                td
                    border: 1px solid #ececf1
                    padding: .5vw 1vw
                    font-family: "Lexend", sans-serif
                    font-size: .8vw
    .right
        display: flex
        align-items: center
        flex-direction: column
        gap: 1vw
        img
            height: 13vw
        h1
            font-family: "Lexend", sans-serif
            font-size: 2vw
        h2
            font-family: "Lexend", sans-serif
            font-size: 1.5vw
        div
            display: flex
            align-items: center
            justify-content: center
            flex-direction: column
            small
                color: #FFB331
                font-family: "Lexend", sans-serif
                font-weight: normal
                font-size: 1.3vw
            h3
                font-size: 2vw
                font-family: "Lexend", sans-serif
                font-weight: 700

.custom_input
    padding: 5px 10px
    cursor: pointer
    border-radius: 5px

.IMG_DIV_alpha
    display: flex
    align-items: center
    justify-content: center
    position: relative
    border: 2px dashed #ececec
    border-radius: 10px
    height: 500px
    margin-top: 20px
    img
        object-fit: contain
        height: 100%
        width: 100%
    input
        display: none
    label
        cursor: pointer
        background: #ececec
        border-radius: 5px
        padding: 5px 10px
        display: flex
        align-items: center
        gap: 5px
        color: #363636
        transition: 200ms all ease
        &:hover
            background: #363636
            color: #fff
    button
        position: absolute
        top: 0
        right: 0
        width: 27px
        height: 27px
        border-radius: 10px
        display: flex
        align-items: center
        justify-content: center
        border: none
        transition: 200ms all ease
        &:hover
            background: #363636
            color: #fff

.bg_editor_head
    display: flex
    align-items: center
    justify-content: space-between
    button
        border: none
        padding: 4px 15px
        border-radius: 4px
        cursor: pointer
        transition: 200ms all ease-in
        background: #ececec
        color: #363636
        &:hover
            color: #fff
            background: #363636

.lecture_icon
    // border: 1px solid red
    width: 60px
    height: 60px
    display: flex
    align-items: center
    justify-content: center
    // padding: 10px
    border-radius: 10px
    background: #267DD4
    i
        color: #fff
        font-size: 30px

.badge_level_alpha
    height: 70px
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
    // height: Hug (51px)px
    padding: 5px 30px 5px 30px
    gap: 5px
    border-radius: 10px
    background: #FFFFFF
    display: flex
    align-items: center
    gap: 2vw
    img
        height: 40px
        animation: scale-level 4s ease-in-out infinite
    div
        display: flex
        align-items: center
        gap: 10px
        h4
            font-weight: 400 !important
        & > i
            font-size: 20px !important
            color: #D34343 !important
            animation: rotate-points 1s ease-in-out infinite
            animation-delay: 5000ms
        span
            font-family: "Lexend", sans-serif
            font-weight: 700
            font-size: 22px
            color: #2D3446
        p
            font-family: "Lexend", sans-serif
            font-weight: 700
            font-size: 18px
            color: #2D3446

@keyframes scale-level
    0%, 100%
        transform: scale(.8)

    50%
        transform: scale(1.1)

@keyframes rotate-points
    0%
        transform: rotate(0deg)

    100%
        transform: rotate(360deg)

.dashboard_beta
    // border: 1px solid red
    border-radius: 10px
    // padding: 20px
    margin: 0px 0px 100px 0
    .dash_badge
        display: flex
        align-items: center
        justify-content: space-between
        .left
            display: flex
            align-items: center
            gap: 10px
            div
                display: flex
                flex-direction: column
                align-items: flex-start
                gap: 7px
                h4
                    font-family: "Lexend", sans-serif
                    font-weight: 600
                p
                    font-family: "Lexend", sans-serif
                    font-weight: 400
                    font-size: 14px
                    padding: 3px 10px
                    border-radius: 50px
                    background: #2D3446
                    color: #fff

    .dash_middle
        display: flex
        align-items: center
        justify-content: center
        gap: 50px
        margin-bottom: 50px
        &>div
            border: 1px solid #ececec
            display: grid
            grid-template-columns: 1fr 1fr
            border-radius: 20px
            padding: 20px
            div
                display: flex
                flex-direction: column
                align-items: center
                gap: 5px
                padding: 0 70px
                h4
                    font-family: "Lexend", sans-serif
                    font-weight: 600
                    i
                        font-size: 20px !important
                        animation: rotate-points 1s ease-in-out infinite
                        animation-delay: 5000ms
                        color: #D34343 !important
                p
                    font-family: "Lexend", sans-serif
                    font-weight: 400
                    font-size: 14px
                &:last-child
                    border-left: 2px solid #ececec

    .dash_stepper
        // border: 1px solid #ececec
        padding: 10px 30px
        // width: 100
        .stepper_track
            background: #ececec
            border-radius: 10px
            height: 20px
            padding: 2px
            position: relative
            .stepper_thumb
                --wid: 0%
                background: #FDE047
                height: 100%
                width: var(--wid)
                border-radius: 10px
                transition: 1s ease 0.3s
                opacity: 1
            .milestone
                position: absolute
                top: -28px
                left: 6.6%
                display: flex
                align-items: center
                flex-direction: column
                filter: grayscale(50)
                img
                    height: 70px
                h4
                    font-family: "Lexend", sans-serif
                    font-weight: 500
                    font-size: 15px
                    white-space: nowrap
                    i
                        color: #73AD01 !important
            .step_2
                left: 19.8%
            .step_3
                left: 33%
            .step_4
                left: 66%
            .step_5
                left: 85.8%
            .step_6
                left: 97%

            .reached
                filter: grayscale(0)

.custom_table
    margin: 5px 0
    height: 30vw
    overflow: auto
    &::-webkit-scrollbar
        width: 10px
    &::-webkit-scrollbar-track
        background: #f1f1f1
    &::-webkit-scrollbar-thumb
        background: #888
        border-radius: 4px
    &::-webkit-scrollbar-thumb:hover
        background: #555
    th
        font-family: "Lexend", sans-serif

.select_btn
    border: 1px solid #393e46
    background: #fff
    color: #393e46
    border-radius: 7px
    padding: 3px 10px
    font-size: 14px
    font-family: "Lexend", sans-serif
    &:hover
        background: #e3e3e3

.selected_btn
    border: 1px solid #393e46
    background: #393e46
    color: #fff
    border-radius: 7px
    padding: 3px 10px
    font-size: 14px
    font-family: "Lexend", sans-serif
    &:hover
        background: #393e46

.selected_btn_alpha
    border: 1px solid #393e46
    background: #393e46
    color: #fff
    border-radius: 7px
    padding: 3px 10px
    font-size: 14px
    font-family: "Lexend", sans-serif
    &:hover
        background: #e3e3e3
        color: #393e46

.team_details
    padding: 10px
    p
        font-size: 14px

.page_header
    display: flex
    align-items: center
    justify-content: space-between
    margin-bottom: 10px
    h4
        font-size: 22px
        font-weight: 500
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
        font-family: "Lexend", sans-serif
    button
        border: 1px solid #7c73e6
        background: #7c73e6
        color: #fff
        border-radius: 0px
        padding: 5px 10px
        font-size: 14px
        font-family: "Lexend", sans-serif
        transition: 200ms all ease
        &:hover
            background: #fff
            color: #7c73e6
.page_layout
    padding: 20px
    background: #fff
    border-radius: 10px

.custom_grid
    display: flex
    flex-direction: column
    gap: 15px
    .grid_card
        border: 2px solid #ececec
        padding: 15px
        border-radius: 15px
        display: flex
        justify-content: space-between
        align-items: center
        .left
            h1
                font-size: 23px
                font-weight: 500
                font-family: "Lexend", sans-serif
                margin-bottom: 5px
                color: #2D3446
            p
                font-size: 16px
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif
                color: #2D3446
        .right
            h4
                font-size: 18px
                font-family: "Lexend", sans-serif
                color: #2D3446

.page_filters
    display: flex
    align-items: flex-end
    gap: 10px
    margin-bottom: 10px
    .input_group
        display: flex
        flex-direction: column
        gap: 5px
        label
            font-weight: 500
        input
            border: 1px solid #2D3446
            border-radius: 5px
            padding: 5px 7px
        select
            border: 1px solid #2D3446
            border-radius: 5px
            padding: 5px 7px
            cursor: pointer

.filter_controls
    display: flex
    align-items: baseline
    justify-content: space-between
    gap: 5px
.search_btn
    border: 1px solid #393e46
    background: #393e46
    color: #fff
    border-radius: 50px
    padding: 5px 12px
    font-size: 14px
    font-family: "Lexend", sans-serif
    &:hover
        background: #e3e3e3
        color: #393e46
.clear_btn
    border: 1px solid #393e46
    background: #393e46
    color: #fff
    border-radius: 50px
    padding: 5px 12px
    font-size: 14px
    font-family: "Lexend", sans-serif
    &:hover
        background: #e3e3e3
        color: #393e46

.page_tabs
    margin: 15px 0
    display: flex
    align-items: center
    gap: 7px
    border-radius: 50px
    padding: 7px 7px
    background: #f2f2f2
    width: max-content
    p
        background: transparent
        border-radius: 50px
        padding: 5px 15px
        font-size: 14px
        font-family: "Lexend", sans-serif
        color: #2D3446
        cursor: pointer
        transition: 200ms all ease
    .active
        background: #fff
        color: #2D3446

.chips_group
    display: flex
    align-items: center
    flex-wrap: wrap
    gap: 5px
    p
        background: #ececec
        color: #000
        font-family: "Lexend", sans-serif
        border-radius: 50px
        padding: 3px 10px
        font-size: 14px

.subission_details
    margin: 20px 3vw
    border: 1px solid #D1C5C5
    border-radius: 1vw
    padding: 20px
    h5
        font-size: 20px
        font-weight: 500
        font-family: "Lexend", sans-serif
        margin-bottom: 5px
        color: #2D3446
        b
            background: #ececec
            color: #000
            font-family: "Lexend", sans-serif
            border-radius: 50px
            padding: 5px 15px
            font-size: 16px
            text-transform: uppercase

    p
        font-size: 16px
        font-family: "Lexend", sans-serif
        margin-bottom: 5px
        color: #2D3446
        margin: 0 1vw
    button
        border: 1px solid #393e46
        background: #ececec
        color: #393e46
        border-radius: 10px
        padding: 3px 10px
        font-size: 12px
        margin: .5vw 1vw
        font-family: "Lexend", sans-serif
        &:hover
            background: #e3e3e3
            color: #393e46

.review_check
    display: flex
    flex-direction: column
    gap: 10px
    padding: 20px 3vw
    .input_group
        display: flex
        gap: 10px
        width: 100%
        .input_fields
            display: flex
            flex-direction: column
            gap: 5px
            label
                font-weight: 500
            textarea,input
                border: 1px solid #2D3446
                border-radius: 5px
                padding: 7px 7px
            select
                border: 1px solid #2D3446
                border-radius: 5px
                padding: 7px 7px
                cursor: pointer

.review_controls
    display: flex
    align-items: baseline
    justify-content: right
    gap: 5px
    button
        border: 1px solid #393e46
        background: #393e46
        color: #fff
        border-radius: 7px
        padding: 5px 12px
        font-size: 14px
        font-family: "Lexend", sans-serif
        &:hover
            background: #e3e3e3
            color: #393e46

.table_image_alpha
    width: 100px
    height: 60px
    border-radius: 7px
    overflow: hidden
    img
        height: 100%
        width: 100%
        object-fit: cover

.table_input
    border: 1px solid #2D3446
    border-radius: 5px
    padding: 5px 7px
    cursor: pointer

.custom_scrollbar
    scroll-behavior: smooth
    &::-webkit-scrollbar
        width: 3px
        height: 3px
    &::-webkit-scrollbar-track
        background: #f1f1f1
    &::-webkit-scrollbar-thumb
        background: #999
        border-radius: 3px
    &::-webkit-scrollbar-thumb:hover
        background: #888

.layout_selected
    background: #F1F5F9
    border-color: #64748B
    &>div
        border-color: #64748B

.icon_selected
    background: #E7EAF6
    &:hover
        background: #E7EAF6 !important

.layout_content_active
    background: #272E3F !important
    color: #fff
    &:hover
        color: #fff !important

.selected_homework
    background: #272E3F !important
    color: #fff
    &:hover
        color: #fff !important

.selected_quiz
    background: #272E3F !important
    color: #fff
    &:hover
        color: #fff !important

.active_template
    background: #0F172A !important
    color: #fff

.active_subtab
    background: #fff !important

// .finalDesign
//     .landscape_design
//         border: 1px solid red !important
// .template
//     scale: 0.7 !important
// .certificate_contianer
//     // border: 1px solid red
//     overflow: hidden
//     height: 90%
//     display: grid
//     gap: 10px
//     grid-template-columns: 300px 5fr 2fr
//     .templetes
//         height: 100%
//         // border-right: 1px solid #363636d0
//         border-radius: 5px
//         box-shadow: inset 0px 0px 10px #36363694
//         padding: 10px
//         display: flex
//         gap: 10px
//         .menu
//             border-right: 2px solid #3636367a
//             display: flex
//             flex-direction: column
//             gap: 10px
//             padding: 0 10px 0 0
//             svg
//                 font-size: 40px
//                 cursor: pointer
//                 padding: 5px
//                 color: #363636
//                 border-bottom: 1px solid #36363657
//                 &:hover
//                     border-radius: 4px
//                     background: #3636362d
