import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { tanstackApiFormdata } from "@/react-query/api";
import { CircleX, ImagePlus } from "lucide-react";
import { useEffect, useState } from "react";

const LayoutsData = [
    {
        name: "1 Column Layout",
        layout: {
            display: "grid",
            gridTemplateColumns: "repeat(1, 1fr)",
        },
        layout_box: [
            {
                content_type: "",
                content: "",
            },
        ],
    },
    {
        name: "2 Column Layout",
        layout: {
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
        },
        layout_box: [
            {
                content_type: "",
                content: "",
            },
            {
                content_type: "",
                content: "",
            },
        ],
    },
    {
        name: "3 Column Layout",
        layout: {
            display: "grid",
            gridTemplateColumns: "repeat(3, 1fr)",
        },
        layout_box: [
            {
                content_type: "",
                content: "",
            },
            {
                content_type: "",
                content: "",
            },
            {
                content_type: "",
                content: "",
            },
        ],
    },
    {
        name: "3 Column Layout",
        layout: {
            display: "grid",
            gridTemplateColumns: "repeat(4, 1fr)",
        },
        layout_box: [
            {
                content_type: "",
                content: "",
            },
            {
                content_type: "",
                content: "",
            },
            {
                content_type: "",
                content: "",
            },
            {
                content_type: "",
                content: "",
            },
        ],
    },
];

const OptionsSettings = ({ setContentData, contentData }) => {
    const [options, setOptions] = useState(contentData?.options || []);
    const [matchOptions, setMatchOptions] = useState(contentData?.matchOptions || []);
    const [fillOptions, setFillOptions] = useState(contentData?.fillOptions || []);
    const [dropdownOptions, setDropdownOptions] = useState(contentData?.dropdown_options || []);

    useEffect(() => {
        if (options?.length > 0) {
            setContentData({ ...contentData, options: options });
        }
    }, [options]);

    useEffect(() => {
        if (matchOptions?.length > 0) {
            setContentData({ ...contentData, matchOptions: matchOptions });
        }
    }, [matchOptions]);

    useEffect(() => {
        if (fillOptions?.length > 0) {
            setContentData({ ...contentData, fillOptions: fillOptions });
        }
    }, [fillOptions]);

    useEffect(() => {
        if (dropdownOptions?.length > 0) {
            setContentData({ ...contentData, dropdown_options: dropdownOptions });
        }
    }, [dropdownOptions]);

    const onOptionChange = (name, value, idx) => {
        let optionData = [...options];
        optionData[idx][name] = name == "isCorrect" ? !optionData[idx][name] : value;
        setOptions(optionData);

        if (name == "src") {
            const formData = new FormData();
            var file = value;
            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");

            onMediaUpload(formData, idx);
        }
    };

    const OptionAdd = () => {
        let opt = { src: "", label: "", isCorrect: false };
        setOptions([...options, opt]);
    };

    const RemoveOption = (idx) => {
        const arr = [...options];
        arr.splice(idx, 1);
        setOptions(arr);
    };

    const onMediaUpload = async (formData, index) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData)
            .then((res) => {
                let optionData = [...options];
                optionData[index]["src"] = res?.data?.fileUrl;
                setOptions(optionData);
            })
            .catch((err) => {
                let optionData = [...options];
                optionData[index]["src"] = "";
                setOptions(optionData);
            });
    };

    const onRemoveImage = (index) => {
        let optionData = [...options];
        optionData[index]["src"] = "";
        setOptions(optionData);
    };

    ////////////////////////////// MATCH THE OPTIONS////////////////////////////////

    const onMatchOptionChange = (name, value, idx) => {
        let optionData = [...matchOptions];
        optionData[idx][name] = value;
        setMatchOptions(optionData);

        if (name == "imageLabel" || name == "imageText") {
            const formData = new FormData();
            var file = value;
            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");

            onMatchOptionsMediaUpload(name, formData, idx);
        }
    };

    const MatchOptionAdd = () => {
        let opt = {
            text: "",
            label: "",
            imageText: "",
            imageLabel: "",
            correctIndex: null,
        };
        setMatchOptions([...matchOptions, opt]);
    };

    const RemoveMatchOption = (idx) => {
        const arr = [...matchOptions];
        arr.splice(idx, 1);
        setMatchOptions(arr);
    };

    const onRemoveMTFImage = (name, index) => {
        let optionData = [...matchOptions];
        optionData[index][name] = "";
        setMatchOptions(optionData);
    };

    const onMatchOptionsMediaUpload = async (name, formData, index) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData)
            .then((res) => {
                let optionData = [...matchOptions];
                optionData[index][name] = res?.data?.fileUrl;
                setMatchOptions(optionData);
            })
            .catch((err) => {
                let optionData = [...matchOptions];
                optionData[index][name] = "";
                setMatchOptions(optionData);
            });
    };

    ////////////////////////////// FILL THE OPTIONS/ ///////////////////////////////

    const onFillOptionChange = (name, value, idx) => {
        let optionData = [...fillOptions];
        optionData[idx][name] = value;
        setFillOptions(optionData);
    };

    const RemoveFillOption = (idx) => {
        const arr = [...fillOptions];
        arr.splice(idx, 1);
        setFillOptions(arr);
    };

    const FillOptionAdd = () => {
        let opt = { label: "", correctIndex: "" };
        setFillOptions([...fillOptions, opt]);
    };

    ////////////////////////////// Dropdwon OPTIONS/ ///////////////////////////////

    const onDropdownOptionChange = (name, value, idx) => {
        let optionData = [...dropdownOptions];

        if (name == "options") {
            optionData[idx]["options"] = value?.split(",");
            optionData[idx]["id"] = `label_${idx}`;
            setDropdownOptions(optionData);
        } else {
            optionData[idx][name] = value;
            optionData[idx]["id"] = `label_${idx}`;
            setDropdownOptions(optionData);
        }
    };

    const RemoveDropdownOption = (idx) => {
        const arr = [...dropdownOptions];
        arr.splice(idx, 1);
        setDropdownOptions(arr);
    };

    const DropdownOptionAdd = () => {
        let opt = { id: "", correct: "", options: [] };
        setDropdownOptions([...dropdownOptions, opt]);
    };

    return (
        <div>
            <Label>Options Creation :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">
                Please define options as per the question&apos;s requirement here.
            </p>
            <div>
                <Label>Select layout of options :-</Label>
                <div className="tw-mt-2 tw-flex tw-gap-2">
                    {LayoutsData?.map((layout, index) => (
                        <div
                            key={index}
                            style={layout?.layout}
                            className={`${false && "layout_selected"} tw-h-[50px] tw-w-[150px] tw-cursor-pointer tw-gap-2 tw-rounded-md tw-border-[1px] tw-border-slate-500 tw-p-2 tw-shadow-sm hover:tw-bg-slate-100`}
                        >
                            {layout?.layout_box?.map((box, idx) => (
                                <div
                                    key={idx}
                                    className="tw-h-full tw-rounded-md tw-border-[1px] tw-border-dashed tw-border-slate-500 tw-p-2 tw-shadow-sm"
                                ></div>
                            ))}
                        </div>
                    ))}
                </div>
            </div>
            {contentData?.componentTypeId == "singlechoice" && (
                <>
                    <div className="tw-mt-5 tw-grid tw-grid-cols-[60px_auto_100px] tw-gap-2">
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Correct</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Option</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                        </div>
                    </div>
                    <div className="tw-mt-3 tw-space-y-3">
                        {options?.map((option, idx) => (
                            <div key={idx} className="tw-grid tw-grid-cols-[60px_auto_100px] tw-items-center tw-gap-2">
                                <div className="tw-text-center">
                                    <Checkbox
                                        className="tw-h-[25px] tw-w-[25px]"
                                        checked={option?.isCorrect}
                                        onCheckedChange={(e) => onOptionChange("isCorrect", e, idx)}
                                    />
                                </div>
                                <div className="">
                                    <Input
                                        id="label"
                                        placeholder="Define option label here"
                                        onChange={(e) => onOptionChange("label", e?.target?.value, idx)}
                                        value={option?.label}
                                    />
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    {options?.length > 1 && (
                                        <Button
                                            variant="outline"
                                            className="tw-rounded-xl"
                                            onClick={() => RemoveOption(idx)}
                                        >
                                            <i className="fa-solid fa-xmark"></i>
                                        </Button>
                                    )}
                                    {options?.length == idx + 1 && (
                                        <Button variant="outline" className="tw-rounded-xl" onClick={OptionAdd}>
                                            <i className="fa-solid fa-plus"></i>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
            {contentData?.componentTypeId == "singlechoice_media" && (
                <>
                    <div className="tw-mt-5 tw-grid tw-grid-cols-[60px_150px_auto_100px] tw-gap-2">
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Correct</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Image</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Option</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                        </div>
                    </div>
                    <div className="tw-mt-3 tw-space-y-3">
                        {options?.map((option, idx) => (
                            <div
                                key={idx}
                                className="tw-grid tw-grid-cols-[60px_150px_auto_100px] tw-items-center tw-gap-2"
                            >
                                <div className="tw-text-center">
                                    <Checkbox
                                        className="tw-h-[25px] tw-w-[25px]"
                                        checked={option?.isCorrect}
                                        onCheckedChange={(e) => onOptionChange("isCorrect", e, idx)}
                                    />
                                </div>
                                <div className="">
                                    <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                        <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                            <img
                                                className="tw-h-full tw-w-full tw-object-cover"
                                                src={option?.src || "/assets/thumbnail-alpha.png"}
                                                alt=""
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-justify-between">
                                            <CircleX
                                                size={18}
                                                className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                onClick={() => onRemoveImage(idx)}
                                            />
                                            <input
                                                type="file"
                                                onChange={(e) => onOptionChange("src", e.target.files[0], idx)}
                                                accept="image/*"
                                                id={`singlechoice_img_${idx}`}
                                                style={{ display: "none" }}
                                            />

                                            <label
                                                htmlFor={`singlechoice_img_${idx}`}
                                                className="tw-flex tw-items-center tw-justify-center"
                                            >
                                                <Badge
                                                    variant="outline"
                                                    className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                >
                                                    <ImagePlus size={16} /> Choose
                                                </Badge>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div className="">
                                    <Input
                                        id="label"
                                        placeholder="Define option label here"
                                        onChange={(e) => onOptionChange("label", e?.target?.value, idx)}
                                        value={option?.label}
                                    />
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    {options?.length > 1 && (
                                        <Button
                                            variant="outline"
                                            className="tw-rounded-xl"
                                            onClick={() => RemoveOption(idx)}
                                        >
                                            <i className="fa-solid fa-xmark"></i>
                                        </Button>
                                    )}
                                    {options?.length == idx + 1 && (
                                        <Button variant="outline" className="tw-rounded-xl" onClick={OptionAdd}>
                                            <i className="fa-solid fa-plus"></i>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
            {contentData?.componentTypeId == "match_the_following" && (
                <>
                    <div className="tw-mt-5 tw-grid tw-grid-cols-[1fr_1fr_100px_100px] tw-gap-2">
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Label</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Option</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Sequence</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                        </div>
                    </div>
                    <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                        {matchOptions?.map((option, idx) => (
                            <div
                                key={idx}
                                className="tw-grid tw-grid-cols-[1fr_1fr_100px_100px] tw-items-center tw-gap-2"
                            >
                                <div className="tw-space-y-1">
                                    <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                        <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                            <img
                                                className="tw-h-full tw-w-full tw-object-contain"
                                                src={option?.imageLabel || "/assets/thumbnail-alpha.png"}
                                                alt=""
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-justify-between">
                                            <CircleX
                                                size={18}
                                                className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                onClick={() => onRemoveMTFImage("imageLabel", idx)}
                                            />
                                            <input
                                                type="file"
                                                onChange={(e) =>
                                                    onMatchOptionChange("imageLabel", e.target.files[0], idx)
                                                }
                                                accept="image/*"
                                                id={`imageLabel_${idx}`}
                                                style={{ display: "none" }}
                                            />

                                            <label
                                                htmlFor={`imageLabel_${idx}`}
                                                className="tw-flex tw-items-center tw-justify-center"
                                            >
                                                <Badge
                                                    variant="outline"
                                                    className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                >
                                                    <ImagePlus size={16} /> Choose
                                                </Badge>
                                            </label>
                                        </div>
                                    </div>
                                    <Input
                                        id="label"
                                        placeholder="Define label here"
                                        onChange={(e) => onMatchOptionChange("label", e?.target?.value, idx)}
                                        value={option?.label}
                                    />
                                </div>
                                <div className="tw-space-y-1">
                                    <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                        <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                            <img
                                                className="tw-h-full tw-w-full tw-object-contain"
                                                src={option?.imageText || "/assets/thumbnail-alpha.png"}
                                                alt=""
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-justify-between">
                                            <CircleX
                                                size={18}
                                                className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                onClick={() => onRemoveMTFImage("imageText", idx)}
                                            />
                                            <input
                                                type="file"
                                                onChange={(e) =>
                                                    onMatchOptionChange("imageText", e.target.files[0], idx)
                                                }
                                                accept="image/*"
                                                id={`imageText_${idx}`}
                                                style={{ display: "none" }}
                                            />

                                            <label
                                                htmlFor={`imageText_${idx}`}
                                                className="tw-flex tw-items-center tw-justify-center"
                                            >
                                                <Badge
                                                    variant="outline"
                                                    className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                >
                                                    <ImagePlus size={16} /> Choose
                                                </Badge>
                                            </label>
                                        </div>
                                    </div>
                                    <Input
                                        id="text"
                                        placeholder="Define text here"
                                        onChange={(e) => onMatchOptionChange("text", e?.target?.value, idx)}
                                        value={option?.text}
                                    />
                                </div>
                                <div className="">
                                    <Input
                                        id="label"
                                        placeholder="Correct"
                                        onChange={(e) => onMatchOptionChange("correctIndex", e?.target?.value, idx)}
                                        value={option?.correctIndex}
                                        type="number"
                                    />
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    {matchOptions?.length > 1 && (
                                        <Button
                                            variant="outline"
                                            className="tw-rounded-xl"
                                            onClick={() => RemoveMatchOption(idx)}
                                        >
                                            <i className="fa-solid fa-xmark"></i>
                                        </Button>
                                    )}
                                    {matchOptions?.length == idx + 1 && (
                                        <Button variant="outline" className="tw-rounded-xl" onClick={MatchOptionAdd}>
                                            <i className="fa-solid fa-plus"></i>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
            {contentData?.componentTypeId == "sequence_arrange" && (
                <>
                    <div className="tw-mt-5 tw-grid tw-grid-cols-[150px_1fr_100px_100px] tw-gap-2">
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Image</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Option</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Sequence</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                        </div>
                    </div>
                    <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                        {matchOptions?.map((option, idx) => (
                            <div
                                key={idx}
                                className="tw-grid tw-grid-cols-[150px_1fr_100px_100px] tw-items-center tw-gap-2"
                            >
                                <div className="tw-space-y-1">
                                    <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                        <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                            <img
                                                className="tw-h-full tw-w-full tw-object-contain"
                                                src={option?.imageText || "/assets/thumbnail-alpha.png"}
                                                alt=""
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-justify-between">
                                            <CircleX
                                                size={18}
                                                className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                onClick={() => onRemoveMTFImage("imageText", idx)}
                                            />
                                            <input
                                                type="file"
                                                onChange={(e) =>
                                                    onMatchOptionChange("imageText", e.target.files[0], idx)
                                                }
                                                accept="image/*"
                                                id={`imageText_${idx}`}
                                                style={{ display: "none" }}
                                            />

                                            <label
                                                htmlFor={`imageText_${idx}`}
                                                className="tw-flex tw-items-center tw-justify-center"
                                            >
                                                <Badge
                                                    variant="outline"
                                                    className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                >
                                                    <ImagePlus size={16} /> Choose
                                                </Badge>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <Input
                                        id="text"
                                        placeholder="Define text here"
                                        onChange={(e) => onMatchOptionChange("text", e?.target?.value, idx)}
                                        value={option?.text}
                                    />
                                </div>
                                <div className="">
                                    <Input
                                        id="label"
                                        placeholder="Correct"
                                        onChange={(e) => onMatchOptionChange("correctIndex", e?.target?.value, idx)}
                                        value={option?.correctIndex}
                                        type="number"
                                    />
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    {matchOptions?.length > 1 && (
                                        <Button
                                            variant="outline"
                                            className="tw-rounded-xl"
                                            onClick={() => RemoveMatchOption(idx)}
                                        >
                                            <i className="fa-solid fa-xmark"></i>
                                        </Button>
                                    )}
                                    {matchOptions?.length == idx + 1 && (
                                        <Button variant="outline" className="tw-rounded-xl" onClick={MatchOptionAdd}>
                                            <i className="fa-solid fa-plus"></i>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
            {contentData?.componentTypeId == "fillin_the_blank" && (
                <>
                    <div className="tw-mt-5 tw-grid tw-grid-cols-[auto_100px_100px] tw-gap-2">
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Option</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Sequence</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                        </div>
                    </div>
                    <div className="tw-mt-3 tw-space-y-3">
                        {fillOptions?.map((option, idx) => (
                            <div key={idx} className="tw-grid tw-grid-cols-[auto_100px_100px] tw-items-center tw-gap-2">
                                <div className="">
                                    <Input
                                        id="label"
                                        placeholder="Define option label here"
                                        onChange={(e) => onFillOptionChange("label", e?.target?.value, idx)}
                                        value={option?.label}
                                    />
                                </div>
                                <div className="">
                                    <Input
                                        id="correctIndex"
                                        placeholder="Correct"
                                        onChange={(e) => onFillOptionChange("correctIndex", e?.target?.value, idx)}
                                        value={option?.correctIndex}
                                        type="number"
                                    />
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    {fillOptions?.length > 1 && (
                                        <Button
                                            variant="outline"
                                            className="tw-rounded-xl"
                                            onClick={() => RemoveFillOption(idx)}
                                        >
                                            <i className="fa-solid fa-xmark"></i>
                                        </Button>
                                    )}
                                    {fillOptions?.length == idx + 1 && (
                                        <Button variant="outline" className="tw-rounded-xl" onClick={FillOptionAdd}>
                                            <i className="fa-solid fa-plus"></i>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}

            {contentData?.componentTypeId == "string_dropdown" && (
                <>
                    <div className="tw-mt-5 tw-grid tw-grid-cols-[auto_120px_100px] tw-gap-2">
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Correct</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Option</h1>
                        </div>
                        <div>
                            <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                        </div>
                    </div>
                    <div className="tw-mt-3 tw-space-y-3">
                        {dropdownOptions?.map((option, idx) => (
                            <div key={idx} className="tw-grid tw-grid-cols-[auto_120px_100px] tw-items-center tw-gap-2">
                                <div className="">
                                    <Input
                                        id="label"
                                        placeholder="Define dropdown options here"
                                        onChange={(e) => onDropdownOptionChange("options", e?.target?.value, idx)}
                                        value={option?.options?.toString()}
                                    />
                                </div>
                                <div>
                                    <SelectNative
                                        id="correct"
                                        onChange={(e) => onDropdownOptionChange("correct", e.target.value, idx)}
                                        value={option?.correct}
                                    >
                                        <option value=""> - Correct - </option>
                                        {option?.options?.map((data) => (
                                            <option key={data} value={data}>
                                                {data}
                                            </option>
                                        ))}
                                    </SelectNative>
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    {dropdownOptions?.length > 1 && (
                                        <Button
                                            variant="outline"
                                            className="tw-rounded-xl"
                                            onClick={() => RemoveDropdownOption(idx)}
                                        >
                                            <i className="fa-solid fa-xmark"></i>
                                        </Button>
                                    )}
                                    {dropdownOptions?.length == idx + 1 && (
                                        <Button variant="outline" className="tw-rounded-xl" onClick={DropdownOptionAdd}>
                                            <i className="fa-solid fa-plus"></i>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
        </div>
    );
};

export default OptionsSettings;
