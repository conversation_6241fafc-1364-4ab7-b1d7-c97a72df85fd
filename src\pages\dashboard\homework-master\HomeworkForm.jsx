import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "sonner";

const defaultValue = {
    course_id: null,
    homework_title: "",
    description: "",
    attachment_url: "",
    submission_date: "",
    homework_points: 0,
    is_assignment: false,
};

const HomeworkForm = ({ open, setOpen, editData, getHomeworkData }) => {
    const params = useParams();
    const today = new Date().toISOString().split("T")[0];
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [courseList, setCourseList] = useState([]);

    const [homework, setHomework] = useState(defaultValue);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setHomework({ ...homework, [name]: value });
        if (name == "attachment_url") {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileUpload(data);
        }
    };

    useEffect(() => {
        getCourses();
    }, []);

    const getCourses = async (payload) => {
        if (localStorage.getItem("is_trainer") == "true") {
            await tanstackApi
                .get("course/list-assigned-courses")
                .then((res) => {
                    setCourseList(res?.data?.data);
                })
                .catch((err) => {
                    setCourseList([]);
                });
        } else {
            await tanstackApi
                .get("course/get-courses")
                .then((res) => {
                    setCourseList(res?.data?.data);
                })
                .catch((err) => {
                    setCourseList([]);
                });
        }
    };

    useEffect(() => {
        if (editData !== null) {
            setHomework({
                course_id: editData?.course_id,
                homework_title: editData?.homework_title,
                description: editData?.description,
                attachment_url: editData?.attachment_url,
                submission_date: editData?.submission_date?.slice(0, 10),
                homework_points: editData?.homework_points,
                is_assignment: false,
            });
        } else {
            setHomework(defaultValue);
        }
    }, [editData]);

    const onClearForm = () => {
        setHomework(defaultValue);
    };

    const RemoveAttachment = () => {
        setHomework({ ...homework, attachment_url: "" });
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setHomework({ ...homework, attachment_url: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setHomework({ ...homework, attachment_url: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setHomework({ ...homework, attachment_url: "" });
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!homework?.homework_title) {
            toast?.warning("Homework title", {
                description: "Homework title is required",
            });
            return false;
        } else if (!homework?.course_id) {
            toast?.warning("Course", {
                description: "Related course selection is required",
            });
            return false;
        } else if (!homework?.submission_date) {
            toast?.warning("Submission date", {
                description: "Homework submission date are required",
            });
            return false;
        } else if (new Date(homework?.submission_date).getTime() < new Date().getTime()) {
            toast?.warning("Invalid Submission date", {
                description: "Homework submission date should be upcoming date.",
            });
            return false;
        } else if (!homework?.homework_points) {
            toast?.warning("Homework points", {
                description: "Homework completion points are required",
            });
            return false;
        }

        if (editData == null) {
            const payload = {
                course_id: homework?.course_id,
                homework_title: homework?.homework_title,
                description: homework?.description || "No Description Yet!",
                attachment_url: homework?.attachment_url || undefined,
                submission_date: homework?.submission_date,
                homework_points: homework?.homework_points,
                is_assignment: false,
                is_public: localStorage.getItem("level") == "levelOne" ? true : false,
            };

            await tanstackApi
                .post("homework/create", { ...payload })
                .then((res) => {
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                    getHomeworkData();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                course_homework_id: editData?.id,
                course_id: homework?.course_id,
                homework_title: homework?.homework_title,
                description: homework?.description || "No Description Yet!",
                attachment_url: homework?.attachment_url || undefined,
                submission_date: homework?.submission_date,
                homework_points: homework?.homework_points,
            };

            await tanstackApi
                .put("homework/update", { ...payload })
                .then((res) => {
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    getHomeworkData();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-[700px]">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">{editData !== null ? "Update" : "Create a new"} homework!</h1>
                        </DialogTitle>
                        <DialogDescription>
                            Fill up below details according to your homework convenience.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="tw-grid tw-grid-cols-[1fr] tw-gap-5">
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="homework_title">
                                        Homework Title <span className="tw-text-red-600">*</span>
                                    </Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={homework?.homework_title}
                                        name="homework_title"
                                        id="homework_title"
                                        placeholder="Enter homework title here"
                                    />
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[1fr_210px_100px] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="category_id">
                                        Courses <span className="tw-text-red-600">*</span>
                                    </Label>
                                    <Select
                                        onValueChange={(e) =>
                                            onChangeHandle({ target: { value: e, name: "course_id" } })
                                        }
                                        value={homework?.course_id}
                                        name="category_id"
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Choose courses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {courseList?.map((data, idx) => (
                                                <SelectItem key={idx} value={data?.id}>
                                                    {data?.course_title}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="tw-space-y-1">
                                    <Label htmlFor="submission_date">
                                        Submission Date <span className="tw-text-red-600">*</span>
                                    </Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={homework?.submission_date}
                                        name="submission_date"
                                        id="submission_date"
                                        type="datetime-local"
                                        min={today}
                                    />
                                </div>
                                <div className="tw-space-y-1">
                                    <Label htmlFor="homework_points">
                                        Points <span className="tw-text-red-600">*</span>
                                    </Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={homework?.homework_points}
                                        name="homework_points"
                                        id="homework_points"
                                        placeholder="Define points here"
                                        type="number"
                                    />
                                </div>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="description">
                                    Description <span className="tw-text-red-600">*</span>
                                </Label>
                                <Textarea
                                    onChange={onChangeHandle}
                                    value={homework?.description}
                                    name="description"
                                    className="tw-text-sm"
                                    placeholder="Enter homework description here."
                                />
                            </div>
                            <div className="tw-mt-2 tw-space-y-3">
                                {homework?.attachment_url && (
                                    <div className="tw-flex tw-flex-col tw-gap-0">
                                        <div>
                                            <i className="fa-solid fa-paperclip"></i>{" "}
                                            <Label>
                                                File URL <span className="tw-text-red-600">*</span>
                                            </Label>
                                        </div>
                                        <a
                                            href={homework?.attachment_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="tw-cursor-pointer tw-text-sm hover:tw-underline"
                                        >
                                            {homework?.attachment_url}
                                        </a>
                                    </div>
                                )}
                                <div>
                                    <div className="tw-mt-2 tw-flex tw-gap-2">
                                        {!homework?.attachment_url && (
                                            <Label
                                                htmlFor="scorm_file"
                                                variant="outline"
                                                className={cn(
                                                    buttonVariants({ variant: "outline" }),
                                                    "aspect-square tw-rounded-xl",
                                                )}
                                            >
                                                <Upload
                                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                                    size={16}
                                                    strokeWidth={2}
                                                    aria-hidden="true"
                                                />
                                                <input
                                                    onChange={onChangeHandle}
                                                    name="attachment_url"
                                                    type="file"
                                                    style={{ display: "none" }}
                                                    id="scorm_file"
                                                />
                                                <div className="max-sm:sr-only">
                                                    {load ? (
                                                        "Uploading"
                                                    ) : (
                                                        <>
                                                            Upload Attachment <small>(Optional)</small>
                                                        </>
                                                    )}{" "}
                                                    {load ? `${uploaded}%` : null}
                                                </div>
                                            </Label>
                                        )}

                                        {homework?.attachment_url && (
                                            <Button
                                                variant="outline"
                                                className="aspect-square tw-rounded-xl"
                                                onClick={RemoveAttachment}
                                            >
                                                <X
                                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                                    size={16}
                                                    strokeWidth={2}
                                                    aria-hidden="true"
                                                />
                                                <Label className="max-sm:sr-only">Remove File</Label>
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <Button type="button" variant="secondary" onClick={onClearForm}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>
                        <Button type="submit" onClick={onDataSubmit}>
                            <i className="fa-solid fa-floppy-disk"></i> Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default HomeworkForm;
