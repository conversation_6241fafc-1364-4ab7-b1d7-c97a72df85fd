import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { tanstackApi } from "@/react-query/api";
import { AddUserPoints } from "@/redux/dashboard/dash/action";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const ReviewCheck = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const params = useParams();
    const [openAlert, setOpenAlert] = useState(false);
    const [database, setDatabase] = useState(null);

    const [submissionDetails, setSubmissionDetails] = useState({
        status: "inprogress",
        marks: null,
        remarks: "",
    });

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setSubmissionDetails({ ...submissionDetails, [name]: value });
    };

    useEffect(() => {
        if (params?.id !== undefined) {
            getSubmissionsData(params?.id);
        }
    }, [params]);

    useEffect(() => {
        setSubmissionDetails({
            status: database?.evaluation_status,
            marks: database?.marks,
            remarks: database?.remarks,
        });
    }, [database]);

    const getSubmissionsData = async (workID) => {
        await tanstackApi
            .post("homework/submissions/get-submissions", {})
            .then((res) => {
                setDatabase(res?.data?.data?.find((dt) => dt?.id == Number(workID)));
            })
            .catch((err) => {
                setDatabase(null);
            });
    };

    const onSubmit = () => {
        setOpenAlert(true);
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!submissionDetails?.marks) {
            toast.warning("Please enter marks");
            return false;
        }

        if (submissionDetails?.marks > database?.lms_course_homework?.homework_points) {
            toast.warning(`Max points are ${database?.lms_course_homework?.homework_points} to be given.`);
            return false;
        }

        if (!submissionDetails?.remarks) {
            toast.warning("Please add some remarks");
            return false;
        }

        let payload = {
            course_homework_submission_id: params?.id,
            status: submissionDetails?.status,
            marks: submissionDetails?.marks,
            remarks: submissionDetails?.remarks,
        };

        await tanstackApi
            .post("homework/submission/review-homework-trainer", { ...payload })
            .then((res) => {
                toast.success("Successfully Remarked");

                punchTimelineLog({
                    user_id: localStorage.getItem("parent_user_id"),
                    event: params?.type?.toLowerCase(),
                    log: `Trainer: ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} reviewed ${params?.type?.toLowerCase()} : ${database?.lms_course_homework?.homework_title} of ${`${database?.lms_user?.first_name} ${database?.lms_user?.last_name}`} successfully.`,
                });

                dispatch(
                    AddUserPoints({
                        user_id: database?.lms_user?.id,
                        points: submissionDetails?.marks,
                        event: `Submit${params?.type}`,
                        entity_id: `${database?.id}`,
                    }),
                );

                navigate("/dashboard/submissions");
            })
            .catch((err) => {
                toast.error("Something went wrong!");
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    const onDownloadSubmissionAttachment = () => {
        const url = database?.submission_attachment;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.target = "_blank";
        a.download = `${database?.submission_attachment}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    const onDownloadAttachment = () => {
        const url = database?.lms_course_homework?.attachment_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.target = "_blank";
        a.download = `${database?.lms_course_homework?.attachment_url}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    return (
        <div className="">
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Checked & confirmed, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible given points will assign to learner&apos;s account.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Yes Checked!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="pageHeader">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/submissions-learner">
                                Learner&apos;s Submission
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Review {params?.type} & leave remarks for learner </BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Link to={"/dashboard/submissions"}>
                    <Button className="">
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                </Link>
            </div>
            <div className="content_body_homework">
                <div className="homework_title">
                    <h1>
                        <i className="fa-solid fa-laptop-file"></i> {database?.lms_course_homework?.homework_title}
                    </h1>
                </div>
                <div className="homework_description">
                    <p>{database?.lms_course_homework?.description}</p>
                </div>
                <div className="homework_details">
                    <div>
                        <p>
                            <i className="fa-solid fa-dice-d6"></i> Points
                        </p>
                        <h4>{database?.lms_course_homework?.homework_points}</h4>
                    </div>
                    <div>
                        <p>
                            {" "}
                            <i className="fa-solid fa-clock"></i> Submission
                        </p>
                        <h4>{moment(database?.lms_course_homework?.submission_date).format("LLL")}</h4>
                    </div>
                    <div>
                        <p>
                            <i className="fa-solid fa-paperclip"></i> Attachments
                        </p>
                        <h4>
                            <button onClick={onDownloadAttachment}>
                                <i className="fa-solid fa-download"></i> Download
                            </button>
                        </h4>
                    </div>
                </div>
            </div>
            <div className="subission_details">
                <div className="tw-mb-4 tw-flex tw-items-center tw-justify-between tw-border-b-[1px] tw-border-dashed tw-pb-3">
                    <h1 className="tw-text-xl tw-font-medium">
                        <i className="fa-regular fa-circle-user"></i>{" "}
                        {`${database?.lms_user?.first_name} ${database?.lms_user?.last_name}`}
                    </h1>
                    <span className="tw-text-md tw-font-mono">
                        Submitted on <strong>{moment(database?.created_at).format("LLL")}</strong>
                    </span>
                </div>
                <h5>#. Submission by user :-</h5>
                <p>{database?.submission}</p>
                <br />
                <h5>#. Submission Attachment:-</h5>
                <button onClick={onDownloadSubmissionAttachment}>
                    <i className="fa-solid fa-download"></i> Download Attached File
                </button>
                <br />
                <br />
                <h5>
                    #. Evaluation Status: <b>{database?.evaluation_status}</b>
                </h5>
            </div>
            <div className="review_check">
                <div className="tw-grid tw-grid-cols-[200px_200px_auto] tw-gap-5">
                    <div className="tw-space-y-1">
                        <Label htmlFor="">Points</Label>
                        <Input
                            className=""
                            onChange={onHandleChange}
                            value={submissionDetails?.marks}
                            name="marks"
                            type="number"
                            placeholder="Give marks here"
                        />
                    </div>{" "}
                    <div className="tw-space-y-1">
                        <Label htmlFor="">Result</Label>
                        <Select
                            name="status"
                            onValueChange={(e) => onHandleChange({ target: { value: e, name: "status" } })}
                            value={submissionDetails?.status}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="pass">Passed</SelectItem>
                                <SelectItem value="fail">Failed</SelectItem>
                                <SelectItem value="inprogress">Inprogress</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <div className="">
                    <div className="tw-space-y-1">
                        <Label htmlFor="">Remarks & Comments</Label>
                        <Textarea
                            placeholder="Enter your remarks & comments here"
                            onChange={onHandleChange}
                            value={submissionDetails?.remarks}
                            name="remarks"
                            rows={5}
                        ></Textarea>
                    </div>
                </div>
                <div className="review_controls">
                    <button onClick={onSubmit}>
                        <i className="fa-solid fa-check"></i> Check & Send Response
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ReviewCheck;
