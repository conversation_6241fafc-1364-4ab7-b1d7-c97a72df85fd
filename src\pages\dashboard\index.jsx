import DashboardLearner from "@/pages/dashboard/learner";
import DomainDashboard from "./dash-master/DomainDashboard";
import SystemDashboard from "./SystemDashboard";
import TrainerDashboard from "./trainer";
import { useGetCertificate } from "@/react-query/certificate";

export default function DashboardPage() {
    return (
        <>
            {localStorage.getItem("level") == "levelThree" ? (
                <>{localStorage.getItem("is_trainer") == "true" ? <TrainerDashboard /> : <DashboardLearner />}</>
            ) : localStorage.getItem("level") == "levelTwo" ? (
                <DomainDashboard />
            ) : (
                <SystemDashboard />
            )}
        </>
    );
}
