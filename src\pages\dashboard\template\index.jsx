import Pagination from "@/components/table/pagination";
import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { useDeleteTemplate, useGetTemplate } from "@/react-query/quizz/template";
import { Trash } from "lucide-react";
import { Loader2 } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const ITEMS_PER_PAGE = 10;

export default function Templates() {
    const [totalPages, setTotalPages] = useState(0);
    const [searchParams] = useSearchParams();
    const loginToken = localStorage.getItem("login_token");
    const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get("page") || "1", 10));
    const offset = (currentPage - 1) * ITEMS_PER_PAGE;
    const limit = ITEMS_PER_PAGE;
    const templates = useGetTemplate({
        limit,
        offset,
        is_public: localStorage.getItem("level") == "levelOne" ? true : false,
    });

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (templates.isSuccess) setTotalPages(Math.ceil(templates.data.pagination.total / ITEMS_PER_PAGE));
    }, [templates]);

    return (
        <>
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-book"></i> Templates
                </h4>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button className="tw-px-2 tw-py-1">
                            <i className="fa-solid fa-plus"></i> Create New
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem asChild>
                            <Link
                                to={`https://lms-course-builder.vercel.app/dashboard/templates/create?type=course&token=${loginToken}`}
                                target="_blank"
                                className="tw-cursor-pointer"
                            >
                                Course
                            </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                            <Link
                                to={`https://lms-course-builder.vercel.app/dashboard/templates/create?type=quiz&token=${loginToken}`}
                                target="_blank"
                                className="tw-cursor-pointer"
                            >
                                Quizz
                            </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                            <Link
                                to={`https://lms-course-builder.vercel.app/dashboard/templates/create?type=interaction&token=${loginToken}`}
                                target="_blank"
                                className="tw-cursor-pointer"
                            >
                                Interactions
                            </Link>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
            <br />
            <div className="custom_table tw-mt-2 tw-h-full">
                <table>
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Creation Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {templates.isLoading && (
                            <tr>
                                <td colSpan={5}>Loading...</td>
                            </tr>
                        )}
                        {templates.data?.data?.map((row, idx) => (
                            <tr key={idx} className="!tw-font-lexend">
                                <td>{row?.template_title}</td>
                                <td>{row?.template_type}</td>
                                <td>{row?.is_draft ? "Draft" : "Active"}</td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td>
                                    <div className="tw-flex tw-gap-2">
                                        <Link
                                            to={`https://lms-course-builder.vercel.app/dashboard/templates/${row?.id}`}
                                            target="_blank"
                                        >
                                            <button className="selected_btn_alpha">
                                                <i className="fa-solid fa-pen-to-square"></i> Edit
                                            </button>
                                        </Link>
                                        <DeleteTemplate id={row?.id} title={row?.template_title} />
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                    itemsPerPage={ITEMS_PER_PAGE}
                />
            </div>
        </>
    );
}

function DeleteTemplate({ id, title }) {
    const deleteFn = useDeleteTemplate();
    const Icon = deleteFn.isPending ? Loader2 : Trash;
    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <button className="selected_btn tw-flex tw-items-center tw-gap-1 tw-font-lexend">
                    <Trash className="tw-size-4" /> Delete
                </button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                        disabled={deleteFn.isPending}
                        onClick={() =>
                            deleteFn.mutate(
                                { id, title },
                                {
                                    onSuccess: (data) => {
                                        toast.success(data.message ?? "Template deleted successfully");
                                    },
                                    onError: (e) => {
                                        toast.error(e.response.data.message ?? "Something went wrong");
                                    },
                                },
                            )
                        }
                    >
                        <Icon className={cn("tw-size-4", deleteFn.isPending && "tw-animate-spin")} /> Delete
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
