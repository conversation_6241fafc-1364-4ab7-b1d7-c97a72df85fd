import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>readcrumb<PERSON><PERSON>,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import AddBundlesToGroup from "./AddBundlesToGroup";
import AddCoursesToGroup from "./AddCoursesToGroup";
import AddUsersToGroup from "./AddUsersToGroup";
import Details from "./Details";

const CreateGroupForm = () => {
    const params = useParams();
    const router = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const [userGroupData, setUserGroupData] = useState(null);

    useEffect(() => {
        if (params?.group_id !== undefined) {
            getUserGroups(params?.group_id);
        }
    }, [params]);

    const getUserGroups = async (group_id) => {
        await tanstackApi
            .post(`user-groups/list`)
            .then((res) => {
                setUserGroupData(res?.data?.data?.find((dt) => dt?.id == Number(group_id)));
            })
            .catch((err) => {
                setUserGroupData(null);
            });
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/user-group-master">User Groups</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{params?.group_id ? "Edit" : "Create new"} group</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button className="tw-px-2 tw-py-1" onClick={() => router(`/dashboard/user-group-master`)}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
            </div>
            <Tabs
                defaultValue={searchParams.get("tab") ?? "basic_details"}
                onValueChange={(e) => setSearchParams({ tab: e })}
                className=""
            >
                {params?.group_id !== undefined && (
                    <TabsList className="tw-grid tw-w-[570px] tw-grid-cols-4">
                        <TabsTrigger value="basic_details" className="tw-gap-2">
                            <i className="fa-solid fa-circle-info"></i> Basic Details
                        </TabsTrigger>
                        <TabsTrigger value="add_users" className="tw-gap-2">
                            <i className="fa-solid fa-user"></i> Assign Users
                        </TabsTrigger>
                        <TabsTrigger value="add_courses" className="tw-gap-2">
                            <i className="fa-solid fa-book"></i> Assign Courses
                        </TabsTrigger>
                        <TabsTrigger value="add_bundles" className="tw-gap-2">
                            <i className="fa-solid fa-box-open"></i> Assign Bundles
                        </TabsTrigger>
                    </TabsList>
                )}
                <TabsContent value="basic_details">
                    <Details userGroupData={userGroupData} getUserGroups={getUserGroups} />
                </TabsContent>
                <TabsContent value="add_users">
                    <AddUsersToGroup userGroupData={userGroupData} getUserGroups={getUserGroups} />
                </TabsContent>
                <TabsContent value="add_courses">
                    <AddCoursesToGroup userGroupData={userGroupData} getUserGroups={getUserGroups} />
                </TabsContent>
                <TabsContent value="add_bundles">
                    <AddBundlesToGroup userGroupData={userGroupData} getUserGroups={getUserGroups} />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default CreateGroupForm;
