import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import { useAddTimelineLog } from "@/react-query/common/timeline";
import { useGetCoursesBundle } from "@/react-query/courses";
import { useGetUserGroupsCourse, useUserGroupAssignCourse } from "@/react-query/user-group";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const AddBundlesToGroup = ({ userGroupData, getUserGroups }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedBundles, setSelectedBundles] = useState([]);
    const punchTimelineLog = useAddTimelineLog();
    const bundleListData = useGetCoursesBundle();
    const assignCourses = useUserGroupAssignCourse();
    const groupCourseData = useGetUserGroupsCourse({ group_id: params?.group_id });

    useEffect(() => {
        if (groupCourseData.isSuccess) {
            setSelectedBundles(
                groupCourseData.data.data
                    ?.filter((dt) => dt?.course_bundle_id !== null)
                    .map((dt) => dt?.course_bundle_id) || [],
            );
        }
    }, [groupCourseData.status]);

    const onCourseSelection = (item) => {
        if (selectedBundles?.includes(item)) {
            setSelectedBundles(selectedBundles?.filter((dt) => dt !== item));
        } else {
            setSelectedBundles([...selectedBundles, item]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (params?.group_id) {
            const payload = {
                group_id: params?.group_id,
                course_bundle_ids: selectedBundles,
            };
            assignCourses.mutate(payload, {
                onSuccess: (data) => {
                    punchTimelineLog.mutate({
                        user_id: localStorage.getItem("userId"),
                        event: "user groups",
                        log: `${selectedBundles?.length} bundles added to ${userGroupData?.name} successfully.`,
                    });
                    toast.success("Updated Successfully", {
                        description: data?.data?.message,
                    });
                    setOpenAlert(false);
                },
                onError: (err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                },
            });
        }
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected bundles in your group&apos;s details.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Select bundles for group</CardTitle>
                            <CardDescription>
                                Click on select button to select any bundle in group. Click save when you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{selectedBundles?.length} Bundle Selected</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Logo</th>
                                    <th>Bundle Name</th>
                                    <th>Course</th>
                                    <th>Created On</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {bundleListData.data?.data?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td
                                            style={{ width: "130px" }}
                                            onClick={() => navigate(`/dashboard/course-bundle-create/${row?.id}`)}
                                        >
                                            <div className="table_image_alpha">
                                                <img
                                                    src={row?.logo_image_url || "/assets/course-placeholder.png"}
                                                    alt=""
                                                />
                                            </div>
                                        </td>
                                        <td onClick={() => navigate(`/dashboard/course-bundle-create/${row?.id}`)}>
                                            {row?.name}
                                        </td>
                                        <td>{row?.is_courses_added} Courses</td>
                                        <td>{moment(row?.createdAt).format("LL")}</td>
                                        <td>
                                            {selectedBundles?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        <Button onClick={() => setOpenAlert(true)}>
                            <i className="fa-solid fa-floppy-disk"></i> Update
                        </Button>
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AddBundlesToGroup;
