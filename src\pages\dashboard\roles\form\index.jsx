import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { useCreateRole, useUpdateRole } from "@/react-query/roles";
import { AxiosError } from "axios";
import { useEffect, useId, useState } from "react";
import { toast } from "sonner";

const defaultValue = {
    display_name: "",
    is_active: true,
};

export default function RoleForm({ open, setOpen, selected }) {
    const updateRole = useUpdateRole();
    const createRole = useCreateRole();
    const formId = useId();

    const [data, setData] = useState(defaultValue);

    useEffect(() => {
        if (selected) {
            setData({
                display_name: selected.display_name,
                is_active: <PERSON><PERSON><PERSON>(selected.is_active),
                id: selected.id,
            });
        } else {
            setData(defaultValue);
        }
    }, [selected]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            const formData = new FormData(e.target);
            const payload = Object.fromEntries(formData.entries());

            if (selected) {
                await updateRole.mutateAsync(payload);
                toast.success("Role Updated Successfully");
            } else {
                if (payload.id) delete payload.id;
                await createRole.mutateAsync(payload);
                toast.success("Role Created Successfully");
            }

            setData(defaultValue);
            setOpen(false);
        } catch (error) {
            toast.error("Something went wrong", {
                description: error instanceof AxiosError ? error.response.data.message : error.message,
            });
        }
    };

    const onChangeHandle = (e) => {
        setData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="tw-z-[1000]">
                <DialogHeader>
                    <DialogTitle>{selected ? "Edit Role" : "Create Role"}</DialogTitle>
                </DialogHeader>
                <div>
                    <form id={formId} onSubmit={handleSubmit} className="tw-space-y-3">
                        <Input type="hidden" name="id" id="id" value={data?.id} />

                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="display_name">Role Name</Label>
                            <Input
                                type="text"
                                name="display_name"
                                value={data?.display_name}
                                onChange={onChangeHandle}
                                id="display_name"
                                placeholder="Role Name"
                            />
                        </div>
                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="is_active">Status</Label>
                            <SelectNative
                                name="is_active"
                                id="is_active"
                                value={data?.is_active}
                                onChange={onChangeHandle}
                                placeholder="Role Name"
                            >
                                <option value="true">Active</option>
                                <option value="false">In Active</option>
                            </SelectNative>
                        </div>
                    </form>
                </div>
                <DialogFooter>
                    <Button variant="secondary" onClick={() => setOpen(false)}>
                        Cancel
                    </Button>
                    <Button variant="success" form={formId} type="submit">
                        Save
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
