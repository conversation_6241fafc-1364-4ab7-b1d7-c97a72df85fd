import AlertSnackbar from "@/components/alert";
import { CONSTANTS } from "@/constants";
import { useSignup } from "@/react-query/auth/signup";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { checkDomainAvaiable, onUserSignUp } from "@/redux/auth/action";
import axios from "axios";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { z } from "zod";

const signUpSchema = z
    .object({
        signUpType: z.enum(["Learner", "Organisation"]).default("Learner"),
        domainName: z.string().optional(),
        firstName: z.string().min(1, "First Name Required").max(20, "Max. 20 Characters Allowed"),
        lastName: z.string().min(1, "Last Name Required").max(15, "Max. 15 Characters Allowed"),
        email: z
            .string()
            .min(1, "User Email Required")
            .max(30, "Max. 30 Characters Email Allowed")
            .email("Please Enter a Valid Email"),
        password: z.string().min(1, "Password Required").min(8, "Enter password with at least 8 characters"),
        confirmPassword: z.string().min(1, "Re-Password Required"),
        organization_name: z.string().optional(),
        job_title: z.string().optional(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Both Passwords should be Same",
        path: ["confirmPassword"],
    })
    .refine(
        (data) => {
            if (data.domainName === "" && data.signUpType === "Organisation") return false;
            return true;
        },
        {
            message: "Domain Name Required",
            path: ["domainName"],
        },
    );

const SignUpPage = () => {
    const dispatch = useDispatch();
    const signupFn = useSignup();
    const [terms, setTerms] = useState("not_accepted");
    const [TermsData, setTermsData] = useState(null);
    const domainHolder = useSelector((state) => state.AuthReducer?.domainHolder);
    const AlertInfo = useSelector((state) => state.alertReducer)?.alertInfoObj;
    const [pwd, setPwd] = useState("password");
    const [confirmPwd, setConfirmPwd] = useState("password");
    const [signUpData, setSignUpData] = useState({
        domainName: "pepsico-12",
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        confirmPassword: "",
        organization_name: "",
        job_title: "",
    });
    const [error, setError] = useState({});

    const [isVerified, setIsVarified] = useState(false);

    const [signUpType, setSignUpType] = useState("Learner");

    const [adminTerms, setAdminTerms] = useState(null);

    useEffect(() => {
        onDomainVerification();
    }, []);

    useEffect(() => {
        localStorage.setItem("signUpAs", "Learner");
        axios.get(`${CONSTANTS.getAPI()}signup/terms-and-conditions`).then((res) => {
            setAdminTerms(res.data.termsAndCondition.term_n_conditions);
        });
    }, []);

    const onDomainVerification = () => {
        if (signUpData.domainName == "") {
            dispatch(AlertSnackInfo({ message: "Enter Domain Name Please", result: false }));
            return false;
        }
        const domain = { name: signUpData.domainName };
        dispatch(checkDomainAvaiable(domain));
    };

    useEffect(() => {
        if (Object.keys(domainHolder).length > 0) {
            if (domainHolder.orgDetails !== null && signUpType === "Learner") {
                setIsVarified(true);
                dispatch(
                    AlertSnackInfo({
                        message: "Domain Verified Successfully",
                        result: true,
                    }),
                );
            } else if (domainHolder.orgDetails == null && signUpType === "Organisation") {
                setIsVarified(true);
                dispatch(
                    AlertSnackInfo({
                        message: domainHolder.message,
                        result: domainHolder.success,
                    }),
                );
            } else {
                setIsVarified(false);
                dispatch(
                    AlertSnackInfo({
                        message: domainHolder.message,
                        result: domainHolder.success,
                    }),
                );
            }
        }
    }, [domainHolder]);

    useEffect(() => {
        if (signUpType === "Learner") setIsVarified(true);
    }, [signUpType]);

    useEffect(() => {
        if (Object.keys(domainHolder).length > 0) {
            var TerData = domainHolder?.orgDetails?.organizationDetails?.term_n_conditions;
            setTermsData(TerData);
        }
    }, [domainHolder]);

    const onTermsRedirection = (e) => {
        if (TermsData !== null) {
            if (TermsData?.dataType === "PDF") {
                window.open(TermsData?.fileUrl, "_blank");
            } else if (TermsData?.dataType === "HTML") {
                localStorage.setItem("terms___conditions", TermsData?.htmlContent);
                window.open("/terms-conditions-user", "_blank");
            }
        }

        if (adminTerms !== null && (TermsData === null || TermsData === undefined)) {
            if (adminTerms?.dataType === "PDF") {
                window.open(adminTerms?.fileUrl, "_blank");
            } else if (adminTerms?.dataType === "HTML") {
                localStorage.setItem("terms___conditions", adminTerms?.htmlContent);
                window.open("/terms-conditions-user", "_blank");
            }
        }
    };

    const handleSignUp = () => {
        if (isVerified !== true) {
            dispatch(AlertSnackInfo({ message: "Please Verify Domain first", result: false }));
            return false;
        }

        const parsedData = signUpSchema.safeParse(signUpData);

        if (!parsedData.success) dispatch(AlertSnackInfo({ message: parsedData.error.message, result: false }));

        if (terms !== "accepted") {
            dispatch(AlertSnackInfo({ message: "Please accept Terms & Conditions", result: false }));
            return false;
        }

        localStorage.setItem("username", parsedData.data.email);
        localStorage.setItem("password", parsedData.data.password);

        if (signUpType == "Learner") {
            const payload = {
                type: "learner",
                data: {
                    role: "learner",
                    first_name: parsedData.data.firstName,
                    last_name: parsedData.data.lastName,
                    email: parsedData.data.email,
                    organization_name: parsedData.data.organization_name,
                    job_title: parsedData.data.job_title,
                    password: parsedData.data.password,
                    domain_name: "",
                    parent_user_id: Object.keys(domainHolder).length > 0 ? domainHolder?.orgDetails?.id : null,
                },
            };
            signupFn.mutate(payload);
        } else {
            localStorage.setItem("signUpAs", "organisation");
            const data = {
                type: "organisation",
                Data: {
                    role: "organization",
                    first_name: signUpData.firstName,
                    last_name: signUpData.lastName,
                    email: signUpData.email,
                    password: signUpData.password,
                    domain_name: signUpData.domainName,
                },
            };

            const domainValidity = localStorage.getItem("domain_validity");
            if (domainValidity) {
                // dispatch(onUserSignUp(data));
                signupFn.mutate(data);
            } else {
                return null;
            }
        }
    };

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setSignUpData({ ...signUpData, [name]: value });
    };

    return (
        <div className="sign_in_alpha">
            <AlertSnackbar AlertInfo={AlertInfo} />
            <header className="alpha_header">
                <div className="left">
                    <img src="/assets/pepsico.webp" alt="" />
                </div>
                <div className="right">
                    <div className="action_cta">
                        <button className="cta_active">Sign up</button>
                        <Link to="/">
                            <button>Sign in</button>
                        </Link>
                    </div>
                </div>
            </header>
            <div className="middle_section_signUP">
                <div className="login_form">
                    <h2>Create your account</h2>
                    <p>
                        Hey, Enter your details to sign up <br /> to your account
                    </p>
                    <div className="form_wrapper">
                        {/* <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type="text"
                                    onInput={() => {
                                        setError({ ...error, domainName: "" });
                                    }}
                                    name="domainName"
                                    placeholder="Enter Domain Name"
                                    onChange={onHandleChange}
                                />
                                <small className="verify_btn" onClick={onDomainVerification}>
                                    Verify
                                </small>
                                {error.domainName && (
                                    <p
                                        className={signUpType == "Learner" ? "main-text" : "main-error-text"}
                                        style={{ marginTop: "0.5rem", color: "green !important" }}
                                    >
                                        &nbsp;&nbsp;{error.domainName}
                                    </p>
                                )}
                            </div>
                        </div> */}
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type="text"
                                    name="organization_name"
                                    onChange={onHandleChange}
                                    placeholder="Organization Name"
                                />
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type="text"
                                    name="job_title"
                                    onChange={onHandleChange}
                                    placeholder="Job title / Current occupation"
                                />
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type="text"
                                    onInput={() => {
                                        setError({ ...error, firstName: "" });
                                    }}
                                    name="firstName"
                                    placeholder="First name"
                                    onChange={onHandleChange}
                                />
                                {error.firstName && (
                                    <p className="main-error-text" style={{ marginTop: "0.5rem" }}>
                                        &nbsp;&nbsp;{error.firstName}
                                    </p>
                                )}
                                {error.lastName && !error.firstName && (
                                    <p className="main-error-text" style={{ marginTop: "0.5rem" }}>
                                        &nbsp;&nbsp;
                                    </p>
                                )}
                            </div>
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type="text"
                                    onInput={() => {
                                        setError({ ...error, lastName: "" });
                                    }}
                                    name="lastName"
                                    placeholder="Last name"
                                    onChange={onHandleChange}
                                />
                                {error.lastName && (
                                    <p className="main-error-text" style={{ marginTop: "0.5rem" }}>
                                        &nbsp;&nbsp;{error.lastName}
                                    </p>
                                )}
                                {!error.lastName && error.firstName && (
                                    <p className="main-error-text" style={{ marginTop: "0.5rem" }}>
                                        &nbsp;&nbsp;
                                    </p>
                                )}
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type="email"
                                    name="email"
                                    onInput={() => {
                                        setError({ ...error, email: "" });
                                    }}
                                    placeholder="Work Email"
                                    onChange={onHandleChange}
                                />
                                {error.email && (
                                    <p className="main-error-text" style={{ marginTop: "0.5rem" }}>
                                        &nbsp;&nbsp;{error.email}
                                    </p>
                                )}
                            </div>
                        </div>

                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type={pwd}
                                    onInput={() => {
                                        setError({ ...error, password: "" });
                                    }}
                                    name="password"
                                    placeholder="Passcode"
                                    onChange={onHandleChange}
                                />
                                <small
                                    onClick={() => {
                                        setPwd(pwd === "password" ? "text" : "password");
                                    }}
                                >
                                    {pwd === "password" ? "Show" : "Hide"}
                                </small>
                                {error.password && (
                                    <p className="main-error-text" style={{ marginTop: "0.5rem" }}>
                                        &nbsp;&nbsp;{error.password}
                                    </p>
                                )}
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    onInput={() => {
                                        setError({ ...error, confirmPassword: "" });
                                    }}
                                    name="confirmPassword"
                                    onChange={onHandleChange}
                                    size="large"
                                    type={confirmPwd}
                                    placeholder="Confirm Passcode"
                                />
                                <small
                                    onClick={() => {
                                        setConfirmPwd(confirmPwd === "password" ? "text" : "password");
                                    }}
                                >
                                    {confirmPwd === "password" ? "Show" : "Hide"}
                                </small>
                                {error.confirmPassword && (
                                    <p className="main-error-text" style={{ marginTop: "0.5rem" }}>
                                        &nbsp;&nbsp;{error.confirmPassword}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                    <small>
                        <input
                            style={{ cursor: "pointer" }}
                            type="checkbox"
                            onChange={(e) => setTerms(terms === "accepted" ? "not_accepted" : "accepted")}
                            checked={terms === "accepted" ? true : false}
                            id="terms_conditions"
                        />{" "}
                        <label htmlFor="terms_conditions">I agree with</label>{" "}
                        <b style={{ cursor: "pointer" }} onClick={onTermsRedirection}>
                            Terms & Condtions
                        </b>
                    </small>
                    <br />
                    <button onClick={handleSignUp}>Sign up as {signUpType}</button>
                    <div className="sign_up_link">
                        Already have an account? <Link to="/">Login</Link>
                    </div>
                </div>
            </div>
            <div className="alpha_footer">PEPSICO @ 2025 Created by The Infowarehouse.</div>
        </div>
    );
};

export default SignUpPage;
