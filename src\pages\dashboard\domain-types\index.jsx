import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Bread<PERSON>rumb<PERSON><PERSON>,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import DomainTypeAddEdit from "./DomainTypeAddEdit";

const DomainTypeMaster = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const [open, setOpen] = useState(false);
    const [editData, setEditData] = useState(null);
    const [DataList, setDataList] = useState([]);

    useEffect(() => {
        getDomainTypes();
    }, []);

    const getDomainTypes = async () => {
        await tanstackApi
            .get("domain-type/all")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const onAddNewType = () => {
        setEditData(null);
        setOpen(true);
    };

    const onEditType = (data) => {
        setEditData(data);
        setOpen(true);
    };

    return (
        <div>
            <DomainTypeAddEdit open={open} setOpen={setOpen} editData={editData} getDomainTypes={getDomainTypes} />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Domain Types</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button className="tw-px-2 tw-py-1" onClick={onAddNewType}>
                    <i className="fa-solid fa-plus"></i> New Domain Type
                </Button>
            </div>
            <div className="tw-mx-5 tw-my-5 tw-flex tw-flex-wrap tw-gap-7">
                {" "}
                {DataList?.map((type, index) => (
                    <div
                        key={index}
                        className="tw-w-[220px] tw-rounded-2xl tw-border-[1px] tw-p-1 tw-pt-3 hover:tw-bg-yellow-50"
                    >
                        <div className="tw-p-2">
                            <div className="tw-flex tw-flex-col tw-items-center tw-gap-4">
                                <div>
                                    <img
                                        src={type?.icon || "/assets/thumbnail-square.png"}
                                        className="tw-h-[100px]"
                                        alt=""
                                    />
                                </div>
                                <h2 className="tw-leading-2 tw-line-clamp-2 tw-font-mono tw-text-lg tw-font-semibold tw-capitalize">
                                    {type?.display_name}
                                </h2>
                            </div>
                            <div className="tw-mt-3 tw-grid tw-grid-cols-[1fr_40px] tw-gap-2">
                                <Button
                                    variant="outline"
                                    className="tw-rounded-xl"
                                    onClick={() => navigate(`/dashboard/domain-type-create/${type?.type}`)}
                                >
                                    <i className="fa-solid fa-check-double"></i> Assign Data
                                </Button>
                                <Button variant="outline" className="tw-rounded-xl" onClick={() => onEditType(type)}>
                                    <i className="fa-solid fa-edit"></i>
                                </Button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default DomainTypeMaster;
