import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { fetchNotificationsReq, getNotificationList } from "@/redux/notification/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* GetAllNotifications(role_code) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `notification/list-all`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data.data)
        .catch((err) => err.response.data);

    if (data) {
        yield put(getNotificationList(data));
    } else {
        yield put(getNotificationList([]));
    }
}

function* ReadNotifications(notify) {
    const data = yield axios
        .put(CONSTANTS.getAPI() + `notification/read-one`, notify.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data.data)
        .catch((err) => err.response.data);

    if (data) {
        yield put(fetchNotificationsReq());
    } else {
        yield put(fetchNotificationsReq());
    }
}

export function* NotificationsWatcher() {
    yield takeEvery(actions.FETCH_NOTIFICATION_REQ, GetAllNotifications);
    yield takeEvery(actions.READ_NOTIFICATION, ReadNotifications);
}

export default function* NotificationSaga() {
    yield all([fork(NotificationsWatcher)]);
}
