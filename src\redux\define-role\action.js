import { ADD_DEFINE_ROLES, ADD_GOALS, ADD_USER_ABOUT } from "@/redux-types";

export const AddDefineRole = (roleData) => {
    return {
        type: ADD_DEFINE_ROLES,
        payload: roleData,
    };
};

export const AddGoals = (goalData) => {
    return {
        type: ADD_GOALS,
        payload: goalData,
    };
};

export const AddUserAbout = (userAboutData) => {
    return {
        type: ADD_USER_ABOUT,
        payload: userAboutData,
    };
};
