import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const AddUsersToSubGroup = ({ groupTeamData, getGroupTeams }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [usersList, setUsersList] = useState([]);
    const [groupUsersList, setGroupUsersList] = useState([]);
    const [subGroupUsersList, setSubGroupUsersList] = useState([]);

    const [openAlert, setOpenAlert] = useState(false);

    const [selectedUsers, setSelectedUsers] = useState([]);

    useEffect(() => {
        if (params?.group_id !== undefined) {
            getGroupUsers({ group_id: params?.group_id });
        }

        if (params?.subgroup_id !== undefined) {
            getSubGroupUsers({ subgroup_id: params?.subgroup_id });
        }
    }, [params]);

    const getGroupUsers = async (payload) => {
        await tanstackApi
            .post("user-groups/users/list", { ...payload })
            .then((res) => {
                setGroupUsersList(res?.data?.data);
            })
            .catch((err) => {
                setGroupUsersList([]);
            });
    };

    const getSubGroupUsers = async (payload) => {
        await tanstackApi
            .post("user-subgroup/list-users", { ...payload })
            .then((res) => {
                setSubGroupUsersList(res?.data?.data);
            })
            .catch((err) => {
                setSubGroupUsersList([]);
            });
    };

    useEffect(() => {
        if (subGroupUsersList?.length > 0) {
            setSelectedUsers(subGroupUsersList?.map((dt) => dt?.user_id) || []);
        }
    }, [subGroupUsersList]);

    const onUsersSelect = (userId) => {
        if (selectedUsers?.includes(userId)) {
            setSelectedUsers(selectedUsers?.filter((dt) => dt !== userId));
        } else {
            if (selectedUsers?.length < groupTeamData?.user_restriction) {
                setSelectedUsers([...selectedUsers, userId]);
            } else {
                toast?.warning(`Max Limit is ${groupTeamData?.user_restriction}`, {
                    description: "Max users limit exceeded",
                });
            }
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (selectedUsers?.length == 0) {
            toast?.warning("Members", {
                description: "Please select some members for team.",
            });
            return false;
        }

        let options = subGroupUsersList?.map((dt) => dt?.user_id) || [];
        let removeData = options?.filter((item) => !selectedUsers?.includes(item));

        const payload = {
            subgroup_id: params?.subgroup_id,
            user_ids: selectedUsers,
        };
        // dispatch(AssignUsersSubGroup(payload));
        await tanstackApi
            .post("user-subgroup/add-users", { ...payload })
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("userId"),
                    event: "teams",
                    log: `${selectedUsers?.length} members added to ${groupTeamData?.name} successfully.`,
                });
                toast.success(`${selectedUsers?.length} Members Added`, {
                    description: res?.data?.message,
                });
                setOpenAlert(false);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });

        if (removeData?.length > 0) {
            const payloadRemove = {
                subgroup_id: params?.subgroup_id,
                user_ids: removeData || [],
            };
            await tanstackApi
                .post("user-subgroup/remove-users", { ...payloadRemove })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "teams",
                        log: `${removeData?.length} members removed to ${groupTeamData?.name} successfully.`,
                    });
                    toast.success(`${removeData?.length} Members Removed`, {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    // getGroupTeams(params?.group_id);
                    // navigate(`/dashboard/course-bundle`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
        // onCloseModal();
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Add Members, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected members in your Team&apos;s details.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Select users for Team</CardTitle>
                            <CardDescription>
                                Click on select button to select any user in Team. Click save when you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">
                                {selectedUsers?.length}/{groupTeamData?.user_restriction} User Selected
                            </p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Profile</th>
                                    <th>User Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {groupUsersList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td style={{ width: "70px" }}>
                                            <Avatar>
                                                <AvatarImage src={row.picture_url} />
                                                <AvatarFallback>
                                                    {row.first_name[0]}
                                                    {row.last_name[0]}
                                                </AvatarFallback>
                                            </Avatar>
                                        </td>
                                        <td>
                                            {row.first_name} {row.last_name}
                                        </td>
                                        <td>{row?.email}</td>
                                        <td>{row?.role}</td>
                                        <td>
                                            {selectedUsers?.includes(row?.id) ? (
                                                <Button onClick={() => onUsersSelect(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onUsersSelect(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                        <Button onClick={() => setOpenAlert(true)}>
                            <i className="fa-solid fa-floppy-disk"></i> Add Members
                        </Button>
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AddUsersToSubGroup;
