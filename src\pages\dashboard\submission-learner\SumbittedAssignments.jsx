import Pagination from "@/components/table/pagination";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const ITEMS_PER_PAGE = 7;

const SumbittedAssignments = ({ allSubmissions, activeTab }) => {
    const [dataList, setDatalist] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(ITEMS_PER_PAGE);

    const [filterState, setFilterState] = useState({
        search: "",
        status: "",
        submission_date: "",
    });

    useEffect(() => {
        if (allSubmissions?.length > 0) {
            setDatalist(allSubmissions?.filter((dt) => dt?.lms_course_homework?.is_assignment == true));
        }
    }, [allSubmissions, activeTab]);

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? item?.homework_title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const status = filterState?.status ? item?.evaluation_status == filterState?.status : true; // Allow all items if no subCategory filter

            const submission = filterState?.submission_date
                ? moment(item.submission_date).format("DD/MMM/YYYY") ===
                  moment(filterState?.submission_date).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            return matchesname && status && submission; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            status: "",
            submission_date: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    return (
        <>
            <div className="page_filters">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Assignment
                    </label>
                    <input
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState?.search}
                        name="search"
                        type="text"
                        placeholder="Search by name ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Status
                    </label>
                    <select className="tw-text-sm" onChange={onFilterChange} value={filterState?.status} name="status">
                        <option value="">- All -</option>
                        <option value="pass">Pass</option>
                        <option value="fail">Fail</option>
                        <option value="inprogress">Inprogress</option>
                    </select>
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Submission Date
                    </label>
                    <input
                        className="tw-text-sm"
                        type="date"
                        onChange={onFilterChange}
                        value={filterState?.submission_date}
                        name="submission_date"
                    />
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>Assignment Title</th>
                            <th>Submimitted On</th>
                            <th>Submission Date</th>
                            <th>Marks</th>
                            <th>Review Status</th>
                            <th>Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>{row?.lms_course_homework?.homework_title}</td>
                                <td>{moment(row?.created_at).format("LLL")}</td>
                                <td>{moment(row?.lms_course_homework?.submission_date).format("LLL")}</td>
                                <td>
                                    {row?.evaluation_status !== "inprogress" &&
                                        `${row?.marks} / ${row?.lms_course_homework?.homework_points}`}
                                </td>
                                <td>
                                    <Badge variant={"outline"} className="tw-capitalize">
                                        {row?.evaluation_status}
                                    </Badge>
                                </td>
                                <td>
                                    <Link to={`/dashboard/submissions-learner-view/Assignment/${row?.id}`}>
                                        <button className="selected_btn">
                                            <i className="fa-solid fa-check-to-slot"></i> Check Remarks
                                        </button>
                                    </Link>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination currentPage={currentPage} totalPages={totalPages} handlePageChange={handlePageChange} />
                </div>
            </div>
        </>
    );
};

export default SumbittedAssignments;
