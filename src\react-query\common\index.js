import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { useMutation, useQuery } from "@tanstack/react-query";

export const useFileUpload = () => {
    return useMutation({
        mutationFn: async (data) => (await tanstackApiFormdata.post("/common/upload-course-file", data)).data,
    });
};

export const useGetMedia = ({ id }) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["media", { id, userId }],
        queryFn: async () => (await tanstackApi.get(`/media-gallery/get/${id}`)).data,
        enabled: !!id,
    });
};
