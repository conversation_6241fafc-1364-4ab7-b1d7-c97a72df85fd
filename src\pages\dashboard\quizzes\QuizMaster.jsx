import Pagination from "@/components/table/pagination";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { quizTypes } from "@/pages/dashboard/quizzes";
import { tanstackApi } from "@/react-query/api";
import { useQuizDelete } from "@/react-query/quizz";
import { Loader2, Trash } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

const ITEMS_PER_PAGE = 7;

const filterStateDefault = {
    search: "",
    course_id: "",
    type: "",
};

export const QuizMaster = () => {
    const navigate = useNavigate();
    const [quizList, setQuizList] = useState([]);
    const [courseList, setCourseList] = useState([]);
    const [searchParams] = useSearchParams();
    const [tableData, setTableData] = useState([]);
    const [currentPage, setCurrentPage] = useState(Number(searchParams.get("page") || 1));
    const [filteredData, setFilteredData] = useState([]);
    const [filterState, setFilterState] = useState(filterStateDefault);
    const loginToken = localStorage.getItem("login_token");
    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        setTableData(filteredData.slice(startIndex, endIndex));
    }, [currentPage, filteredData]);

    const onSearch = () => {
        const data = quizList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const subscription = filterState?.course_id ? item?.course_id == filterState?.course_id : true; // Allow all items if no subCategory filter
            const validity = filterState?.type ? item?.type == filterState?.type : true; // Allow all items if no subCategory filter

            return matchesSearch && subscription && validity; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(quizList);
        setFilterState(filterStateDefault);
    };

    useEffect(() => {
        getQuizData();
        getCourses();
    }, []);

    useEffect(() => {
        setFilteredData(quizList);
    }, [quizList]);

    const getQuizData = async () => {
        await tanstackApi
            .post("quiz/list", {
                is_public: localStorage.getItem("level") == "levelOne" ? true : false,
            })
            .then((res) => {
                setQuizList(res?.data?.data);
            })
            .catch((err) => {
                setQuizList([]);
            });
    };

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const onRedirect = (row) => {
        navigate(`/dashboard/quiz-create/${row?.id}`);
    };

    return (
        <>
            <div>
                <div className="tw-flex tw-justify-between">
                    <h4 className="tw-text-xl tw-font-semibold">
                        <i className="fa-solid fa-gamepad"></i> My Quizzes
                    </h4>
                    <Button className="tw-px-2 tw-py-1" onClick={() => navigate(`/dashboard/quiz-create`)}>
                        <i className="fa-solid fa-plus"></i> New Quiz
                    </Button>
                </div>
                <br />
                <div className="page_filters tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Quiz Title
                        </label>
                        <input
                            value={filterState?.search}
                            name="search"
                            onChange={onFilterChange}
                            className="tw-text-sm"
                            type="text"
                            placeholder="Search by quiz title ..."
                        />
                    </div>

                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Related Course
                        </label>
                        <select
                            className="tw-text-sm"
                            value={filterState?.course_id}
                            name="course_id"
                            onChange={onFilterChange}
                        >
                            <option value=""> - Choose Course - </option>
                            {courseList?.map((course, idx) => (
                                <option value={course?.id} key={idx}>
                                    {course?.course_title}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Type
                        </label>
                        <select className="tw-text-sm" value={filterState?.type} name="type" onChange={onFilterChange}>
                            <option value=""> - Choose Type - </option>
                            {quizTypes?.map((type, idx) => (
                                <option value={type} key={idx}>
                                    {type}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table className="tw-font-lexend">
                        <thead>
                            <tr>
                                <th>Quiz</th>
                                <th>Title</th>
                                <th>Type</th>
                                {/* <th>Max Points</th> */}
                                {/* <th>Graded</th> */}
                                <th>Access</th>
                                <th>Course</th>
                                <th>Question</th>
                                <th>Creation Date</th>
                                <th>Schedule Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td onClick={() => onRedirect(row)}>
                                        <div className="table_image_alpha">
                                            <img src={row?.thumbnail_img || "/assets/course-placeholder.png"} alt="" />
                                        </div>
                                    </td>
                                    <td onClick={() => onRedirect(row)}>{row?.title}</td>
                                    <td onClick={() => onRedirect(row)}>{row?.type}</td>
                                    {/* <td onClick={() => onRedirect(row)}>{row?.max_points}</td> */}
                                    {/* <td onClick={() => onRedirect(row)}>{row?.is_graded ? "Yes" : "No"}</td> */}
                                    <td onClick={() => onRedirect(row)}>
                                        <div className="chips_group">
                                            <p>{row?.is_public ? "Public" : "Private"}</p>
                                        </div>
                                    </td>
                                    <td>
                                        {row?.lms_course?.course_title?.length > 30
                                            ? `${row?.lms_course?.course_title?.slice(0, 30)} ...`
                                            : row?.lms_course?.course_title}
                                    </td>
                                    <td>{row?.components?.length}</td>
                                    <td>{moment(row?.createdAt).format("LL")}</td>
                                    <td>{moment(row?.schedule_date).format("LL")}</td>
                                    <td>
                                        <div className="tw-flex tw-flex-wrap tw-gap-1">
                                            <Link to={`/dashboard/quiz-create/${row?.id}`}>
                                                <button className="selected_btn">
                                                    <i className="fa-solid fa-edit"></i> Edit
                                                </button>
                                            </Link>{" "}
                                            <Link
                                                to={`https://lms-course-builder.vercel.app/dashboard/quiz-config/${row?.id}?token=${loginToken}`}
                                                target="_blank"
                                            >
                                                <button className="selected_btn">
                                                    <i className="fa-solid fa-gears"></i> Configure
                                                </button>
                                            </Link>
                                            <DeleteQuiz id={row?.id} title={row?.title} />
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">
                            {ITEMS_PER_PAGE}
                        </p>
                    </div>
                    <div>
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            handlePageChange={handlePageChange}
                            itemsPerPage={ITEMS_PER_PAGE}
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export function DeleteQuiz({ id, title }) {
    const deleteFn = useQuizDelete();
    const Icon = deleteFn.isPending ? Loader2 : Trash;
    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <button className="selected_btn tw-flex tw-items-center tw-gap-1 tw-font-lexend">
                    <Trash className="tw-size-4" /> Delete
                </button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                        disabled={deleteFn.isPending}
                        onClick={() =>
                            deleteFn.mutate(
                                { id, title },
                                {
                                    onSuccess: (data) => {
                                        toast.success(data.message ?? "Quiz deleted successfully");
                                    },
                                    onError: (e) => {
                                        toast.error(e.response.data.message ?? "Something went wrong");
                                    },
                                },
                            )
                        }
                    >
                        <Icon className={cn("tw-size-4", deleteFn.isPending && "tw-animate-spin")} /> Delete
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
