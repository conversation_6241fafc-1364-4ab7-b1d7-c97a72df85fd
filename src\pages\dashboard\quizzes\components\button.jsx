import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Cog, Edit, Paintbrush, SwatchBook, Trash, X } from "lucide-react";

const iconList = {
    edit: Edit,
    sample: SwatchBook,
    cog: Cog,
    delete: Trash,
    close: X,
    design: Paintbrush,
};

/**
 * QuizConfigButton component renders a button with a specific icon based on the 'icon' prop.
 * It uses the Button component from '@/components/ui/button' and applies custom styles.
 *
 * @param {Object} props - The props object passed to the component.
 * @param {keyof typeof iconList} props.icon - The icon to be displayed on the button. Can be 'edit', 'sample', 'cog', or 'close'.
 * @returns {React.ReactElement} The QuizConfigButton component.
 */
export default function QuizConfigButton({ icon, ...props }) {
    const Icon = iconList[icon];
    return (
        <Button
            title={icon}
            {...props}
            variant="secondary"
            size="icon"
            className={cn(
                "tw-flex !tw-size-8 tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 !tw-rounded-full tw-border-[1px] tw-border-solid tw-border-gray-100 tw-bg-gray-100 tw-p-1 tw-shadow-sm tw-transition-all hover:tw-bg-gray-200",
            )}
        >
            <Icon className="!tw-size-4" />
        </Button>
    );
}
