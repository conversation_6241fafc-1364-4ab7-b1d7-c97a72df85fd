import { cn, isUrl } from "@/lib/utils";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import ContentSlideLayout from "../layouts/ContentSlideLayout";

const Singlechoice = ({ handleNextSlide, handlePrevSlide, className, data, sequence, onComponentAnswer, template }) => {
    const [selected, setSelected] = useState(data?.answerKey ?? null);
    const handleClick = (option) => {
        setSelected(option);
        let ans = data?.options?.find((dt) => dt?.label == option.label);
        let points = Boolean(ans?.isCorrect) == true ? data?.points : 0;
        onComponentAnswer(sequence, option, points);
    };

    const ref = useRef(null);

    const getCurrentScale = () => {
        const style = getComputedStyle(ref.current);
        const transform = style.transform;
        if (transform && transform.includes("matrix")) {
            const match = transform.match(/matrix\(([^)]+)\)/);
            if (match) {
                const values = match[1].split(", ");
                return parseFloat(values[0]);
            }
        }
        return 1;
    };

    const zoomIn = () => {
        const currentScale = getCurrentScale();
        if (currentScale >= 3) return;
        ref.current.style.transform = `scale(${currentScale + 0.25})`;
    };

    const zoomOut = () => {
        const currentScale = getCurrentScale();
        if (currentScale <= 0.25) return;
        ref.current.style.transform = `scale(${currentScale - 0.25})`;
    };

    const toggleFullscreen = () => {
        if (document.fullscreenElement) return document.exitFullscreen();
        if (ref.current) ref.current.requestFullscreen();
    };

    useEffect(() => {
        if (ref.current) ref.current.style.transform = "scale(1)";
    }, [data]);

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div
                className={cn(
                    "tw-grid tw-min-h-[18rem] tw-w-full tw-grid-rows-1 tw-gap-6",
                    isUrl(data?.question_thumbnail) ? "tw-grid-cols-2" : "tw-grid-cols-1",
                )}
            >
                {isUrl(data?.question_thumbnail) && (
                    <div className="tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
                        <div className="tw-relative tw-mt-7 tw-h-[300px] tw-w-[420px] tw-overflow-hidden">
                            <img
                                ref={ref}
                                src={data?.question_thumbnail}
                                alt=""
                                className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw--rotate-3 tw-rounded-xl tw-object-cover"
                            />
                            <motion.div
                                className="tw-absolute tw-bottom-0 tw-left-0 tw-z-20 tw-flex tw-gap-1 tw-border tw-bg-white tw-px-2"
                                key={sequence}
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                            >
                                <button onClick={zoomIn}>
                                    <i className="fa-solid fa-magnifying-glass-plus"></i>
                                </button>
                                <button onClick={zoomOut}>
                                    <i className="fa-solid fa-magnifying-glass-minus"></i>
                                </button>
                                <button onClick={toggleFullscreen}>
                                    <i className="fa-solid fa-expand"></i>
                                </button>
                            </motion.div>
                            <motion.img
                                initial={{ scale: 1, rotate: 1 }}
                                animate={{
                                    scale: 1.1,
                                }}
                                transition={{
                                    duration: 1,
                                    repeat: Infinity,
                                    repeatType: "reverse",
                                }}
                                src={"/quiz/spark, sparkle, 26.png"}
                                alt=""
                                className="tw-absolute tw-right-[-50px] tw-top-[-110px] tw-z-0 tw-aspect-[1/1.25] tw-w-[150px]"
                            />
                            <motion.img
                                initial={{ rotate: 10 }}
                                animate={{
                                    rotate: -10,
                                    transition: {
                                        duration: 2,
                                        repeat: Infinity,
                                        repeatType: "reverse",
                                    },
                                }}
                                src="/quiz/Question Mark.png"
                                alt=""
                                className="tw-absolute tw-bottom-[-3%] tw-right-[-5%] tw-z-0 tw-aspect-[1/1.25] tw-w-[80px]"
                            />
                        </div>
                    </div>
                )}
                <div className="tw-h-full tw-w-full">
                    <div className="tw-mb-5 tw-flex tw-items-start tw-gap-4 tw-text-[40px] tw-leading-[40px]">
                        <p>{sequence}. </p>
                        <p style={{ fontSize: data?.styles?.question?.fontSize }}>{data?.name}</p>
                    </div>
                    <div
                        className={cn("tw-grid tw-grid-cols-1 tw-gap-4")}
                        style={{
                            gridTemplateColumns: data?.optionColumns || "1fr 1fr",
                        }}
                    >
                        {data?.options.map((option, index) => {
                            const isChosen = selected?.label === option?.label;
                            return (
                                <motion.button
                                    key={index}
                                    initial={{ y: 100, opacity: 0.5 }}
                                    whileInView={{ y: 0, opacity: 1 }}
                                    transition={{
                                        duration: 0.25,
                                        delay: index * 0.1,
                                        ease: "easeInOut",
                                    }}
                                    style={{
                                        backgroundColor:
                                            data?.styles?.[isChosen ? "selected" : "default"]?.backgroundColor,
                                        color: data?.styles?.[isChosen ? "selected" : "default"]?.color,
                                        fontFamily: data?.styles?.answer?.fontFamily,
                                        fontSize: data?.styles?.answer?.fontSize,
                                        lineHeight: 1,
                                        borderWidth: data?.styles?.[isChosen ? "selected" : "default"]?.borderWidth,
                                        borderColor: data?.styles?.[isChosen ? "selected" : "default"]?.borderColor,
                                        borderStyle: data?.styles?.[isChosen ? "selected" : "default"]?.borderStyle,
                                    }}
                                    className={cn("tw-rounded-md tw-border tw-py-4 tw-text-[30px] tw-leading-[30px]")}
                                    onClick={() => handleClick(option)}
                                >
                                    {option?.label}
                                </motion.button>
                            );
                        })}
                    </div>
                </div>
            </div>
        </ContentSlideLayout>
    );
};

export default Singlechoice;
