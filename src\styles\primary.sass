.register_steps
    margin-top: 6rem !important
    h1
        font-family: "<PERSON><PERSON> sans"
        text-transform: uppercase
        font-size: 20px
        font-weight: 700
        letter-spacing: 2px
        border-bottom: 1px solid var(--color-light-gray)
        text-shadow: 1.5px 1.5px rgba(109, 109, 109, 0.15)

        svg
            font-size: 26px
            margin-top: -6px

    h3
        font-size: 18px
        line-height: 22px

    .define_form
        grid-template-columns: repeat(5,1fr)

    .cat_reg_form
        p
            color: var(--color-dark-gray)

        .goal_input
            grid-template-columns: repeat(3,1fr)
            width: 66%

        .width_cat_reg
            width: 45%

        h1
            svg
                margin-right: 4px

        .react_select_label
            font-size: 15px
            line-height: 18px
            color: var(--input-label-color)
            opacity: 0.8
            letter-spacing: 0.06rem

        .training_mode_cb
            grid-template-columns: repeat(3, 1fr)

    .reg_form_about
        gap: 300px
        .width_cat_reg
            width: 45%
            flex-shrink: 0

        .company_logo_box
            width: auto
            img
                display: block
                height: 210px
                object-fit: cover
                border-radius: 5px
                border: 1px solid rgba(0, 0, 0, 0.3)
                width: 350px
                cursor: pointer

            input
                display: none

            label
                align-self: center
                
.input_radio_checkbox_lms
    input    
        accent-color: var(--input-primary-color-70)
        cursor: pointer
        margin-top: -1px
        flex-shrink: 0

    label
        cursor: pointer
        font-size: 15px
        line-height: 18px

.create_course_first_step
    width: 100%
    background: white
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 10px
    & > div
        width: 540px
           
.simple_brdcrmb_lms
    gap: 5.4px
    & > div
        gap: 5.4px
        font-size: 15.2px
        line-height: 18px
        & > a
            color: var(--color-dark-gray)

            &:hover
                color: var(--input-value-color)
                text-decoration: underline

        & > a.active_brdcrmb
            color: var(--input-primary-color)
            &:hover
                color: var(--input-primary-color)
                text-decoration: none
        
        & > span
            color: var(--color-dark-gray)

.create_course_font_color
    color: var(--input-primary-color-85) !important

.cc_btn_2
    font-size: 14px
    line-height: 18px
    min-height: 32px
    padding: 8px 12px

.courses_tabs
    .course_tab_box
        background: white
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 10px

    .next_btn_cc
        font-size: 13px
        line-height: 15px
        min-height: 28px
        padding: 7.5px 9px 7.5px 12px
        svg
            margin-top: -2px

    .prev_btn_cc
        font-size: 13px
        line-height: 15px
        min-height: 28px
        padding: 7.5px 0
        border: 0
        box-shadow: none
        svg
            margin-top: -2px

        &:hover
            background-color: transparent
            color: var(--input-value-color)

    .select_currency_type
        width: 140px
        select
            option:first-child
                background-color: rgba(0, 0, 0, 0.05)

        & > .valid_value ~ span
            left: 11px

    .discount_amount_input
        width: 212px

    .coupon_code_box
        width: 600px
        flex-wrap: wrap
        & > div
            padding: 4px 8px
            background: var(--input-primary-color-85)
            border-radius: 5px
            font-size: 14px
            color: white
            line-height: 18px
            letter-spacing: 0.05rem
            box-shadow: rgba(186, 186, 186, 0.25) 4px 4px 20px

            svg
                margin: -2px 0 0 5px 
                cursor: pointer

    .file_type_prerequisite
        & > div
            width: 50%

        select
            option:first-child
                background-color: rgba(0, 0, 0, 0.05)

    .file_upload_progress_bar
        width: 50%
        border-radius: 4px
        & > div
            color: white
            text-align: center
            border-radius: 4px
            font-weight: 500
            font-size: 14px
            line-height: 18px
            padding: 4px

    .course_curriculum_cc
        textarea
            height: 120px

        .curri_chapter_order
            input[type="number"]
                width: 100px

        .course_curri_cc_title
            flex-grow: 1

    .cc_quill
        .quill
            .ql-snow
                border: 1px solid var(--input-primary-color-70) !important

            .ql-container
                border-top: 0 !important

    .textarea_lp
        margin-bottom: 4px
        textarea
            height: 120px

    .cc_details_img_box
        height: 220px
        & > div
            border-radius: 8px
            height: 100%
            & > label
                border-width: 1px
                border-radius: 8px
                box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 15px
    
    .video_player_lp
        width: 500px

    .cc_details_flex_item
        width: 50%

    .width_cc_cs_inputs
        width: 30%

    .width_dual_input_day
        width: 30%
    
    .width_dual_input_date
        width: 30%
        input[type="date"]
            width: 100%

    .select_wo_first_opt
        select
            option:first-child
                background-color: rgba(0, 0, 0, 0.05)
            