import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

export default function QuestionsList({ questions, currentIndex, answeredQuestions, setCurrentQuestionIndex }) {
    return (
        <Card className="tw-border-none tw-bg-orange-800/80 tw-p-4 tw-text-white tw-shadow-lg">
            <h3 className="tw-mb-3 tw-font-semibold">Quiz Questions List</h3>
            <div className="tw-space-y-2">
                {questions.map((question, index) => {
                    const isAnswered = question.id in answeredQuestions;
                    const isCurrent = index === currentIndex;

                    return (
                        <div
                            key={question.id}
                            onClick={() => setCurrentQuestionIndex(index)}
                            className={cn(
                                "tw-flex tw-cursor-pointer tw-items-center tw-justify-between tw-rounded-md tw-p-2",
                                isCurrent ? "tw-bg-orange-700" : "tw-bg-orange-900/50 hover:tw-bg-orange-800/70",
                            )}
                        >
                            <span className="tw-truncate tw-pr-2 tw-text-sm">Quiz Question {index + 1}</span>
                            {isAnswered && <Check className="tw-h-4 tw-w-4 tw-text-green-400" />}
                        </div>
                    );
                })}
            </div>
        </Card>
    );
}
