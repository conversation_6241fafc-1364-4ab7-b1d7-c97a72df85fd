import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useFileUpload } from "@/react-query/common";
import { Upload, X } from "lucide-react";
import { useState } from "react";

const optionStructure = {
    label: "",
    description: "",
    image_src: "",
    x: "",
    y: "",
    annotion_icon: "",
    bg_sound: "",
};

export default function InteractionsOptions({ data, setData }) {
    const [options, setOptions] = useState([optionStructure]);

    return (
        <div>
            <div className="tw-space-y-2">
                {options?.map((data, idx) => {
                    return <InteractionsOptionsItem key={idx} index={idx} data={options} setData={setOptions} />;
                })}
            </div>
        </div>
    );
}

function InteractionsOptionsItem({ index, data, setData }) {
    const upload = useFileUpload();

    const [fileUrl, setFileUrl] = useState(data.image_src);
    const onImageChange = async (e) => {
        try {
            if (e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                const prev = [...data];
                prev[index].image_src = response?.fileUrl;
                setData(prev);
                setFileUrl(response?.fileUrl);
            }
        } catch (error) {
            console.error(error);
        }
    };

    return (
        <div className="tw-space-y-4">
            <div className="tw-grid tw-w-full tw-items-start tw-gap-2">
                <Label htmlFor="label">Label</Label>
                <Input type="text" name="label" id="label" placeholder="Label" />
            </div>
            <div className="tw-grid tw-w-full tw-items-start tw-gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea name="description" rows={5} id="description" placeholder="Description" />
            </div>
            <div className="tw-flex tw-w-full tw-flex-col tw-items-start tw-gap-2">
                <Label htmlFor="bg_image">Background Image</Label>
                {fileUrl && (
                    <div className="tw-aspect-video tw-h-36 tw-object-contain">
                        <img src={fileUrl} alt="" className="tw-size-full tw-rounded-md" />
                    </div>
                )}
                <div className="tw-flex tw-items-center tw-gap-2">
                    {fileUrl && (
                        <Button
                            variant="destructive"
                            className="aspect-square max-sm:p-0"
                            onClick={() => {
                                const prev = [...data];
                                prev[index].image_src = "";
                                setData(prev);
                                setFileUrl("");
                            }}
                        >
                            <X className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                            <Label className="max-sm:sr-only">Remove</Label>
                        </Button>
                    )}
                    <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10">
                        <Upload className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                        <input
                            onChange={onImageChange}
                            type="file"
                            style={{ display: "none" }}
                            id="bg_image"
                            accept="image/*"
                        />
                        <Label htmlFor="bg_image" className="max-sm:sr-only">
                            {upload.isPending ? "Uploading..." : "Upload Background Image"}
                        </Label>
                    </Button>
                </div>
            </div>
        </div>
    );
}
