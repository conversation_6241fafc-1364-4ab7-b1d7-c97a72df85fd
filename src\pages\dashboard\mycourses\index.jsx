import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
    Pagin<PERSON>,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import CreateCourse from "./CreateCourse";

const courseStatusList = [
    {
        key: "completed",
        label: "Published",
    },
    {
        key: "pending",
        label: "Pending",
    },
    {
        key: "deleted",
        label: "Deleted",
    },
    {
        key: "hold",
        label: "Hold",
    },
];

const MyCourses = () => {
    const navigate = useNavigate();
    const role = localStorage.getItem("role_category");
    const lgnRole = localStorage.getItem("login_role");
    const level = localStorage.getItem("level");
    const tkn = localStorage.getItem("login_token");
    const tknRole = atob(tkn.split(".")[1]);
    const tknObj = JSON.parse(tknRole);
    const superAdmin =
        role === "administrator" && level === "levelOne" && lgnRole === "super-admin" && tknObj?.role === lgnRole;
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(7);
    const [categoryList, setCategoryList] = useState([]);
    const [courseStatus, setCourseStatus] = useState(null);
    const [filterState, setFilterState] = useState({
        search: "",
        category_id: "",
        sub_category_id: "",
        course_status: "",
    });

    const [open, setOpen] = useState(false);
    const [openAlert, setOpenAlert] = useState(false);

    useEffect(() => {
        getCourses();
        getCategory();
    }, []);

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData, recordsPerPage]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? item?.course_title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const categories = filterState?.category_id ? item?.category_id == filterState?.category_id : true; // Allow all items if no subCategory filter
            const subcategory = filterState?.sub_category_id
                ? item?.sub_category_id == filterState?.sub_category_id
                : true; // Allow all items if no subCategory filter
            const status = filterState?.course_status ? item?.course_status == filterState?.course_status : true; // Allow all items if no subCategory filter

            return matchesname && categories && subcategory && status; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            category_id: "",
            sub_category_id: "",
            course_status: "",
        });
    };

    const getCategory = async (payload) => {
        await tanstackApi
            .get("course/categories/get-course-category")
            .then((res) => {
                setCategoryList(res?.data?.data);
            })
            .catch((err) => {
                setCategoryList([]);
            });
    };

    const onSetCourseStatus = async () => {
        let payload = courseStatus;
        await tanstackApi
            .post("course/creation/update-course-status", { ...payload })
            .then((res) => {
                toast.success("Status changed", {
                    description: `Course status has been changed successfully`,
                });
                getCourses();
            })
            .catch((err) => {
                toast.error("Something went wront", {
                    description: `Course status not changed Sorry!`,
                });
            });
    };

    const onCourseStatusChange = (e, courseID) => {
        let payload = {
            course_id: courseID,
            status: e?.target.value,
        };
        setCourseStatus(payload);
        setOpenAlert(true);
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will change your course&apos;s status it will reflect in all course listings.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onSetCourseStatus}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <CreateCourse open={open} setOpen={setOpen} getCourses={getCourses} />
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-book"></i> My Courses
                </h4>
                <Button onClick={() => setOpen(true)} className="tw-px-2 tw-py-1">
                    <i className="fa-solid fa-plus"></i> New Course
                </Button>
            </div>
            <br />
            <div className="page_filters tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Course Title
                    </label>
                    <input
                        className="tw-text-sm"
                        type="text"
                        placeholder="Search by course title ..."
                        name="search"
                        value={filterState?.search}
                        onChange={onChangeHandle}
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Category
                    </label>
                    <select
                        className="tw-text-sm"
                        name="category_id"
                        onChange={onChangeHandle}
                        value={filterState?.category_id}
                    >
                        <option value=""> - Choose Category - </option>
                        {categoryList?.map((data, idx) => (
                            <option key={idx} value={data?.id}>
                                {data?.category_name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Course Status
                    </label>
                    <select
                        className="tw-text-sm"
                        name="course_status"
                        onChange={onChangeHandle}
                        value={filterState?.course_status}
                    >
                        <option value=""> - Choose Status - </option>
                        {courseStatusList?.map((status, idx) => (
                            <option key={idx} value={status?.key}>
                                {status?.label}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table tw-mt-2 tw-h-full tw-overflow-y-auto">
                <table>
                    <thead>
                        <tr>
                            <th>Course</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Category</th>
                            <th>Access</th>
                            <th>Course Status</th>
                            <th>Creation Date</th>
                            <th>Expiration On</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                    <div className="table_image_alpha">
                                        <img src={row?.course_banner_url || "/assets/course-placeholder.png"} alt="" />
                                    </div>
                                </td>
                                <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                    {row?.course_title}
                                </td>
                                <td>{row?.is_scorm ? "SCORM" : "Combined"}</td>
                                <td>{row?.lms_course_category?.category_name}</td>
                                <td>{row?.is_public ? "Public" : "Private"}</td>
                                <td>
                                    <select
                                        name="course_status"
                                        value={row?.course_status}
                                        id=""
                                        className="table_input"
                                        onChange={(e) => onCourseStatusChange(e, row?.id)}
                                    >
                                        {courseStatusList?.map((status, idx) => (
                                            <option key={idx} value={status?.key}>
                                                {status?.label}
                                            </option>
                                        ))}
                                    </select>
                                </td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td>
                                    {row?.lms_course_settings?.[0]?.expiration_date
                                        ? moment(row?.lms_course_settings?.[0]?.expiration_date).format("LL")
                                        : moment(row?.createdAt).add(1, "year").format("LL")}
                                </td>
                                <td>
                                    <div className="tw-flex tw-items-center tw-justify-center tw-gap-2">
                                        {(superAdmin || !row?.is_public) && (
                                            <Link to={`/dashboard/edit-course/${row?.id}`}>
                                                <button className="selected_btn_alpha">
                                                    <i className="fa-solid fa-pen-to-square"></i> Edit
                                                </button>
                                            </Link>
                                        )}
                                        <Link to={`/dashboard/view-course/${row?.id}`}>
                                            <button className="selected_btn_alpha">
                                                <i className="fa-solid fa-eye"></i> View
                                            </button>
                                        </Link>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination className="">
                        <PaginationContent>
                            {/* Previous Button */}
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page Numbers with Ellipses */}
                            {currentPage > 3 && (
                                <>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                            1
                                        </PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                </>
                            )}

                            {getVisiblePages().map((page) => (
                                <PaginationItem key={page}>
                                    <PaginationLink
                                        href="#"
                                        isActive={page === currentPage}
                                        onClick={() => handlePageChange(page)}
                                    >
                                        {page}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {currentPage < totalPages - 2 && (
                                <>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                            {totalPages}
                                        </PaginationLink>
                                    </PaginationItem>
                                </>
                            )}

                            {/* Next Button */}
                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </>
    );
};

export default MyCourses;
