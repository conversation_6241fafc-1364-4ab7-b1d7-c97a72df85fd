import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { tanstackApiFormdata } from "@/react-query/api";
import { AddUserPoints } from "@/redux/dashboard/dash/action";
import { SubmitHomeworkData } from "@/redux/gamification/action";
import moment from "moment";
import { toast } from "sonner";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";

const ContentHomework = ({ content }) => {
    const dispatch = useDispatch();
    const { handleNext } = usePlayer();
    const [attemptHomework, setAttemptHomework] = useState(false);
    const [percentage, setPercentage] = useState(null);
    const [inprogress, setInprogress] = useState("done");

    useEffect(() => {
        setAttemptHomework(false);
        setPercentage(null);
        setInprogress("done");
    }, [content]);

    const [submissionDetails, setSubmissionDetails] = useState({
        submission: "",
        submission_attachment: "",
    });

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setSubmissionDetails({ ...submissionDetails, [name]: value });
    };

    const onAttemptHomework = () => {
        setAttemptHomework(true);
    };

    const onBackHomework = () => {
        setAttemptHomework(false);
    };

    const onAttachmentChange = (e, index) => {
        if (e.target.files[0]) {
            const formData = new FormData();
            var file = e.target.files[0];

            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");

            onAttachmentUpload(formData);
        }
    };

    const onAttachmentUpload = async (formData) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setPercentage(percent);
                },
            })
            .then((res) => {
                setSubmissionDetails({
                    ...submissionDetails,
                    submission_attachment: res.data.fileUrl,
                });
            })
            .catch((err) => {
                setSubmissionDetails({
                    ...submissionDetails,
                    submission_attachment: "",
                });
            });
    };

    const onSubmitHomework = async (e) => {
        e.preventDefault();
        if (inprogress == "completed") {
            return handleNext("completed");
        }

        if (!submissionDetails?.submission) {
            toast.warning("Submission Details", {
                description: "Please write submission details.",
            });
            return false;
        }

        setInprogress("started");
        let payload = {
            submission: submissionDetails?.submission,
            homework_id: content?.homework_id,
            submission_attachment: submissionDetails?.submission_attachment,
        };

        dispatch(SubmitHomeworkData(payload));
        setInprogress("completed");

        if (content?.content_type == "ASSIGNMENT" || content?.content_type == "HOMEWORK") {
            dispatch(
                AddUserPoints({
                    user_id: localStorage.getItem("userId"),
                    event: content?.content_type == "ASSIGNMENT" ? "Submit Homework" : "Submit Assignment",
                    points: content?.homework?.homework_points,
                    entity_id: `${content?.homework_id}`,
                }),
            );
        }
    };

    const onDownload = () => {
        const url = content?.homework?.attachment_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.target = "_blank";
        a.download = `${content?.homework?.attachment_url}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    return (
        <div className="content_body_homework">
            {attemptHomework ? (
                <>
                    <label htmlFor={content?.content_type + content?.id} className="homework_submission">
                        <textarea
                            name="submission"
                            id=""
                            rows={8}
                            placeholder="Type details here ..."
                            onChange={onHandleChange}
                            value={submissionDetails?.submission}
                        ></textarea>
                        <input
                            type="file"
                            name=""
                            id={content?.content_type + content?.id}
                            onChange={onAttachmentChange}
                        />
                        <div>
                            <i className="fa-solid fa-paperclip"></i> Add Attachment
                        </div>
                        <p>Uploaded File: {percentage && <>{percentage}%</>}</p>
                        <a href={submissionDetails?.submission_attachment} target="_blank" rel="noopener noreferrer">
                            {submissionDetails?.submission_attachment}
                        </a>
                    </label>
                </>
            ) : (
                <>
                    <div className="homework_title">
                        {content?.content_type == "ASSIGNMENT" && <i className="fa-solid fa-laptop-file"></i>}
                        {content?.content_type == "HOMEWORK" && <i className="fa-solid fa-chalkboard"></i>}
                        <h1>{content?.homework?.homework_title}</h1>
                    </div>
                    <div className="homework_description">
                        <p>{content?.homework?.description}</p>
                    </div>
                    <div className="homework_details">
                        <div>
                            <p>
                                <i className="fa-solid fa-dice-d6"></i> Points
                            </p>
                            <h4>{content?.homework?.homework_points}</h4>
                        </div>
                        <div>
                            <p>
                                <i className="fa-solid fa-clock"></i> Submission
                            </p>
                            <h4>{moment(content?.homework?.submission_date).format("LLL")}</h4>
                        </div>
                        <div>
                            <p>
                                <i className="fa-solid fa-paperclip"></i> Attachments
                            </p>
                            {content?.homework?.attachment_url ? (
                                <button onClick={onDownload}>
                                    <i className="fa-solid fa-download"></i> Download
                                </button>
                            ) : (
                                <button className="tw-opacity-[.2]">File not found!</button>
                            )}
                        </div>
                    </div>
                </>
            )}
            {attemptHomework ? (
                <div className="homework_action">
                    <button onClick={onBackHomework}>
                        <i className="fa-solid fa-arrow-left-long"></i> Back
                    </button>
                    {inprogress == "done" ? (
                        <button onClick={onSubmitHomework}>
                            <i className="fa-solid fa-floppy-disk"></i> Submit {content?.content_type?.toLowerCase()}
                        </button>
                    ) : inprogress == "started" ? (
                        <button>
                            <i className="fa-solid fa-spinner tw-animate-spin"></i> Please wait ...
                        </button>
                    ) : inprogress == "completed" ? (
                        <>
                            <button>
                                <i className="fa-solid fa-check"></i> Submitted Successfully!
                            </button>
                            <button onClick={onSubmitHomework}>
                                <i className="fa-solid fa-check"></i> Next
                            </button>
                        </>
                    ) : (
                        ""
                    )}
                </div>
            ) : (
                <div className="homework_action">
                    <button onClick={onAttemptHomework}>
                        <i className="fa-regular fa-circle-play"></i> Attempt {content?.content_type?.toLowerCase()}
                    </button>
                </div>
            )}
        </div>
    );
};

export default ContentHomework;
