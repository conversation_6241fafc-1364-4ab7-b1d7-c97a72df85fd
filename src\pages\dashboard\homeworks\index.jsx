import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getSubmissionBadge } from "@/lib/utils";
import { FetchUserHomeworkList } from "@/redux/gamification/action";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";

const UserHomeworks = () => {
    const dispatch = useDispatch();

    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(10);

    const [filterState, setFilterState] = useState({
        search: "",
        status: "",
        submission_date: "",
    });

    const dataList = useSelector((state) => state.GamificationReducer)?.userHomworkeList;

    useEffect(() => {
        var data = {
            is_assignment: false,
            is_public: localStorage.getItem("level") == "levelOne",
        };
        dispatch(FetchUserHomeworkList(data));
    }, []);

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? item?.homework_title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const status = filterState?.status == String(Boolean(item?.is_submitted)); // Allow all items if no subCategory filter

            const submission = filterState?.submission_date
                ? moment(item.submission_date).format("DD/MMM/YYYY") ===
                  moment(filterState?.submission_date).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            return matchesname && status && submission; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            status: "",
            submission_date: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    return (
        <div className="">
            <div>
                <div className="pageHeader">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Assigned Homework</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
                <br />
                <div className="page_filters">
                    <div className="input_group">
                        <label htmlFor="" className="tw-text-sm">
                            Homework
                        </label>
                        <input
                            onChange={onFilterChange}
                            value={filterState?.search}
                            name="search"
                            className="tw-text-sm"
                            type="text"
                            placeholder="Search by name ..."
                        />
                    </div>
                    <div className="input_group">
                        <label htmlFor="" className="tw-text-sm">
                            Status
                        </label>
                        <select
                            id=""
                            className="tw-text-sm"
                            onChange={onFilterChange}
                            value={filterState?.status}
                            name="status"
                        >
                            <option value="">All</option>
                            <option value="true">Submitted</option>
                            <option value="false">Not Submitted</option>
                        </select>
                    </div>
                    <div className="input_group">
                        <label htmlFor="" className="tw-text-sm">
                            Submission Date
                        </label>
                        <input
                            type="date"
                            onChange={onFilterChange}
                            value={filterState?.submission_date}
                            name="submission_date"
                            className="tw-text-sm"
                        />
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>Homework Title</th>
                                <th>Points</th>
                                <th>Details</th>
                                <th>Related Course</th>
                                <th>Submission Date</th>
                                <th>Deadline</th>
                                <th>Submission Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData.length == 0 ? (
                                <tr>
                                    <td colSpan={8}>
                                        <div className="tw-flex tw-h-40 tw-items-center tw-justify-center">
                                            <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                                No Homework Found
                                            </h2>
                                        </div>
                                    </td>
                                </tr>
                            ) : (
                                tableData?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>{row?.homework_title}</td>
                                        <td>
                                            <b>{row?.homework_points}</b>
                                        </td>
                                        <td>
                                            <div>
                                                <p>
                                                    Group :{" "}
                                                    {row?.subGroupDetails
                                                        ? row?.subGroupDetails?.lms_user_group?.name
                                                        : "-"}
                                                </p>
                                                <p>Team : {row?.subGroupDetails ? row?.subGroupDetails?.name : "-"}</p>
                                                <p>Course : </p>
                                            </div>
                                        </td>
                                        <td>{row?.lms_course?.course_title}</td>
                                        <td>{moment(row?.submission_date).format("LLL")}</td>
                                        <td>
                                            <Badge className={getSubmissionBadge(row?.submission_date)?.badgeClass}>
                                                {getSubmissionBadge(row?.submission_date)?.message}
                                            </Badge>
                                        </td>
                                        <td>
                                            {row?.is_submitted ? (
                                                <Badge>Submitted</Badge>
                                            ) : (
                                                <Badge variant={"outline"}>Not Yet!</Badge>
                                            )}
                                        </td>
                                        <td>
                                            <Link to={`/dashboard/attempt-homework/${row?.id}`}>
                                                <button className="selected_btn">
                                                    <i className="fa-regular fa-folder-open"></i> View
                                                </button>
                                            </Link>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination className="">
                            <PaginationContent>
                                {/* Previous Button */}
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {/* Page Numbers with Ellipses */}
                                {currentPage > 3 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                1
                                            </PaginationLink>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    </>
                                )}

                                {getVisiblePages().map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href="#"
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {currentPage < totalPages - 2 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                {totalPages}
                                            </PaginationLink>
                                        </PaginationItem>
                                    </>
                                )}

                                {/* Next Button */}
                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UserHomeworks;
