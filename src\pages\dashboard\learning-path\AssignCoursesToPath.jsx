import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const AssignCoursesToPath = ({ pathData, getLearningPath }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [courseList, setCourseList] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [courseArray, setCourseArray] = useState([
        {
            course_id: null,
            description: "",
            step_name: "",
            order: 1,
            step_image_url: "",
        },
    ]);

    useEffect(() => {
        if (pathData?.lms_learning_path_steps?.length > 0) {
            setCourseArray(pathData?.lms_learning_path_steps);
        } else {
            setCourseArray([
                {
                    course_id: null,
                    description: "",
                    step_name: "",
                    order: 1,
                    step_image_url: "",
                },
            ]);
        }
    }, [pathData]);

    useEffect(() => {
        getCourses();
    }, []);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const onHandleChange = (name, value, idx) => {
        let optionData = [...courseArray];
        optionData[idx][name] = value;
        setCourseArray(optionData);

        if (name == "step_image_url") {
            const formData = new FormData();
            var file = value;
            formData.append("file", file);
            formData.append("category", "LEARNING-PATHS");

            onMatchOptionsMediaUpload(name, formData, idx);
        }
    };

    const OptionAdd = () => {
        let opt = {
            course_id: null,
            description: "",
            step_name: "",
            order: 1,
            step_image_url: "",
        };
        setCourseArray([...courseArray, opt]);
    };

    const RemoveOption = (idx) => {
        const arr = [...courseArray];
        arr.splice(idx, 1);
        setCourseArray(arr);
    };

    const onRemoveImage = (name, index) => {
        let optionData = [...courseArray];
        optionData[index][name] = "";
        setCourseArray(optionData);
    };

    const onMatchOptionsMediaUpload = async (name, formData, index) => {
        await tanstackApiFormdata
            .post("common/upload-file", formData)
            .then((res) => {
                let optionData = [...courseArray];
                optionData[index][name] = res?.data?.fileUrl;
                setCourseArray(optionData);
            })
            .catch((err) => {
                let optionData = [...courseArray];
                optionData[index][name] = "";
                setCourseArray(optionData);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (courseArray?.length == 0) {
            toast?.warning("Steps", {
                description: "Please create some steps with courses for learning path.",
            });
            return false;
        }

        if (params?.path_id !== undefined) {
            const payload = {
                learning_path_id: params?.path_id,
                data: courseArray,
            };

            await tanstackApi
                .post("learning-path/assign-course", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "learning path",
                        log: `${courseArray?.length} courses assigned to ${pathData?.name} successfully.`,
                    });
                    toast.success("Assigned Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    getLearningPath(params?.path_id);
                    // navigate(`/dashboard/learning-path-view/${params?.path_id}`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add below created steps along with selected courses into it in your
                            learning path.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Add courses to learning path</CardTitle>
                            <CardDescription>
                                Click on Add Button to add new button record. Click save when you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{courseArray?.length} Steps</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-flex tw-h-[55vh] tw-flex-col tw-gap-4 tw-overflow-auto">
                        {courseArray?.map((option, idx) => (
                            <div
                                key={idx}
                                className="tw-grid tw-grid-cols-[400px_1fr_60px] tw-gap-5 tw-border-b-[2px] tw-border-dashed tw-pb-4"
                            >
                                <div className="tw-flex tw-w-full tw-flex-col tw-gap-2 tw-p-1">
                                    <div className="tw-relative tw-h-[200px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                        <img
                                            className="tw-h-full tw-w-full tw-object-cover"
                                            src={option?.step_image_url || "/assets/thumbnail-alpha.png"}
                                            alt=""
                                        />
                                    </div>
                                    <div className="tw-flex tw-justify-center tw-gap-2">
                                        {option?.step_image_url == "" && (
                                            <Label
                                                htmlFor={`step_image_url_${idx}`}
                                                className={cn(
                                                    buttonVariants({ variant: "outline" }),
                                                    "aspect-square max-sm:p-0 tw-cursor-pointer",
                                                )}
                                            >
                                                <Upload
                                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                                    size={16}
                                                    strokeWidth={2}
                                                    aria-hidden="true"
                                                />
                                                <input
                                                    onChange={(e) =>
                                                        onHandleChange("step_image_url", e.target.files[0], idx)
                                                    }
                                                    type="file"
                                                    style={{ display: "none" }}
                                                    id={`step_image_url_${idx}`}
                                                    accept="image/*"
                                                />
                                                <div className="max-sm:sr-only">Upload Image</div>
                                            </Label>
                                        )}
                                        {option?.step_image_url !== "" && (
                                            <Button
                                                variant="outline"
                                                className="aspect-square max-sm:p-0"
                                                onClick={() => onRemoveImage("step_image_url", idx)}
                                            >
                                                <X
                                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                                    size={16}
                                                    strokeWidth={2}
                                                    aria-hidden="true"
                                                />
                                                <Label className="max-sm:sr-only">Remove</Label>
                                            </Button>
                                        )}
                                    </div>
                                </div>
                                <div className="tw-space-y-1">
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="name">Select Course</Label>
                                        <SelectNative
                                            onChange={(e) => onHandleChange("course_id", e.target.value, idx)}
                                            name="course_id"
                                            value={option?.course_id}
                                        >
                                            <option value="">- Choose Course -</option>
                                            {courseList?.map((course, index) => (
                                                <option value={course?.id} key={index}>
                                                    {course?.course_title}
                                                </option>
                                            ))}
                                        </SelectNative>
                                    </div>
                                    <div className="tw-grid tw-grid-cols-[1fr_150px] tw-gap-2">
                                        <div className="tw-space-y-1">
                                            <Label htmlFor="name">Step Name</Label>
                                            <Input
                                                onChange={(e) => onHandleChange("step_name", e.target.value, idx)}
                                                name="step_name"
                                                value={option?.step_name}
                                                id="name"
                                                placeholder="Enter learning path name here"
                                            />
                                        </div>
                                        <div className="tw-space-y-1">
                                            <Label htmlFor="order">Step Order</Label>
                                            <Input
                                                onChange={(e) => onHandleChange("order", e.target.value, idx)}
                                                name="order"
                                                value={option?.order}
                                                id="order"
                                                placeholder="eg: 1"
                                            />
                                        </div>
                                    </div>
                                    <div className="tw-grid tw-grid-cols-1 tw-items-end tw-gap-2">
                                        <div className="tw-space-y-1">
                                            <Label htmlFor="description">Description</Label>
                                            <Textarea
                                                onChange={(e) => onHandleChange("description", e.target.value, idx)}
                                                name="description"
                                                value={option?.description}
                                                className="tw-text-sm"
                                                placeholder="Define learning path description here."
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-2">
                                    {courseArray?.length > 1 && (
                                        <Button variant="outline" onClick={() => RemoveOption(idx)}>
                                            <i className="fa-solid fa-xmark"></i>
                                        </Button>
                                    )}
                                    {courseArray?.length == idx + 1 && (
                                        <Button variant="outline" onClick={OptionAdd}>
                                            <i className="fa-solid fa-plus"></i>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                        {courseArray?.length > 0 && (
                            <Button onClick={() => setOpenAlert(true)}>
                                <i className="fa-solid fa-floppy-disk"></i> Save Steps
                            </Button>
                        )}
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AssignCoursesToPath;
