import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import { tanstackApi } from "@/react-query/api";
import { Fullscreen } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

const ContentSCORM = ({ content, Bookmarking }) => {
    const [play_type, setplay_type] = useState("iframe");
    const ref = useRef(null);
    const { fullscreen, updateBookmarking } = usePlayer();
    const [contentFullScreen, setContentFullScreen] = useState(false);
    const [isPlay, setIsPlay] = useState(false);
    const [scormDetails, setScormDetails] = useState(null);
    const [SCO_DATA, setSCO_DATA] = useState(null);
    const [scormEvents, setScormEvents] = useState([]);
    const [completionStatus, setCompletionStatus] = useState("not attempted");
    const [progressPercentage, setProgressPercentage] = useState(0);

    const handleChange = (event) => {
        setplay_type(event.target.value);
    };

    useEffect(() => {
        setScormDetails(null);
        setSCO_DATA(null);
        setIsPlay(false);
        setScormEvents([]);
        setCompletionStatus("not attempted");
        setProgressPercentage(0);
    }, [content?.id]);

    useEffect(() => {
        if (content?.lecture_data) {
            setScormDetails(content.lecture_data);
            const bookmarkedProgress = content.lecture_data?.scorm_progress_data;
            if (bookmarkedProgress) {
                if (bookmarkedProgress["cmi.core.lesson_status"]) {
                    setCompletionStatus(bookmarkedProgress["cmi.core.lesson_status"]);
                    if (["completed", "passed", "failed"].includes(bookmarkedProgress["cmi.core.lesson_status"]))
                        setProgressPercentage(100);
                }
                if (bookmarkedProgress["cmi.completion_status"]) {
                    setCompletionStatus(bookmarkedProgress["cmi.completion_status"]);
                    if (bookmarkedProgress["cmi.completion_status"] === "completed") setProgressPercentage(100);
                }
                if (bookmarkedProgress["cmi.progress_measure"] !== undefined) {
                    const progress = parseFloat(bookmarkedProgress["cmi.progress_measure"]);
                    if (!isNaN(progress)) setProgressPercentage(Math.round(progress * 100));
                }
            }
        }
    }, [content]);

    useEffect(() => {
        if (isPlay && play_type === "iframe" && !fullscreen) toggleContentFullScreen();
    }, [isPlay, play_type, fullscreen]);

    const onScormPlay = async () => {
        if (!scormDetails?.scorm_id) return toast.error("SCORM details not loaded.");
        try {
            const res = await tanstackApi.post(`course/scorm/init-scorm/${scormDetails.scorm_id}`);
            setSCO_DATA(res.data.data);
            setIsPlay(true);
        } catch (err) {
            toast.error("Failed to initialize SCORM. Please try again.");
            setSCO_DATA(null);
        }
    };

    useEffect(() => {
        if (SCO_DATA && scormDetails?.scorm_id && isPlay) {
            const data = {
                data: SCO_DATA,
                basepath: SCO_DATA[scormDetails.scorm_id]?.activity?.base_path,
                opentype: play_type,
                height: "100%",
                width: "100%",
                scormId: scormDetails.scorm_id,
                token: localStorage.getItem("login_token"),
            };
            console.log("Loading SCORM with data:", data);
            if (window.loadDataControl) {
                window.loadDataControl(data);
            } else {
                console.error("window.loadDataControl is not defined. SCORM player cannot be loaded.");
                toast.error("SCORM player component is missing.");
            }
        }
    }, [SCO_DATA, scormDetails, isPlay, play_type]);

    console.log(completionStatus);

    useEffect(() => {
        if (completionStatus == "completed") updateBookmarking(content, true);
    }, [completionStatus]);

    const UpdateScormData = (param) => {
        console.log("SCORM Update param received:", param);
        if (!scormDetails?.scorm_id) return;

        const dataToUpdateBackend = {
            scormId: scormDetails.scorm_id,
            data: param,
        };

        setScormEvents((prev) => [
            ...prev,
            {
                timestamp: new Date().toISOString(),
                data: param,
            },
        ]);

        tanstackApi
            .put("course/scorm/update-scorm-details", dataToUpdateBackend)
            .catch((err) => console.error("Failed to update SCORM details on backend:", err));

        if (param && typeof param === "object") {
            for (const key in param) {
                const value = param[key];

                if (key === "cmi.core.lesson_status") {
                    setCompletionStatus(value);
                    if (["completed", "passed", "failed"].includes(value)) {
                        setProgressPercentage(100);
                    } else if (value === "incomplete" && progressPercentage === 100) {
                        // If it was complete and now it's incomplete, reset progress
                        // (though this scenario is less common unless content is reset)
                        // You might need more nuanced logic here based on content behavior
                    }
                }

                if (key === "cmi.completion_status") {
                    setCompletionStatus(value);
                    if (value === "completed") setProgressPercentage(100);
                }
                if (key === "cmi.success_status") {
                    // Often "passed" or "failed". You might want to update completionStatus
                    // or have a separate state for success_status.
                    // For simplicity, we'll let completion_status primarily drive completion.
                    // Example: if (value === "passed" && completionStatus !== "completed") setCompletionStatus("passed");
                }
                if (key === "cmi.progress_measure") {
                    const progress = parseFloat(value);
                    if (!isNaN(progress)) setProgressPercentage(Math.round(progress * 100));
                }
                if (key === "cmi.score.scaled") {
                    const scaledScore = parseFloat(value);
                    if (!isNaN(scaledScore) && !param["cmi.progress_measure"])
                        setProgressPercentage(Math.round(scaledScore * 100));
                }
            }
        }
    };

    useEffect(() => {
        window.getUpdateFunction(UpdateScormData);
        // Cleanup not strictly necessary if getUpdateFunction overwrites,
        // but good practice if it could accumulate listeners:
        // return () => { if (window.clearUpdateFunction) window.clearUpdateFunction(UpdateScormData); };
    }, [scormDetails?.scorm_id]); // Rerun if scorm_id changes

    useEffect(() => {
        const handleScormEvent = (event) => {
            console.log("Received SCORM message event:", event.data);
        };

        window.addEventListener("message", handleScormEvent);
        return () => window.removeEventListener("message", handleScormEvent);
    }, []);

    const onStopScorm = () => {
        setIsPlay(false);

        if (contentFullScreen) {
            document.exitFullscreen().catch(() => {});
            setContentFullScreen(false);
        }
    };

    const toggleContentFullScreen = () => {
        const elem = ref.current;
        if (!elem) return;

        if (!document.fullscreenElement) {
            elem.requestFullscreen()
                .then(() => setContentFullScreen(true))
                .catch((err) => {
                    toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
                });
        } else {
            document
                .exitFullscreen()
                .then(() => setContentFullScreen(false))
                .catch(() => {});
        }
    };

    useEffect(() => {
        const handleFullscreenChange = () => {
            setContentFullScreen(!!document.fullscreenElement);
        };
        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
    }, []);

    return (
        <div className="content_body_scorm tw-relative tw-flex tw-h-full tw-w-full tw-flex-col tw-items-center tw-justify-center">
            <div
                ref={ref}
                className={cn(
                    "scorm-player-container",
                    isPlay ? "tw-h-[500px] tw-w-full" : "tw-hidden",
                    contentFullScreen ? "scorm-is-fullscreen" : "",
                )}
            >
                {isPlay && <div id="MainScormPlayer" className="tw-h-full tw-w-full" />}
            </div>
            <div className="scorm_controls">
                {isPlay ? (
                    <button onClick={onStopScorm}>
                        <i className="fa-solid fa-circle-stop"></i> <p>Stop Scorm</p>
                    </button>
                ) : (
                    <>
                        <select value={play_type} onChange={handleChange}>
                            <option value="iframe">Embeded</option>
                            <option value="popup">Popup</option>
                        </select>
                        <button onClick={onScormPlay}>
                            <i className="fa-solid fa-circle-play"></i> <p>Play Scorm</p>
                        </button>
                    </>
                )}
            </div>
            <div id="sco_status_box" className="sco_status !tw-hidden" />
            <Button
                onClick={toggleContentFullScreen}
                variant="secondary"
                size="icon"
                className="tw-absolute tw-inset-0 tw-left-0 tw-top-0 tw-z-50"
            >
                <Fullscreen />
            </Button>
        </div>
    );
};

export default ContentSCORM;
