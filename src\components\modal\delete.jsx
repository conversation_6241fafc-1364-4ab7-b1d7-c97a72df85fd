import { deleteCourse } from "@/redux/course/action";
import { DeleteModule } from "@/redux/module/action";
import { deleteRole } from "@/redux/roles/action";
import { Button, Grid, Modal, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

const DeleteModal = ({ open, setOpen, dataID, section }) => {
    const [messageType, setMessageType] = useState("");

    const handleClose = () => {
        setOpen(false);
    };

    const dispatch = useDispatch();

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: "50%",
        // maxWidth: "50%",
        bgcolor: "white",
        boxShadow: 24,
        borderRadius: "5px",
        p: 4,
    };

    const onDeleteHandler = () => {
        if (section === "module") {
            dispatch(DeleteModule(dataID?.id));
            handleClose();
        } else if (section === "level2-roles") {
            const payload = {
                id: dataID,
            };
            dispatch(deleteRole(payload));
        } else if (section === "course-list") {
            dispatch(deleteCourse(dataID));
        }
    };
    useEffect(() => {
        if (section === "module") {
            setMessageType("Module");
        } else if (section === "level2-roles") {
            setMessageType("Role");
        } else if (section === "course-list") {
            setMessageType("Course");
        }
    }, []);

    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
        >
            <Box sx={style}>
                <Grid container>
                    <Typography variant="h5" color="primary" component="h5" m={1} alignItems="center">
                        Are you sure you want to delete this {messageType} ?
                    </Typography>
                    <Grid container xs={12} sm={6} md={12}>
                        <Grid
                            item
                            sx={{ display: "flex", justifyContent: "right", gap: "1rem" }}
                            xs={12}
                            sm={6}
                            md={12}
                        >
                            <Button variant="outlined" color="primary" onClick={handleClose}>
                                Cancel
                            </Button>
                            <Button variant="contained" color="error" onClick={onDeleteHandler}>
                                Yes
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </Box>
        </Modal>
    );
};

export default DeleteModal;
