/* Table  */

.table_lms
    max-height: 500px
    overflow: auto
    &::-webkit-scrollbar
        width: 14px
        height: 13.5px
        background: #e8e9ed
        border-radius: 8px

    &::-webkit-scrollbar-thumb 
        background-color: rgba(16, 23, 42, 0.3)
        border: 2px solid rgba(0,0,0,0)
        border-radius: 8px
        background-clip: content-box !important

    table 
        border-collapse: separate
        border-spacing: 0
        padding-top: 0
        letter-spacing: 0.04rem
        thead 
            position: sticky
            top: 0
            left: 0
            z-index: 10
            background: #fff
            th
                padding: 20px 24px
                border-bottom: 1px solid rgba(68,68,68,.3)
                border-top: 1px solid rgba(68,68,68,.3)
                white-space: nowrap
                background: #10172a
                text-align: center
                text-transform: uppercase
                cursor: default
                font-size: 14px
                line-height: 18px
                font-weight: 500
                color: #00df34
                // &:first-child
                //     padding-left: 0

        tbody
            tr
                cursor: pointer
                &:hover
                    background-color: rgba(0, 0, 0, 0.1)
                    
                td
                    padding: 20px
                    border-bottom: 1px solid rgba(68,68,68,.3)
                    white-space: nowrap
                    font-size: 17px
                    text-align: center
                    line-height: 22px
                    color: #10172a

    @media (max-width: 767px)
        margin-left: 0 !important
        margin-right: 0 !important
        max-height: 75vh

        table 
            thead 
                th
                    padding: 5vw 6.25vw
                    font-size: 4.06vw
                    line-height: 4.69vw

            tbody
                tr      
                    td
                        padding: 20px
                        font-size: 5vw
                        line-height: 6.25vw

        &::-webkit-scrollbar
            display: none
            -webkit-appearance: none

        &::-webkit-scrollbar-thumb 
            display: none
            -webkit-appearance: none

// Breadcrumb 

.breadcrumb_lms
    display: inline-block
    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.35)
    overflow: hidden
    border-radius: 5px
    width: max-content
    a
        text-decoration: none
        outline: none
        display: block
        float: left
        font-size: 14px
        line-height: 36px
        color: white
        padding: 0 10px 0 40px
        background: #666
        background: linear-gradient(#666, #333)
        position: relative

        &:first-child
            padding-left: 26px
            border-radius: 5px 0 0 5px

        &:last-child
            border-radius: 0 5px 5px 0
            padding-right: 20px

        &:hover
            background: #333
            background: linear-gradient(#333, #000)

            &::after
                background: #333
                background: linear-gradient(135deg, #333, #000)

        &::after
            content: ''
            position: absolute
            top: 0 
            right: -18px
            width: 36px 
            height: 36px
            transform: scale(0.707) rotate(45deg)
            z-index: 1
            background: #666
            background: linear-gradient(135deg, #666, #333)
            box-shadow: 2px -2px 0 2px rgba(0, 0, 0, 0.4), 3px -3px 0 2px rgba(255, 255, 255, 0.1)
            border-radius: 0 5px 0 50px

    a.active_brdcrmb
        background: #333
        background: linear-gradient(#333, #000)
        font-weight: 500

        &::after
            background: #333
            background: linear-gradient(135deg, #333, #000)

        &:last-child::after
            content: none

           