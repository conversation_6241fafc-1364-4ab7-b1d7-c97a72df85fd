import {
    Bread<PERSON>rumb,
    Breadc<PERSON>bItem,
    Bread<PERSON>rumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import AddBundlesToGroup from "./AddBundlesToGroup";
import AddCoursesToGroup from "./AddCoursesToGroup";
import Details from "./Details";

const DomainForm = () => {
    const params = useParams();
    const router = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/domain-users">Domains</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{params?.domain_id ? "Edit" : "Create new"} domain</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <Button className="tw-px-2 tw-py-1" onClick={() => router(`/dashboard/domain-users`)}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
                {/* </Link> */}
            </div>
            <Tabs
                defaultValue={searchParams.get("tab") ?? "basic_details"}
                className=""
                onValueChange={(value) => setSearchParams({ tab: value })}
            >
                {params?.domain_id !== undefined && (
                    <TabsList className="tw-grid tw-w-[500px] tw-grid-cols-3">
                        <TabsTrigger value="basic_details" className="tw-gap-2">
                            <i className="fa-solid fa-circle-info"></i> Basic Details
                        </TabsTrigger>
                        <TabsTrigger value="assign_courses" className="tw-gap-2">
                            <i className="fa-solid fa-book"></i> Assign Courses
                        </TabsTrigger>
                        <TabsTrigger value="assign_bundles" className="tw-gap-2">
                            <i className="fa-solid fa-box-open"></i> Assign Bundles
                        </TabsTrigger>
                    </TabsList>
                )}
                <TabsContent value="basic_details">
                    <Details />
                </TabsContent>
                <TabsContent value="assign_courses">
                    <AddCoursesToGroup />
                </TabsContent>
                <TabsContent value="assign_bundles">
                    <AddBundlesToGroup />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default DomainForm;
