import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
    "tw-inline-flex tw-items-center tw-rounded-full tw-border tw-px-2.5 tw-py-0.5 tw-text-xs tw-font-semibold tw-transition-colors tw-focus:tw-outline-none tw-focus:tw-ring-2 tw-focus:tw-ring-ring tw-focus:tw-ring-offset-2",
    {
        variants: {
            variant: {
                default: "tw-border-transparent tw-bg-primary tw-text-primary-foreground hover:tw-bg-primary/80",
                secondary:
                    "tw-border-transparent tw-bg-secondary tw-text-secondary-foreground hover:tw-bg-secondary/80",
                destructive:
                    "tw-border-transparent tw-bg-destructive tw-text-destructive-foreground hover:tw-bg-destructive/80",
                outline: "tw-text-foreground",
                success: "tw-border-transparent tw-bg-green-600 tw-text-white",
                warning: "tw-border-transparent tw-bg-amber-400 tw-text-black",
                "success-outline": "tw-bg-green-100 tw-border-green-500 tw-text-green-700",
                "warning-outline": "tw-bg-amber-100 tw-border-amber-500 tw-text-amber-700",
                "danger-outline": "tw-bg-red-100 tw-border-red-500 tw-text-red-700",
            },
        },
        defaultVariants: {
            variant: "default",
        },
    },
);

function Badge({ className, variant, ...props }) {
    return <div className={cn(badgeVariants({ variant }), className)} {...props} />;
}

export { Badge, badgeVariants };
