import { FETCH_REPORTS_REQ, FETCH_TIMELINE_LOGS_REQ, GET_REPORTS_LIST, GET_TIMELINE_LOGS_LIST } from "@/redux-types";

const initialState = {
    reports: [],
    timelineLogs: [],
    isLoading: true,
    error: null,
};

const ReportReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_REPORTS_REQ:
            return {
                ...state,
                isLoading: true,
            };

        case GET_REPORTS_LIST:
            return {
                ...state,
                isLoading: false,
                reports: action.payload,
                error: action.payload,
            };
        case FETCH_TIMELINE_LOGS_REQ:
            return {
                ...state,
                isLoading: true,
            };

        case GET_TIMELINE_LOGS_LIST:
            return {
                ...state,
                isLoading: false,
                timelineLogs: action.payload,
                error: action.payload,
            };
        default:
            return state;
    }
};

export default ReportReducer;
