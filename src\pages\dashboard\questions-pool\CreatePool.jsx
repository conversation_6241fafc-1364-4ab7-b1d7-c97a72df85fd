import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alogClose,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MultipleSelector from "@/components/ui/multiselect";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const CreatePool = ({ open, setOpen, editData, getPoolData }) => {
    const navigate = useNavigate();
    const [tagsList, setTagsList] = useState([]);
    const [selectedTags, setSelectedTags] = useState([]);
    const [optionsArray, setOptionsArray] = useState([]);

    const [poolName, setPoolName] = useState("");

    useEffect(() => {
        let options = tagsList?.map((data) => {
            return {
                label: data?.display_name,
                value: data?.id,
            };
        });
        setOptionsArray(options);
    }, [tagsList]);

    useEffect(() => {
        if (editData !== null) {
            setPoolName(editData?.name);

            let tags = tagsList?.filter((dt) => editData?.tags?.includes(dt?.display_name));

            let options = tags?.map((data) => {
                return {
                    label: data?.display_name,
                    value: data?.id,
                };
            });
            setSelectedTags(options);
        }
    }, [editData]);

    useEffect(() => {
        getTagsList();
    }, []);

    const getTagsList = async () => {
        await tanstackApi
            .get("tag-master/list-tags")
            .then((res) => {
                setTagsList(res?.data?.data);
            })
            .catch((err) => {
                setTagsList([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (editData !== null) {
            const payload = {
                questionPoolId: editData?.id,
                name: poolName,
                tag_ids: selectedTags?.map((dt) => dt?.value),
            };

            await tanstackApi
                .put("question-pool/update", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "question pool",
                        log: `${poolName} has been updated successfully.`,
                    });
                    getPoolData();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                name: poolName,
                tag_ids: selectedTags?.map((dt) => dt?.value),
            };

            await tanstackApi
                .post("question-pool/create", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "question pool",
                        log: `${poolName} has been created successfully.`,
                    });
                    getPoolData();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-lg">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">{editData !== null ? "Update" : "Create new"} pool</h1>
                        </DialogTitle>
                        <DialogDescription>
                            fill below details according to your question pool convenience.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-5">
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Pool Name</Label>
                                <Input
                                    placeholder="Define pool name here"
                                    onChange={(e) => setPoolName(e.target.value)}
                                    value={poolName}
                                />
                            </div>
                        </div>
                        <div className="tw-flex tw-flex-col tw-gap-2">
                            <Label>Tags</Label>
                            <MultipleSelector
                                commandProps={{
                                    label: "Add tags",
                                }}
                                value={selectedTags}
                                defaultOptions={optionsArray}
                                onChange={(e) => setSelectedTags(e)}
                                placeholder="Add tags"
                                hideClearAllButton
                                hidePlaceholderWhenSelected
                                emptyIndicator={<p className="text-center text-sm">No results found</p>}
                            />
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                Close
                            </Button>
                        </DialogClose>
                        <Button onClick={onDataSubmit}>{editData !== null ? "Update" : "Create"} Pool</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default CreatePool;
