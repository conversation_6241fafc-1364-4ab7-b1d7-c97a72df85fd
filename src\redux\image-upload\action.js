import { FILE_UPLOADED_RESPONSE, UPLOAD_FILE_CONTAINER, UPLOAD_FORM_FILE_CONTAINER } from "@/redux-types";

export const fileUploadReq = (imageData) => {
    return {
        type: UPLOAD_FILE_CONTAINER,
        payload: imageData,
    };
};

export const imageUpload = (imageData) => {
    return {
        type: UPLOAD_FORM_FILE_CONTAINER,
        payload: imageData,
    };
};

export const imageResponse = (data) => {
    return {
        type: FILE_UPLOADED_RESPONSE,
        payload: data,
    };
};
