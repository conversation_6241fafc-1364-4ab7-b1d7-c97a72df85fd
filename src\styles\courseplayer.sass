.course_player_alpha
    // border: 1px solid red
    height: 100dvh
    padding: 1vw
    background: #DCDEE0
    display: grid
    grid-template-rows: 1fr 14fr
    gap: 1vw

.player_header
    display: grid
    grid-template-columns: 1fr 11fr
    gap: .5vw
    .header_logo
        background: #fff
        border-radius: 1vw
        padding: .2vw
        display: flex
        align-items: center
        justify-content: center
        img
            height: 2.2vw
    .header_details
        background: #fff
        border-radius: 1vw
        padding: .7vw
        display: flex
        align-items: center
        justify-content: space-between
        .left
            h5
                font-size: 1.1vw
                line-height: 1.1vw
                font-family: "Lexend", sans-serif
        .right
            display: flex
            align-items: center
            gap: .5vw
            .plyr_breadcrumbs
                p
                    font-size: .8vw
                    line-height: .8vw
                    font-family: "Lexend", sans-serif
                    font-weight: 400
            .plyr_action
                i
                    height: .8vw
                    width: .8vw
                    font-size: .8vw

.player_bottom
    // border: 1px solid red
    display: grid
    gap: 1vw
    max-height: 100%
    overflow: hidden
    grid-template-columns: 16vw auto
.player_minimize
    grid-template-columns: auto

.player_navigation
    display: block
    background: #fff
    border-radius: 1vw
    padding: .8vw
    overflow: auto
    .content_row
        h6
            display: flex
            align-items: center
            gap: .5vw
            padding: .5vw .3vw
            font-size: .8vw
            font-weight: 700
            font-family: "Lexend", sans-serif
            i
                cursor: pointer
                font-size: 1vw
        .row_wrapper
            display: flex
            // align-items: center
            flex-direction: column
            gap: .5vw
            padding: .5vw .5vw
            .row_box
                display: grid
                grid-template-columns: 1.5vw auto
                div
                    i
                        color: #dbd8d8
                        display: inline-block
                        animation: scale-icon 1s ease-in-out infinite

                p
                    font-size: .75vw
                    cursor: pointer
                    font-family: "Lexend", sans-serif
                    &:hover
                        text-decoration: underline
                .active_lecture
                    color: #000
                    font-weight: 500
                .consumed_lecture
                    color: #18AB44
                    // font-weight: 500

@keyframes scale-icon
    0%, 100%
        transform: scale(0)

    50%
        transform: scale(1)

.navigation_minimize
    display: none

.main_player
    display: grid
    gap: 1vw
    overflow: hidden
    height: 100%
    grid-template-rows: 1fr 3.5vw
    &:fullscreen
        background: #DCDEE0
        padding: 1vw
    /* width */
    &::-webkit-scrollbar
        width: 10px

    /* Track */
    &::-webkit-scrollbar-track
        background: #f1f1f1
        border-radius: 1vw

    /* Handle */
    &::-webkit-scrollbar-thumb
        background: #888
        border-radius: 1vw

    /* Handle on hover */
    &::-webkit-scrollbar-thumb:hover
        background: #555

.main_content_box
    // background: #fff
    border-radius: 1.3vw
    background-image: url("/assets/content-bg.png")
    background-position: center
    background-repeat: no-repeat
    background-size: cover
    padding: .8vw
    position: relative
    display: grid
    grid-template-rows: 3.7vw 1fr
    gap: 1vw
    // overflow: auto
    .absolute_div
        position: absolute
        width: 100%
        height: 100%
        inset: 0
        border-radius: 1.3vw
        background: #fff
        opacity: .7
        z-index: 0
    .content_header
        display: grid
        gap: .5vw
        grid-template-columns: 3.7vw auto
        position: relative
        z-index: 1
        .icon_div
            aspect-ratio: 1/1
            background: #00AFF0
            border-radius: .7vw
            display: flex
            align-items: center
            justify-content: center
            i
                color: #fff
                font-size: 1.6vw

        .title_div
            display: flex
            align-items: center
            background: #00AFF0
            border-radius: .7vw
            padding: .8vw 1vw
            h5
                color: #fff
                font-size: 1.3vw
                line-height: 1.3vw
                font-weight: 700
                font-family: "Lexend", sans-serif

.player_cntrls
    // border: 1px solid red
    border-radius: 1vw
    display: flex
    justify-content: space-between
    align-items: center
    .left
        display: flex
        align-items: center
        gap: .5vw
    .middle
        display: flex
        align-items: center
        gap: .5vw
        // padding: 0.5vw 1vw
        .progress_bar
            background: #fff
            border-radius: 1vw
            width: 46px
            height: 46px
            display: flex
            justify-content: center
            align-items: center
            border-radius: 50%
        .progress_details
            background: #fff
            border-radius: 1vw
            display: flex
            align-items: center
            justify-content: space-between
            gap: 1vw
            height: 2.5vw
            padding: 0 .8vw
            b
                color: #535354b4
                font-family: "Lexend", sans-serif
                font-size: .8vw
                line-height: .8vw
            p
                color: #00AFF0
                font-family: "Lexend", sans-serif
                font-size: .7vw
                line-height: .7vw
    .right
        display: flex
        align-items: center
        gap: .5vw

.icon_btns
    width: 2.5vw
    height: 2.5vw
    border-radius: .8vw
    display: flex
    align-items: center
    justify-content: center
    gap: .5vw
    cursor: pointer !important
    background: #fff
    transition: 150ms all ease
    border: 2px solid transparent
    i
        color: #535354b4
        font-size: 1.2vw
    &:hover
        background: #fff
        border: 2px solid #B5C1CA

.plyr_btns
    padding: 0 1vw
    height: 2.8vw
    border-radius: .8vw
    display: flex
    align-items: center
    gap: .5vw
    cursor: pointer !important
    background: #fff
    transition: 150ms all ease
    border: 2px solid transparent
    i
        color: #535354b4
        font-size: 1.25vw
    p
        font-size: 1.25vw
        // line-height: 1.0714285714
        font-weight: 700
        letter-spacing: -0.005em
        font-family: "Lexend", sans-serif
        // font-family: "Public Sans", sans-serif
        color: #535354b4
        margin: 0
        // text-transform: uppercase
    &:hover
        background: #fff
        border: 2px solid #B5C1CA

.content_body
    // overflow: auto
    position: relative
    // max-height: 500px
    z-index: 1
    // border: 1px solid red
    display: grid
    grid-template-columns: 1fr 1fr
    padding: 0 3vw
    gap: 1vw
    h1
        // font-family: "Lexend", sans-serif
    p
        // font-family: "Lexend", sans-serif
    .animate_IMG
        // border: 1px solid red
        height: 100%
        width: 100%
        display: flex
        // align-items: center
        justify-content: center
        .container
            position: relative
            height: 80%
            width: 80%
            overflow: hidden
        img
            object-fit: contain
            height: 100%
            width: 100%
            position: absolute
            inset: 0
            // animation: popup 1s ease-out forwards
            // animation-delay: 0s
            // opacity: 0
            // scale: 1
        .zoom_opts
            position: absolute
            bottom: 0
            right: 0
            display: flex
            gap: .5vw
            background: #fff
            border-radius: 1vw
            padding: .25vw
            button
                all: unset
                display: flex
                align-items: center
                justify-content: center
                font-size: 1vw
    .animate_HTML
        // opacity: 0 // Initial state
        // animation: slideInFromLeft 1s ease-out forwards

.content_body_two
    display: flex
    justify-content: center
    align-items: center
    z-index: 1
    embed
        width: 90%
        height: 90%

.content_body_scorm
    z-index: 1
    #sco_status
        // display: none !important
    #sco_status_box
        // display: none !important
        display: flex
    // align-items: center
        justify-content: center
        flex-direction: column
        gap: 1vw
    embed
        width: 90%
        height: 90%
    .scorm_controls
        display: flex
        align-items: center
        justify-content: center
        gap: .5vw
        select
            padding: 0 .5vw
            outline: none
            height: 2vw
            border-radius: .5vw
            display: flex
            align-items: center
            gap: .3vw
            cursor: pointer !important
            background: #fff
            transition: 150ms all ease
            border: 1px solid #B5C1CA
            font-size: 1vw
            // line-height: 1.0714285714
            font-weight: 600
            letter-spacing: -0.005em
            font-family: "Lexend", sans-serif
            // font-family: "Public Sans", sans-serif
            color: #535354b4
            margin: 0
        button
            padding: .5vw
            height: 2vw
            border-radius: .5vw
            display: flex
            align-items: center
            gap: .3vw
            cursor: pointer !important
            background: #fff
            transition: 150ms all ease
            border: 1px solid #B5C1CA
            i
                color: #535354b4
                font-size: 1vw
            p
                font-size: 1vw
                // line-height: 1.0714285714
                font-weight: 600
                letter-spacing: -0.005em
                font-family: "Lexend", sans-serif
                // font-family: "Public Sans", sans-serif
                color: #535354b4
                margin: 0
                // text-transform: uppercase
            &:hover
                background: #fff
                border: 2px solid #B5C1CA

// @keyframes slideInFromLeft
//     0%
//         opacity: 0
//         // transform: translateX(-100%)

//     100%
//         opacity: 1
//         // transform: translateX(0)

// @keyframes popup
//     0%
//         opacity: 0
//         // transform: scale(0)

//     100%
//         opacity: 1
//         // transform: scale(1)

.course_intro
    text-align: center
    padding: 1vw
    h1
        font-family: "Lexend", sans-serif
        font-size: 1.8vw
        line-height: 1.8vw
        font-weight: 700
    p
        font-family: "Lexend", sans-serif
        font-size: 1vw
        line-height: 1.5vw
        margin-top: 20px

.course_content_arr
    display: flex
    flex-direction: column
    align-items: center
    gap: 1vw
    .content_arr_row
        h6
            display: flex
            align-items: center
            // gap: 1vw
            font-family: "Lexend", sans-serif
            margin-bottom: .2vw
        div
            display: flex
            flex-direction: column
            gap: .3vw
            font-family: "Lexend", sans-serif
            margin-left: 2vw

.customModal
    border-radius: 1vw
    padding: 3vw
    width: 30vw
    h4
        font-family: "Lexend", sans-serif
        font-size: 1vw

    h2
        font-family: "Lexend", sans-serif
        font-size: 1.5vw
        font-weight: normal
        text-align: center
        margin-top: 1vw
    div
        display: flex
        align-items: center
        justify-content: center
        gap: 1vw
        margin: 2vw
        button
            padding: 0 1vw
            height: 2.8vw
            border-radius: .8vw
            display: flex
            align-items: center
            gap: .5vw
            cursor: pointer !important
            background: #535354b4
            transition: 150ms all ease
            border: 2px solid transparent
            i
                color: #fff
                font-size: 1.2vw
            p
                font-size: 1.2vw
                // line-height: 1.0714285714
                font-weight: 500
                letter-spacing: -0.005em
                font-family: "Lexend", sans-serif
                // font-family: "Public Sans", sans-serif
                color: #fff
                margin: 0
                // text-transform: uppercase
            &:hover
                background: #fff
                border: 2px solid #B5C1CA
                i
                    color: #535354b4
                p
                    color: #535354b4

.content_body_homework
    z-index: 1
    padding: 1vw 3.5vw
    .homework_title
        display: flex
        align-items: center
        gap: .5vw
        i
            font-size: 1.6vw

        h1
            font-family: "Lexend", sans-serif
            font-size: 1.7vw
            font-weight: 700
    .homework_description
        padding: .5vw
        p
            font-family: "Lexend", sans-serif
            font-size: .9vw
            font-weight: 400
    .homework_details
        margin-top: .5vw
        display: flex
        align-items: center
        justify-content: left
        gap: 4vw
        border: 1px solid #5353548e
        border-radius: 1vw
        padding: 1vw
        div
            display: flex
            flex-direction: column
            gap: .5vw
            p
                color: #535354d7
                font-family: "Lexend", sans-serif
                font-size: 1vw
            h4
                font-family: "Lexend", sans-serif
                font-size: 1.2vw
            button
                padding: .2vw 1.5vw
                border-radius: 50px
                cursor: pointer !important
                color: #535354
                font-size: .85vw
                font-weight: 500
                font-family: "Lexend", sans-serif
                background: #fff
                border: 1px solid #535354
                transition: 150ms all ease
                &:hover
                    background: #535354
                    color: #fff
    .homework_action
        margin-top: 1vw
        display: flex
        align-items: center
        justify-content: right
        gap: 1vw
        button
            padding: .3vw 1vw
            border-radius: .5vw
            cursor: pointer !important
            color: #fff
            font-size: 1vw
            font-weight: 600
            font-family: "Lexend", sans-serif
            background: #868686
            border: 1px solid transparent
            transition: 150ms all ease
            text-transform: capitalize
            &:hover
                background: #535354
                color: #fff
    .homework_submission
        display: flex
        flex-direction: column
        gap: .2vw
        align-items: flex-start
        textarea
            border: 1px solid #ececec
            border-radius: .5vw
            font-family: "Lexend", sans-serif
            font-size: .9vw
            padding: .5vw
            width: 100%
        input
            display: none
        label
            margin-top: 1vw
            padding: .2vw 1.5vw
            border-radius: 50px
            cursor: pointer !important
            color: #535354
            font-size: .85vw
            font-weight: 500
            font-family: "Lexend", sans-serif
            background: #fff
            border: 1px solid #535354
            transition: 150ms all ease
            &:hover
                background: #535354
                color: #fff
        p
            margin-top: 1vw
            font-size: .8vw
            font-family: "Lexend", sans-serif
        a
            font-size: .8vw
            font-family: "Lexend", sans-serif
            cursor: pointer
            &:hover
                text-decoration: underline
