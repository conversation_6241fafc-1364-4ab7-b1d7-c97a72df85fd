import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { findCountryCode, getUserData } from "@/data/country-language";
import { tanstackApi } from "@/react-query/api";
import { Shopping<PERSON><PERSON>, Trash } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import ViewDetails from "./ViewDetails";

function subTotalPrice(data) {
    return data.reduce((total, item) => total + item.quantity * item.unit_price, 0)?.toFixed(2);
}

const CreateOrderLearner = () => {
    const navigate = useNavigate();
    const [normalPriceList, setNormalPriceList] = useState([]);
    const [cartDetails, setCartDetails] = useState([]);
    const [currencyData, setCurrencyData] = useState(null);
    const [open, setOpen] = useState(false);
    const [editData, setEditData] = useState(null);
    const [openAlert, setOpenAlert] = useState(false);
    const [filterState, setFilterState] = useState({
        search: "",
        users: "",
        validity_type: "",
        subscription_type: "",
    });
    const [filteredData, setFilteredData] = useState([]);
    const [couponCode, setCouponCode] = useState("");
    const [couponDetails, setCouponDetails] = useState(null);
    const [discount, setDiscount] = useState(0);
    const [subTotal, setSubTotal] = useState(0);
    const [total, setTotal] = useState(0);
    const courseData = filteredData.filter((dt) => dt?.course_id !== null);
    const bundleData = filteredData.filter((dt) => dt?.bundle_id !== null);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = normalPriceList?.filter((item) => {
            const matchesname = filterState?.search
                ? (item?.course?.course_title || item?.bundle?.name)
                      ?.toLowerCase()
                      ?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter
            const validity = filterState?.validity_type ? item?.validity_type == filterState?.validity_type : true; // Allow all items if no subCategory filter

            const subscription = filterState?.subscription_type
                ? item?.subscription_type == filterState?.subscription_type
                : true; // Allow all items if no subCategory filter
            return matchesname && validity && subscription; // Both must match
        });
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(normalPriceList);
        setFilterState({
            search: "",
            users: "",
            validity_type: "",
            subscription_type: "",
        });
    };

    useEffect(() => {
        if (currencyData == null) {
            (() => {
                const { language } = getUserData();
                const countryData = findCountryCode(language ?? "USD");
                setCurrencyData(countryData.currency);
            })();
        } else {
            getNormalPriceList();
            getCartDetails();
        }
    }, [currencyData]);

    useEffect(() => {
        if (normalPriceList?.length > 0) {
            setFilteredData(normalPriceList);
        } else {
            setFilteredData([]);
        }
    }, [normalPriceList]);

    useEffect(() => {
        if (cartDetails?.length > 0) {
            setSubTotal(subTotalPrice(cartDetails));
        } else {
            setSubTotal(0);
        }
    }, [cartDetails]);

    useEffect(() => {
        if (couponDetails?.discountValue !== undefined) {
            if (couponDetails?.discountType == "percent") {
                let amount = subTotalPrice(cartDetails);
                setDiscount((amount * couponDetails?.discountValue) / 100);
            } else {
                setDiscount(couponDetails?.discountValue);
            }
        }
    }, [couponDetails]);

    useEffect(() => {
        setTotal((subTotal - discount).toFixed(2));
    }, [discount, subTotal]);

    const getNormalPriceList = async () => {
        await tanstackApi
            .get("course-bundle-prices/list", { params: { currency_code: currencyData.code } })
            .then((res) => setNormalPriceList(res?.data?.data || []))
            .catch(() => setNormalPriceList([]));
    };

    const getCartDetails = async () => {
        await tanstackApi
            .get("cart/list-cart")
            .then((res) => {
                setCartDetails(res?.data?.data);
            })
            .catch((err) => {
                setCartDetails([]);
            });
    };

    const onAddtoCart = async (data) => {
        let alreadyAdded = cartDetails?.map((dt) => dt?.course_id || dt?.bundle_id);
        if (alreadyAdded?.includes(data?.course_id || data?.bundle_id)) {
            toast.warning("Warning", {
                description: "This item is already there in your cart.",
            });
            return false;
        }

        let paylod = {
            quantity: 1,
            unit_price: data?.converted_price,
            course_id: data?.course_id || undefined,
            bundle_id: data?.bundle_id || undefined,
            is_retail_purchase: true,
            subscription_type: data?.subscription_type,
            validity_type: data?.validity_type,
            currency_code: currencyData.code,
        };
        await tanstackApi
            .post("cart/add-to-cart", { ...paylod })
            .then((res) => {
                getCartDetails();
                toast.success("Added item to cart");
                setOpen(false);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const onDeleteCartItem = async (item) => {
        await tanstackApi
            .delete(`cart/delete-cart-item/${item}`)
            .then((res) => {
                getCartDetails();
                toast.success("Removed item from cart");
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const validateCoupon = async () => {
        let paylod = {
            couponCode: couponCode,
            purchaseAmount: subTotalPrice(cartDetails),
        };
        await tanstackApi
            .post("invoice/validate-coupon", { ...paylod })
            .then((res) => {
                toast.success(couponCode, {
                    description: res?.data?.message,
                });
                setCouponDetails(res?.data);
            })
            .catch((err) => {
                toast.warning(couponCode, {
                    description: err?.response?.data?.message,
                });
                setCouponDetails(null);
            });
    };

    const onViewDetails = (data) => {
        setEditData(data);
        setOpen(true);
    };

    const onCheckout = async (e) => {
        e.preventDefault();
        if (cartDetails?.length == 0) {
            return toast.warning("Empty Cart!", {
                description: "Please add items into cart to proceed.",
            });
        }

        let paylod = {
            couponCode: couponCode,
            currency: currencyData.code,
            currency_symbol: currencyData.symbol,
            cancelUrl: `${window.location.origin}/dashboard/shop-learner?page=1`,
            successUrl: `${window.location.origin}/dashboard/shop-learner?page=1&success=true`,
        };

        await tanstackApi
            .post("invoice/create", { ...paylod })
            .then((res) => {
                if (res?.data?.success) {
                    toast.success("Proceed for payment");
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "shop",
                        log: `You have made purchase from shop of amount ${currencyData?.symbol}${total} created successfully`,
                    });
                    window.open(res?.data?.data?.sessionUrl, "_self");
                }
            })
            .catch((err) => {
                toast.error("Something went wrong");
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Continue to payment, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            Click on Pay now button to continue the payment process. Add required details to generate
                            the order. Thanks
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onCheckout}>Yes! Pay Now</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <ViewDetails open={open} setOpen={setOpen} editData={editData} onAddtoCart={onAddtoCart} />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/shop-domain">Shop</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Create new order</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button
                    onClick={() => navigate(`/dashboard/shop-domain`)}
                    variant="outline"
                    className="tw-px-2 tw-py-1"
                >
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
            </div>
            <div className="tw-mt-5 tw-grid tw-grid-cols-[1fr_450px] tw-gap-3">
                <div>
                    <Tabs defaultValue="Courses" className="tw-w-full">
                        <TabsList className="tw-grid tw-w-[220px] tw-grid-cols-2">
                            <TabsTrigger value="Courses" className="tw-flex tw-gap-2">
                                <i className="fa-solid fa-book"></i> Courses
                            </TabsTrigger>
                            <TabsTrigger value="Bundles" className="tw-flex tw-gap-2">
                                <i className="fa-solid fa-box-open"></i> Bundles
                            </TabsTrigger>
                        </TabsList>
                        <div className="tw-mt-3 tw-grid tw-grid-cols-[250px_150px_150px_90px_90px] tw-items-center tw-gap-2">
                            <Input
                                onChange={onFilterChange}
                                name="search"
                                value={filterState?.search}
                                placeholder="Search by name ..."
                            />
                            <Select
                                value={filterState?.validity_type}
                                onValueChange={(e) =>
                                    onFilterChange({
                                        target: {
                                            value: e,
                                            name: "validity_type",
                                        },
                                    })
                                }
                            >
                                <SelectTrigger className="">
                                    <SelectValue placeholder="Validity" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value={"limited"}>Limited</SelectItem>
                                        <SelectItem value={"lifetime"}>Lifetime</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                            <Select
                                value={filterState?.subscription_type}
                                onValueChange={(e) =>
                                    onFilterChange({
                                        target: {
                                            value: e,
                                            name: "subscription_type",
                                        },
                                    })
                                }
                            >
                                <SelectTrigger className="">
                                    <SelectValue placeholder="Subscription" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value={"monthly"}>Monthly</SelectItem>
                                        <SelectItem value={"annually"}>Annually</SelectItem>
                                        <SelectItem value={"one-time"}>One-time</SelectItem>
                                        <SelectItem value={"trial"}>Trial</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                            <Button onClick={onSearch}>
                                <i className="fa-solid fa-magnifying-glass"></i> Search
                            </Button>
                            <Button variant="outline" onClick={onClear}>
                                <i className="fa-solid fa-xmark"></i> Clear
                            </Button>
                        </div>
                        <TabsContent value="Courses">
                            <div className="tw-flex tw-flex-wrap tw-items-start tw-gap-4 tw-px-0 tw-py-2">
                                {courseData.length == 0 ? (
                                    <div className="tw-flex tw-h-[320px] tw-w-full tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden tw-rounded-xl tw-border tw-border-dashed">
                                        <div>No Courses Found</div>
                                    </div>
                                ) : (
                                    courseData.map((course, index) => (
                                        <div
                                            key={index}
                                            className="tw-flex tw-h-[320px] tw-w-[250px] tw-flex-col tw-justify-between tw-overflow-hidden tw-rounded-xl tw-border"
                                        >
                                            <div>
                                                <div
                                                    className="tw-h-[140px] tw-overflow-hidden tw-rounded-lg tw-shadow-sm"
                                                    onClick={() => onViewDetails(course)}
                                                >
                                                    <img
                                                        className="tw-h-full tw-w-full tw-object-cover"
                                                        src={course?.course?.course_banner_url}
                                                        alt=""
                                                    />
                                                </div>
                                                <div className="tw-mt-2 tw-space-y-2 tw-px-2">
                                                    <h1 className="tw-text-md tw-line-clamp-1 tw-font-lexend tw-font-medium tw-text-slate-700">
                                                        {course?.course?.course_title}
                                                    </h1>
                                                    <p className="tw-flex tw-items-center tw-gap-2 tw-text-xs tw-text-slate-400">
                                                        {course?.course?.lms_course_category?.category_name} /{" "}
                                                        {course?.course?.lms_course_sub_category?.sub_category_name}
                                                    </p>
                                                    <div className="tw-flex tw-items-center tw-gap-1 tw-capitalize">
                                                        <Badge variant={"outline"}>{course?.subscription_type}</Badge>
                                                        <Badge variant={"outline"}>{course?.validity_type}</Badge>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="tw-p-2 tw-font-sans tw-text-xl tw-font-medium">
                                                    <h2>
                                                        {/* {course?.currency?.currency_symbol}{" "} */}
                                                        {currencyData?.symbol}
                                                        {course?.converted_price?.toFixed(2)}
                                                    </h2>
                                                </div>
                                                <div className="tw-grid tw-grid-cols-2 tw-gap-2 tw-p-2">
                                                    <Button variant="outline" onClick={() => onViewDetails(course)}>
                                                        <i className="fa-solid fa-eye"></i> Details
                                                    </Button>
                                                    <Button variant="outline" onClick={() => onAddtoCart(course, true)}>
                                                        <i className="fa-solid fa-cart-plus"></i> Add
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </div>
                        </TabsContent>
                        <TabsContent value="Bundles">
                            <div className="tw-flex tw-flex-wrap tw-items-start tw-gap-4 tw-px-0 tw-py-2">
                                {bundleData.length == 0 ? (
                                    <div className="tw-flex tw-h-[320px] tw-w-full tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden tw-rounded-xl tw-border tw-border-dashed">
                                        <div>No Bundles Found</div>
                                    </div>
                                ) : (
                                    bundleData.map((course, index) => (
                                        <div
                                            key={index}
                                            className="tw-flex tw-h-[320px] tw-w-[250px] tw-flex-col tw-justify-between tw-overflow-hidden tw-rounded-xl tw-border-[1px]"
                                        >
                                            <div>
                                                <div className="tw-h-[140px] tw-overflow-hidden tw-rounded-lg tw-shadow-sm">
                                                    <img
                                                        className="tw-h-full tw-w-full tw-object-cover"
                                                        src={course?.bundle?.logo_image_url}
                                                        alt=""
                                                    />
                                                </div>
                                                <div className="tw-mt-2 tw-space-y-1 tw-px-2">
                                                    <h1 className="tw-text-md tw-line-clamp-1 tw-font-lexend tw-font-medium tw-text-slate-700">
                                                        {course?.bundle?.name}
                                                    </h1>
                                                    <p className="tw-line-clamp-1 tw-text-sm tw-text-slate-400">
                                                        {course?.bundle?.description}
                                                    </p>
                                                    <div className="tw-flex tw-items-center tw-gap-1 tw-capitalize">
                                                        <Badge variant={"outline"}>{course?.subscription_type}</Badge>
                                                        <Badge variant={"outline"}>{course?.validity_type}</Badge>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="tw-p-2 tw-font-lexend tw-text-xl">
                                                    <h2>
                                                        {currencyData?.symbol}
                                                        {course?.converted_price?.toFixed(2)}
                                                    </h2>
                                                </div>
                                                <div className="tw-grid tw-grid-cols-2 tw-gap-2 tw-p-2">
                                                    <Button variant="outline" onClick={() => onViewDetails(course)}>
                                                        <i className="fa-solid fa-eye"></i> Details
                                                    </Button>
                                                    <Button variant="outline" onClick={() => onAddtoCart(course, true)}>
                                                        <i className="fa-solid fa-cart-plus"></i> Add
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </div>
                        </TabsContent>
                    </Tabs>
                </div>
                <div className="tw-sticky tw-top-2 tw-flex tw-h-[80vh] tw-flex-col tw-justify-between tw-rounded-2xl tw-border-[1px] tw-p-2">
                    <div className="">
                        <div className="tw-m-2 tw-flex tw-items-center tw-justify-between tw-border-b-[1px] tw-pb-4">
                            <h1 className="tw-flex tw-items-center tw-gap-2 tw-font-lexend tw-text-slate-600">
                                <ShoppingCart /> Cart Summary
                            </h1>
                            <Badge variant={"outline"}>{cartDetails?.length} item</Badge>
                        </div>
                        <div className="custom_scrollbar tw-flex tw-max-h-[40vh] tw-flex-col tw-gap-3 tw-overflow-auto tw-p-3">
                            {cartDetails?.map((cart, index) => (
                                <div
                                    key={index}
                                    className="tw-grid tw-grid-cols-[80px_1fr] tw-gap-2 tw-border-b-[1px] tw-border-dashed tw-pb-2"
                                >
                                    <div className="tw-h-[70px] tw-w-[70px] tw-overflow-hidden tw-rounded-lg tw-shadow-sm">
                                        <img
                                            className="tw-h-full tw-w-full tw-object-cover"
                                            src={cart?.course?.course_banner_url || cart?.bundle?.logo_image_url}
                                            alt=""
                                        />
                                    </div>
                                    <div className="tw-flex tw-flex-col tw-justify-between tw-gap-2">
                                        <h1 className="tw-font tw-text-md tw-line-clamp-1 tw-font-normal">
                                            {cart?.course?.course_title || cart?.bundle?.name}
                                        </h1>
                                        <div className="tw-space-x-1">
                                            <Badge variant={"outline"}>
                                                {cart?.course !== null ? "Course" : "Bundle"}
                                            </Badge>
                                            <Badge variant={"outline"} className="tw-capitalize">
                                                {cart?.subscription_type}
                                            </Badge>
                                            <Badge variant={"outline"} className="tw-capitalize">
                                                {cart?.validity_type}
                                            </Badge>
                                        </div>
                                        <div className="tw-flex tw-items-center tw-justify-between">
                                            <div className="tw-flex tw-items-center tw-gap-1">
                                                {cart?.is_retail_purchase ? (
                                                    <p className="tw-font-medium tw-text-gray-400">One unit</p>
                                                ) : (
                                                    <p className="tw-font-medium tw-text-gray-400">
                                                        {`${cart?.bulk_price_details?.lower_limit} - ${
                                                            cart?.bulk_price_details?.upper_limit
                                                        }`}{" "}
                                                        Users
                                                    </p>
                                                )}
                                            </div>
                                            <div className="tw-flex tw-items-center tw-gap-1">
                                                <h1 className="tw-text-md tw-p-1 tw-font-lexend">
                                                    {currencyData?.symbol}
                                                    {(cart?.unit_price * cart?.quantity)?.toFixed(2)}
                                                </h1>
                                                <div
                                                    className="tw-cursor-pointer tw-rounded-full tw-bg-red-100 tw-p-1 tw-text-red-400 hover:tw-bg-slate-200"
                                                    onClick={() => onDeleteCartItem(cart?.id)}
                                                >
                                                    <Trash size={14} />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="tw-space-y-3 tw-p-4">
                        {couponDetails?.discountValue && (
                            <div>
                                You have got {couponDetails?.discountValue}{" "}
                                {couponDetails?.discountType == "percent" ? "%" : currencyData?.symbol} discount on{" "}
                                {couponCode}.
                            </div>
                        )}
                        <div className="tw-mb-2 tw-grid tw-grid-cols-[1fr_80px] tw-gap-2">
                            <Input onChange={(e) => setCouponCode(e.target.value)} placeholder="Enter Coupon Code" />
                            <Button variant="outline" onClick={validateCoupon}>
                                {couponDetails ? "Applied" : "Apply"}
                            </Button>
                        </div>

                        <div className="tw-space-y-2">
                            <div className="tw-flex tw-justify-between">
                                <p className="tw-text-sm">Subtotal</p>
                                <h1 className="tw-text-md tw-font-lexend">
                                    {currencyData?.symbol}
                                    {subTotal}
                                </h1>
                            </div>
                            <div className="tw-flex tw-justify-between">
                                <p className="tw-text-sm">Discount</p>
                                <h1 className="tw-text-md tw-font-lexend">
                                    {discount && `- ${currencyData?.symbol}${discount}`}
                                </h1>
                            </div>
                        </div>
                        <div className="tw-flex tw-justify-between">
                            <p className="tw-text-lg">Total</p>
                            <h1 className="tw-font-lexend tw-text-xl tw-font-semibold">
                                {currencyData?.symbol}
                                {total}
                            </h1>
                        </div>
                        <div className="tw-mt-2">
                            <Button onClick={() => setOpenAlert(true)} variant="" className="tw-w-full tw-text-lg">
                                Checkout <i className="fa-solid fa-cart-shopping"></i>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreateOrderLearner;
