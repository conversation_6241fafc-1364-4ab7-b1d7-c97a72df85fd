import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    Di<PERSON>Header,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

function getDurationInMinutes(startTime, endTime) {
    // Split the start and end time strings into hours, minutes, and seconds
    const [startHours, startMinutes, startSeconds] = startTime.split(":").map(Number);
    const [endHours, endMinutes, endSeconds] = endTime.split(":").map(Number);

    // Convert start and end times to total minutes since midnight
    const startTotalMinutes = startHours * 60 + startMinutes + startSeconds / 60;
    const endTotalMinutes = endHours * 60 + endMinutes + endSeconds / 60;

    // Calculate the difference in minutes
    const duration = parseInt(endTotalMinutes - startTotalMinutes);

    return duration;
}

const PricingAddEdit = ({ open, setOpen, editData, getPriceList, activeTab }) => {
    const navigate = useNavigate();
    const [openAlert, setOpenAlert] = useState(false);

    const [courseList, setCourseList] = useState([]);
    const [bundleList, setBundleList] = useState([]);
    const [currencyList, setCurrencyList] = useState([]);

    const [pricingDetails, setPricingDetails] = useState({
        course_id: null,
        bundle_id: null,
        price: null,
        is_free: false,
        validity_type: "lifetime",
        subscription_type: "trial",
        valid_days: null,
        currency_id: null,
    });

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        if (name == "is_free") {
            setPricingDetails({ ...pricingDetails, [name]: !pricingDetails?.is_free });
        } else {
            setPricingDetails({ ...pricingDetails, [name]: value });
        }
    };

    useEffect(() => {
        if (editData !== null) {
            setPricingDetails({
                course_id: editData?.course_id,
                bundle_id: editData?.bundle_id,
                price: editData?.price,
                is_free: editData?.is_free,
                validity_type: editData?.validity_type,
                subscription_type: editData?.subscription_type,
                valid_days: editData?.valid_days,
                currency_id: editData?.currency_id,
            });
        } else {
            setPricingDetails({
                course_id: null,
                bundle_id: null,
                price: null,
                is_free: false,
                validity_type: "lifetime",
                subscription_type: "trial",
                valid_days: null,
                currency_id: null,
            });
        }
    }, [editData]);

    const onClearForm = () => {
        setPricingDetails({
            course_id: null,
            bundle_id: null,
            price: "",
            is_free: false,
            validity_type: "lifetime",
            subscription_type: "trial",
            valid_days: "",
            currency_id: null,
        });
    };

    useEffect(() => {
        getCurrency();
    }, []);

    useEffect(() => {
        if (activeTab == "Course Pricing") {
            getCourses();
        } else {
            getCourseBundles();
        }
    }, [activeTab]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getCourseBundles = async (payload) => {
        await tanstackApi
            .get("course-bundle/get-course-bundles")
            .then((res) => {
                setBundleList(res?.data?.data);
            })
            .catch((err) => {
                setBundleList([]);
            });
    };

    const getCurrency = async (payload) => {
        await tanstackApi
            .get("currency/listing")
            .then((res) => {
                setCurrencyList(res?.data?.data);
            })
            .catch((err) => {
                setCurrencyList([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!pricingDetails?.course_id && activeTab == "Course Pricing") {
            toast.warning("Course", {
                description: "Course selection is required",
            });
            return false;
        }

        if (!pricingDetails?.bundle_id && activeTab == "Bundle Pricing") {
            toast.warning("Bundle", {
                description: "Bundle selection is required",
            });
            return false;
        }

        if (!pricingDetails?.validity_type) {
            toast.warning("Validity type", {
                description: "Validity type is required",
            });
            return false;
        }
        if (!pricingDetails?.subscription_type) {
            toast.warning("Subscription type", {
                description: "Subscription type is required",
            });
            return false;
        }
        if (!pricingDetails?.price && pricingDetails?.is_free == false) {
            toast.warning("Price", {
                description: "Pricing is required",
            });
            return false;
        }
        if (!pricingDetails?.valid_days) {
            toast.warning("Valid days", {
                description: "Valid days are required",
            });
            return false;
        }
        if (!pricingDetails?.currency_id && pricingDetails?.is_free == false) {
            toast.warning("Currency", {
                description: "Currency selection is required",
            });
            return false;
        }

        if (editData !== null) {
            const payload = {
                course_id: pricingDetails?.course_id || undefined,
                bundle_id: pricingDetails?.bundle_id || undefined,
                price: pricingDetails?.is_free ? 0 : pricingDetails?.price,
                is_free: pricingDetails?.is_free,
                validity_type: pricingDetails?.validity_type,
                subscription_type: pricingDetails?.subscription_type,
                valid_days: pricingDetails?.valid_days,
                currency_id: pricingDetails?.is_free ? null : pricingDetails?.currency_id,
            };

            await tanstackApi
                .put(`course-bundle-prices/update/${editData?.id}`, { ...payload })
                .then((res) => {
                    getPriceList();
                    toast.success("Pricing Updated", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                course_id: pricingDetails?.course_id || undefined,
                bundle_id: pricingDetails?.bundle_id || undefined,
                price: pricingDetails?.is_free ? 0 : pricingDetails?.price,
                is_free: pricingDetails?.is_free,
                validity_type: pricingDetails?.validity_type,
                subscription_type: pricingDetails?.subscription_type,
                valid_days: pricingDetails?.valid_days,
                currency_id: pricingDetails?.currency_id,
            };

            await tanstackApi
                .post("course-bundle-prices/create", { ...payload })
                .then((res) => {
                    getPriceList();
                    toast.success("Pricing Created", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">
                                {editData ? "Update" : "Add"} {activeTab}
                            </h1>
                        </DialogTitle>
                        <DialogDescription>Fill below details add {activeTab}.</DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-grid tw-grid-cols-1 tw-gap-3">
                            {activeTab == "Course Pricing" ? (
                                <div className="tw-space-y-1">
                                    <Label htmlFor="name">Courses *</Label>
                                    <Select
                                        onValueChange={(e) =>
                                            onHandleChange({ target: { value: e, name: "course_id" } })
                                        }
                                        value={pricingDetails?.course_id}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select course" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectGroup>
                                                {courseList?.map((data, idx) => (
                                                    <SelectItem key={idx} value={data?.id}>
                                                        {data?.course_title}
                                                    </SelectItem>
                                                ))}
                                            </SelectGroup>
                                        </SelectContent>
                                    </Select>
                                </div>
                            ) : (
                                <div className="tw-space-y-1">
                                    <Label htmlFor="name">Bundles *</Label>
                                    <Select
                                        onValueChange={(e) =>
                                            onHandleChange({ target: { value: e, name: "bundle_id" } })
                                        }
                                        value={pricingDetails?.bundle_id}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select bundles" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectGroup>
                                                {bundleList?.map((data, idx) => (
                                                    <SelectItem key={idx} value={data?.id}>
                                                        {data?.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectGroup>
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </div>
                        <div className="tw-grid tw-grid-cols-3 tw-items-end tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Validity type *</Label>
                                <Select
                                    onValueChange={(e) =>
                                        onHandleChange({ target: { value: e, name: "validity_type" } })
                                    }
                                    value={pricingDetails?.validity_type}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select course" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            <SelectItem value={"limited"}>Limited</SelectItem>
                                            <SelectItem value={"lifetime"}>Lifetime</SelectItem>
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Subscription type *</Label>
                                <Select
                                    onValueChange={(e) =>
                                        onHandleChange({ target: { value: e, name: "subscription_type" } })
                                    }
                                    value={pricingDetails?.subscription_type}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select course" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            <SelectItem value={"monthly"}>Monthly</SelectItem>
                                            <SelectItem value={"annually"}>Annually</SelectItem>
                                            <SelectItem value={"one-time"}>One-time</SelectItem>
                                            <SelectItem value={"trial"}>Trial</SelectItem>
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="tw-mb-2">
                                <div className="tw-flex tw-items-center tw-space-x-2">
                                    <Switch
                                        id="airplane-mode"
                                        checked={pricingDetails?.is_free}
                                        onCheckedChange={(e) =>
                                            onHandleChange({ target: { value: e, name: "is_free" } })
                                        }
                                    />
                                    <Label htmlFor="airplane-mode">Free!</Label>
                                </div>
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-4 tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Valid Days</Label>
                                <Input
                                    placeholder="Enter valid days"
                                    name="valid_days"
                                    type="number"
                                    onChange={onHandleChange}
                                    value={pricingDetails?.valid_days}
                                />
                            </div>
                            {!pricingDetails?.is_free && (
                                <>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="name">Price</Label>
                                        <Input
                                            placeholder="Define price"
                                            name="price"
                                            type="number"
                                            onChange={onHandleChange}
                                            value={pricingDetails?.price}
                                        />
                                    </div>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="name">Currency *</Label>
                                        <Select
                                            onValueChange={(e) =>
                                                onHandleChange({ target: { value: e, name: "currency_id" } })
                                            }
                                            value={pricingDetails?.currency_id}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select currency" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    {currencyList?.map((curr, idx) => (
                                                        <SelectItem value={curr?.id} key={idx}>
                                                            {curr?.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <Button type="button" variant="secondary" onClick={onClearForm}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>
                        <Button onClick={onDataSubmit}>
                            <i className="fa-regular fa-floppy-disk"></i> {editData ? "Update" : "Create"} Pricing
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default PricingAddEdit;
