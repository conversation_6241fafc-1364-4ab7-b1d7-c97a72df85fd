import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { Info } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "sonner";

const today = new Date().toISOString().split("T")[0];

const defaultValue = {
    course_accessed_by: "PUBLIC",
    course_code: "",
    is_course_timeline: true,
    navigation_sidenav: true,
    expiration_date: "",
    course_certificate_id: "",
    course_published_type: "PUBLISHED",
    start_last_date: "",
    start_days: null,
    completion_days: null,
    completion_last_date: "",
    certificate_accept_days: null,
    certificate_accept_last_date: "",
    attempts_allowed: 0,
    passing_marks: 0,
    total_marks: 0,
};

const CourseSettings = ({ CourseData, getCourses }) => {
    const timeline = ["Timeline", "No Timeline"];

    const handleClear = () => {
        const settings = CourseData?.courseSettings || {};
        setCourseSettings({
            course_accessed_by: settings.course_accessed_by || defaultValue.course_accessed_by,
            course_code: settings.course_code || defaultValue.course_code,
            is_course_timeline: settings.is_course_timeline ?? defaultValue.is_course_timeline,
            navigation_sidenav: settings.navigation_sidenav ?? defaultValue.navigation_sidenav,
            expiration_days: settings.expiration_days || defaultValue.expiration_days,
            expiration_date: settings.expiration_date?.slice(0, 10) || defaultValue.expiration_date,
            course_certificate_id: settings.course_certificate_id || defaultValue.course_certificate_id,
            course_published_type: settings.course_published_type || defaultValue.course_published_type,
            start_last_date: settings.start_last_date?.slice(0, 10) || defaultValue.start_last_date,
            start_days: settings.start_days || defaultValue.start_days,
            completion_days: settings.completion_days || defaultValue.completion_days,
            completion_last_date: settings.completion_last_date?.slice(0, 10) || defaultValue.completion_last_date,
            certificate_accept_days: settings.certificate_accept_days || defaultValue.certificate_accept_days,
            certificate_accept_last_date:
                settings.certificate_accept_last_date?.slice(0, 10) || defaultValue.certificate_accept_last_date,
            attempts_allowed: settings.attempts_allowed || defaultValue.attempts_allowed,
            passing_marks: settings.passing_marks || defaultValue.passing_marks,
            total_marks: settings.total_marks || defaultValue.total_marks,
        });
        setSelectedOption(settings.is_course_timeline ? "Timeline" : "No Timeline");
    };
    const params = useParams();
    const [certificateList, setCertificateList] = useState([]);
    const [courseSettings, setCourseSettings] = useState(defaultValue);
    const [selectedOption, setSelectedOption] = useState("No Timeline");

    const handleChange = (value) => {
        setSelectedOption(value);
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        if (name == "navigation_sidenav") {
            setCourseSettings({ ...courseSettings, [name]: !courseSettings?.navigation_sidenav });
        } else {
            setCourseSettings({ ...courseSettings, [name]: value });
        }
    };

    useEffect(() => {
        getCertificate();
    }, []);

    useEffect(() => {
        if (CourseData !== null) {
            setCourseSettings({
                course_accessed_by: CourseData?.courseSettings?.course_accessed_by,
                course_code: CourseData?.courseSettings?.course_code,
                is_course_timeline: CourseData?.courseSettings?.is_course_timeline,
                navigation_sidenav: CourseData?.courseSettings?.navigation_sidenav,
                expiration_days: CourseData?.courseSettings?.expiration_days,
                expiration_date: CourseData?.courseSettings?.expiration_date?.slice(0, 10),
                course_certificate_id: CourseData?.courseSettings?.course_certificate_id,
                course_published_type: CourseData?.courseSettings?.course_published_type,
                start_last_date: CourseData?.courseSettings?.start_last_date?.slice(0, 10),
                start_days: CourseData?.courseSettings?.start_days,
                completion_days: CourseData?.courseSettings?.completion_days,
                completion_last_date: CourseData?.courseSettings?.completion_last_date?.slice(0, 10),
                certificate_accept_days: CourseData?.courseSettings?.certificate_accept_days || 0,
                certificate_accept_last_date: CourseData?.courseSettings?.certificate_accept_last_date?.slice(0, 10),
                attempts_allowed: CourseData?.courseSettings?.attempts_allowed || 0,
                passing_marks: CourseData?.courseSettings?.passing_marks || 0,
                total_marks: CourseData?.courseSettings?.total_marks || 0,
            });
            setSelectedOption(CourseData?.courseSettings?.is_course_timeline ? "Timeline" : "No Timeline");
        }
    }, [CourseData]);

    const getCertificate = async (payload) => {
        await tanstackApi
            .get("certificate/get-certificates/course-completion")
            .then((res) => {
                setCertificateList(res?.data?.data?.certificateList);
            })
            .catch((err) => {
                setCertificateList([]);
            });
    };

    const handleDate = (value, name) => {
        const today = new Date();
        const selectedDate = new Date(value);

        const timeDifference = selectedDate.getTime() - today.getTime();
        const daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

        setCourseSettings({
            ...courseSettings,
            [name]: daysDifference,
        });
    };

    useEffect(() => {
        if (courseSettings?.expiration_date) {
            handleDate(courseSettings?.expiration_date, "expiration_days");
        }
    }, [courseSettings?.expiration_date]);

    useEffect(() => {
        if (courseSettings?.start_last_date) {
            handleDate(courseSettings?.start_last_date, "start_days");
        }
    }, [courseSettings?.start_last_date]);

    useEffect(() => {
        if (courseSettings?.completion_last_date) {
            handleDate(courseSettings?.completion_last_date, "completion_days");
        }
    }, [courseSettings?.completion_last_date]);

    useEffect(() => {
        if (courseSettings?.certificate_accept_last_date) {
            handleDate(courseSettings?.certificate_accept_last_date, "certificate_accept_days");
        }
    }, [courseSettings?.certificate_accept_last_date]);

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!courseSettings.course_certificate_id) {
            toast.warning("Certificate", {
                description: `Certificate selection is required.`,
            });
            return false;
        }

        const payload = {
            course_id: params?.course_id,
            step: "settings",
            data: {
                course_accessed_by: courseSettings?.course_accessed_by || "PUBLIC",
                course_code: courseSettings?.course_code || "ABC0123",
                is_course_timeline: selectedOption === "Timeline" ? true : false,
                navigation_sidenav: courseSettings?.navigation_sidenav,
                expiration_days: courseSettings?.expiration_days || undefined,
                expiration_date: courseSettings?.expiration_date || undefined,
                course_certificate_id: courseSettings?.course_certificate_id || undefined,
                course_published_type: courseSettings?.course_published_type || "PUBLISHED",
                start_last_date: courseSettings?.start_last_date || undefined,
                start_days: courseSettings?.start_days || undefined,
                completion_days: courseSettings?.completion_days || undefined,
                completion_last_date: courseSettings?.completion_last_date || undefined,
                certificate_accept_days: courseSettings?.certificate_accept_days || undefined,
                certificate_accept_last_date: courseSettings?.certificate_accept_last_date || undefined,
                attempts_allowed: courseSettings?.attempts_allowed || 2,
                passing_marks: courseSettings?.passing_marks || 0,
                total_marks: courseSettings?.total_marks || 0,
            },
        };

        await tanstackApi
            .post("course/creation/update-course-details", { ...payload })
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("userId"),
                    event: "course",
                    log: `${CourseData.course_title} settings updated successfully`,
                });
                toast.success("Saved Successfully", {
                    description: res?.data?.message,
                });
                getCourses({ course_id: params?.course_id });
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Course settings</CardTitle>
                <CardDescription>
                    Add course timeline, validity & certificate details here. Click save when you&apos;re done.
                </CardDescription>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-1 tw-gap-7">
                    <div className="tw-space-y-3">
                        <div className="tw-grid tw-grid-cols-[200px_150px_150px_150px_auto] tw-gap-2">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Course Code</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={courseSettings?.course_code}
                                    name="course_code"
                                    id="name"
                                    placeholder="eg: ABCD12345"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="attempts_allowed">Attempts</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={courseSettings?.attempts_allowed}
                                    name="attempts_allowed"
                                    id="attempts_allowed"
                                    type="number"
                                    placeholder="Attempts allowed here"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="total_marks">Total Marks</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={courseSettings?.total_marks}
                                    name="total_marks"
                                    id="total_marks"
                                    type="number"
                                    placeholder="Enter total Marks here"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="passing_marks">Passing Marks</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={courseSettings?.passing_marks}
                                    name="passing_marks"
                                    id="passing_marks"
                                    type="number"
                                    placeholder="Enter passing Marks here"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-[400px_auto] tw-gap-2">
                            <div className="tw-w-full tw-space-y-1">
                                <Label htmlFor="username">Certificate *</Label>
                                <Select
                                    onValueChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "course_certificate_id" } })
                                    }
                                    value={courseSettings?.course_certificate_id}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose Certificate" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {certificateList?.map((data, idx) => (
                                            <SelectItem key={idx} value={data?.id}>
                                                {data?.title}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="tw-ml-5 tw-flex tw-items-center tw-space-y-3 tw-pt-3">
                                <RadioGroup value={selectedOption} onValueChange={handleChange} className="tw-flex">
                                    <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                                        {timeline?.map((data, idx) => (
                                            <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                                <RadioGroupItem value={data} id={data} />
                                                <Label htmlFor={data} className="tw-cursor-pointer">
                                                    {data}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                    </div>
                    {selectedOption == "Timeline" && (
                        <div className="tw-space-y-3">
                            <div className="tw-grid tw-grid-cols-[200px_170px_auto] tw-gap-2">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="expiration_days">Expiration Days</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.expiration_days}
                                        name="expiration_days"
                                        id="expiration_days"
                                        placeholder="Define Expiration Days here"
                                    />
                                </div>
                                <div className="tw-space-y-1">
                                    <Label htmlFor="expiration_date">Expiration Date</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.expiration_date}
                                        min={today}
                                        name="expiration_date"
                                        id="expiration_date"
                                        type="date"
                                    />
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[200px_170px_auto] tw-gap-2">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="start_days">Start Days</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.start_days}
                                        name="start_days"
                                        id="start_days"
                                        placeholder="Course Start Days here"
                                    />
                                </div>
                                <div className="tw-space-y-1">
                                    <Label htmlFor="start_last_date">Start Date</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.start_last_date}
                                        min={today}
                                        name="start_last_date"
                                        id="start_last_date"
                                        type="date"
                                    />
                                </div>
                                <div className="tw-ml-2 tw-flex tw-items-center tw-gap-2 tw-pt-3">
                                    <Info />{" "}
                                    <label htmlFor="name">This is the last date to start or commence the course</label>
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[200px_170px_auto] tw-gap-2">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="completion_days">Completion Days</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.completion_days}
                                        name="completion_days"
                                        id="completion_days"
                                        placeholder="Course Completion Days here"
                                    />
                                </div>
                                <div className="tw-space-y-1">
                                    <Label htmlFor="completion_last_date">Completion Date</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.completion_last_date}
                                        min={today}
                                        name="completion_last_date"
                                        id="completion_last_date"
                                        type="date"
                                    />
                                </div>
                                <div className="tw-ml-2 tw-flex tw-items-center tw-gap-2 tw-pt-3">
                                    <Info /> <label htmlFor="name">This is the last date to complete the course</label>
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[200px_170px_auto] tw-gap-2">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="certificate_accept_days">Certificate Issue Days</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.certificate_accept_days}
                                        name="certificate_accept_days"
                                        id="certificate_accept_days"
                                        placeholder="Certificate issue Days here"
                                    />
                                </div>
                                <div className="tw-space-y-1">
                                    <Label htmlFor="certificate_accept_last_date">Certificate Issue Date</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={courseSettings?.certificate_accept_last_date}
                                        min={today}
                                        name="certificate_accept_last_date"
                                        id="certificate_accept_last_date"
                                        type="date"
                                    />
                                </div>
                                <div className="tw-ml-2 tw-flex tw-items-center tw-gap-2 tw-pt-3">
                                    <Info />{" "}
                                    <label htmlFor="name">
                                        On this date learners will get their certificate after course completion
                                    </label>
                                </div>
                            </div>
                        </div>
                    )}
                    <div className="tw-grid tw-grid-cols-[400px_auto] tw-gap-2">
                        <div className="tw-items-top tw-flex tw-space-x-2">
                            <Checkbox
                                id="navigation_sidenav"
                                checked={courseSettings?.navigation_sidenav}
                                onCheckedChange={(e) =>
                                    onChangeHandle({ target: { value: e, name: "navigation_sidenav" } })
                                }
                                name="navigation_sidenav"
                            />
                            <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                <label
                                    htmlFor="navigation_sidenav"
                                    className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                >
                                    Navigation Through Sidenav
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" variant="outline" onClick={handleClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> Save
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default CourseSettings;
