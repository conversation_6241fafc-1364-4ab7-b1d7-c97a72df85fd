import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import MapModal from "./MapModal";
import { useZoomCredCheck } from "@/react-query/zoom";

function getDurationInMinutes(startTime, endTime) {
    // Split the start and end time strings into hours, minutes, and seconds
    const [startHours, startMinutes, startSeconds] = startTime.split(":").map(Number);
    const [endHours, endMinutes, endSeconds] = endTime.split(":").map(Number);

    // Convert start and end times to total minutes since midnight
    const startTotalMinutes = startHours * 60 + startMinutes + startSeconds / 60;
    const endTotalMinutes = endHours * 60 + endMinutes + endSeconds / 60;

    // Calculate the difference in minutes
    const duration = parseInt(endTotalMinutes - startTotalMinutes);

    return duration;
}

const CreateNewZoomClass = ({ open, setOpen, editData, params, getSchdedules }) => {
    const [openMapModal, setOpenMapModal] = useState(false);
    const zoomCredCheck = useZoomCredCheck();
    const mode = [
        {
            label: "Zoom Meet",
            value: "zoom",
            disabled: zoomCredCheck.data?.success == false,
        },
        {
            label: "Google Meet",
            value: "google-meet",
        },
    ];
    const [loadingState, setLoadingState] = useState(false);
    const [locationDetails, setLocationDetails] = useState(null);
    const [scheduleDetails, setScheduleDetails] = useState({
        chapter_id: null,
        topic: "",
        overview: "",
        type: "online",
        meet_type: zoomCredCheck.data?.success ? "zoom" : "google-meet",
        start_time: "",
        end_time: "",
        duration: "",
        date: "",
        latitude: "",
        longitude: "",
    });

    const handleClear = () => {
        setScheduleDetails((prev) => ({
            ...prev,
            chapter_id: null,
            topic: "",
            overview: "",
            type: "online",
            start_time: "",
            end_time: "",
            duration: "",
            date: "",
            latitude: "",
            longitude: "",
        }));
    };

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setScheduleDetails({ ...scheduleDetails, [name]: value });
    };

    useEffect(() => {
        if (editData !== null) {
            setScheduleDetails({
                chapter_id: editData?.chapter_id,
                topic: editData?.topic,
                overview: editData?.overview,
                type: editData?.type,
                meet_type: editData?.meet_type,
                start_time: editData?.start_time,
                end_time: editData?.end_time,
                duration: editData?.duration,
                date: editData?.date,
                location: editData?.location,
                latitude: editData?.latitude,
                longitude: editData?.longitude,
            });
        }
    }, [editData]);

    useEffect(() => {
        if (scheduleDetails?.start_time && scheduleDetails?.end_time) {
            setScheduleDetails({
                ...scheduleDetails,
                duration: getDurationInMinutes(scheduleDetails?.start_time, scheduleDetails?.end_time),
            });
        }
    }, [scheduleDetails?.start_time, scheduleDetails?.end_time]);

    useEffect(() => {
        if (scheduleDetails?.latitude && scheduleDetails?.longitude) {
            getAddress(scheduleDetails?.latitude, scheduleDetails?.longitude);
        }
    }, [scheduleDetails]);

    const getAddress = async (lat, lng) => {
        try {
            const response = await fetch(
                `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`,
            );
            const data = await response.json();
            setLocationDetails(data);
        } catch (error) {
            console.error("Error fetching location details:", error);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!scheduleDetails?.topic) {
            toast.warning("Topic", {
                description: "Topic is required",
            });
            return false;
        }
        if (!scheduleDetails?.date) {
            toast.warning("Date", {
                description: "date selection is required",
            });
            return false;
        }
        if (!scheduleDetails?.start_time) {
            toast.warning("Start time", {
                description: "Start time selection is required",
            });
            return false;
        }
        if (!scheduleDetails?.end_time) {
            toast.warning("End time", {
                description: "End time selection is required",
            });
            return false;
        }

        if (editData !== null) {
            const payload = {
                chapter_id: params?.chapter_id,
                topic: scheduleDetails?.topic,
                overview: scheduleDetails?.overview,
                type: scheduleDetails?.type,
                meet_type: scheduleDetails?.type == "offline" ? undefined : scheduleDetails?.meet_type,
                location: scheduleDetails?.location || undefined,
                start_time: scheduleDetails?.start_time,
                end_time: scheduleDetails?.end_time,
                duration: scheduleDetails?.duration?.toString(),
                date: scheduleDetails?.date,
                latitude: scheduleDetails?.latitude?.toString() || undefined,
                longitude: scheduleDetails?.longitude?.toString() || undefined,
            };
            setLoadingState(true);
            await tanstackApi
                .put(`chapter-class/update/${editData?.id}`, { ...payload })
                .then((res) => {
                    getSchdedules();

                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "zoom module",
                        log: `Meeting schedule has been updated for Chapter: ${params?.chapter_title} successfully.`,
                    });
                    toast.success("Schedule Updated", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                })
                .finally(() => {
                    setLoadingState(false);
                });
        } else {
            const payload = {
                chapter_id: params?.chapter_id,
                topic: scheduleDetails?.topic,
                overview: scheduleDetails?.overview,
                type: scheduleDetails?.type,
                meet_type: scheduleDetails?.type == "offline" ? undefined : scheduleDetails?.meet_type,
                location: scheduleDetails?.location || undefined,
                start_time: scheduleDetails?.start_time,
                end_time: scheduleDetails?.end_time,
                duration: scheduleDetails?.duration?.toString(),
                date: scheduleDetails?.date,
                latitude: scheduleDetails?.latitude?.toString() || undefined,
                longitude: scheduleDetails?.longitude?.toString() || undefined,
            };
            setLoadingState(true);
            await tanstackApi
                .post("chapter-class/create", { ...payload })
                .then((res) => {
                    getSchdedules();
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "zoom module",
                        log: `Meeting schedule has been created for Chapter: ${params?.chapter_title} successfully.`,
                    });
                    toast.success("Schedule Created", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                })
                .finally(() => {
                    setLoadingState(false);
                });
        }
    };

    const onOpenMap = () => {
        setOpenMapModal(true);
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <div>
            <MapModal
                open={openMapModal}
                setOpen={setOpenMapModal}
                setScheduleDetails={setScheduleDetails}
                scheduleDetails={scheduleDetails}
            />
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">{editData !== null ? "Update" : "Add"} Online class</h1>
                        </DialogTitle>
                        <DialogDescription>Fill below details to schedule new online class.</DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-grid tw-grid-cols-2 tw-items-center tw-gap-3">
                            <div className="tw-flex tw-items-center tw-space-x-2">
                                <Switch
                                    id="airplane-mode"
                                    checked={scheduleDetails?.type == "online"}
                                    onCheckedChange={(e) =>
                                        onHandleChange({ target: { value: e ? "online" : "offline", name: "type" } })
                                    }
                                />
                                <Label htmlFor="airplane-mode">{scheduleDetails?.type}</Label>
                            </div>
                            {scheduleDetails?.type == "online" && (
                                <div className="tw-flex tw-items-center tw-justify-end">
                                    <RadioGroup
                                        value={scheduleDetails?.meet_type}
                                        onValueChange={(e) =>
                                            onHandleChange({ target: { value: e, name: "meet_type" } })
                                        }
                                        className="tw-flex"
                                    >
                                        <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                                            {mode?.map((data, idx) => (
                                                <div
                                                    className="tw-flex tw-items-center tw-space-x-2 [&[data-disabled=true]]:tw-cursor-not-allowed [&[data-disabled=true]]:tw-opacity-50"
                                                    key={idx}
                                                >
                                                    <RadioGroupItem value={data?.value} id={data?.value} />
                                                    <Label
                                                        htmlFor={data?.value}
                                                        className="tw-cursor-pointer [&[data-disabled=true]]:tw-cursor-not-allowed"
                                                    >
                                                        {data?.label}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                    </RadioGroup>
                                </div>
                            )}
                        </div>
                        {scheduleDetails?.meet_type == "google-meet" && (
                            <div className="tw-grid tw-grid-cols-1">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="name">Google Meet Link</Label>
                                    <Input
                                        placeholder="eg: https://meet.google.com/abc-defg-kml"
                                        onChange={onHandleChange}
                                        value={scheduleDetails?.location}
                                        name="location"
                                    />
                                </div>
                            </div>
                        )}
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Topic *</Label>
                                <Input
                                    placeholder="Define class topic here"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.topic}
                                    name="topic"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Overview *</Label>
                                <Textarea
                                    placeholder="Enter overview of this class or topic"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.overview}
                                    name="overview"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-4 tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Date *</Label>
                                <Input
                                    name="date"
                                    type="date"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.date}
                                    min={new Date().toISOString().split("T")[0]}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Start time *</Label>
                                <Input
                                    placeholder="eg: sEcReT789XYZ_exampleKey"
                                    name="start_time"
                                    type="time"
                                    onChange={onHandleChange}
                                    step="1"
                                    value={scheduleDetails?.start_time}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">End time *</Label>
                                <Input
                                    placeholder="eg: sEcReT789XYZ_exampleKey"
                                    name="end_time"
                                    type="time"
                                    onChange={onHandleChange}
                                    step="1"
                                    value={scheduleDetails?.end_time}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">
                                    Duration <small>(Mins)</small> *
                                </Label>
                                <Input
                                    disabled
                                    placeholder="Duration in mins"
                                    name="duration"
                                    type="number"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.duration}
                                />
                            </div>
                        </div>
                        {scheduleDetails?.type == "offline" && (
                            <>
                                <div className="tw-grid tw-grid-cols-4 tw-items-end tw-gap-3">
                                    <div>
                                        <Button onClick={onOpenMap} className="tw-w-full" variant="outline">
                                            <i className="fa-solid fa-earth-americas"></i>{" "}
                                            {scheduleDetails?.latitude && scheduleDetails?.longitude
                                                ? "Change Location"
                                                : "Open Map"}
                                        </Button>
                                    </div>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="name">Latitude</Label>
                                        <Input
                                            placeholder="eg: 0.000000"
                                            name="latitude"
                                            type="number"
                                            onChange={onHandleChange}
                                            value={scheduleDetails?.latitude}
                                        />
                                    </div>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="name">Longitude</Label>
                                        <Input
                                            placeholder="eg: 0.000000"
                                            name="longitude"
                                            type="number"
                                            onChange={onHandleChange}
                                            value={scheduleDetails?.longitude}
                                        />
                                    </div>
                                </div>
                                {locationDetails?.display_name && (
                                    <div className="tw-ml-2 tw-flex tw-items-center tw-gap-2 tw-text-slate-500">
                                        <i className="fa-solid fa-location-dot"></i>{" "}
                                        <p>{locationDetails?.display_name}</p>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                    {zoomCredCheck.data?.success == false && scheduleDetails.meet_type == "zoom" && (
                        <div className="tw-rounded-lg tw-border-red-500 tw-bg-red-100 tw-px-4 tw-py-2 tw-font-medium tw-text-red-500">
                            Add Zoom Credentials to enable this option
                        </div>
                    )}

                    <DialogFooter className="sm:justify-start">
                        <Button type="button" onClick={handleClear} variant="secondary">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>

                        {loadingState ? (
                            <Button>
                                <i className="fa-solid fa-spinner tw-animate-spin"></i> Please Wait ...
                            </Button>
                        ) : (
                            <Button
                                onClick={onDataSubmit}
                                disabled={zoomCredCheck.data?.success == false && scheduleDetails.meet_type == "zoom"}
                                className="tw-cursor-not-allowed disabled:tw-opacity-80"
                            >
                                <i className="fa-regular fa-floppy-disk"></i> {editData ? "Update" : "Create"} Schedule
                            </Button>
                        )}
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default CreateNewZoomClass;
