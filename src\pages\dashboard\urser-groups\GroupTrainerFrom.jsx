import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const GroupTrainerFrom = ({ open, setOpen, getClassListMapping }) => {
    const params = useParams();
    const navigate = useNavigate();

    const [groupsList, setGroupsList] = useState([]);
    const [selectedGroups, setSelectedGroups] = useState([]);
    const [selectedTrainer, setSelectedTrainer] = useState(null);
    const [trainers, setTrainers] = useState([]);
    const [languages, setLanguages] = useState([]);

    const [groupTrainers, setGroupTrainers] = useState([]);

    useEffect(() => {
        getUserGroups();
        getTrainers();
        getLanguages();
    }, []);

    const getUserGroups = async (payload) => {
        await tanstackApi
            .post("user-groups/list")
            .then((res) => {
                setGroupsList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setGroupsList([]);
            });
    };

    const getTrainers = async () => {
        await tanstackApi
            .get("users/get-users")
            .then((res) => {
                setTrainers(res?.data?.data?.filter((dt) => dt?.is_trainer == 1));
            })
            .catch((err) => {
                setTrainers([]);
            });
    };

    const getLanguages = async () => {
        await tanstackApi
            .get("language/list")
            .then((res) => {
                setLanguages(res?.data?.data);
            })
            .catch((err) => {
                setLanguages([]);
            });
    };

    const onGroupSelection = (item) => {
        if (selectedGroups?.includes(item)) {
            setSelectedGroups(selectedGroups?.filter((dt) => dt !== item));
        } else {
            setSelectedGroups([...selectedGroups, item]);
        }
    };

    useEffect(() => {
        if (selectedTrainer) {
            getMappedTrainers(selectedTrainer);
        }
    }, [selectedTrainer]);

    useEffect(() => {
        if (groupTrainers?.length > 0) {
            setSelectedGroups(groupTrainers?.map((dt) => dt?.lms_user_group?.id));
        }
    }, [groupTrainers]);

    const getMappedTrainers = async (trainerId) => {
        await tanstackApi
            .post("class/list", { trainer_id: trainerId })
            .then((res) => {
                setGroupTrainers(res?.data?.data);
            })
            .catch((err) => {
                setGroupTrainers([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        let finalGroups = selectedGroups?.filter(
            (dt) => !groupTrainers?.map((dt) => dt?.lms_user_group?.id)?.includes(dt),
        );

        let options = groupTrainers?.map((dt) => dt?.lms_user_group?.id) || [];
        let removeData = options?.filter((item) => !selectedGroups?.includes(item));

        if (!selectedTrainer) {
            toast?.warning("Trainer", {
                description: "Select an trainer to perform to assign.",
            });
            return false;
        }

        const payload = {
            trainer_id: selectedTrainer,
            assign_group_ids: finalGroups,
            deassign_group_ids: removeData,
        };

        let trainer = trainers?.find((dt) => dt?.id == Number(selectedTrainer));

        await tanstackApi
            .post("class/assign", { ...payload })
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("userId"),
                    event: "user groups",
                    log: `${groupsList
                        ?.filter((dt) => finalGroups?.includes(dt?.id))
                        ?.map((dt) => dt?.name)
                        ?.toString()} groups assigned to Trainer - ${`${trainer?.first_name} ${trainer?.last_name}`} successfully.`,
                });
                toast.success("Trainer assigned to selected groups", {
                    description: res?.data?.message,
                });
                setOpen(false);
                // navigate(`/dashboard/user-group-master`);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">Trainer & Group Mapping</h1>
                        </DialogTitle>
                        <DialogDescription>Assign multiple classes / user group to selected trainer</DialogDescription>
                    </DialogHeader>
                    <div className="tw-grid tw-grid-cols-[1fr] tw-gap-5">
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <div className="tw-grid tw-grid-cols-1 tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="Trainer">Trainers</Label>
                                    <Select
                                        onValueChange={(e) => setSelectedTrainer(e)}
                                        value={selectedTrainer}
                                        id="Trainer"
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select Trainer" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {trainers?.map((data, idx) => (
                                                <SelectItem key={idx} value={data?.id}>
                                                    {`${data?.first_name} ${data?.last_name}`}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            <div>
                                <div className="custom_table">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Group Name</th>
                                                <th>Language</th>
                                                <th>Users</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {groupsList?.map((row, idx) => (
                                                <tr key={idx}>
                                                    <td>{row?.name}</td>
                                                    <td>{languages?.find((dt) => dt?.id == row?.language_id)?.name}</td>
                                                    <td>
                                                        {row?.user_count}/{row?.user_restriction}
                                                    </td>
                                                    <td>
                                                        {selectedGroups?.includes(row?.id) ? (
                                                            <Button onClick={() => onGroupSelection(row?.id)}>
                                                                Selected
                                                            </Button>
                                                        ) : (
                                                            <Button
                                                                variant="outline"
                                                                onClick={() => onGroupSelection(row?.id)}
                                                            >
                                                                Choose
                                                            </Button>
                                                        )}
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                <i className="fa-solid fa-xmark"></i> Close
                            </Button>
                        </DialogClose>
                        <Button type="submit" onClick={onDataSubmit}>
                            <i className="fa-solid fa-floppy-disk"></i> Assign
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default GroupTrainerFrom;
