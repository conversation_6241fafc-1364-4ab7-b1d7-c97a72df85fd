import Pagination from "@/components/table/pagination";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const courseStatusList = [
    {
        key: "not attempted",
        label: "Not Attempted",
    },
    {
        key: "in progress",
        label: "In Progress",
    },
    {
        key: "completed",
        label: "Completed",
    },
    {
        key: "browsed",
        label: "Browsed",
    },
];

const ITEMS_PER_PAGE = 7;

const CoursePage = () => {
    const navigate = useNavigate();
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(ITEMS_PER_PAGE);
    const [categoryList, setCategoryList] = useState([]);
    const [subCategoryList, setSubCategoryList] = useState([]);

    const [filterState, setFilterState] = useState({
        search: "",
        category_id: "",
        sub_category_id: "",
        course_status: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
        if (name == "category_id") {
            getSubCategory(value);
        }
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.course_title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const status = filterState.course_status
                ? item.course_status?.replaceAll("_", " ").includes(filterState.course_status)
                : true;

            const category = filterState?.category_id
                ? item?.course?.category_id === Number(filterState?.category_id)
                : true; // Allow all items if no subCategory filter
            const subcategory = filterState?.sub_category_id
                ? item?.course?.sub_category_id === Number(filterState?.sub_category_id)
                : true; // Allow all items if no subCategory filter

            return matchesSearch && category && subcategory && status; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            category_id: "",
            sub_category_id: "",
            course_status: "",
        });
        onStatusChange("");
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getCourses({});
        getCategory();
    }, []);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("reports/get-course-reports-user", { params: payload })
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    useEffect(() => {
        if (filterState?.category_id) {
            getSubCategory(filterState?.category_id);
        }
    }, [filterState]);

    const getCategory = async (payload) => {
        await tanstackApi
            .get("course/categories/get-course-category")
            .then((res) => {
                setCategoryList(res?.data?.data);
            })
            .catch((err) => {
                setCategoryList([]);
            });
    };

    const getSubCategory = async (payload) => {
        await tanstackApi
            .get(`course/categories/get-sub-categories/${payload}`)
            .then((res) => {
                setSubCategoryList(res?.data?.data);
            })
            .catch((err) => {
                setSubCategoryList([]);
            });
    };

    const onStatusChange = (value) => {
        if (value !== "") {
            getCourses({ course_status: value });
        } else {
            getCourses({});
        }
    };

    return (
        <>
            <div className="tw-flex tw-justify-between">
                <div className="pageHeader">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>My Courses</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
            </div>
            <div className="page_filters tw-mt-4 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Course Title
                    </label>
                    <input
                        onChange={onChangeHandle}
                        value={filterState?.search}
                        name="search"
                        className="tw-text-sm"
                        type="text"
                        placeholder="Search by course title ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Course Status
                    </label>
                    <select
                        className="tw-text-sm"
                        value={filterState.course_status}
                        onChange={onChangeHandle}
                        name="course_status"
                        id=""
                    >
                        <option value=""> - All - </option>
                        {courseStatusList?.map((status, idx) => (
                            <option key={idx} value={status?.key}>
                                {status?.label}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Category
                    </label>
                    <select
                        onChange={onChangeHandle}
                        value={filterState?.category_id}
                        name="category_id"
                        className="tw-text-sm"
                        id=""
                    >
                        <option value=""> - Choose Category - </option>
                        {categoryList?.map((data, idx) => (
                            <option key={idx} value={data?.id}>
                                {data?.category_name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table tw-mt-2 tw-h-[65vh]">
                <table>
                    <thead>
                        <tr>
                            <th>Course</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Category</th>
                            <th>Progress</th>
                            <th>Status</th>
                            <th>Start Date</th>
                            <th>Expiration Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td onClick={() => navigate(`/course-details/view/${row?.id}/0/0/`)}>
                                    <div className="table_image_alpha">
                                        <img
                                            src={row?.course?.course_banner_url || "/assets/course-placeholder.png"}
                                            alt=""
                                        />
                                    </div>
                                </td>
                                <td onClick={() => navigate(`/course-details/view/${row?.id}/0/0/`)}>
                                    {row?.course_title}
                                </td>
                                <td>{row?.course?.is_scorm ? "SCORM" : "Combined"}</td>
                                <td>{row?.course?.lms_course_category?.category_name}</td>
                                <td>{row?.progress >= 0 ? `${row?.progress}%` : "-"}</td>
                                <td className="tw-capitalize">{row?.course_status?.replaceAll("_", " ")}</td>
                                <td>{moment(row?.startDate).format("LL")}</td>
                                <td>
                                    {row?.course?.lms_course_settings?.[0]?.expiration_date
                                        ? moment(row?.course?.lms_course_settings?.[0]?.expiration_date).format("LL")
                                        : moment(row?.startDate).add(1, "year").format("LL")}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination currentPage={currentPage} totalPages={totalPages} handlePageChange={handlePageChange} />
                </div>
            </div>
        </>
    );
};

export default CoursePage;
