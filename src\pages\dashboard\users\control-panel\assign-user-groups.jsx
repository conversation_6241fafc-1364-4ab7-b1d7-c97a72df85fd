import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTable } from "@/pages/dashboard/users/control-panel/data-table";
import { useAddUserToGroups, useGetAssignedUserGroups, useGetUserGroups } from "@/react-query/users/group";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function AssignUserGroups({ userId }) {
    const [search, setSearch] = useState("");
    const [userGroup, setUserGroup] = useState([]);
    const [selectedGroup, setSelectedGroup] = useState([]);
    const [assigned, setAssigned] = useState([]);
    const [delted, setDelted] = useState([]);
    const [assignedGroupID, setAssignedGroupID] = useState([]);
    const userGroupData = useGetUserGroups({ offset: 0, limit: 20, userId });
    const assignedUserGroupData = useGetAssignedUserGroups({ userId });
    const addUserToGroups = useAddUserToGroups({ offset: 0, limit: 20 });

    useEffect(() => {
        if (userGroupData.status == "success") setUserGroup(userGroupData.data.data);
    }, [userGroupData.status]);

    useEffect(() => {
        if (assignedUserGroupData.status == "success") {
            setAssigned(assignedUserGroupData.data.groups?.map((c) => c?.group_id));
            setAssignedGroupID(assignedUserGroupData.data.groups?.map((c) => c?.group_id));
        }
    }, [assignedUserGroupData.status]);

    useEffect(() => {
        if (addUserToGroups.status == "success") {
            toast.success("Courses Bundle Assigned", {
                description: addUserToGroups.data.message,
            });
            setSelectedGroup([]);
            setDelted([]);
        }
        if (addUserToGroups.status == "error")
            toast.error("Something went wrong", {
                description: addUserToGroups.error.response?.data?.message,
            });
    }, [addUserToGroups.status]);

    const selectGroup = (id) => {
        if (delted.includes(id)) {
            const fitered = delted.filter((dt) => dt !== id);
            setDelted(fitered);
        }
        if (selectedGroup.includes(id)) {
            setSelectedGroup([...selectedGroup]);
        } else {
            if (!assignedGroupID.includes(id)) {
                setSelectedGroup([...selectedGroup, id]);
            }
        }
        if (assigned.includes(id)) {
            setAssigned([...assigned]);
        } else {
            setAssigned([...assigned, id]);
        }
    };

    const unSelectedGroupHandler = (id) => {
        if (assignedGroupID.includes(id)) {
            if (!delted.includes(id)) {
                setDelted([...delted, id]);
            }
        }
        if (assigned.includes(id)) {
            const filtered = assigned.filter((group) => group !== id);
            setAssigned(filtered);
        }

        if (selectedGroup.includes(id)) {
            const filtered = selectedGroup.filter((group) => group !== id);
            setSelectedGroup(filtered);
        }
    };

    const assignGroupHandler = () => {
        const payload = {
            userId,
            groupIds: selectedGroup,
            deletedIds: delted,
        };
        addUserToGroups.mutate(payload);
    };

    const columns = [
        {
            accessorKey: "name",
            header: "Title",
        },
        {
            accessorKey: "createdAt",
            header: "Created On",
            cell: ({ row }) => format(row.getValue("createdAt"), "PP"),
        },
        {
            accessorKey: "id",
            header: "Action",
            cell: ({ row }) => {
                const isAssigned = assigned?.includes(row.getValue("id"));
                const Icon = isAssigned ? DoneAllIcon : ControlPointIcon;
                return (
                    <Button
                        variant={isAssigned ? "primary" : "outline"}
                        onClick={() => {
                            if (isAssigned) return unSelectedGroupHandler(row.getValue("id"));
                            selectGroup(row.getValue("id"));
                        }}
                    >
                        <Icon /> {isAssigned ? "Assigned" : "Select"}
                    </Button>
                );
            },
        },
    ];

    return (
        <div>
            {" "}
            <div className="tw-my-3 tw-flex tw-items-center tw-justify-between tw-gap-2 tw-font-lexend">
                <div className="tw-w-full">
                    <Input
                        placeholder="Search Courses..."
                        type="search"
                        value={search}
                        onChange={(event) => setSearch(event.target.value)}
                        className="tw-w-full tw-max-w-lg"
                    />
                </div>
                <div className="tw-flex tw-w-1/4 tw-items-center tw-justify-end tw-gap-3">
                    {selectedGroup?.length > 0 && <p>{selectedGroup?.length} Group Selected</p>}
                    {(delted?.length > 0 || selectedGroup.length > 0) && (
                        <Button variant="success" onClick={assignGroupHandler}>
                            {delted?.length > 0 ? "Update Groups" : "Assign Groups"}
                        </Button>
                    )}
                </div>
            </div>
            <DataTable columns={columns} data={userGroup} search={search} />
        </div>
    );
}
