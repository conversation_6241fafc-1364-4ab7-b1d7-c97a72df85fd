import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import useLocalStorage from "@/hooks/use-local-storage";
import { cn } from "@/lib/utils";
import AssignCourse from "@/pages/dashboard/users/control-panel/assign-course";
import AssignCourseBundle from "@/pages/dashboard/users/control-panel/assign-course-bundle";
import AssignUserGroups from "@/pages/dashboard/users/control-panel/assign-user-groups";
import { useGetUsersListing } from "@/react-query/users";
import ReplyAllIcon from "@mui/icons-material/ReplyAll";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

export default function UserControlPanelPage() {
    const params = useParams();
    const [level] = useLocalStorage("level", null);
    const [userData, setUserData] = useState({});
    const userListing = useGetUsersListing(level);
    const userCode = params.user_code.split("_")[0];
    const userId = params.user_code.split("_")[1];

    useEffect(() => {
        if (userListing.status == "success") {
            const f = userListing.data.data.find((dt) => dt.user_code == userCode);
            setUserData(f);
        }
    }, [userListing.status]);

    const colors = ["!tw-bg-yellow-500", "!tw-bg-green-500", "!tw-bg-blue-500", "!tw-bg-purple-500", "!tw-bg-teal-500"];
    const color = colors[Math.floor(Math.random() * colors.length)];

    return (
        <div>
            <div className="tw-flex tw-gap-4">
                <Avatar className="!tw-size-24">
                    <AvatarImage className="tw-aspect-square" src={userData?.picture_url} />
                    <AvatarFallback className={cn("tw-text-4xl tw-font-medium tw-text-white", color)}>
                        {userData?.first_name?.[0]}
                    </AvatarFallback>
                </Avatar>
                <div className="tw-font-lexend">
                    <p className="tw-text-2xl tw-font-medium">
                        {userData?.first_name} {userData?.last_name}
                        <Badge className="tw-ml-4" variant="outline">
                            {userData?.role}
                        </Badge>
                    </p>
                    <div className="tw-mt-2 tw-space-y-1">
                        <p>
                            Email : <span className="tw-font-bold">{userData?.email}</span>
                        </p>
                        <p>
                            User code : <span className="tw-font-bold">{userData?.user_code}</span>
                        </p>
                        <div className="tw-pt-1">
                            <Badge
                                className={cn("tw-pointer-events-none", {
                                    "!tw-bg-blue-500": userData?.status == "Active",
                                    "!tw-bg-gray-500": userData?.status == "Inactive",
                                    "!tw-bg-green-500": userData?.status == "Pending",
                                    "!tw-bg-red-500": userData?.status == "Delete",
                                })}
                            >
                                {userData?.status}
                            </Badge>
                        </div>
                    </div>
                </div>
                <Button className="tw-ml-auto" variant="outline" asChild>
                    <Link to="/dashboard/users">
                        <ReplyAllIcon />
                        Back
                    </Link>
                </Button>
            </div>
            <div className="tw-mt-5">
                <Tabs defaultValue="assign-course" className="">
                    <TabsList className="tw-gap-3 tw-border-0 !tw-bg-transparent !tw-p-0 tw-font-lexend">
                        <CustomTabTrigger value="assign-course">ASSIGN COURSES</CustomTabTrigger>
                        <CustomTabTrigger value="assign-course-bundle">ASSIGN COURSE BUNDLE</CustomTabTrigger>
                        <CustomTabTrigger value="assign-user-groups">ASSIGN USER GROUPS</CustomTabTrigger>
                    </TabsList>
                    <TabsContent value="assign-course">
                        <AssignCourse userId={userId} />
                    </TabsContent>
                    <TabsContent value="assign-course-bundle">
                        <AssignCourseBundle userId={userId} />
                    </TabsContent>
                    <TabsContent value="assign-user-groups">
                        <AssignUserGroups userId={userId} />
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
}

function CustomTabTrigger({ children, value, className, ...props }) {
    return (
        <TabsTrigger
            value={value}
            className={cn(
                "tw-flex tw-items-center tw-justify-center tw-rounded-lg tw-border-2 tw-bg-slate-300 tw-bg-transparent tw-px-4 tw-py-2 tw-text-sm tw-font-medium tw-text-slate-700 tw-transition-all",
                "data-[state=active]:tw-border-blue-500 data-[state=active]:tw-bg-blue-50 data-[state=active]:tw-text-blue-700",
                className,
            )}
            {...props}
        >
            {children}
        </TabsTrigger>
    );
}
