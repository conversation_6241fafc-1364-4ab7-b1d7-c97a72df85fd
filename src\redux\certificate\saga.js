import * as actions from "@/redux-types";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

import { CONSTANTS } from "@/constants";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import {
    certificateBackgroundTypeList,
    getCertificateList,
    getCertificateRespoonse,
    uploadedCertificateList,
} from "@/redux/certificate/action";
import { addTimelineLog } from "@/redux/reports/action";

function* fetchCertificateTypesList() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "certificate/get-background-types", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (data) {
        yield put(certificateBackgroundTypeList(data));
    }
}

function* fethcertifiTp(e) {
    const { certificate_type } = e.payload;
    const data = yield axios
        .get(CONSTANTS.getAPI() + `certificate/get-certificates/${certificate_type}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (data) {
        yield put(getCertificateList(data));
    }
}

function* fetchUploadedCertificateBasedOnType(type) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `certificate/get-certificates/${type.payload}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (data) {
        yield put(uploadedCertificateList(data));
    }
}

function* addNewCertificate(certificate) {
    const addData = yield axios
        .post(CONSTANTS.getAPI() + `certificate/creation/add-certificate`, certificate.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (addData) {
        yield put(
            addTimelineLog({
                user_id: localStorage.getItem("userId"),
                event: "certificate",
                log: `${certificate.payload?.title} created successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
            }),
        );
        yield put(AlertSnackInfo({ message: addData?.message, result: addData?.success }));
        window.location.href = "/dashboard/certificate";
        if (addData?.success) {
            yield put(getCertificateRespoonse(addData?.success));
        }
    }
}

function* editCertificate(certificate) {
    const editData = yield axios
        .put(CONSTANTS.getAPI() + `certificate/creation/update-certificate`, certificate.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (editData) {
        yield put(
            addTimelineLog({
                user_id: localStorage.getItem("userId"),
                event: "certificate",
                log: `${certificate.payload?.title} updated successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
            }),
        );
        yield put(AlertSnackInfo({ message: editData?.message, result: editData?.success }));
        window.location.href = "/dashboard/certificate";
        if (editData?.success) {
            yield put(getCertificateRespoonse(editData?.success));
        }
    }
}

function* generateCertificate(certificate) {
    const addData = yield axios
        .post(`https://yakpdf.p.rapidapi.com/pdf`, certificate.payload, {
            headers: {
                "content-type": "application/json",
                "X-RapidAPI-Key": "**************************************************",
                "X-RapidAPI-Host": "yakpdf.p.rapidapi.com",
            },
            responseType: "arraybuffer",
        })
        .then((res) => res.data)
        .catch((err) => err.response.data);
    if (addData) {
        const blob = new Blob([addData], { type: "application/pdf" });
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = "download.pdf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

export function* certificateWatcher() {
    yield takeEvery(actions.FETCH_CERTI_BACKGROUND_TYPES_REQ, fetchCertificateTypesList);
    yield takeEvery(actions.FETCH_COMPLETED_CERTIFICATE, fethcertifiTp);
    yield takeEvery(actions.FETCH_UPLOADED_CERTIFICATE_REQ, fetchUploadedCertificateBasedOnType);
    yield takeEvery(actions.CREATE_CERTIFICATE, addNewCertificate);
    yield takeEvery(actions.EDIT_CERTIFICATE, editCertificate);
    yield takeEvery(actions.GENERATE_CERTIFICATE, generateCertificate);
}

export default function* CertificateSaga() {
    yield all([fork(certificateWatcher)]);
}
