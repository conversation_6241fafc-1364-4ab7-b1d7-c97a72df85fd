import {
    ADD_TIMELINE_LOG,
    FETCH_CERTIFICATE_REPORTS_REQ,
    FETCH_REPORTS_REQ,
    FETCH_TIMELINE_LOGS_REQ,
    GET_REPORTS_LIST,
    GET_TIMELINE_LOGS_LIST,
} from "@/redux-types";

export const fetchREPORTSREQ = (data) => {
    return {
        type: FETCH_REPORTS_REQ,
        payload: data,
    };
};

export const fetchCertificateREPORTSREQ = (data) => {
    return {
        type: FETCH_CERTIFICATE_REPORTS_REQ,
        payload: data,
    };
};

export const getREPORTS = (data) => {
    return {
        type: GET_REPORTS_LIST,
        payload: data,
    };
};

export const fetchTimelineLogsREQ = (data) => {
    return {
        type: FETCH_TIMELINE_LOGS_REQ,
        payload: data,
    };
};

export const getTimelineLogs = (data) => {
    return {
        type: GET_TIMELINE_LOGS_LIST,
        payload: data,
    };
};

export const addTimelineLog = (userData) => {
    return {
        type: ADD_TIMELINE_LOG,
        payload: userData,
    };
};
