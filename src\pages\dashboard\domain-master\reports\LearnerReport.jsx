import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { utils, writeFileXLSX } from "xlsx";

const courseStatusList = [
    { key: "completed", label: "Completed" },
    { key: "in progress", label: "In Progress" },
    { key: "not started", label: "Not Started" },
];

// Default filter state used for initialization and resetting.
const defaultFilterState = {
    search: "",
    course_status: "",
};

const LearnerReport = () => {
    const params = useParams();
    const [dataList, setDataList] = useState([]);
    const [filterState, setFilterState] = useState(defaultFilterState);
    const [appliedFilter, setAppliedFilter] = useState(defaultFilterState);
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(11);

    // Fetch reports on mount.
    useEffect(() => {
        getReports();
    }, []);

    const getReports = async () => {
        try {
            const res = await tanstackApi.get("reports/get-learner-progress", {
                params: { domain_id: params?.domain_id },
            });
            setDataList(res?.data?.data);
        } catch (err) {
            setDataList([]);
        }
    };

    // Handle changes in filter inputs.
    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    // Apply filters only when Search is clicked.
    const onSearch = () => {
        setAppliedFilter({ ...filterState });
        setCurrentPage(1);
    };

    // Reset filter inputs and applied filter.
    const onClear = () => {
        setFilterState(defaultFilterState);
        setAppliedFilter(defaultFilterState);
        setCurrentPage(1);
    };

    // Compute filtered data based on appliedFilter.
    const filteredData = useMemo(() => {
        return dataList.filter((item) => {
            const fullName = `${item?.first_name} ${item?.last_name}`;
            const matchesName = appliedFilter.search
                ? fullName.toLowerCase().includes(appliedFilter.search.toLowerCase())
                : true;
            const matchesStatus = appliedFilter.course_status
                ? item?.course_status === appliedFilter.course_status
                : true;
            return matchesName && matchesStatus;
        });
    }, [dataList, appliedFilter]);

    // Compute paginated data.
    const tableData = useMemo(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        return filteredData.slice(startIndex, startIndex + recordsPerPage);
    }, [filteredData, currentPage, recordsPerPage]);

    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    // Determine visible page numbers.
    const getVisiblePages = () => {
        const pagesToShow = 3;
        const halfRange = Math.floor(pagesToShow / 2);
        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }
        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }
        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    function excelConverter() {
        const finalData = filteredData.map((row) => ({
            "User Name": `${row?.first_name} ${row?.last_name}`,
            Email: row?.email,
            Course: row?.course_name,
            "Start Date": moment(row?.start_date).format("L"),
            "Completion Date": moment(row?.end_date).format("L"),
            Progress: `${row?.progress} %`,
            Status: row?.course_status,
            Certificate: row?.certificate,
            "Certificate Issue Date": row?.issue_date,
            "Certificate Expiry Date": row?.certificate_validity,
        }));
        const wb = utils.book_new();
        utils.book_append_sheet(wb, utils.json_to_sheet(finalData));
        writeFileXLSX(wb, "Learner Reports.xlsx");
    }

    const onDownload = (row) => {
        const url = row?.certificate;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `Certificate : ${row?.first_name} ${row?.last_name}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    };

    return (
        <>
            <div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                        <div className="input_group">
                            <label className="tw-text-sm">Search</label>
                            <input
                                className="tw-text-sm"
                                type="text"
                                placeholder="Username or email ..."
                                name="search"
                                value={filterState.search}
                                onChange={onFilterChange}
                            />
                        </div>
                        <div className="input_group">
                            <label className="tw-text-sm">Status</label>
                            <select
                                className="tw-text-sm"
                                name="course_status"
                                value={filterState.course_status}
                                onChange={onFilterChange}
                            >
                                <option value=""> - Choose Status - </option>
                                {courseStatusList.map((status, idx) => (
                                    <option key={idx} value={status.key}>
                                        {status.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="filter_controls">
                            <button className="search_btn" onClick={onSearch}>
                                <i className="fa-solid fa-magnifying-glass"></i> Search
                            </button>
                            <button className="clear_btn" onClick={onClear}>
                                <i className="fa-solid fa-xmark"></i> Clear
                            </button>
                        </div>
                    </div>
                    <Button className="tw-px-2 tw-py-1" onClick={excelConverter}>
                        <i className="fa-solid fa-download"></i> Export Data
                    </Button>
                </div>

                <div className="custom_table tw-mt-4">
                    <table>
                        <thead>
                            <tr>
                                <th>User Name</th>
                                <th>Email</th>
                                <th>Course</th>
                                <th>Start Date</th>
                                <th>Completion Date</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>Certificate</th>
                                <th>Certificate Issue Date</th>
                                <th>Certificate Expiry Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData.length > 0 ? (
                                tableData.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>{`${row?.first_name} ${row?.last_name}`}</td>
                                        <td>{row?.email}</td>
                                        <td>{row?.course_name}</td>
                                        <td>{row?.startDate ? moment(row?.startDate).format("LL") : "-"}</td>
                                        <td>{row?.end_date ? moment(row?.end_date).format("LL") : "-"}</td>
                                        <td>{row?.progress}%</td>
                                        <td>
                                            <Badge className="tw-capitalize" variant="outline">
                                                {row?.course_status}
                                            </Badge>
                                        </td>
                                        <td>
                                            {row?.certificate ? (
                                                <Button variant="outline" onClick={() => onDownload(row)}>
                                                    Download
                                                </Button>
                                            ) : (
                                                "-"
                                            )}
                                        </td>
                                        <td>{row?.issue_date ? moment(row?.issue_date).format("LL") : "-"}</td>
                                        <td>
                                            {row?.certificate_validity
                                                ? moment(row?.certificate_validity).format("LL")
                                                : "-"}
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td
                                        colSpan={10}
                                        className="tw-h-40 tw-py-4 tw-text-center tw-font-medium tw-text-slate-500"
                                    >
                                        No reports found.
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>

                <div className="tw-mt-4 tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select
                            onValueChange={(e) => {
                                setRecordsPerPage(e);
                                setCurrentPage(1);
                            }}
                            value={recordsPerPage}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination>
                            <PaginationContent>
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {currentPage > 3 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                1
                                            </PaginationLink>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    </>
                                )}

                                {getVisiblePages().map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href="#"
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {currentPage < totalPages - 2 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                {totalPages}
                                            </PaginationLink>
                                        </PaginationItem>
                                    </>
                                )}

                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </>
    );
};

export default LearnerReport;
