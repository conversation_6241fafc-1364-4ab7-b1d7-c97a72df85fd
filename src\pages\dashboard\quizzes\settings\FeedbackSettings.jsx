import { Label } from "@/components/ui/label";
import BulletList from "@tiptap/extension-bullet-list";
import { Color } from "@tiptap/extension-color";
import Document from "@tiptap/extension-document";
import Dropcursor from "@tiptap/extension-dropcursor";
import FontFamily from "@tiptap/extension-font-family";
import Image from "@tiptap/extension-image";
import ListItem from "@tiptap/extension-list-item";
import ListKeymap from "@tiptap/extension-list-keymap";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Typography from "@tiptap/extension-typography";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import "@vidstack/react/player/styles/base.css";
import "@vidstack/react/player/styles/plyr/theme.css";

const FeedbackSettings = ({ setContentData, contentData }) => {
    const onHandleChange = (value, name) => {
        setContentData({
            ...contentData,
            feedbacks: { ...contentData?.feedbacks, [name]: value },
        });
    };

    return (
        <div>
            <Label>Customized Feedbacks :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">
                Please define feedbacks here for all three cases mentioned below.
            </p>
            <div className="tw-mt-10 tw-space-y-5">
                <div className="tw-space-y-3">
                    <Label htmlFor="name" className="tw-flex tw-items-center tw-gap-2">
                        <i className="fa-solid fa-check"></i> Feedback - Correct Answer
                    </Label>
                    <div className="tw-rounded-md tw-border-[1px]">
                        <HTMLEditor
                            onHandleChange={onHandleChange}
                            name="correctAns"
                            value={contentData?.feedbacks?.correctAns}
                            placeholder="Create feedback for correct answer here"
                        />
                    </div>
                </div>

                <div className="tw-space-y-3">
                    <Label htmlFor="name" className="tw-flex tw-items-center tw-gap-2">
                        <i className="fa-solid fa-filter"></i> Feedback - Partially Correct Answer
                    </Label>
                    <div className="tw-rounded-md tw-border-[1px]">
                        <HTMLEditor
                            onHandleChange={onHandleChange}
                            name="partiallyCorrectAns"
                            value={contentData?.feedbacks?.partiallyCorrectAns}
                            placeholder="Create feedback for partially correct answer here"
                        />
                    </div>
                </div>

                <div className="tw-space-y-3">
                    <Label htmlFor="name" className="tw-flex tw-items-center tw-gap-2">
                        <i className="fa-solid fa-xmark"></i> Feedback - Wrong Answer
                    </Label>
                    <div className="tw-rounded-md tw-border-[1px]">
                        <HTMLEditor
                            onHandleChange={onHandleChange}
                            name="incorrectAns"
                            value={contentData?.feedbacks?.incorrectAns}
                            placeholder="Create feedback for wrong answer here"
                        />
                    </div>
                </div>
            </div>
            <br />
            <hr />
            <br />
            <div className="tw-space-y-3">
                <Label htmlFor="name" className="tw-flex tw-items-center tw-gap-2">
                    <i className="fa-solid fa-circle-check"></i>Body - Right Answer To Show
                </Label>
                <div className="tw-rounded-md tw-border-[1px]">
                    <HTMLEditor
                        onHandleChange={onHandleChange}
                        name="rightAnswerToShow"
                        value={contentData?.feedbacks?.rightAnswerToShow}
                        placeholder="Define right answer to show here."
                    />
                </div>
            </div>
        </div>
    );
};

export default FeedbackSettings;

const HTMLEditor = ({ onHandleChange, name, value, placeholder }) => {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                text: {
                    HTMLAttributes: {
                        class: "tw-text-sm",
                    },
                },
            }),
            Color,
            Document,
            Paragraph,
            TextStyle.configure({
                HTMLAttributes: {},
            }),
            BulletList,
            ListItem,
            FontFamily.configure({
                types: ["textStyle"],
            }),
            Dropcursor,
            ListKeymap,
            Placeholder.configure({
                placeholder: placeholder,
            }),
            Image,
            TextAlign.configure({
                types: ["heading", "paragraph"],
            }),
            Typography,
        ],
        content: value,
        editorProps: {
            attributes: {
                spellcheck: "false",
            },
        },
        onUpdate: ({ editor }) => {
            onHandleChange(editor.getHTML(), name);
        },
    });

    return <TipTapEditor editor={editor} />;
};

import BubbleMenu from "@/pages/dashboard/createcourse/chaptercontent/slides/editor/bubble-menu";
import MenuBar from "@/pages/dashboard/createcourse/chaptercontent/slides/editor/menu-bar";
import { EditorContent } from "@tiptap/react";

function TipTapEditor({ editor }) {
    return (
        <div className="tw-prose tw-relative tw-w-full tw-max-w-full tw-overflow-y-auto prose-h1:tw-my-2 prose-p:tw-mb-3 prose-p:tw-mt-2 prose-p:tw-leading-snug prose-img:tw-my-2 prose-hr:tw-my-4 prose-hr:tw-border-gray-500">
            <MenuBar editor={editor} />
            <EditorContent editor={editor} />
            <BubbleMenu editor={editor} />
        </div>
    );
}
