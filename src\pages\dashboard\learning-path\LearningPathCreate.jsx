import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import AssignCoursesToPath from "./AssignCoursesToPath";
import AssignGroupsToLearningPath from "./AssignGroupsToLearningPath";
import AssignUsersToPath from "./AssignUsersToPath";
import BasicDetails from "./BasicDetails";

const LearningPathCreate = () => {
    const params = useParams();
    const router = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const [pathData, setPathData] = useState(null);

    useEffect(() => {
        if (params?.path_id !== undefined) {
            getLearningPath(params?.path_id);
        }
    }, [params]);

    const getLearningPath = async (payload) => {
        await tanstackApi
            .get(`learning-path/view-learning-path-details/${payload}`)
            .then((res) => {
                setPathData(res?.data?.data);
            })
            .catch((err) => {
                setPathData(null);
            });
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/learning-path">Learning Paths</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{params?.path_id ? "Update" : "Create new"} Learning Path</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <Button
                    className="tw-px-2 tw-py-1"
                    onClick={() => router(`/dashboard/learning-path-view/${params?.path_id}`)}
                >
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
                {/* </Link> */}
            </div>
            <Tabs
                defaultValue={searchParams.get("tab") ?? "basic_details"}
                className=""
                onValueChange={(val) => setSearchParams({ tab: val })}
            >
                {params?.path_id !== undefined && (
                    <TabsList className="tw-grid tw-w-[550px] tw-grid-cols-4">
                        <TabsTrigger value="basic_details" className="tw-gap-2">
                            <i className="fa-solid fa-circle-info"></i> Basic Details
                        </TabsTrigger>
                        <TabsTrigger value="add_courses" className="tw-gap-2">
                            <i className="fa-solid fa-book"></i> Add Courses
                        </TabsTrigger>
                        <TabsTrigger value="assign_users" className="tw-gap-2">
                            <i className="fa-solid fa-user"></i> Assign Users
                        </TabsTrigger>
                        <TabsTrigger value="assign_groups" className="tw-gap-2">
                            <i className="fa-solid fa-users"></i> Assign Groups
                        </TabsTrigger>
                    </TabsList>
                )}
                <TabsContent value="basic_details">
                    <BasicDetails pathData={pathData} getLearningPath={getLearningPath} />
                </TabsContent>
                <TabsContent value="add_courses">
                    <AssignCoursesToPath pathData={pathData} getLearningPath={getLearningPath} />
                </TabsContent>
                <TabsContent value="assign_users">
                    <AssignUsersToPath pathData={pathData} getLearningPath={getLearningPath} />
                </TabsContent>
                <TabsContent value="assign_groups">
                    <AssignGroupsToLearningPath pathData={pathData} getLearningPath={getLearningPath} />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default LearningPathCreate;
