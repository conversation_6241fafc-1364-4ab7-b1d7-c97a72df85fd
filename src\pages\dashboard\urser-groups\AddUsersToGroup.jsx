import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const AddUsersToGroup = ({ userGroupData, getUserGroups }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [usersList, setUsersList] = useState([]);
    const [groupUsersList, setGroupUsersList] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState([]);

    useEffect(() => {
        getUsers();

        if (params?.group_id !== undefined) {
            getGroupUsers({ group_id: params?.group_id });
        }
    }, [params]);

    const getUsers = async (payload) => {
        await tanstackApi
            .get("users/get-users")
            .then((res) => {
                setUsersList(res?.data?.data?.filter((dt) => dt?.is_trainer == 0));
            })
            .catch((err) => {
                setUsersList([]);
            });
    };

    const getGroupUsers = async (payload) => {
        await tanstackApi
            .post("user-groups/users/list", { ...payload })
            .then((res) => {
                setGroupUsersList(res?.data?.data);
            })
            .catch((err) => {
                setGroupUsersList([]);
            });
    };

    useEffect(() => {
        if (groupUsersList?.length > 0) {
            setSelectedUsers(groupUsersList?.map((dt) => dt?.id) || []);
        }
    }, [groupUsersList]);

    const onCourseSelection = (userId) => {
        if (selectedUsers?.includes(userId)) {
            setSelectedUsers(selectedUsers?.filter((dt) => dt !== userId));
        } else {
            if (selectedUsers?.length < userGroupData?.user_restriction) {
                setSelectedUsers([...selectedUsers, userId]);
            } else {
                toast?.warning(`Max Limit is ${userGroupData?.user_restriction}`, {
                    description: "Max users limit exceeded",
                });
            }
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();
        if (params?.group_id !== undefined) {
            const payload = {
                group_id: params?.group_id,
                user_ids: selectedUsers,
            };

            await tanstackApi
                .post("user-groups/users/add", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "user groups",
                        log: `${selectedUsers?.length} users added to ${userGroupData?.name} successfully.`,
                    });
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    getUserGroups(params?.group_id);
                    // navigate(`/dashboard/course-bundle`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected users in your group&apos;s details.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Select users for group</CardTitle>
                            <CardDescription>
                                Click on select button to select any user in group. Click save when you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">
                                {selectedUsers?.length}/{userGroupData?.user_restriction} User Selected
                            </p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Profile</th>
                                    <th>User Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {usersList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td style={{ width: "70px" }}>
                                            <Avatar>
                                                <AvatarImage src={row.picture_url} />
                                                <AvatarFallback>
                                                    {row.first_name[0]}
                                                    {row.last_name[0]}
                                                </AvatarFallback>
                                            </Avatar>
                                        </td>
                                        <td>
                                            {row.first_name} {row.last_name}
                                        </td>
                                        <td>{row?.email}</td>
                                        <td>{row?.role}</td>
                                        <td>
                                            {selectedUsers?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        <Button onClick={() => setOpenAlert(true)}>
                            <i className="fa-solid fa-floppy-disk"></i> Update
                        </Button>
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AddUsersToGroup;
