import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    Di<PERSON>Header,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import MapModal from "./MapModal";
import { useZoomCredCheck } from "@/react-query/zoom";

function getDurationInMinutes(startTime, endTime) {
    // Split the start and end time strings into hours, minutes, and seconds
    const [startHours, startMinutes, startSeconds] = startTime.split(":").map(Number);
    const [endHours, endMinutes, endSeconds] = endTime.split(":").map(Number);

    // Convert start and end times to total minutes since midnight
    const startTotalMinutes = startHours * 60 + startMinutes + startSeconds / 60;
    const endTotalMinutes = endHours * 60 + endMinutes + endSeconds / 60;

    // Calculate the difference in minutes
    const duration = parseInt(endTotalMinutes - startTotalMinutes);

    return duration;
}

const ScheduleClassAddEdit = ({ open, setOpen, editData, isRegistered, getSchdedules }) => {
    const [openMapModal, setOpenMapModal] = useState(false);
    const zoomCredCheck = useZoomCredCheck();

    const mode = [
        {
            label: "Zoom Meet",
            value: "zoom",
        },
        {
            label: "Google Meet",
            value: "google-meet",
        },
    ];
    const [openAlert, setOpenAlert] = useState(false);

    const [courseList, setCourseList] = useState([]);
    const [chapterList, setChapterList] = useState([]);

    const [locationDetails, setLocationDetails] = useState(null);

    const [scheduleDetails, setScheduleDetails] = useState({
        chapter_id: null,
        topic: "",
        overview: "",
        type: "online",
        meet_type: "zoom",
        start_time: "",
        end_time: "",
        duration: "",
        date: "",
        latitude: "",
        longitude: "",
    });

    const handleClear = () => {
        setScheduleDetails((prev) => ({
            ...prev,
            chapter_id: null,
            topic: "",
            overview: "",
            type: "online",
            start_time: "",
            end_time: "",
            duration: "",
            date: "",
            latitude: "",
            longitude: "",
        }));
    };

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setScheduleDetails({ ...scheduleDetails, [name]: value });
    };

    useEffect(() => {
        if (editData !== null) {
            setScheduleDetails({
                chapter_id: editData?.chapter_id,
                topic: editData?.topic,
                overview: editData?.overview,
                type: editData?.type,
                meet_type: editData?.meet_type,
                start_time: editData?.start_time,
                end_time: editData?.end_time,
                duration: editData?.duration,
                date: editData?.date,
                location: editData?.location,
                latitude: editData?.latitude,
                longitude: editData?.longitude,
            });
        }
    }, [editData]);

    useEffect(() => {
        getCourses();
    }, []);

    useEffect(() => {
        if (scheduleDetails?.course_id) {
            getChapters({ course_id: scheduleDetails?.course_id });
        }
    }, [scheduleDetails?.course_id]);

    useEffect(() => {
        if (scheduleDetails?.start_time && scheduleDetails?.end_time) {
            setScheduleDetails({
                ...scheduleDetails,
                duration: getDurationInMinutes(scheduleDetails?.start_time, scheduleDetails?.end_time),
            });
        }
    }, [scheduleDetails?.start_time, scheduleDetails?.end_time]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getChapters = async (payload) => {
        await tanstackApi
            .post("course/view-course", { ...payload })
            .then((res) => {
                setChapterList(res?.data?.data?.courseChapters);
            })
            .catch((err) => {
                setChapterList([]);
            });
    };

    useEffect(() => {
        if (scheduleDetails?.latitude && scheduleDetails?.longitude) {
            getAddress(scheduleDetails?.latitude, scheduleDetails?.longitude);
        }
    }, [scheduleDetails]);

    const getAddress = async (lat, lng) => {
        try {
            const response = await fetch(
                `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`,
            );
            const data = await response.json();
            setLocationDetails(data);
        } catch (error) {
            console.error("Error fetching location details:", error);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!scheduleDetails?.chapter_id) {
            toast.warning("Chapter", {
                description: "Chapter selection is required",
            });
            return false;
        }
        if (!scheduleDetails?.topic) {
            toast.warning("Topic", {
                description: "Topic is required",
            });
            return false;
        }
        if (!scheduleDetails?.date) {
            toast.warning("Date", {
                description: "date selection is required",
            });
            return false;
        }
        if (!scheduleDetails?.start_time) {
            toast.warning("Start time", {
                description: "Start time selection is required",
            });
            return false;
        }
        if (!scheduleDetails?.end_time) {
            toast.warning("End time", {
                description: "End time selection is required",
            });
            return false;
        }

        if (editData !== null) {
            const payload = {
                chapter_id: scheduleDetails?.chapter_id,
                topic: scheduleDetails?.topic,
                overview: scheduleDetails?.overview,
                type: scheduleDetails?.type,
                meet_type: scheduleDetails?.type == "offline" ? undefined : scheduleDetails?.meet_type,
                location: scheduleDetails?.location || undefined,
                start_time: scheduleDetails?.start_time,
                end_time: scheduleDetails?.end_time,
                duration: scheduleDetails?.duration?.toString(),
                date: scheduleDetails?.date,
                latitude: scheduleDetails?.latitude?.toString() || undefined,
                longitude: scheduleDetails?.longitude?.toString() || undefined,
            };

            await tanstackApi
                .put(`chapter-class/update/${editData?.id}`, { ...payload })
                .then((res) => {
                    getSchdedules();
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "zoom module",
                        log: `Meeting schedule has been updated for Chapter: ${chapterList?.find((dt) => dt?.id == Number(scheduleDetails?.chapter_id))?.chapter_title} successfully.`,
                    });
                    toast.success("Schedule Updated", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                chapter_id: scheduleDetails?.chapter_id,
                topic: scheduleDetails?.topic,
                overview: scheduleDetails?.overview,
                type: scheduleDetails?.type,
                meet_type: scheduleDetails?.type == "offline" ? undefined : scheduleDetails?.meet_type,
                location: scheduleDetails?.location || undefined,
                start_time: scheduleDetails?.start_time,
                end_time: scheduleDetails?.end_time,
                duration: scheduleDetails?.duration?.toString(),
                date: scheduleDetails?.date,
                latitude: scheduleDetails?.latitude?.toString() || undefined,
                longitude: scheduleDetails?.longitude?.toString() || undefined,
            };

            await tanstackApi
                .post("chapter-class/create", { ...payload })
                .then((res) => {
                    getSchdedules();
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "zoom module",
                        log: `Meeting schedule has been created for Chapter: ${chapterList?.find((dt) => dt?.id == Number(scheduleDetails?.chapter_id))?.chapter_title} successfully.`,
                    });
                    toast.success("Schedule Created", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const onOpenMap = () => {
        setOpenMapModal(true);
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <div>
            <MapModal
                open={openMapModal}
                setOpen={setOpenMapModal}
                setScheduleDetails={setScheduleDetails}
                scheduleDetails={scheduleDetails}
            />
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">{isRegistered ? "Update" : "Add"} Zoom class schedule</h1>
                        </DialogTitle>
                        <DialogDescription>fill below details from your zoom account.</DialogDescription>
                    </DialogHeader>

                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-grid tw-grid-cols-2 tw-items-center tw-gap-3">
                            <div className="tw-flex tw-items-center tw-space-x-2">
                                <Switch
                                    id="airplane-mode"
                                    checked={scheduleDetails?.type == "online"}
                                    onCheckedChange={(e) =>
                                        onHandleChange({ target: { value: e ? "online" : "offline", name: "type" } })
                                    }
                                />
                                <Label htmlFor="airplane-mode">{scheduleDetails?.type}</Label>
                            </div>
                            {scheduleDetails?.type == "online" && (
                                <div className="tw-flex tw-items-center tw-justify-end">
                                    <RadioGroup
                                        value={scheduleDetails?.meet_type}
                                        onValueChange={(e) =>
                                            onHandleChange({ target: { value: e, name: "meet_type" } })
                                        }
                                        className="tw-flex"
                                    >
                                        <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                                            {mode?.map((data, idx) => (
                                                <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                                    <RadioGroupItem value={data?.value} id={data?.value} />
                                                    <Label htmlFor={data?.value} className="tw-cursor-pointer">
                                                        {data?.label}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                    </RadioGroup>
                                </div>
                            )}
                        </div>

                        {scheduleDetails?.meet_type == "google-meet" && (
                            <div className="tw-grid tw-grid-cols-1">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="name">Google Meet Link</Label>
                                    <Input
                                        placeholder="eg: https://meet.google.com/abc-defg-hij"
                                        onChange={onHandleChange}
                                        value={scheduleDetails?.location}
                                        name="location"
                                    />
                                </div>
                            </div>
                        )}
                        <div className="tw-grid tw-grid-cols-2 tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Course *</Label>
                                <Select
                                    onValueChange={(e) => onHandleChange({ target: { value: e, name: "course_id" } })}
                                    value={scheduleDetails?.course_id}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select course" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            {courseList?.map((data, idx) => (
                                                <SelectItem key={idx} value={data?.id}>
                                                    {data?.course_title}
                                                </SelectItem>
                                            ))}
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Chapter *</Label>
                                <Select
                                    onValueChange={(e) => onHandleChange({ target: { value: e, name: "chapter_id" } })}
                                    value={scheduleDetails?.chapter_id}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select chapter" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            {chapterList?.map((data, idx) => (
                                                <SelectItem key={idx} value={data?.id}>
                                                    {data?.chapter_title}
                                                </SelectItem>
                                            ))}
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Topic *</Label>
                                <Input
                                    placeholder="eg: sEcReT789XYZ_exampleKey"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.topic}
                                    name="topic"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Overview</Label>
                                <Textarea
                                    placeholder="eg: account_12345XYZ_example"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.overview}
                                    name="overview"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-4 tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Date *</Label>
                                <Input
                                    name="date"
                                    type="date"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.date}
                                    min={new Date().toISOString().split("T")[0]}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Start time *</Label>
                                <Input
                                    placeholder="eg: sEcReT789XYZ_exampleKey"
                                    name="start_time"
                                    type="time"
                                    onChange={onHandleChange}
                                    step="1"
                                    value={scheduleDetails?.start_time}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">End time *</Label>
                                <Input
                                    placeholder="eg: sEcReT789XYZ_exampleKey"
                                    name="end_time"
                                    type="time"
                                    onChange={onHandleChange}
                                    step="1"
                                    value={scheduleDetails?.end_time}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">
                                    Duration <small>(Mins)</small> *
                                </Label>
                                <Input
                                    disabled
                                    placeholder="Duration in mins"
                                    name="duration"
                                    type="number"
                                    onChange={onHandleChange}
                                    value={scheduleDetails?.duration}
                                />
                            </div>
                        </div>
                        {scheduleDetails?.type == "offline" && (
                            <>
                                <div className="tw-grid tw-grid-cols-4 tw-items-end tw-gap-3">
                                    <div>
                                        <Button onClick={onOpenMap} className="tw-w-full" variant="outline">
                                            <i className="fa-solid fa-earth-americas"></i>{" "}
                                            {scheduleDetails?.latitude && scheduleDetails?.longitude
                                                ? "Change Location"
                                                : "Open Map"}
                                        </Button>
                                    </div>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="name">Latitude</Label>
                                        <Input
                                            placeholder="eg: 0.000000"
                                            name="latitude"
                                            type="number"
                                            onChange={onHandleChange}
                                            value={scheduleDetails?.latitude}
                                        />
                                    </div>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="name">Longitude</Label>
                                        <Input
                                            placeholder="eg: 0.000000"
                                            name="longitude"
                                            type="number"
                                            onChange={onHandleChange}
                                            value={scheduleDetails?.longitude}
                                        />
                                    </div>
                                </div>
                                {locationDetails?.display_name && (
                                    <div className="tw-ml-2 tw-flex tw-items-center tw-gap-2 tw-text-slate-500">
                                        <i className="fa-solid fa-location-dot"></i>{" "}
                                        <p>{locationDetails?.display_name}</p>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                    {zoomCredCheck.data?.success == false && scheduleDetails.meet_type == "zoom" && (
                        <div className="tw-rounded-lg tw-border-red-500 tw-bg-red-100 tw-px-4 tw-py-2 tw-font-medium tw-text-red-500">
                            Add Zoom Credentials to enable this option
                        </div>
                    )}
                    <DialogFooter className="sm:justify-start">
                        <Button type="button" variant="secondary" onClick={handleClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>
                        <Button
                            onClick={onDataSubmit}
                            disabled={zoomCredCheck.data?.success == false && scheduleDetails.meet_type == "zoom"}
                            className="tw-cursor-not-allowed disabled:tw-opacity-80"
                        >
                            <i className="fa-regular fa-floppy-disk"></i> {editData ? "Update" : "Create"} Schedule
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default ScheduleClassAddEdit;
