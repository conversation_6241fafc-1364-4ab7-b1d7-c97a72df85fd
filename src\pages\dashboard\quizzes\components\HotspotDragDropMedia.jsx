import { isUrl } from "@/lib/utils";
import QuizContainer from "@/pages/dashboard/quizzes/components/container";
import { Move } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import Draggable from "react-draggable";

const HotspotDragDropMedia = ({ ...props }) => {
    const { content, componentsArray, setComponentsArray, index } = props;
    const containerRef = useRef(null);
    const data = componentsArray[index];
    const [circles, setCircles] = useState([]);
    const [dropzones, setDropzones] = useState([]);
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
    const hotspotDropzone = content?.hotspotDropzone;

    const updateContainerSize = useCallback(() => {
        if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            setContainerSize({ width: rect.width, height: rect.height });
        }
    }, [containerRef]);

    useEffect(() => {
        updateContainerSize();
        window.addEventListener("resize", updateContainerSize);
        return () => {
            window.removeEventListener("resize", updateContainerSize);
        };
    }, []);

    const clampPosition = useCallback(
        (x, y) => {
            return {
                x: Math.min(Math.max(x, 0), containerSize.width),
                y: Math.min(Math.max(y, 0), containerSize.height),
            };
        },
        [containerSize.width, containerSize.height],
    );

    // Convert relative positions to absolute positions
    const getAbsolutePosition = useCallback(
        (relativeX, relativeY) => {
            if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
            return clampPosition(relativeX * containerSize.width, relativeY * containerSize.height);
        },
        [containerSize.width, containerSize.height, clampPosition],
    );

    // Convert absolute positions to relative positions
    const getRelativePosition = useCallback(
        (data, absoluteX, absoluteY) => {
            if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
            return {
                ...data,
                x: absoluteX / containerSize.width,
                y: absoluteY / containerSize.height,
            };
        },
        [containerSize.width, containerSize.height],
    );

    useEffect(() => {
        if (content?.hotspots?.length > 0) setCircles(content?.hotspots);
    }, [content?.hotspots]);

    useEffect(() => {
        if (hotspotDropzone?.length > 0) setDropzones(content?.hotspotDropzone);
    }, [hotspotDropzone]);

    const handleStop = (index, pos) => {
        setCircles((prevCircles) =>
            prevCircles.map((circle, i) => (i === index ? { ...getRelativePosition(circle, pos.x, pos.y) } : circle)),
        );
    };

    // Handle draggable stop for dropzones
    const handleStopDropzone = (index, pos) => {
        setDropzones((prevZones) =>
            prevZones.map((zone, i) => (i === index ? { ...getRelativePosition(zone, pos.x, pos.y) } : zone)),
        );
    };

    useEffect(() => {
        let options = [...componentsArray];
        options[index].hotspotDropzone = dropzones;
        options[index].hotspots = circles;
        setComponentsArray(options);
    }, [circles, dropzones]);

    return (
        <QuizContainer {...props} type="hotspot_dragdrop_media" empty={content?.hotspotDropzone?.length == 0}>
            <div className="tw-flex tw-items-center tw-justify-center tw-px-10 tw-py-7">
                <div
                    className="tw-overflow-hidden tw-rounded-lg tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{
                        backgroundImage: `url(${content?.question_thumbnail})`,
                        position: "relative",
                        aspectRatio: "12 / 7",
                        width: "650px",
                        // height: "350px",
                    }}
                    id="dnd_img_container"
                    ref={containerRef}
                >
                    {/* Render Hotspots */}
                    {circles.map((position, index) => {
                        const { x, y } = getAbsolutePosition(position.x, position.y);
                        return (
                            <Draggable
                                key={`circle-${index}`}
                                bounds="parent"
                                position={{ x, y }}
                                onStop={(e, data) => handleStop(index, data)}
                            >
                                <div className="tw-absolute tw-z-10 tw-flex tw-h-fit tw-w-fit tw-cursor-grab tw-flex-col tw-items-center tw-justify-center tw-gap-1 tw-rounded-full tw-border tw-border-black tw-bg-gray-100 tw-p-2">
                                    <img
                                        className="tw-h-[30px]"
                                        src={position.src || "/assets/image.png"}
                                        alt={position.name}
                                    />
                                    <p
                                        className="tw-text-xs tw-font-medium"
                                        style={{
                                            color: data?.styles?.answer?.color,
                                            fontFamily: data?.styles?.answer?.fontFamily,
                                            fontSize: data?.styles?.answer?.fontSize,
                                        }}
                                    >
                                        {position.name}
                                    </p>
                                </div>
                            </Draggable>
                        );
                    })}

                    {/* Render Dropzones */}
                    {dropzones.map((position, index) => {
                        return (
                            <HotspotDraggable
                                key={`dropzone-${index}`}
                                handleStopDropzone={handleStopDropzone}
                                d_index={index}
                                position={position}
                                getAbsolutePosition={getAbsolutePosition}
                                // updateSize={updateSize}
                            />
                        );
                    })}
                </div>
            </div>
        </QuizContainer>
    );
};

export default HotspotDragDropMedia;

const HotspotDraggable = ({ handleStopDropzone, d_index, position, getAbsolutePosition, currentTab }) => {
    const { x, y } = getAbsolutePosition(position.x, position.y);
    const ref = useRef(null);

    return (
        <Draggable
            key={`dropzone-${currentTab}`}
            bounds="parent"
            handle=".handle"
            position={{ x, y }}
            onStop={(e, data) => handleStopDropzone(d_index, data)}
        >
            <div
                ref={ref}
                className="tw-absolute tw-aspect-square tw-min-w-[50px] tw-rounded-md tw-border-[2px] tw-border-dashed tw-border-gray-200 tw-bg-transparent"
                style={{
                    overflow: "hidden",
                    width: 100,
                    height: 100,
                }}
            >
                <div className="handle tw-absolute tw-right-0 tw-top-0 tw-z-50 tw-cursor-grab">
                    <Move className="tw-size-3" />
                </div>
                {isUrl(position?.src) ? (
                    <img
                        src={position?.src}
                        alt=""
                        className="tw-absolute tw-inset-0 tw-size-full tw-select-none tw-object-contain"
                    />
                ) : (
                    <p className="tw-text-xs tw-font-medium">{position.zone}</p>
                )}
            </div>
        </Draggable>
    );
};
