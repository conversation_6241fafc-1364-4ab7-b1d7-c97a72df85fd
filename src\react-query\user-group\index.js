import { tanstackApi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useGetUserGroupsCourse({ group_id }) {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["user-group-courses", { userId, group_id }],
        queryFn: async () => {
            return (await tanstackApi.post("user-groups/list-courses", { group_id })).data;
        },
        enabled: !!group_id,
    });
}

export function useUserGroupAssignCourse() {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.post("user-groups/assign-courses", data)).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["user-group-courses"] });
        },
    });
}
