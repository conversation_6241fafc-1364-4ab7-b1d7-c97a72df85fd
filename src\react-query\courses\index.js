import { tanstack<PERSON>pi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useViewCourses = (course_id) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["view-course", { course_id, userId }],
        queryFn: async () => {
            return (await tanstackApi.post("course/view-course", { course_id })).data;
        },
        enabled: !!course_id,
    });
};

export const useUpdateCourse = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.put("course/creation/update-chapter", data)).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["view-course"] });
        },
    });
};

export const useBulkUpdateCourse = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.put("course/creation/bulk-update-chapter-content", data)).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["view-course"] });
        },
    });
};

export const useCreateCourse = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.post("course/creation/save-chapter", data)).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["view-course"] });
        },
    });
};

export const useDeleteContent = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.delete("course/creation/delete-chapter-content", { data })).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["view-course"] });
        },
    });
};

export const useDeleteChapter = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.delete(`course/creation/delete-chapter`, { data })).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["view-course"] });
        },
    });
};

export const useGetCourses = () => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["courses", userId],
        queryFn: async () => {
            return (await tanstackApi.get(`course/get-courses`)).data;
        },
    });
};

export const useGetCoursesBundle = () => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["course-bundle", { userId }],
        queryFn: async () => {
            return (await tanstackApi.get(`course-bundle/get-course-bundles`)).data;
        },
    });
};

export const useGetUserCourseBundle = ({ userId }) => {
    return useQuery({
        queryKey: ["assigned-bundle", { userId }],
        queryFn: async () => {
            return (await tanstackApi.post(`learner-permission/get-assigned-course-bundles`, { userId })).data;
        },
    });
};

export const useGetUserAssignCourseBundle = ({ userId }) => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.post(`learner-permission/add-course-bundles-to-user`, { userId, ...data })).data;
        },
        onSuccess: () => queryClient.invalidateQueries(["assigned-bundle", { userId }]),
    });
};

export const useGetUserCourse = () => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["user-course", { userId }],
        queryFn: async () => {
            return (await tanstackApi.get(`reports/get-course-reports-user`)).data;
        },
    });
};
