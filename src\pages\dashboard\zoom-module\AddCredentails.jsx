import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

const defaultValue = {
    client_id: "",
    client_secret: "",
    account_id: "",
};

const formSchema = z.object({
    client_id: z.string({ required_error: "Client ID is required" }).min(1, "Client ID is required"),
    client_secret: z.string({ required_error: "Client secret is required" }).min(1, "Client secret is required"),
    account_id: z.string({ required_error: "Account ID is required" }).min(1, "Account ID is required"),
});

const AddCredentails = ({ open, setOpen, isRegistered, checkCreds }) => {
    const [openAlert, setOpenAlert] = useState(false);

    const [credentials, setCredentials] = useState(defaultValue);

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setCredentials({ ...credentials, [name]: value });
    };

    const handleClear = () => {
        setCredentials(defaultValue);
    };

    const onSubmit = (e) => {
        e.preventDefault();

        const payload = formSchema.safeParse(credentials);
        if (!payload.success) {
            return toast.warning("Validation Error", {
                description: payload.error.errors[0].message,
            });
        }

        setOpenAlert(true);
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        const payload = formSchema.safeParse(credentials);
        if (!payload.success) {
            return toast.warning("Validation Error", {
                description: payload.error.errors[0].message,
            });
        }

        await tanstackApi
            .post("creds/create", { ...payload.data })
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("userId"),
                    event: "zoom module",
                    log: `Zoom module has been integrated to Account: ${credentials?.account_id} successfully.`,
                });
                toast.success("Credentials Added", {
                    description: res?.data?.message,
                });
                checkCreds();
                setOpen(false);
                setOpenAlert(false);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Integrate, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible. Zoom will be integrated with LMS
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Yes Integrate!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-lg">
                    <DialogHeader>
                        <DialogTitle className="tw-text-2xl">
                            {isRegistered ? "Update" : "Add"} Zoom Credentials
                        </DialogTitle>
                        <DialogDescription>fill below details from your zoom account.</DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="client_id">Client ID *</Label>
                                <Input
                                    type="text"
                                    placeholder="eg: abc12345XYZ_clientID"
                                    onChange={onHandleChange}
                                    id="client_id"
                                    name="client_id"
                                    value={credentials?.client_id}
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="client_secret">Client Secret *</Label>
                                <Input
                                    type="text"
                                    placeholder="eg: sEcReT789XYZ_exampleKey"
                                    onChange={onHandleChange}
                                    id="client_secret"
                                    name="client_secret"
                                    value={credentials?.client_secret}
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="account_id">Account ID *</Label>
                                <Input
                                    type="text"
                                    placeholder="eg: account_12345XYZ_example"
                                    onChange={onHandleChange}
                                    id="account_id"
                                    name="account_id"
                                    value={credentials?.account_id}
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <Button type="button" variant="secondary" onClick={handleClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>
                        <Button type="button" onClick={onSubmit}>
                            <i className="fa-regular fa-floppy-disk"></i> Submit
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default AddCredentails;
