import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { cn, isUrl } from "@/lib/utils";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import { motion } from "framer-motion";
import { useRef, useState } from "react";

export function Timeline({ interactions, template }) {
    const data = interactions?.structure?.database;
    const ref = useRef(null);
    const [currentTab, setCurrentTab] = useState(data[0]?.label);
    const { handleNext, handlePrev } = usePlayer();

    const handlePrevSlide = () => {
        const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (currentIndex === 0) return handlePrev();
        setCurrentTab(data[currentIndex - 1]?.label);
    };

    const handleNextSlide = () => {
        const currentIndex = data.findIndex((dt) => dt?.label == currentTab);
        if (data.length - 1 === currentIndex) return handleNext();
        setCurrentTab(data[currentIndex + 1]?.label);
    };

    return (
        <>
            <div className="tw-relative">
                <div className="tw-flex tw-h-full tw-w-full tw-flex-wrap tw-gap-[1rem]" ref={ref}>
                    <motion.div className="tw-h-full tw-w-full tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-bg-white tw-text-[40px] tw-text-black">
                        <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-[600px] tw-h-full">
                            <TabsList className="tw-absolute tw--top-6 tw-left-0 tw-right-0 tw-gap-4 tw-bg-transparent">
                                {data.map((step, index) => {
                                    return (
                                        <TabsTrigger
                                            key={index}
                                            value={step.label}
                                            className="tw-rounded-2xl tw-border-4 tw-border-[#3B3A3E] tw-bg-[#FFE19A] tw-px-6 tw-font-lazyDog !tw-text-[22px] tw-leading-none tw-text-[#3B3A3E] data-[state=active]:tw-bg-[#F8CA5C] data-[state=active]:tw-text-[#3B3A3E] 2xl:!tw-text-[36px]"
                                        >
                                            {index + 1}. {step.label}
                                        </TabsTrigger>
                                    );
                                })}
                            </TabsList>
                            <div className="tw-h-full tw-min-h-96 tw-w-full tw-border-4 tw-border-black tw-px-4 tw-pb-4 tw-pt-8">
                                {data.map((step, index) => (
                                    <TabsContent
                                        key={index}
                                        value={step.label}
                                        className="tw-grid tw-size-full tw-h-full tw-grid-cols-2 tw-grid-rows-1 tw-gap-4"
                                    >
                                        <div className="tw-font-lazyDog">
                                            <h2 className="tw-text-[30px] 2xl:tw-text-[45px]">{step.label}</h2>
                                            <div
                                                className="tw-text-base"
                                                dangerouslySetInnerHTML={{ __html: step.description }}
                                            />
                                        </div>
                                        <div className="tw-relative tw-size-full">
                                            {isUrl(step.image_src) ? (
                                                <img
                                                    src={step.image_src}
                                                    className="tw-absolute tw-inset-0 tw-size-full tw-rounded-xl tw-object-cover"
                                                    alt={step.label}
                                                />
                                            ) : (
                                                <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/60" />
                                            )}
                                        </div>
                                    </TabsContent>
                                ))}
                            </div>
                        </Tabs>
                    </motion.div>
                </div>
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    onClick={handlePrevSlide}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px]",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}
