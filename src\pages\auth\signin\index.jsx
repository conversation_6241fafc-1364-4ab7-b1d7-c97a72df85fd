import AlertSnackbar from "@/components/alert";
import { onUserLogin } from "@/redux/auth/action";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import ChangePassword from "./ChangePassword";
import { useLogin } from "@/react-query/auth/login";
import { z } from "zod";
import { AlertSnackInfo } from "@/redux/alert/alert-action";

const loginSchema = z.object({
    email: z.string().min(1, "Email Required").email("Please Enter a Valid Email"),
    password: z.string().min(1, "Password Required"),
});

const SignInPage = () => {
    const dispatch = useDispatch();
    const loginFn = useLogin();
    const user = useSelector((state) => state.AuthReducer?.onUserData);
    const AlertInfo = useSelector((state) => state.alertReducer)?.alertInfoObj;
    const [login, setLogin] = useState({
        email: "",
        password: "",
    });
    const [inputPwdType, setInputPwdType] = useState("password");
    const [open, setOpen] = useState(false);

    const [openModal, setOpenModal] = useState(false);

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setLogin({ ...login, [name]: value });
    };

    const navigate = useNavigate();

    useEffect(() => {
        if (Object.keys(user ?? {}).length > 0) {
            if (user?.role_category === "sub-user") {
                navigate("/dashboard");
            } else if (user?.role_category === "user") {
                navigate(user?.organizationDetails?.signup_completed === true ? "/dashboard/" : "/signup-steps");
            } else if (user?.role_category === "administrator") {
                navigate("/dashboard");
            }
        }
    }, [user]);

    const handleLogin = () => {
        const data = loginSchema.safeParse(login);
        if (!data.success) dispatch(AlertSnackInfo({ message: data.error.message, result: false }));
        loginFn.mutate(login);
    };

    useEffect(() => {
        if (Object.keys(AlertInfo ?? {}).length > 0) {
            if (AlertInfo.result == true) setOpen(false);
        }
    }, [AlertInfo]);

    return (
        <div className="sign_in_alpha">
            <ChangePassword open={openModal} setOpen={setOpenModal} />
            <header className="alpha_header">
                <div className="left">
                    <img src="/assets/pepsico.webp" alt="" />
                </div>
                <div className="right">
                    {/* <BsGlobe2 size={22} /> */}
                    <div className="action_cta">
                        <Link to="/signup">
                            <button>Sign up</button>
                        </Link>
                        <button className="cta_active">Sign in</button>
                    </div>
                </div>
            </header>
            <AlertSnackbar AlertInfo={AlertInfo} />
            <div className="middle_section">
                <div className="login_form">
                    <h2>LMS Login</h2>
                    <p>
                        Hey, Enter your details to get sign in <br /> to your account
                    </p>
                    <div className="form_wrapper">
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    type="text"
                                    name="email"
                                    onChange={onHandleChange}
                                    placeholder="Enter email / Username"
                                />
                                <i className="fa-solid fa-o"></i>
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <input
                                    name="password"
                                    onChange={onHandleChange}
                                    type={inputPwdType}
                                    placeholder="Passcode"
                                />
                                <small
                                    onClick={() => {
                                        setInputPwdType(inputPwdType === "password" ? "text" : "password");
                                    }}
                                >
                                    {inputPwdType === "password" ? "Show" : "Hide"}
                                </small>
                            </div>
                        </div>
                    </div>

                    <small>
                        <Link onClick={() => setOpenModal(true)}>Having trouble in sign in?</Link>
                    </small>
                    <br />
                    <button onClick={handleLogin}>Sign in</button>
                    <div className="other_sign_in">
                        <small>
                            <i className="fa-solid fa-minus"></i> Our Features <i className="fa-solid fa-minus"></i>
                        </small>
                        <div>
                            <button>
                                <i className="fa-solid fa-graduation-cap"></i> <b>Courses</b>
                            </button>
                            <button>
                                <i className="fa-solid fa-certificate"></i> <b>Certificate</b>
                            </button>
                            <button>
                                <i className="fa-solid fa-file-lines"></i> <b>Reports</b>
                            </button>
                        </div>
                    </div>
                    <div className="sign_up_link">
                        Don&apos;t have any account? <Link to="/signup">Request Now</Link>
                    </div>
                </div>
                {/* <img src="/assets/right.png" alt="" /> */}
            </div>
            <div className="alpha_footer">PEPSICO @ 2025 Created by The Infowarehouse.</div>
        </div>
    );
};

export default SignInPage;
