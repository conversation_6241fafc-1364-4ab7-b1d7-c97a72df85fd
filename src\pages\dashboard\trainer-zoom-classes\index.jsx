import Pagination from "@/components/table/pagination";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { tanstackApi } from "@/react-query/api";
import { isBefore } from "date-fns";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const ITEMS_PER_PAGE = 10;

const schedule_range = ["today", "this_week", "this_month", "this_year", "custom"];

const hasDatePassed = (date) => {
    return isBefore(date, new Date());
};

const TrainerZoomClasses = () => {
    const navigate = useNavigate();

    const [filterState, setFilterState] = useState({
        type: "",
        start_date: "",
        end_date: "",
    });

    const [openAlert, setOpenAlert] = useState(false);
    const [editData, setEditData] = useState(null);
    const [tableData, setTableData] = useState([]);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

    useEffect(() => {
        if (filteredData?.length > 0) {
            const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
            const endIndex = startIndex + ITEMS_PER_PAGE;
            setTableData(filteredData.slice(startIndex, endIndex));
        }
    }, [currentPage, filteredData]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        getSchedules({});
    }, []);

    const getSchedules = async (payload) => {
        await tanstackApi
            .get("chapter-class/list-trainer", { params: payload })
            .then((res) => {
                setDataList(res?.data?.data);
                setFilteredData(res?.data?.data); // Initialize filtered data with all data
            })
            .catch((err) => {
                setDataList([]);
                setFilteredData([]);
            });
    };

    const onSearch = () => {
        let payload = {
            type: filterState?.type,
            start_date: filterState?.type === "custom" ? filterState?.start_date : undefined,
            end_date: filterState?.type === "custom" ? filterState?.end_date : undefined,
        };
        getSchedules(payload);
    };

    const onClear = () => {
        setFilterState({
            type: "",
            start_date: "",
            end_date: "",
        });
        getSchedules({});
    };

    const onJoinClass = () => {
        punchTimelineLog({
            user_id: localStorage.getItem("parent_user_id"),
            event: "sessions",
            log: `Trainer: ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} attended ${editData?.topic} successfully.`,
        });

        window.open(editData?.location, "_blank");
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you ready to join the class?</AlertDialogTitle>
                        <AlertDialogDescription>Click on join button to continue. Thank you!</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onJoinClass}>Yes Join!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Zoom Classes</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
            </div>
            <div className="page_filters tw-mt-4 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Schedules
                    </label>
                    <select onChange={onFilterChange} value={filterState?.type} name="type" className="tw-text-sm">
                        <option value="">- All -</option>
                        {schedule_range?.map((data, idx) => (
                            <option key={idx} value={data} className="tw-capitalize">
                                {data?.replaceAll("_", " ")}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        From
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState?.start_date}
                        name="start_date"
                        className="tw-text-sm"
                        type="date"
                        disabled={filterState?.type !== "custom"}
                        style={{ cursor: filterState?.type !== "custom" && "not-allowed" }}
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        To
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState?.end_date}
                        name="end_date"
                        className="tw-text-sm"
                        type="date"
                        disabled={filterState?.type !== "custom"}
                        style={{ cursor: filterState?.type !== "custom" && "not-allowed" }}
                    />
                </div>

                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table tw-mt-4">
                <table className="tw-font-lexend">
                    <thead>
                        <tr>
                            <th>Meet</th>
                            <th>Type</th>
                            <th>Topic</th>
                            <th>Chapter & Course</th>
                            <th>Scheduled On</th>
                            <th>Time</th>
                            <th>Duration</th>
                            <th>Classes</th>
                        </tr>
                    </thead>
                    <tbody className="tw-font-lexend">
                        {filteredData.length === 0 ? (
                            <tr>
                                <td colSpan="8" className="tw-h-40 tw-py-4 tw-text-center tw-font-lexend">
                                    No classes found.
                                </td>
                            </tr>
                        ) : (
                            tableData?.map((row, idx) => {
                                return (
                                    <tr key={idx}>
                                        <td>
                                            <div className="tw-flex tw-items-center tw-gap-2">
                                                <img
                                                    className="tw-h-8"
                                                    src={
                                                        row?.meet_type === "zoom"
                                                            ? "/assets/zoom.png"
                                                            : row?.meet_type === "google-meet"
                                                              ? "/assets/google.png"
                                                              : ""
                                                    }
                                                    alt=""
                                                />
                                                <Label>
                                                    {row?.meet_type === "zoom"
                                                        ? "Zoom"
                                                        : row?.meet_type === "google-meet"
                                                          ? "Google"
                                                          : ""}
                                                </Label>
                                            </div>
                                        </td>
                                        <td>
                                            {row?.type === "online" ? (
                                                <Badge className={"tw-font-lexend"}>Online</Badge>
                                            ) : (
                                                <Badge variant={"outline"} className={"tw-font-lexend"}>
                                                    Offline
                                                </Badge>
                                            )}
                                        </td>
                                        <td>{row?.topic}</td>
                                        <td>
                                            <div>
                                                <p>{row?.chapter?.chapter_title}</p>
                                                <p>{row?.chapter?.lms_course?.course_title}</p>
                                            </div>
                                        </td>
                                        <td>{moment(row?.date).format("LL")}</td>
                                        <td>{`${row?.start_time} - ${row?.end_time}`}</td>
                                        <td>{`${row?.duration} mins`}</td>
                                        <td>
                                            {row?.type === "offline" ? (
                                                <Button
                                                    variant=""
                                                    onClick={() =>
                                                        navigate(
                                                            `/dashboard/trainer-classes-learners/${row?.id}/${row?.topic}`,
                                                        )
                                                    }
                                                >
                                                    <i className="fa-solid fa-users-line"></i> Attendance
                                                </Button>
                                            ) : (
                                                <>
                                                    {hasDatePassed(
                                                        `${moment(row?.date).format("YYYY-MM-DD")}T${row?.start_time}`,
                                                    ) ? (
                                                        <Button variant="delete">
                                                            <i className="fa-solid fa-clock-rotate-left"></i> Expired
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            variant="primary"
                                                            onClick={() => {
                                                                setEditData(row);
                                                                setOpenAlert(true);
                                                            }}
                                                        >
                                                            <i className="fa-solid fa-video"></i> Join Class
                                                        </Button>
                                                    )}
                                                </>
                                            )}
                                        </td>
                                    </tr>
                                );
                            })
                        )}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">
                        {ITEMS_PER_PAGE}
                    </p>
                </div>
                <div>
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        handlePageChange={handlePageChange}
                        itemsPerPage={ITEMS_PER_PAGE}
                    />
                </div>
            </div>
        </div>
    );
};

export default TrainerZoomClasses;
