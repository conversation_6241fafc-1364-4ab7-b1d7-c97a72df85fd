import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    B<PERSON><PERSON>rumbI<PERSON>,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
// import QuestionTypeForm from "./QuestionTypeForm";
import { Badge } from "@/components/ui/badge";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { utils, writeFileXLSX } from "xlsx";

const courseStatusList = [
    {
        key: "completed",
        label: "Completed",
    },
    {
        key: "in progress",
        label: "In Progress",
    },
    {
        key: "not started",
        label: "Not Started",
    },
];

const LearnerReport = () => {
    const navigate = useNavigate();
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(11);

    const [filterState, setFilterState] = useState({
        search: "",
        course_status: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        if (filteredData?.length > 0) {
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;
            let options = filteredData.slice(startIndex, endIndex);
            setTableData(options);
        }
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? `${item?.first_name} ${item?.last_name}`?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const matchesEvent = filterState?.course_status ? item?.course_status === filterState?.course_status : true; // Allow all items if no subCategory filter

            return matchesname && matchesEvent; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            course_status: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getReports();
    }, []);

    const getReports = async () => {
        await tanstackApi
            .get("reports/get-learner-progress")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    function excelConverter() {
        var finalData = filteredData?.map((row, index) => {
            return {
                "User Name": `${row?.first_name} ${row?.last_name}`,
                Email: row?.email,
                Course: row?.course_name,
                "Start Date": moment(row?.start_date).format("L"),
                "Completion Date": moment(row?.end_date).format("L"),
                Progress: `${row?.progress} %`,
                Status: row?.course_status,
                Certificate: row?.certificate,
                "Certificate Issue Date": row?.issue_date,
                "Certificate Expiry Date": row?.certificate_validity,
            };
        });
        const wb = utils.book_new();
        utils.book_append_sheet(wb, utils.json_to_sheet(finalData));
        writeFileXLSX(wb, "Learner Reports.xlsx");
    }

    const onDownload = (row) => {
        const url = row?.certificate;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `Certificate : ${`${row?.first_name} ${row?.last_name}`}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    return (
        <>
            <div>
                <div className="tw-flex tw-justify-between">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink href="#">Reports</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Learner Reports</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <Button className="tw-px-2 tw-py-1" onClick={excelConverter}>
                        <i className="fa-solid fa-download"></i> Export Data
                    </Button>
                </div>
                {/* <br /> */}
                <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label htmlFor="">Search</label>
                        <input
                            type="text"
                            placeholder="Username or email ..."
                            onChange={onFilterChange}
                            value={filterState?.search}
                            name="search"
                        />
                    </div>
                    <div className="input_group">
                        <label htmlFor="">Status</label>
                        <select onChange={onFilterChange} value={filterState?.course_status} name="course_status">
                            <option value=""> - Choose Status - </option>
                            {courseStatusList?.map((status, idx) => (
                                <option key={idx} value={status?.key}>
                                    {status?.label}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>User Name</th>
                                <th>Email</th>
                                <th>Course</th>
                                <th>Start Date</th>
                                <th>Completion Date</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>Certificate</th>
                                <th>Certificate Issue Date</th>
                                <th>Certificate Expiry Date</th>
                            </tr>
                        </thead>
                        <tbody className="tw-font-lexend">
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>{`${row?.first_name} ${row?.last_name}`}</td>
                                    <td>{row?.email}</td>
                                    <td>{row?.course_name}</td>
                                    <td>{row?.start_date ? moment(row?.start_date).format("LL") : "-"}</td>
                                    <td>
                                        {row?.end_date && moment(row?.end_date).isValid()
                                            ? moment(row?.end_date).format("LL")
                                            : "-"}
                                    </td>
                                    <td>{row?.progress}%</td>
                                    <td>
                                        <Badge className="tw-capitalize" variant={"outline"}>
                                            {row?.course_status}
                                        </Badge>
                                    </td>
                                    <td>
                                        {row?.certificate ? (
                                            <Button variant="outline" onClick={() => onDownload(row)}>
                                                Download
                                            </Button>
                                        ) : (
                                            "-"
                                        )}
                                    </td>
                                    <td>
                                        {row?.certificate_issue_date && moment(row?.certificate_issue_date).isValid()
                                            ? moment(row?.certificate_issue_date).format("LL")
                                            : "-"}
                                    </td>
                                    <td>
                                        {row?.certificate_validity && moment(row?.certificate_validity).isValid()
                                            ? moment(row?.certificate_validity).format("LL")
                                            : row?.certificate_issue_date &&
                                                moment(row?.certificate_issue_date).isValid()
                                              ? moment(row?.certificate_issue_date).add(1, "year").format("LL")
                                              : "-"}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination className="">
                            <PaginationContent>
                                {/* Previous Button */}
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {/* Page Numbers with Ellipses */}
                                {currentPage > 3 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                1
                                            </PaginationLink>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    </>
                                )}

                                {getVisiblePages().map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href="#"
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {currentPage < totalPages - 2 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                {totalPages}
                                            </PaginationLink>
                                        </PaginationItem>
                                    </>
                                )}

                                {/* Next Button */}
                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </>
    );
};

export default LearnerReport;
