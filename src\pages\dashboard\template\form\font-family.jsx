import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import ColorInput from "@/pages/dashboard/template/form/color-input";
import { useState } from "react";

const fontBoldMapping = {
    Normal: "400",
    Mediumn: "500",
    SemiBold: "600",
    Bold: "700",
    ExtraBold: "800",
};

const elementStyles = [
    {
        type: "color",
        name: "color",
    },
    {
        type: "color",
        name: "background_color",
    },
    {
        type: "select",
        name: "font_family",
        options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
    },
    {
        type: "text",
        name: "font_size",
    },
    {
        type: "select",
        name: "text_align",
        options: ["left", "center", "right"],
    },
    {
        type: "select",
        name: "font_weight",
        options: ["Normal", "Medium", "SemiBold", "Bold", "ExtraBold"],
    },
    {
        type: "select",
        name: "text_decoration",
        options: ["underline", "line-through", "overline"],
    },
];

export default function FontFamilyForm({ setTemplate, template }) {
    const [activeTab, setActiveTab] = useState("body");

    const setData = ({ name, value, element }) => {
        setTemplate((prev) => {
            const oldData = { ...prev };
            if (!oldData.font_family) oldData.font_family = {};
            if (!oldData.font_family[element] || typeof oldData.font_family[element] !== "object") {
                oldData.font_family[element] = elementStyles.reduce((acc, style) => {
                    acc[style.name] = "";
                    return acc;
                }, {});
            }
            oldData.font_family[element][name] = value;
            return oldData;
        });
    };

    const handleChange = (e, element) => {
        let { name, value } = e.target;
        if (name == "font_weight") value = fontBoldMapping[value];
        setData({ name, value, element });
    };

    return (
        <div>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="tw-h-auto tw-flex-wrap">
                    {["body", "subtitle", "title"].map((key) => {
                        return (
                            <TabsTrigger key={key} value={key} className="tw-capitalize">
                                {key.replaceAll("_", " ")}
                            </TabsTrigger>
                        );
                    })}
                </TabsList>
                {["body", "subtitle", "title"].map((key) => {
                    return (
                        <TabsContent key={key} value={key} className="tw-space-y-3">
                            {elementStyles.map(({ name, type, options }) => {
                                return (
                                    <div key={name} className="tw-space-y-1">
                                        <Label className="tw-capitalize">{name.replaceAll("_", " ")}</Label>
                                        {type !== "select" &&
                                            (type == "color" ? (
                                                <ColorInput
                                                    onHandleChange={(value) => setData({ name, value, element: key })}
                                                    value={template?.font_family?.[key]?.[name]}
                                                    name={name}
                                                />
                                            ) : (
                                                <Input
                                                    value={template?.font_family?.[key]?.[name]}
                                                    name={name}
                                                    onChange={(e) => handleChange(e, key)}
                                                    type={type}
                                                />
                                            ))}
                                        {type == "select" && (
                                            <SelectNative name={name} onChange={(e) => handleChange(e, key)}>
                                                <option value="">Select Options</option>
                                                {options.map((v) => (
                                                    <option
                                                        selected={v == template?.font_family?.[key]?.[name]}
                                                        key={v}
                                                        value={v}
                                                    >
                                                        {v}
                                                    </option>
                                                ))}
                                            </SelectNative>
                                        )}
                                    </div>
                                );
                            })}
                        </TabsContent>
                    );
                })}
            </Tabs>
        </div>
    );
}
