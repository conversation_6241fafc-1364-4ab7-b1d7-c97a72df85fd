import { CONSTANTS } from "@/constants";
import * as Actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* DefineRole(roleData) {
    const defineRolePost = yield axios
        .put(`${CONSTANTS.getAPI()}organization/update-signup-steps`, roleData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("auth_signup_token") ? localStorage.getItem("auth_signup_token") : localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            alert(err?.response?.data?.message);
            return errMsg;
        });
    if (defineRolePost) {
        yield put(
            AlertSnackInfo({
                message: `${
                    defineRolePost.message
                } as ${defineRolePost.data.organization_info.define.role_code.toUpperCase()}`,
                result: defineRolePost.success,
            }),
        );
    }
}

function* AddUserAbout(aboutUserData) {
    const defineRolePost = yield axios
        .put(`${CONSTANTS.getAPI()}organization/update-signup-steps`, aboutUserData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("auth_signup_token") ? localStorage.getItem("auth_signup_token") : localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            const orgData = response.data.organization_info;
            localStorage.setItem("org_data", JSON.stringify(orgData));
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            alert(err?.response?.data?.message);
            return errMsg;
        });
    if (defineRolePost) {
        yield put(
            AlertSnackInfo({
                message:
                    `About section is Updated successfully` +
                    ` of ${defineRolePost.data.organization_info.define.role_code.toUpperCase()}`,
                result: defineRolePost.success,
            }),
        );
    }
}

function* AddGoalSaga(goalsData) {
    const defineRolePost = yield axios
        .put(`${CONSTANTS.getAPI()}organization/update-signup-steps`, goalsData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("auth_signup_token") ? localStorage.getItem("auth_signup_token") : localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            alert(err?.response?.data?.message);
            return errMsg;
        });
    if (defineRolePost) {
        if (defineRolePost?.success) window.location.href = "/";
        yield put(
            AlertSnackInfo({
                message:
                    `Goals section is Updated successfully` +
                    ` of ${defineRolePost.data.organization_info.define.role_code.toUpperCase()}`,
                result: defineRolePost.success,
            }),
        );
    }
}

export function* DefineRoleWatcher() {
    yield takeEvery(Actions.ADD_DEFINE_ROLES, DefineRole);
    yield takeEvery(Actions.ADD_USER_ABOUT, AddUserAbout);
    yield takeEvery(Actions.ADD_GOALS, AddGoalSaga);
}

export default function* DefineRoleSaga() {
    yield all([fork(DefineRoleWatcher)]);
}
