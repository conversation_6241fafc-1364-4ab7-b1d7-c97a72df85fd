import { CONSTANTS } from "@/constants";
import { setCookie } from "@/lib/helpers/cookie";
import { timelineLog } from "@/react-query/common/timeline";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { onDomainData, onPermissionDataSave, onUserPersonalData } from "@/redux/auth/action";
import { addTimelineLog } from "@/redux/reports/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* onUSERLogin(loginDetails) {
    const loginData = yield axios
        .post(`${CONSTANTS.getAPI()}auth/signin`, loginDetails.payload)
        .then((res) => {
            var response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response;
            return errMsg;
        });
    if (loginData) {
        if (loginData?.success) {
            const permissions = JSON.stringify(loginData.data.permissions);
            localStorage.setItem("permissions", permissions);
            localStorage.setItem("login_token", loginData?.token);
            localStorage.setItem("userId", loginData?.data?.id);
            localStorage.setItem("lms_fName", loginData?.data?.first_name);
            localStorage.setItem("lms_lName", loginData?.data?.last_name);
            localStorage.setItem("user_email", loginData?.data?.email);
            localStorage.setItem("user_image", loginData?.data?.picture_url);
            localStorage.setItem("domain_name", loginData?.data?.domain_name);
            localStorage.setItem("user_code", loginData?.data?.user_code);
            localStorage.setItem("user_loggedIn", true);
            localStorage.setItem("role_category", loginData?.data?.role_category);
            localStorage.setItem("is_trainer", loginData?.data?.is_trainer);
            localStorage.setItem("login_role", loginData?.data?.role);
            document.cookie = `login_token=${loginData?.token}; path=/; max-age=${60 * 60 * 24 * 7}`;

            // debugger
            if (loginData?.data?.organizationDetails !== undefined) {
                const org_data = JSON.stringify(loginData?.data?.organizationDetails?.organization_info);
                localStorage.setItem("org_data", org_data);
            } else {
                localStorage.setItem("org_data", JSON.stringify({}));
            }
            if (loginData?.data?.role_category == "administrator") {
                localStorage.setItem("level", "levelOne");
                localStorage.setItem(
                    "terms_conditions",
                    JSON.stringify(loginData?.data?.organizationDetails?.term_n_conditions),
                );
            } else if (loginData?.data?.role_category === "user") {
                localStorage.setItem("level", "levelTwo");
                localStorage.setItem(
                    "terms_conditions",
                    JSON.stringify(loginData?.data?.organizationDetails?.term_n_conditions),
                );
            } else if (loginData?.data?.role_category == "sub-user") {
                localStorage.setItem("level", "levelThree");
                localStorage.setItem("parent_user_id", loginData?.data?.parent_user_id);
                localStorage.setItem("QL_email", loginData.data.email);
                localStorage.setItem("QL_password", loginDetails.payload.password);

                yield put(
                    addTimelineLog({
                        user_id: loginData?.data?.parent_user_id,
                        event: "login",
                        log: `${loginData?.data?.first_name} ${loginData?.data?.last_name} logged in successfully `,
                    }),
                );
            }

            localStorage.removeItem("auth_signup_token");

            yield put(onUserPersonalData({ ...loginData?.data }));
            yield put(onPermissionDataSave(permissions));
        } else {
            yield put(AlertSnackInfo({ message: loginData.data.message, result: loginData.data.success }));
        }
    }
}

function* onUserSignUp(signUpData) {
    const loginData = yield axios
        .post(`${CONSTANTS.getAPI()}signup`, signUpData.payload.Data)
        .then((res) => {
            var response = res.data;
            return response;
        })
        .catch((err) => {
            alert(err?.response?.data?.message);
            var errMsg = err.response;
            return errMsg;
        });
    if (loginData) {
        const domainValidity = localStorage.getItem("domain_validity");
        if (loginData?.success) {
            if (signUpData.payload.type === "learner") {
                localStorage.setItem("id", loginData.data.id);
                localStorage.setItem("email", loginData.data.email);
                localStorage.setItem("first_name", loginData.data.first_name);
                localStorage.setItem("last_name", loginData.data.last_name);
                localStorage.setItem("status", loginData.data.status);
                localStorage.setItem("parent_user_id", loginData.data.parent_user_id);
                localStorage.setItem("auth_signup_token", loginData.token);
                localStorage.setItem("QL_email", loginData.data.email);
                localStorage.setItem("QL_password", signUpData.payload.Data.password);

                timelineLog({
                    user_id: loginData?.data?.parent_user_id,
                    event: "signup",
                    log: `${loginData?.data?.first_name} ${loginData?.data?.last_name} Sign up successfully `,
                });

                window.location.href = "/learner-profile";
            } else if (signUpData.payload.type === "organisation") {
                localStorage.setItem("auth_signup_token", loginData.token);
                window.location.href = "/signup-steps";
                localStorage.removeItem("domain_validity");
            }
        }
    } else {
        yield null;
    }
}

function* onDomainNameCheck(domainName) {
    yield console.log("Auth");
    const domain = yield axios
        .post(`${CONSTANTS.getAPI()}signup/check-name`, domainName.payload)
        .then((res) => {
            var response = res.data;
            return response;
        })
        .catch((err) => {
            alert(err?.response?.data?.message);
            var errMsg = err.response;
            return errMsg;
        });
    if (domain) {
        yield put(onDomainData(domain));
        if (!domain?.validity) {
            localStorage.setItem("domain_validity", false);
        } else {
            localStorage.setItem("domain_validity", true);
        }
    } else {
        yield put(onDomainData({}));
    }
}

function* onSaveUserDomain(domain) {
    const domainRes = yield axios
        .post(`${CONSTANTS.getAPI()}signup/check-name`, {
            name: domain.payload,
        })
        .then((res) => {
            var response = res.data;
            alert(res?.data?.message);
            return response;
        })
        .catch((err) => {
            alert(err?.response?.data?.message);
            var errMsg = err.response;
            return errMsg;
        });

    if (domainRes.validity === true) {
        const responce = yield axios
            .put(
                `${CONSTANTS.getAPI()}users/update-user`,
                {
                    domain_name: domain.payload,
                    id: localStorage.getItem("userId"),
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${localStorage.getItem("login_token")}`,
                    },
                },
            )
            .then((res) => {
                var response = res.data;
                alert("Details Updated Successfully");
                localStorage.setItem("domain_name", response?.data?.domain_name);
                return response;
            })
            .catch((err) => {
                alert(err?.response?.data?.message);
                var errMsg = err.response;
                return errMsg;
            });
        if (responce) {
            yield put(onDomainData(responce));
        } else {
            yield put(onDomainData({}));
        }
    } else {
        alert("Domain is not valid");
    }
}

function* onSaveUserPersonDetails(data) {
    const responce = yield axios
        .put(
            `${CONSTANTS.getAPI()}users/update-user`,
            {
                ...data.payload,
                id: localStorage.getItem("userId"),
            },
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("login_token")}`,
                },
            },
        )
        .then((res) => {
            var response = res.data;
            alert("Details Updated Successfully");
            localStorage.setItem("lms_fName", response?.data?.first_name);
            localStorage.setItem("lms_lName", response?.data?.last_name);
            localStorage.setItem("user_email", response?.data?.email);
            return response;
        })
        .catch((err) => {
            alert(err?.response?.data?.message);
            var errMsg = err.response;
            return errMsg;
        });
    if (responce) {
        yield put(onUserPersonalData(responce.data));
    } else {
        yield put(onUserPersonalData({}));
    }
}

export function* AuthLoginWatcher() {
    yield takeEvery(actions.LOGIN_REQUEST, onUSERLogin);
    yield takeEvery(actions.USER_SIGN_UP, onUserSignUp);
    yield takeEvery(actions.DOMAIN_NAME_CHECK, onDomainNameCheck);
    yield takeEvery(actions.DOMAIN_NAME_SAVE, onSaveUserDomain);
    yield takeEvery(actions.USER_PERSONAL_DETAILS_SAVE, onSaveUserPersonDetails);
}

export default function* AuthSaga() {
    yield all([fork(AuthLoginWatcher)]);
}
