import { colorsList } from "@/data/colors";
import { cn } from "@/lib/utils";
import ContentSlideLayout from "@/pages/dashboard/course-player/quiz-content/layouts/ContentSlideLayout";
import { DndContext, useDraggable, useDroppable } from "@dnd-kit/core";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

function DraggableItem({ id, children, className }) {
    const { attributes, listeners, setNodeRef, transform } = useDraggable({ id });
    const style = {
        transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
        cursor: "grab",
    };

    return (
        <div ref={setNodeRef} style={style} className={className} {...listeners} {...attributes}>
            {children}
        </div>
    );
}

function DroppableArea({ id, children, className = "" }) {
    const { isOver, setNodeRef } = useDroppable({ id });

    return (
        <div ref={setNodeRef} className={cn(className, isOver && "tw-border-blue-500")}>
            {children}
        </div>
    );
}

function MatchTheFollowing({
    handleNextSlide,
    handlePrevSlide,
    className,
    data,
    sequence,
    onComponentAnswer,
    template,
}) {
    const boxes = data?.matchOptions?.map((item) => ({
        id: `box-${item.id}`,
        label: item.label,
        image: item.imageLabel,
    }));

    const initialOptions = data?.matchOptions?.map((item) => ({
        id: item.id,
        text: item.text,
        image: item.imageText,
    }));

    const shuffleArray = (array) => array.sort(() => Math.random() - 0.5);
    const [availableOptions, setAvailableOptions] = useState(shuffleArray([...initialOptions]));

    const [placeholders, setPlaceholders] = useState(() => {
        const init = {};
        boxes.forEach((box) => {
            init[box.id] = null;
        });
        return init;
    });

    useEffect(() => {
        const orderedOptions = Object.keys(placeholders)
            .sort((a, b) => {
                const aNum = parseInt(a.split("-")[1], 10);
                const bNum = parseInt(b.split("-")[1], 10);
                return aNum - bNum;
            })
            .map((boxKey) => {
                const optionId = placeholders[boxKey];
                return data?.matchOptions.find((option) => option.id === optionId);
            });
        const ans = orderedOptions.filter((dt) => dt).filter((dt, idx) => Number(dt.correctIndex) - 1 == idx);
        let ppq = data?.points / data?.matchOptions?.length;
        let points = Math.round(ans?.length * ppq);
        onComponentAnswer(sequence, orderedOptions, points);
    }, [placeholders]);

    const handleDragEnd = (event) => {
        const { active, over } = event;
        if (!over) return;
        const activeId = active.id;
        const overId = over.id;

        if (overId === "options-droppable") {
            const sourceBoxId = Object.keys(placeholders).find((boxId) => placeholders[boxId] === activeId);
            if (sourceBoxId) {
                setPlaceholders((prev) => ({ ...prev, [sourceBoxId]: null }));
                const optionToReturn = initialOptions.find((option) => option.id === activeId);
                setAvailableOptions((prev) => [...prev, optionToReturn]);
            }
            return;
        }

        if (overId.startsWith("box-")) {
            const isFromAvailable = availableOptions.some((option) => option.id === activeId);
            if (isFromAvailable) {
                setAvailableOptions((prev) => prev.filter((option) => option.id !== activeId));
                setPlaceholders((prev) => {
                    const newPlaceholders = { ...prev };
                    const replacedOptionId = newPlaceholders[overId];
                    newPlaceholders[overId] = activeId;
                    if (replacedOptionId) {
                        const optionToReturn = initialOptions.find((option) => option.id === replacedOptionId);
                        setAvailableOptions((prevOptions) => [...prevOptions, optionToReturn]);
                    }
                    return newPlaceholders;
                });
            } else {
                const sourceBoxId = Object.keys(placeholders).find((boxId) => placeholders[boxId] === activeId);
                if (sourceBoxId) {
                    setPlaceholders((prev) => {
                        const newPlaceholders = { ...prev };
                        const targetOptionId = newPlaceholders[overId];
                        newPlaceholders[overId] = activeId;
                        newPlaceholders[sourceBoxId] = targetOptionId || null;
                        return newPlaceholders;
                    });
                }
            }
        }
    };

    const getOptionById = (id) => initialOptions.find((option) => option.id === id);

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data: data?.name ?? "Complete the gaps using the appropriate words from the box.",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div className="tw-grid tw-h-full tw-min-h-[20rem] tw-w-full tw-grid-cols-1 tw-gap-6">
                <DndContext onDragEnd={handleDragEnd}>
                    <div className="tw-grid tw-grid-cols-4 tw-grid-rows-[1fr_minmax(40px,_auto)] tw-gap-x-6">
                        {boxes.map((box) => (
                            <DroppableArea key={box.id} id={box.id}>
                                <motion.div
                                    className="tw-h-full tw-rounded-lg tw-bg-teal-200 tw-p-2 tw-text-center tw-font-mono tw-text-[40px] tw-leading-[40px] tw-text-white"
                                    style={{
                                        color: data?.styles?.question?.color,
                                        fontFamily: data?.styles?.question?.fontFamily,
                                        fontSize: data?.styles?.question?.fontSize,
                                        lineHeight: 1,
                                        backgroundColor: data?.styles?.question?.backgroundColor,
                                        borderColor: data?.styles?.question?.borderColor,
                                        borderWidth: data?.styles?.question?.borderWidth,
                                        borderStyle: data?.styles?.question?.borderStyle,
                                    }}
                                >
                                    {box.label}
                                    {box.image && (
                                        <div className="tw-mt-2 tw-flex tw-items-center tw-justify-center">
                                            <img
                                                src={box.image}
                                                alt={box.label}
                                                className="tw-h-40 tw-w-full tw-object-contain"
                                            />
                                        </div>
                                    )}
                                </motion.div>
                            </DroppableArea>
                        ))}

                        {boxes.map((box, idx) => (
                            <DroppableArea
                                key={box.id}
                                id={box.id}
                                className={cn(
                                    "tw-flex tw-h-full tw-w-full tw-items-center tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-600",
                                )}
                            >
                                {placeholders[box.id] ? (
                                    <DraggableItem id={placeholders[box.id]} className="tw-flex-1 tw-rounded-lg">
                                        <div
                                            className="tw-flex tw-flex-1 tw-flex-col tw-items-center tw-justify-center tw-gap-x-5 tw-rounded-lg tw-py-3"
                                            style={{
                                                backgroundColor: colorsList[Number(box.id.replace("box-", "")) - 1],
                                                color: data?.styles?.selected?.color,
                                                fontSize: data?.styles?.selected?.fontSize,
                                                borderColor: data?.styles?.selected?.borderColor,
                                                borderWidth: data?.styles?.selected?.borderWidth,
                                                borderStyle: data?.styles?.selected?.borderStyle,
                                            }}
                                        >
                                            {getOptionById(placeholders[box.id])?.text || ""}
                                            {getOptionById(placeholders[box.id])?.image && (
                                                <img
                                                    src={getOptionById(placeholders[box.id])?.image}
                                                    alt="option"
                                                    className="tw-h-10 tw-w-full tw-select-none tw-object-contain"
                                                />
                                            )}
                                        </div>
                                    </DraggableItem>
                                ) : (
                                    <div className="tw-flex tw-w-full tw-items-center tw-justify-center tw-p-2">
                                        <img
                                            className="tw-size-12 tw-opacity-50"
                                            src="/assets/match_the_following.png"
                                        />
                                    </div>
                                )}
                            </DroppableArea>
                        ))}
                    </div>
                    <DroppableArea
                        id="options-droppable"
                        className="tw-mt-8 tw-h-fit tw-min-h-40 tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-600 tw-p-4"
                    >
                        <div className="tw-flex tw-min-w-16 tw-items-center tw-justify-center tw-gap-4">
                            {availableOptions.map((option) => (
                                <DraggableItem key={option.id} id={option.id}>
                                    <div
                                        className="tw-rounded-lg"
                                        style={{
                                            padding: "10px",
                                            textAlign: "center",
                                            color: data?.styles?.answer?.color,
                                            fontFamily: data?.styles?.answer?.fontFamily,
                                            fontSize: data?.styles?.answer?.fontSize,
                                            lineHeight: 1,
                                            borderColor: data?.styles?.answer?.borderColor,
                                            borderWidth: data?.styles?.answer?.borderWidth,
                                            borderStyle: data?.styles?.answer?.borderStyle,
                                            backgroundColor: colorsList[Number(option.id) - 1],
                                        }}
                                    >
                                        {option.text}
                                        {option.image && (
                                            <img
                                                src={option.image}
                                                alt="option"
                                                className="tw-h-20 tw-w-full tw-select-none tw-object-contain"
                                            />
                                        )}
                                    </div>
                                </DraggableItem>
                            ))}
                        </div>
                    </DroppableArea>
                </DndContext>
            </div>
        </ContentSlideLayout>
    );
}

export default MatchTheFollowing;
