import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogClose,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    <PERSON><PERSON>Header,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "sonner";

const DomainTypeAddEdit = ({ open, setOpen, editData, getDomainTypes }) => {
    const params = useParams();

    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const [domainType, setDomainType] = useState({
        display_name: "",
        icon: "",
        type: "",
    });

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setDomainType({ ...domainType, [name]: value });
    };

    useEffect(() => {
        if (editData !== null) {
            setDomainType({
                display_name: editData?.display_name,
                icon: editData?.icon,
                type: editData?.type,
            });
        } else {
            setDomainType({
                display_name: "",
                icon: "",
                type: "",
            });
        }
    }, [editData]);

    const RemoveImage = () => {
        setDomainType({ ...domainType, icon: "" });
    };

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-IMAGES");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setDomainType({ ...domainType, icon: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setDomainType({ ...domainType, icon: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setDomainType({ ...domainType, icon: "" });
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!domainType?.icon) {
            toast?.warning("Display Icon", {
                description: "Domain type display icon is required",
            });
            return false;
        } else if (!domainType?.display_name) {
            toast?.warning("Display Name", {
                description: "Domain type display name is required",
            });
            return false;
        }

        if (editData == null) {
            const payload = {
                display_name: domainType?.display_name,
                icon: domainType?.icon,
                type: domainType?.display_name?.toLowerCase()?.replaceAll(" ", "_"),
            };

            await tanstackApi
                .post("domain-type/create", { ...payload })
                .then((res) => {
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                    getDomainTypes();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                display_name: domainType?.display_name,
                icon: domainType?.icon,
                type: domainType?.display_name?.toLowerCase()?.replaceAll(" ", "_"),
            };

            await tanstackApi
                .put(`domain-type/update/${editData?.id}`, { ...payload })
                .then((res) => {
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    getDomainTypes();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-[800px]">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">
                                {editData !== null ? "Update" : "Create a new"} domain type!
                            </h1>
                        </DialogTitle>
                        <DialogDescription>
                            Fill up below details according to your domain type convenience.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="tw-rows-cols-2 tw-grid tw-gap-5">
                        <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                            <div className="tw-aspect-[2/1] tw-w-[60%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                                <img
                                    className="tw-aspect-[1/1] tw-w-full tw-rounded-md tw-object-contain"
                                    src={domainType?.icon || "/assets/thumbnail-alpha.png"}
                                />
                            </div>
                            <div className="tw-flex tw-gap-2">
                                {!domainType?.icon && (
                                    <Button
                                        htmlFor="icon"
                                        className={cn(
                                            buttonVariants({ variant: "outline" }),
                                            "aspect-square max-sm:p-0 tw-rounded-xl",
                                        )}
                                    >
                                        <Upload
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <input
                                            onChange={onImageChange}
                                            type="file"
                                            style={{ display: "none" }}
                                            id="icon"
                                            accept="image/*"
                                        />
                                        <Label htmlFor="icon" className="max-sm:sr-only">
                                            {load ? "Uploading" : "Upload Icon"} {load ? `${uploaded}%` : null}
                                        </Label>
                                    </Button>
                                )}
                                {domainType?.icon && (
                                    <Button
                                        variant="outline"
                                        className="aspect-square max-sm:p-0"
                                        onClick={RemoveImage}
                                    >
                                        <X
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <Label className="max-sm:sr-only">Remove Icon</Label>
                                    </Button>
                                )}
                            </div>
                        </div>
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="display_name">Display Name</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={domainType?.display_name}
                                        name="display_name"
                                        id="display_name"
                                        placeholder="Enter domain type display name here"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                <i className="fa-solid fa-xmark"></i> Close
                            </Button>
                        </DialogClose>
                        <Button type="submit" onClick={onDataSubmit}>
                            <i className="fa-solid fa-floppy-disk"></i> Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default DomainTypeAddEdit;
