import { cn, isUrl } from "@/lib/utils";
import { motion, useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import ContentSlideLayout from "../layouts/ContentSlideLayout";

const StringDropdown = ({
    handleNextSlide,
    handlePrevSlide,
    className,
    data,
    sequence,
    onComponentAnswer,
    template,
}) => {
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { scale: 0.5 },
        inView: { scale: 1 },
    };
    const [tempSelectedValues, setTempSelectedValues] = useState(Array(data?.dropdown_options?.length).fill(""));

    useEffect(() => {
        const ans = data?.dropdown_options.filter((dt, idx) => dt?.correct == tempSelectedValues[idx]);
        let ppq = data?.points / data?.dropdown_options?.length;
        let points = Math.round(ans?.length * ppq);

        onComponentAnswer(sequence, tempSelectedValues, points);
    }, [tempSelectedValues]);

    const handleTempChange = (index, event) => {
        const newTempValues = [...tempSelectedValues];
        newTempValues[index] = event.target.value;
        setTempSelectedValues(newTempValues);
    };

    const renderTemplate = () => {
        const parts = data?.question?.split(/{(\d+)}/g);
        return parts?.map((part, index) => {
            if (index % 2 === 1) {
                const dropdownIndex = parseInt(part, 10);
                const dt = data?.dropdown_options[dropdownIndex];
                const randomString = Math.random().toString(36).substring(2, 15);
                const isSelected = tempSelectedValues[dropdownIndex] == "" ? "default" : "selected";
                return (
                    <motion.label
                        key={index}
                        variants={variants}
                        initial="initial"
                        whileInView={inView ? "inView" : "initial"}
                        transition={{
                            duration: 0.5,
                        }}
                        htmlFor={`option-${randomString}`}
                        className="p-1 tw-inline-flex tw-h-12 tw-rounded-md tw-border tw-border-gray-500 tw-bg-white"
                        style={{
                            color: data?.styles?.answer?.color,
                            fontFamily: data?.styles?.answer?.fontFamily,
                            fontSize: data?.styles?.answer?.fontSize,
                            lineHeight: 1.5,
                            backgroundColor: data?.styles?.[isSelected]?.backgroundColor,
                            borderWidth: data?.styles?.[isSelected]?.borderWidth,
                            borderColor: data?.styles?.[isSelected]?.borderColor,
                            borderStyle: data?.styles?.[isSelected]?.borderStyle,
                        }}
                    >
                        <select
                            key={index}
                            value={tempSelectedValues[dropdownIndex]}
                            onChange={(event) => handleTempChange(dropdownIndex, event)}
                            id={`option-${randomString}`}
                            className="p-1 tw-w-full tw-outline-none"
                        >
                            <option value="">- Select -</option>
                            {dt?.options?.map((option, idx) => (
                                <option key={idx} value={option}>
                                    {option}
                                </option>
                            ))}
                        </select>
                    </motion.label>
                );
            } else {
                return part;
            }
        });
    };

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: 1,
                data: data?.name ?? "Drag the image on correct placeholder make sure the label is correct for image.",
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div className="tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden">
                <div
                    className={cn(
                        "tw-grid tw-h-1/2 tw-w-full tw-flex-wrap tw-gap-[2rem]",
                        isUrl(data?.question_thumbnail) ? "tw-grid-cols-2" : "tw-grid-cols-1",
                    )}
                    ref={ref}
                >
                    {isUrl(data?.question_thumbnail) && (
                        <div className="tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
                            <div className="tw-relative tw-mt-7 tw-h-[300px] tw-w-[420px]">
                                <img
                                    src={data?.question_thumbnail}
                                    alt=""
                                    className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw--rotate-3 tw-rounded-xl tw-object-cover"
                                />
                                <motion.img
                                    initial={{ scale: 1, rotate: 1 }}
                                    animate={{
                                        scale: 1.1,
                                    }}
                                    transition={{
                                        duration: 1,
                                        repeat: Infinity,
                                        repeatType: "reverse",
                                    }}
                                    src={"/quiz/spark, sparkle, 26.png"}
                                    alt=""
                                    className="tw-absolute tw-right-[-50px] tw-top-[-110px] tw-z-0 tw-aspect-[1/1.25] tw-w-[150px]"
                                />
                                <motion.img
                                    initial={{ rotate: 10 }}
                                    animate={{
                                        rotate: -10,
                                        transition: {
                                            duration: 2,
                                            repeat: Infinity,
                                            repeatType: "reverse",
                                        },
                                    }}
                                    src="/quiz/Question Mark.png"
                                    alt=""
                                    className="tw-absolute tw-bottom-[-3%] tw-right-[-5%] tw-z-0 tw-aspect-[1/1.25] tw-w-[80px]"
                                />
                            </div>
                        </div>
                    )}
                    <motion.div className="tw-inline-flex tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[40px] tw-text-black">
                        <p
                            style={{
                                backgroundColor: data?.styles?.question?.backgroundColor,
                                color: data?.styles?.question?.color,
                                fontFamily: data?.styles?.question?.fontFamily,
                                fontSize: data?.styles?.question?.fontSize,
                                lineHeight: 1.75,
                            }}
                        >
                            {data !== null && renderTemplate()}
                        </p>
                    </motion.div>
                </div>
            </div>
        </ContentSlideLayout>
    );
};

export default StringDropdown;
