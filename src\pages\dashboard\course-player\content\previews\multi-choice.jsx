import { useEffect, useState } from "react";

const MultiChoice = ({ data, index, onComponentAnswer, Disable, correctAns }) => {
    const [compValue, setCompValue] = useState(data?.answerKey || []);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        let ans = data?.options.filter((option) => compValue.includes(option.name));
        let ppq = data?.points / data?.options?.filter((dt) => dt?.isCorrect == true)?.length;
        let points = ans?.filter((dt) => dt?.isCorrect == true)?.length * ppq;
        onComponentAnswer(index, compValue, points); // TODO - value needed to be round
    }, [compValue]);

    const onOptionChange = (item) => {
        if (compValue.includes(item)) {
            setCompValue(compValue.filter((dt) => dt !== item));
        } else {
            setCompValue([...compValue, item]);
        }
    };

    useEffect(() => {
        setIsMounted(true);
        return () => {
            setIsMounted(false);
        };
    }, []);

    return (
        <div className="comp_control">
            <div className="multi_choice_input">
                <label htmlFor="">
                    {data?.name || "Component title here"} <b>{data?.preference == "required" && "*"}</b>
                </label>
                <div className="option_box" style={{ gridTemplateColumns: data?.optionColumns }}>
                    {data?.options?.map((option, idx) => (
                        <div key={idx} className={compValue.includes(option?.name) && "isSelected"}>
                            <input
                                type="checkbox"
                                id={option?.name}
                                onChange={() => onOptionChange(option?.name)}
                                checked={compValue.includes(option?.name)}
                                disabled={Disable}
                            />
                            <label
                                htmlFor={option?.name}
                                className={option?.isCorrect && correctAns ? "isCorrect" : ""}
                            >
                                {option?.name}
                            </label>
                        </div>
                    ))}
                </div>
                {data?.specialInstruction && (
                    <p className="special_instruction">
                        <i className="fa-solid fa-circle-info"></i>
                        <small>{data?.specialInstruction}</small>
                    </p>
                )}
            </div>
        </div>
    );
};

export default MultiChoice;
