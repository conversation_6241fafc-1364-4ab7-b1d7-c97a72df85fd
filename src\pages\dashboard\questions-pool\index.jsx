import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { Clock, UserRoundPen } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import CreatePool from "./CreatePool";

const QuestionPool = () => {
    const navigate = useNavigate();
    const [dataList, setDataList] = useState([]);
    const [open, setOpen] = useState(false);
    const [editData, setEditData] = useState(null);
    const [tagsList, setTagsList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);

    const [filterState, setFilterState] = useState({
        search: "",
        tag: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.name?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const matchesTag = filterState?.tag ? item?.tags?.includes(filterState?.tag) : true; // Allow all items if no subCategory filter

            return matchesSearch && matchesTag; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            tag: "",
        });
    };

    useEffect(() => {
        getPoolData();
        getTagsList();
    }, []);

    useEffect(() => {
        setFilteredData(dataList);
    }, [dataList]);

    const getTagsList = async () => {
        await tanstackApi
            .get("tag-master/list-tags")
            .then((res) => {
                setTagsList(res?.data?.data);
            })
            .catch((err) => {
                setTagsList([]);
            });
    };

    const getPoolData = async () => {
        await tanstackApi
            .get("question-pool/list", { params: { is_public: localStorage.getItem("level") == "levelOne" } })
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const AddNewPool = () => {
        setEditData(null);
        setOpen(true);
    };

    const EditPool = (data) => {
        setEditData(data);
        setOpen(true);
    };

    return (
        <div>
            <CreatePool open={open} setOpen={setOpen} editData={editData} getPoolData={getPoolData} />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Question Pool</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <div>
                    <Button onClick={AddNewPool}>
                        <i className="fa-solid fa-plus"></i> New Pool
                    </Button>
                </div>
            </div>
            <div className="page_filters tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Pool title
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState?.search}
                        name="search"
                        className="tw-text-sm"
                        style={{ minWidth: "250px" }}
                        type="text"
                        placeholder="Search by title ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Tags
                    </label>
                    <select onChange={onFilterChange} value={filterState?.tag} className="tw-text-sm" name="tag" id="">
                        <option value=""> - Select tag - </option>
                        {tagsList?.map((type, idx) => (
                            <option value={type?.display_name} key={idx}>
                                {type?.display_name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="tw-mt-5 tw-flex tw-flex-wrap tw-gap-5 tw-p-0">
                {filteredData?.map((pool, index) => (
                    <div
                        key={index}
                        className="tw-flex tw-w-[350px] tw-flex-col tw-justify-between tw-rounded-xl tw-border-[1px] tw-p-1"
                    >
                        <div className="tw-p-2">
                            <div className="tw-flex tw-items-center tw-gap-2">
                                <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                    {pool?.name}
                                </h2>
                            </div>
                            <div className="tw-my-3 tw-flex tw-flex-wrap tw-gap-1">
                                {pool?.tags?.map((tag, idx) => (
                                    <Badge variant={"outline"} key={idx}>
                                        {tag}
                                    </Badge>
                                ))}
                            </div>
                        </div>
                        <div className="tw-p-2">
                            <div className="tw-mt-1 tw-space-y-2">
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <UserRoundPen size={18} />{" "}
                                    {pool?.created_by_user?.first_name + " " + pool?.created_by_user?.last_name}
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Clock size={18} /> Created on {moment(pool?.createdAt).format("LL")}
                                </span>{" "}
                            </div>
                            <div className="tw-mt-3 tw-grid tw-grid-cols-2 tw-gap-2">
                                {/* <div
                                    // onClick={() => navigate(`/dashboard/user-teams/${pool?.id}/${pool?.name}`)}
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <i className="fa-solid fa-trash"></i>
                                    Delete
                                </div> */}
                                <div
                                    onClick={() =>
                                        navigate(`/dashboard/question-import-pool/${pool?.id}/${pool?.name}`)
                                    }
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <i className="fa-solid fa-cube"></i>
                                    Questions
                                </div>
                                <div
                                    onClick={() => EditPool(pool)}
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <i className="fa-solid fa-pen-to-square"></i>
                                    Edit
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default QuestionPool;
