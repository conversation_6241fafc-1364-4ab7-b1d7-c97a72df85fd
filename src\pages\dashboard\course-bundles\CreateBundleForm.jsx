import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import Details from "./Details";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import AddCoursesToBundle from "./AddCoursesToBundle";

const CreateBundleForm = () => {
    const params = useParams();
    const router = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const [bundleData, setBundleData] = useState(null);

    useEffect(() => {
        if (params?.bundle_id !== undefined) {
            getBundle(params?.bundle_id);
        }
    }, [params]);

    const getBundle = async (payload) => {
        await tanstackApi
            .get(`course-bundle/get-one-course-bundle/${payload}`)
            .then((res) => {
                setBundleData(res?.data?.data);
            })
            .catch((err) => {
                setBundleData(null);
            });
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/course-bundle">Course Bundles</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Create new bundle</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <Button className="tw-px-2 tw-py-1" onClick={() => router(`/dashboard/course-bundle`)}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
                {/* </Link> */}
            </div>
            <Tabs
                defaultValue={searchParams.get("tab") ?? "basic_details"}
                onValueChange={(e) => setSearchParams({ tab: e })}
            >
                {params?.bundle_id !== undefined && (
                    <TabsList className="tw-grid tw-w-[320px] tw-grid-cols-2">
                        <TabsTrigger value="basic_details" className="tw-gap-2">
                            <i className="fa-solid fa-circle-info"></i> Basic Details
                        </TabsTrigger>
                        <TabsTrigger value="add_courses" className="tw-gap-2">
                            <i className="fa-solid fa-gear"></i> Add Courses
                        </TabsTrigger>
                    </TabsList>
                )}
                <TabsContent value="basic_details">
                    <Details bundleData={bundleData} getBundle={getBundle} />
                </TabsContent>
                <TabsContent value="add_courses">
                    <AddCoursesToBundle bundleData={bundleData} getBundle={getBundle} />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default CreateBundleForm;
