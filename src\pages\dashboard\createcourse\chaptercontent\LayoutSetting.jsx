import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { animationVariants } from "@/pages/dashboard/course-player/content/type/animation";
import { AudioLines, CodeXml, Image, SquarePlay } from "lucide-react";
import { useEffect, useState } from "react";

const LayoutsData = [
    {
        name: "1 Column Layout",
        layout: {
            display: "grid",
            gridTemplateColumns: "repeat(1, 1fr)",
        },
        layout_box: [
            {
                content_type: "TEXT",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
        ],
    },
    {
        name: "2 Column Layout",
        layout: {
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
        },
        layout_box: [
            {
                content_type: "TEXT",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
            {
                content_type: "IMAGE",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
        ],
    },
    {
        name: "3 Column Layout",
        layout: {
            display: "grid",
            gridTemplateColumns: "repeat(3, 1fr)",
        },
        layout_box: [
            {
                content_type: "IMAGE",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
            {
                content_type: "AUDIO",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
            {
                content_type: "VIDEO",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
        ],
    },
    {
        name: "1/1 Rows Layout",
        layout: {
            display: "grid",
            gridTemplateRows: "repeat(2, 1fr)",
        },
        layout_box: [
            {
                content_type: "TEXT",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
            {
                content_type: "TEXT",
                content: "",
                x: "",
                y: "",
                scale: "",
                duration: "",
                delay: "",
                type: "",
            },
        ],
    },
];

const LayoutSetting = ({ setContentData, contentData }) => {
    const [currentLayout, setCurrentLayout] = useState(contentData?.lecture_data || LayoutsData[1]);
    const [layoutBoxes, setLayoutBoxes] = useState(contentData?.lecture_data?.layout_box || currentLayout?.layout_box);

    const onLayoutSelect = (layout) => {
        setCurrentLayout(layout);
        setLayoutBoxes(layout?.layout_box);
    };

    const onLayoutChange = (index, name, value) => {
        let options = [...layoutBoxes];

        if (name == "x" || name == "y" || name == "scale") {
            options[index][name] = value;
            setLayoutBoxes(options);
        } else if (name == "duration" || name == "delay") {
            options[index][name] = Number(value);
            setLayoutBoxes(options);
        } else {
            options[index][name] = value;
            setLayoutBoxes(options);
        }
    };

    useEffect(() => {
        setContentData({
            ...contentData,
            lecture_data: {
                ...currentLayout,
                layout_box: layoutBoxes,
            },
        });
    }, [layoutBoxes]);

    return (
        <div className="tw-mt-3">
            <Label>Layouts of HTML content :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">
                Choose any one layout for slide and. Click on card & set configuration of each block.
            </p>
            <div className="tw-mt-2 tw-flex tw-gap-2">
                {LayoutsData?.map((layout, index) => (
                    <div
                        key={index}
                        onClick={() => onLayoutSelect(layout)}
                        style={layout?.layout}
                        className={`${currentLayout?.name == layout?.name && "layout_selected"} tw-h-[100px] tw-w-[130px] tw-cursor-pointer tw-gap-2 tw-rounded-md tw-border-[1px] tw-border-slate-500 tw-p-3 tw-shadow-sm hover:tw-bg-slate-100`}
                    >
                        {layout?.layout_box?.map((box, idx) => (
                            <div
                                key={idx}
                                className="tw-h-full tw-rounded-md tw-border-[1px] tw-border-dashed tw-border-slate-500 tw-p-3 tw-shadow-sm"
                            ></div>
                        ))}
                    </div>
                ))}
            </div>
            <div className="custom_scrollbar tw-mt-5 tw-grid tw-h-[27vw] tw-gap-3 tw-overflow-y-auto tw-pr-2">
                {layoutBoxes?.map((box, index) => (
                    <Card className="" key={index}>
                        <CardHeader>
                            <div>
                                <Label>Layout Box {index + 1}</Label>
                            </div>
                            <div className="tw-items-top tw-flex tw-justify-between">
                                <div className="tw-inline-flex -tw-space-x-px tw-rounded-lg rtl:tw-space-x-reverse">
                                    <Button
                                        className={`tw-rounded-none tw-shadow-none first:tw-rounded-s-lg last:tw-rounded-e-lg focus-visible:tw-z-10 ${box?.content_type == "TEXT" && "layout_content_active"}`}
                                        variant="outline"
                                        onClick={() => onLayoutChange(index, "content_type", "TEXT")}
                                    >
                                        <CodeXml
                                            className="-tw-ms-1 tw-me-2 tw-opacity-60"
                                            size={18}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        Text
                                    </Button>
                                    <Button
                                        className={`tw-rounded-none tw-shadow-none first:tw-rounded-s-lg last:tw-rounded-e-lg focus-visible:tw-z-10 ${box?.content_type == "IMAGE" && "layout_content_active"}`}
                                        variant="outline"
                                        onClick={() => onLayoutChange(index, "content_type", "IMAGE")}
                                    >
                                        <Image
                                            className="-tw-ms-1 tw-me-2 tw-opacity-60"
                                            size={18}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        Image
                                    </Button>

                                    <Button
                                        className={`tw-rounded-none tw-shadow-none first:tw-rounded-s-lg last:tw-rounded-e-lg focus-visible:tw-z-10 ${box?.content_type == "AUDIO" && "layout_content_active"}`}
                                        variant="outline"
                                        onClick={() => onLayoutChange(index, "content_type", "AUDIO")}
                                    >
                                        <AudioLines
                                            className="-tw-ms-1 tw-me-2 tw-opacity-60"
                                            size={18}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        Audio
                                    </Button>
                                    <Button
                                        className={`tw-rounded-none tw-shadow-none first:tw-rounded-s-lg last:tw-rounded-e-lg focus-visible:tw-z-10 ${box?.content_type == "VIDEO" && "layout_content_active"}`}
                                        variant="outline"
                                        onClick={() => onLayoutChange(index, "content_type", "VIDEO")}
                                    >
                                        <SquarePlay
                                            className="-tw-ms-1 tw-me-2 tw-opacity-60"
                                            size={18}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        Video
                                    </Button>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <h1 className="tw-font-mono tw-text-sm tw-font-bold">
                                Animation Settings for{" "}
                                <Badge className="tw-capitalize" variant="outline">
                                    {box?.content_type?.toLowerCase()}
                                </Badge>
                            </h1>
                            <hr className="tw-my-2" />
                            <div className="tw-grid tw-w-full tw-items-center tw-gap-2">
                                <div className="tw-grid tw-grid-cols-3 tw-gap-2">
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="duration">
                                            Duration <span className="tw-text-xs tw-font-normal">(sec)</span>
                                        </Label>
                                        <Input
                                            id="duration"
                                            placeholder="eg: 5.5"
                                            type="number"
                                            step="0.1"
                                            onChange={(e) => onLayoutChange(index, "duration", e.target.value)}
                                            value={box?.duration}
                                        />
                                    </div>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="delay">
                                            Delay <span className="tw-text-xs tw-font-normal">(sec)</span>
                                        </Label>
                                        <Input
                                            id="delay"
                                            placeholder="eg: 5s"
                                            type="number"
                                            step="0.1"
                                            onChange={(e) => onLayoutChange(index, "delay", e.target.value)}
                                            value={box?.delay}
                                        />
                                    </div>
                                    <div className="tw-space-y-1">
                                        <Label htmlFor="type">Animation Type</Label>
                                        <SelectNative
                                            id="type"
                                            onChange={(e) => onLayoutChange(index, "type", e.target.value)}
                                            value={box?.type}
                                        >
                                            {Object.keys(animationVariants)?.map((data, idx) => (
                                                <option value={data} key={idx}>
                                                    {data}
                                                </option>
                                            ))}
                                        </SelectNative>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
};

export default LayoutSetting;
