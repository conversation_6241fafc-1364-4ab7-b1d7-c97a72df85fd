import { tanstackApiFormdata } from "@/react-query/api";
import { AddUserAbout } from "@/redux/define-role/action";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";

let sectors = [
    { value: "technology", name: "Technology" },
    { value: "finance", name: "Finance" },
    { value: "healthcare", name: "Healthcare" },
    { value: "retail", name: "Retail" },
    { value: "education", name: "Education" },
    { value: "manufacturing", name: "Manufacturing" },
    { value: "energy", name: "Energy" },
    { value: "real_estate", name: "Real Estate" },
    { value: "transportation", name: "Transportation" },
    { value: "telecommunications", name: "Telecommunications" },
    { value: "hospitality", name: "Hospitality" },
    { value: "food_beverage", name: "Food & Beverage" },
    { value: "media_entertainment", name: "Media & Entertainment" },
    { value: "agriculture", name: "Agriculture" },
    { value: "non_profit", name: "Non-Profit" },
    { value: "government", name: "Government" },
    { value: "consulting", name: "Consulting" },
    { value: "construction", name: "Construction" },
    { value: "legal", name: "Legal" },
];

const MyProfileCompanyAbout = () => {
    const dispatch = useDispatch();
    const [orgDetails, setOrgDetails] = useState(JSON.parse(localStorage.getItem("org_data"))?.about);

    const onAboutChange = (e) => {
        const { name, value } = e.target;
        setOrgDetails({ ...orgDetails, [name]: value });
    };

    const onMediaChange = (e) => {
        if (e.target.files[0]) {
            const formData = new FormData();
            var file = e.target.files[0];

            formData.append("file", file);
            formData.append("category", "ORGANIZATION-PROFILE");

            ImageUpload(formData);
        }
    };

    const ImageUpload = (data) => {
        tanstackApiFormdata
            .post("common/upload-file", data)
            .then((res) => {
                if (res.data.success) {
                    setOrgDetails({ ...orgDetails, company_logo: res?.data?.fileUrl });
                }
            })
            .catch((err) => {});
    };

    const updateAboutSection = () => {
        let payload = {
            step: "about",
            data: {
                company_logo: orgDetails?.company_logo,
                company_name: orgDetails?.company_name,
                employe_size: orgDetails?.employe_size,
                company_sector: orgDetails?.company_sector,
            },
        };
        dispatch(AddUserAbout(payload));
        
        toast.success("Details Updated");
    };

    return (
        <>
            <div className="section_details">
                <div className="section_heading">
                    <h6>
                        <i className="fa-solid fa-building-circle-exclamation"></i> About Company
                    </h6>
                    <button onClick={updateAboutSection}>
                        <i className="fa-solid fa-square-pen"></i> Update Info
                    </button>
                </div>
                <div className="section_wrapper">
                    <div className="left">
                        <div className="input_group !tw-mx-0">
                            <div className="input_text" style={{ flex: 1 }}>
                                <label htmlFor="">Company Name</label>
                                <input
                                    type="text"
                                    placeholder="Enter company name here"
                                    value={orgDetails?.company_name}
                                    onChange={onAboutChange}
                                    name="company_name"
                                />
                            </div>
                        </div>
                        <div className="input_group !tw-mx-0">
                            <div className="input_select" style={{ flex: 1 }}>
                                <label htmlFor="">Team Employee Size</label>
                                <select value={orgDetails?.employe_size} onChange={onAboutChange} name="employe_size">
                                    <option value=""> - Choose employee size - </option>
                                    <option value="0-10">0 - 10</option>
                                    <option value="10-50">10 - 50</option>
                                    <option value="50-100">50 - 100</option>
                                    <option value="100-500">100 - 500</option>
                                    <option value="500-1000">500 - 1000</option>
                                </select>
                            </div>
                        </div>
                        <div className="input_group !tw-mx-0">
                            <div className="input_select" style={{ flex: 1 }}>
                                <label htmlFor="">Your Company Sector</label>
                                <select
                                    value={orgDetails?.company_sector}
                                    onChange={onAboutChange}
                                    name="company_sector"
                                >
                                    <option value=""> - Choose company sector - </option>
                                    {sectors?.map((sectr, idx) => (
                                        <option key={idx} value={sectr?.value}>
                                            {sectr?.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div className="right">
                        <div className="org_logo">
                            <img src={orgDetails?.company_logo} alt="" id="" />
                        </div>
                        <input
                            onChange={onMediaChange}
                            type="file"
                            accept="image/*"
                            name="company_logo"
                            id="org_logo"
                        />
                        <label htmlFor="org_logo">
                            <i className="fa-solid fa-image"></i> Choose Logo
                        </label>
                    </div>
                </div>
            </div>
        </>
    );
};

export default MyProfileCompanyAbout;
