import { Badge } from "@/components/ui/badge";
import {
    Pagin<PERSON>,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const SumbittedHomeworks = ({ allSubmissions, activeTab, assginedData }) => {
    const [dataList, setDatalist] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(10);

    const [filterState, setFilterState] = useState({
        search: "",
        status: "",
        created_at: "",
        homework_id: "",
    });

    useEffect(() => {
        if (allSubmissions?.length > 0) {
            setDatalist(allSubmissions?.filter((dt) => dt?.lms_course_homework?.is_assignment == false));
        }
    }, [allSubmissions, activeTab]);

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? `${item?.lms_user?.first_name} ${item?.lms_user?.last_name}`
                      ?.toLowerCase()
                      ?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const status = filterState?.status ? item?.evaluation_status == filterState?.status : true; // Allow all items if no subCategory filter
            const homework = filterState?.homework_id ? item?.homework_id == filterState?.homework_id : true; // Allow all items if no subCategory filter

            const submission = filterState?.created_at
                ? moment(item.created_at).format("DD/MMM/YYYY") ===
                  moment(filterState?.created_at).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            return matchesname && status && submission && homework; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            status: "",
            created_at: "",
            homework_id: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const badgeMapping = {
        pass: "success-outline",
        fail: "danger-outline",
        inprogress: "warning-outline",
    };

    return (
        <>
            <div className="page_filters">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Name / Email
                    </label>
                    <input
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState?.search}
                        type="text"
                        name="search"
                        placeholder="Search by user name ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Homeworks
                    </label>
                    <select
                        className="tw-text-sm"
                        name="homework_id"
                        id=""
                        onChange={onFilterChange}
                        value={filterState?.homework_id}
                    >
                        <option value=""> - Choose Homework - </option>
                        {assginedData
                            ?.filter((dt) => dt?.is_assignment == false)
                            ?.map((work, idx) => (
                                <option key={idx} value={work?.id}>
                                    {work?.homework_title}
                                </option>
                            ))}
                    </select>
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Submission Date
                    </label>
                    <input
                        className="tw-text-sm"
                        type="date"
                        onChange={onFilterChange}
                        value={filterState?.created_at}
                        name="created_at"
                    />
                </div>{" "}
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Status
                    </label>
                    <select className="tw-text-sm" onChange={onFilterChange} value={filterState?.status} name="status">
                        <option value="">- All -</option>
                        <option value="pass">Passed</option>
                        <option value="fail">Failed</option>
                        <option value="inprogress">Inprogress</option>
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>User Name</th>
                            <th>Homework Title</th>
                            <th>Submimitted On</th>
                            <th>Submission Date</th>
                            <th>Marks</th>
                            <th>Review Status</th>
                            <th>Submission & Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>
                                    {row?.lms_user?.first_name} {row?.lms_user?.last_name}
                                </td>
                                <td>{row?.lms_course_homework?.homework_title}</td>
                                <td>{moment(row?.created_at).format("LLL")}</td>
                                <td>{moment(row?.lms_course_homework?.submission_date).format("LLL")}</td>
                                <td>
                                    {row?.evaluation_status !== "inprogress" &&
                                        `${row?.marks} / ${row?.lms_course_homework?.homework_points}`}
                                </td>
                                <td>
                                    <Badge variant={badgeMapping[row?.evaluation_status]} className="tw-capitalize">
                                        {row?.evaluation_status}
                                    </Badge>
                                </td>
                                <td>
                                    <Link to={`/dashboard/submissions/Homework/${row?.id}`}>
                                        <button className="selected_btn">
                                            <i className="fa-solid fa-check-to-slot"></i> Review & Check
                                        </button>
                                    </Link>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination className="">
                        <PaginationContent>
                            {/* Previous Button */}
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page Numbers with Ellipses */}
                            {currentPage > 3 && (
                                <>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                            1
                                        </PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                </>
                            )}

                            {getVisiblePages().map((page) => (
                                <PaginationItem key={page}>
                                    <PaginationLink
                                        href="#"
                                        isActive={page === currentPage}
                                        onClick={() => handlePageChange(page)}
                                    >
                                        {page}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {currentPage < totalPages - 2 && (
                                <>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                            {totalPages}
                                        </PaginationLink>
                                    </PaginationItem>
                                </>
                            )}

                            {/* Next Button */}
                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </>
    );
};

export default SumbittedHomeworks;
