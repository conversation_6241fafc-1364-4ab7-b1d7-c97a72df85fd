import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { colorsList } from "@/data/colors";
import { cn, isUrl } from "@/lib/utils";
import { useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import { useFileUpload } from "@/react-query/common";
import { motion } from "framer-motion";
import { Upload, X } from "lucide-react";
import { useCallback, useRef, useState } from "react";

export function Process({ template }) {
    const { tabList } = useEditInteraction();
    const [index, setIndex] = useState(0);

    const handlePrevSlide = () => {
        if (index === 0) return;
        setIndex((prev) => prev - 1);
    };

    const handleNextSlide = () => {
        if (tabList.length - 1 === index) return;
        setIndex((prev) => prev + 1);
    };

    return (
        <>
            <div className="tw-relative">
                <motion.div className="tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[40px] tw-text-black">
                    <Tabs
                        value={tabList[index]?.label}
                        onValueChange={(val) => setIndex(tabList.findIndex((dt) => dt?.label == val))}
                        className="w-[400px] tw-grid tw-h-full tw-grid-rows-[50px_1fr]"
                    >
                        <div className="tw-flex tw-items-center tw-justify-center">
                            <TabsList className="tw-h-full tw-w-fit tw-bg-transparent tw-px-5 tw-py-0">
                                {tabList.map((step, idx) => {
                                    const color = colorsList[idx];
                                    return (
                                        <TabsTrigger
                                            key={idx}
                                            value={step.label}
                                            style={{
                                                background: `linear-gradient(-135deg,transparent 22px, ${color} 22px, ${color} 100% ) top right, linear-gradient(-45deg, transparent 22px,${color} 22px, ${color} 100% ) bottom right`,
                                                backgroundSize: "100% 50%",
                                                backgroundRepeat: "no-repeat",
                                                transform: `${idx !== 0 ? `translateX(-${2 * idx}rem)` : ""} ${idx === index ? "scale(1)" : "scale(1)"}`,
                                                paddingLeft: idx !== 0 ? `2rem` : "1rem",
                                                zIndex: tabList.length - idx,
                                            }}
                                            className={cn(
                                                "tw-relative tw-flex tw-items-center tw-justify-center tw-bg-transparent tw-pr-12 tw-font-lazyDog !tw-text-[22px] tw-leading-none tw-text-[#3B3A3E] tw-shadow-none data-[state=active]:tw-bg-transparent data-[state=active]:tw-text-[#3B3A3E] data-[state=active]:tw-shadow-none 2xl:!tw-text-[40px]",
                                            )}
                                        >
                                            <span className="tw-relative tw-z-10">
                                                {idx + 1}. {step.label}
                                            </span>
                                        </TabsTrigger>
                                    );
                                })}
                            </TabsList>
                        </div>
                        {tabList.map((step, index) => (
                            <TabItem key={index} index={index} item={step} />
                        ))}
                    </Tabs>
                </motion.div>
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    onClick={handlePrevSlide}
                    disabled={0 === index}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    disabled={tabList.length - 1 === index}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px] disabled:tw-opacity-70",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}

function TabItem({ item, index }) {
    const { handleTabChange } = useEditInteraction();
    const [titleEditable, setTitleEditable] = useState(false);
    const [descriptionEditable, setDescriptionEditable] = useState(false);
    const titleRef = useRef(null);
    const descriptionRef = useRef(null);
    const upload = useFileUpload();

    const handleImageChange = useCallback(async (e) => {
        let imageUrl = "";
        try {
            if (e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                imageUrl = response?.fileUrl;
            }
        } catch (error) {
            imageUrl = "";
        } finally {
            handleTabChange("image_src", imageUrl, index);
        }
    }, []);

    const removeImage = useCallback(() => {
        handleTabChange("image_src", "", index);
    }, []);

    const handleTitleEditable = useCallback(() => {
        setTitleEditable((prev) => !prev);
    }, []);

    const handleDescriptionEditable = useCallback(() => {
        setDescriptionEditable((prev) => !prev);
    }, []);

    const handleTitleBlur = useCallback(() => {
        handleTabChange("label", titleRef?.current?.innerText, index);
    }, []);

    const handleDescriptionBlur = useCallback(() => {
        handleTabChange("description", descriptionRef?.current?.innerText, index);
    }, []);

    return (
        <TabsContent key={index} value={item.label}>
            <div className="tw-relative 2xl:tw-mt-5">
                <div className="tw-absolute tw-right-0 tw-top-0 tw-z-20 tw-flex">
                    {isUrl(item.image_src) ? (
                        <Button variant="outline" className="aspect-square max-sm:p-0" onClick={removeImage}>
                            <X className="opacity-60" size={16} strokeWidth={2} aria-hidden="true" />
                            <Label className="max-sm:sr-only">Remove</Label>
                        </Button>
                    ) : (
                        <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10">
                            <Upload className="opacity-60" size={16} strokeWidth={2} aria-hidden="true" />
                            <input
                                onChange={handleImageChange}
                                type="file"
                                style={{ display: "none" }}
                                id="bg_image"
                                accept="image/*"
                            />
                            <Label htmlFor="bg_image" className="max-sm:sr-only">
                                {upload.isPending ? "Uploading..." : "Upload Image"}
                            </Label>
                        </Button>
                    )}
                </div>
                <div className="tw-relative tw-h-40 tw-w-full 2xl:tw-h-60">
                    {item.image_src && (
                        <img
                            className="tw-absolute tw-inset-0 tw-size-full tw-rounded-lg tw-object-cover tw-object-center"
                            src={item.image_src}
                            alt={item.label}
                        />
                    )}
                    <div className="tw-absolute tw-inset-0 tw-size-full tw-rounded-lg tw-bg-black/40"></div>
                </div>
                <div className="tw-mt-2 tw-font-lazyDog">
                    <h2 className="tw-text-[20px] 2xl:tw-text-[30px]">
                        {index + 1}.{" "}
                        <span
                            ref={titleRef}
                            contentEditable={titleEditable}
                            onDoubleClick={handleTitleEditable}
                            onBlur={handleTitleBlur}
                        >
                            {item.label}
                        </span>
                    </h2>
                    <div
                        ref={descriptionRef}
                        contentEditable={descriptionEditable}
                        onDoubleClick={handleDescriptionEditable}
                        onBlur={handleDescriptionBlur}
                        className="tw-text-base"
                        dangerouslySetInnerHTML={{ __html: item.description }}
                    />
                </div>
            </div>
        </TabsContent>
    );
}

export function ProcessPreview() {
    const list = [0, 1, 2, 3];

    return (
        <div className="tw-grid tw-size-full tw-grid-cols-1 tw-grid-rows-7 tw-items-center tw-justify-center tw-gap-1">
            <div className="tw-row-span-1 tw-flex tw-w-full tw-items-center tw-justify-center tw-gap-0">
                {list.map((item) => (
                    <div
                        key={item}
                        style={{
                            backgroundColor: colorsList[item],
                        }}
                        className={cn(
                            "tw-relative tw-flex tw-aspect-video tw-w-[20px] tw-items-center tw-justify-center tw-overflow-hidden tw-rounded tw-border tw-bg-slate-300 tw-transition-all",
                            1 === item && "tw-scale-125",
                        )}
                    />
                ))}
            </div>
            <div className="tw-row-span-6 tw-h-full tw-w-full tw-rounded-lg tw-bg-slate-300"></div>
        </div>
    );
}
