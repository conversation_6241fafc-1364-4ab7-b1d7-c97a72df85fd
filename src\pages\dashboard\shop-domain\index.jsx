import Pagination from "@/components/table/pagination";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const ITEMS_PER_PAGE = 7;

const statuses = [
    { value: "PENDING", label: "Pending" },
    { value: "PAID", label: "Paid" },
    { value: "CANCELLED", label: "Cancelled" },
    { value: "FAILED", label: "Failed" },
    { value: "PROCESSING", label: "Processing" },
];

const ShopDomain = () => {
    const navigate = useNavigate();
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [filterState, setFilterState] = useState({
        search: "",
        status: "",
        invoice_date: "",
    });
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

    useEffect(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? item?.invoice_number?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const status = filterState?.status ? item?.status == filterState?.status : true; // Allow all items if no subCategory filter

            const invoiceDate = filterState?.invoice_date
                ? moment(item.invoice_date).format("DD/MMM/YYYY") ===
                  moment(filterState?.invoice_date).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            return matchesname && status && invoiceDate; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            status: "",
            invoice_date: "",
        });
    };

    useEffect(() => {
        getInvoiceList();
    }, []);

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    const getInvoiceList = async () => {
        await tanstackApi
            .get("invoice/list")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Shop</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button className="tw-px-2 tw-py-1" onClick={() => navigate(`/dashboard/create-order-domain`)}>
                    <i className="fa-solid fa-cart-shopping"></i> New Order
                </Button>
            </div>
            <div className="page_filters tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Search
                    </label>
                    <input
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState?.search}
                        name="search"
                        type="text"
                        placeholder="Search by invoice Id"
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Invoice Date
                    </label>
                    <input
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState?.invoice_date}
                        type="date"
                        name="invoice_date"
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Status
                    </label>
                    <select className="tw-text-sm" onChange={onFilterChange} value={filterState?.status} name="status">
                        <option value=""> - All - </option>
                        {statuses?.map((opn, idx) => (
                            <option value={opn?.value} key={idx}>
                                {opn?.label}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>Invoice ID</th>
                            <th>Transaction ID</th>
                            <th>Particulars</th>
                            <th>Discount</th>
                            <th>Total Amount</th>
                            <th>Invoice Date & Time</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody className="tw-font-lexend">
                        {tableData.length == 0 ? (
                            <tr>
                                <td colSpan={8}>
                                    <div className="tw-flex tw-h-20 tw-items-center tw-justify-center">
                                        <p>No invoice found.</p>
                                    </div>
                                </td>
                            </tr>
                        ) : (
                            tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>
                                        <b>#.{row?.invoice_number}</b>
                                    </td>
                                    <td>{row?.stripe_payment_id || "-"}</td>
                                    <td>{row?.invoice_items?.length} Item</td>
                                    <td>${row?.discount}</td>
                                    <td>
                                        <b>${row?.total_amount}</b>
                                    </td>
                                    <td>{moment(row?.invoice_date).format("LLL")}</td>
                                    <td>{row?.status}</td>
                                    <td>
                                        <Link to={`/dashboard/view-order/${row?.id}`}>
                                            <Button>
                                                <i className="fa-solid fa-eye"></i> View Order
                                            </Button>
                                        </Link>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">7</p>
                </div>
                <div>
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        handlePageChange={handlePageChange}
                        itemsPerPage={ITEMS_PER_PAGE}
                    />
                </div>
            </div>
        </div>
    );
};

export default ShopDomain;
