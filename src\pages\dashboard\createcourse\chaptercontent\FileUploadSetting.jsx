import { Button, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { tanstackApiFormdata } from "@/react-query/api";
import { Check, Copy, Upload, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

const useFileUpload = (initialUrl = "", initialFileName = "") => {
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [fileURL, setFileURL] = useState(initialUrl);
    const [fileName, setFileName] = useState(initialFileName);

    const onFileChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileUpload(data, imageData);
        }
    };

    const onFileUpload = async (formData, imageData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setFileURL(res.data.fileUrl);
                    setFileName(imageData?.name);
                } else {
                    setLoad(false);
                    setFileURL("");
                }
            })
            .catch((err) => {
                setLoad(false);
                setFileURL("");
            });
    };

    const onRemoveFile = () => {
        setFileName("");
        setFileURL("");
    };

    return { load, uploaded, fileURL, fileName, onFileChange, onRemoveFile };
};

const FileUploadSetting = ({ setContentData, contentData }) => {
    return (
        <div className="tw-mt-4">
            {contentData?.content_type == "MP4" && (
                <VideoUpload setContentData={setContentData} contentData={contentData} />
            )}
            {contentData?.content_type == "ZIP" && (
                <SCROMUpload setContentData={setContentData} contentData={contentData} />
            )}
            {contentData?.content_type == "MP3" && (
                <AudioUpload setContentData={setContentData} contentData={contentData} />
            )}
            {contentData?.content_type == "EMBED" && (
                <EmbedUpload setContentData={setContentData} contentData={contentData} />
            )}
        </div>
    );
};

export default FileUploadSetting;

const VideoUpload = ({ setContentData, contentData }) => {
    const { load, uploaded, fileURL, fileName, onFileChange, onRemoveFile } = useFileUpload(
        contentData?.content_url,
        contentData?.lecture_data?.file_name,
    );

    useEffect(() => {
        setContentData({
            ...contentData,
            content_url: fileURL,
            lecture_data: {
                file_name: fileName,
            },
        });
    }, [fileURL]);

    return (
        <div>
            <Label>Select video file here :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">File supported .mp4 </p>
            <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                <div className="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center tw-gap-3 tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2 tw-py-5">
                    <img className="tw-h-[50px] tw-w-[50px] tw-rounded-md" src="/assets/video.png" />
                    <p className="tw-text-md tw-text-slate-400">{fileName || "Upload Video .mp4 file (max 150mb)"}</p>
                    <div className="tw-mt-2 tw-flex tw-gap-2">
                        <Label
                            htmlFor="video_file"
                            className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                        >
                            <Upload
                                className="opacity-60 sm:-ms-1 sm:me-2"
                                size={16}
                                strokeWidth={2}
                                aria-hidden="true"
                            />
                            <input
                                onChange={onFileChange}
                                type="file"
                                style={{ display: "none" }}
                                id="video_file"
                                accept="video/*"
                            />
                            <div className="max-sm:sr-only">
                                {load ? "Uploading" : "Upload .mp4 File"} {load ? `${uploaded}%` : null}
                            </div>
                        </Label>

                        {fileURL && (
                            <Button variant="outline" className="aspect-square tw-rounded-xl" onClick={onRemoveFile}>
                                <X
                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                                <Label className="max-sm:sr-only">Remove</Label>
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            <div className="tw-mt-4">
                <Label>File URL :- </Label>
                <br />
                <a className="tw-text-sm" href={fileURL} target="_blank">
                    {fileURL}
                </a>
            </div>
        </div>
    );
};

const AudioUpload = ({ setContentData, contentData }) => {
    const { load, uploaded, fileURL, fileName, onFileChange, onRemoveFile } = useFileUpload(
        contentData?.content_url,
        contentData?.lecture_data?.file_name,
    );

    useEffect(() => {
        setContentData({
            ...contentData,
            content_url: fileURL,
            lecture_data: {
                file_name: fileName,
            },
        });
    }, [fileURL]);

    return (
        <div>
            <Label>Select audio file here :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">File supported .mp3 </p>
            <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                <div className="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center tw-gap-3 tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2 tw-py-5">
                    <img className="tw-h-[50px] tw-w-[50px] tw-rounded-md" src="/assets/audio.png" />
                    <p className="tw-text-md tw-text-slate-400">{fileName || "Upload Video .mp3 file (max 15mb)"}</p>
                    <div className="tw-mt-2 tw-flex tw-gap-2">
                        <Button variant="outline" className="aspect-square tw-rounded-xl">
                            <Upload
                                className="opacity-60 sm:-ms-1 sm:me-2"
                                size={16}
                                strokeWidth={2}
                                aria-hidden="true"
                            />
                            <input
                                onChange={onFileChange}
                                type="file"
                                style={{ display: "none" }}
                                id="video_file"
                                accept="audio/*"
                            />
                            <Label htmlFor="video_file" className="max-sm:sr-only">
                                {load ? "Uploading" : "Upload .mp3 File"} {load ? `${uploaded}%` : null}
                            </Label>
                        </Button>

                        {fileURL && (
                            <Button variant="outline" className="aspect-square tw-rounded-xl" onClick={onRemoveFile}>
                                <X
                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                                <Label className="max-sm:sr-only">Remove</Label>
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            <div className="tw-mt-4">
                <Label>File URL :- </Label>
                <br />
                <a className="tw-text-sm" href={fileURL} target="_blank">
                    {fileURL}
                </a>
            </div>
        </div>
    );
};

const SCROMUpload = ({ setContentData, contentData }) => {
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [fileURL, setFileURL] = useState(contentData?.content_url || "");
    const [scormData, setScormData] = useState(contentData?.lecture_data || {});

    useEffect(() => {
        setContentData({
            ...contentData,
            content_url: fileURL,
            lecture_data: {
                file_name: scormData?.file_name,
                scorm_id: scormData?.scorm_id,
                basePath: scormData?.basePath,
                isCredit: true,
                width: 900,
                height: 600,
            },
        });
    }, [fileURL, scormData]);

    const onFileChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            onFileUpload(data, imageData);
        }
    };

    const onFileUpload = async (formData, imageData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("course/upload-scorm/upload-scorm-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setFileURL(res.data.launchFile);
                    setScormData({
                        file_name: res?.data.scormTitle,
                        scorm_id: res?.data?.scormId,
                        basePath: res.data.launchFile,
                    });
                } else {
                    setLoad(false);
                    setFileURL("");
                    setScormData({});
                }
            })
            .catch((err) => {
                setLoad(false);
                setFileURL("");
                setScormData({});
            });
    };

    const onRemoveFile = () => {
        setFileURL("");
        setScormData({});
    };
    return (
        <div>
            <Label>Select file here :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">File supported .zip </p>
            <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                <div className="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center tw-gap-3 tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2 tw-py-5">
                    <img className="tw-h-[50px] tw-w-[50px] tw-rounded-md" src="/assets/scorm.png" />
                    <p className="tw-text-md tw-text-slate-400">
                        {scormData?.file_name || "Upload SCORM .zip file (max 100mb)"}
                    </p>
                    <div className="tw-mt-2 tw-flex tw-gap-2">
                        <Button variant="outline" className="aspect-square tw-rounded-xl">
                            <Upload
                                className="opacity-60 sm:-ms-1 sm:me-2"
                                size={16}
                                strokeWidth={2}
                                aria-hidden="true"
                            />
                            <input
                                onChange={onFileChange}
                                type="file"
                                style={{ display: "none" }}
                                id="scorm_file"
                                accept=".zip,.rar,.7zip"
                            />
                            <Label htmlFor="scorm_file" className="max-sm:sr-only">
                                {load ? "Uploading" : "Upload .zip File"} {load ? `${uploaded}%` : null}
                            </Label>
                        </Button>

                        {fileURL && (
                            <Button variant="outline" className="aspect-square tw-rounded-xl" onClick={onRemoveFile}>
                                <X
                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                                <Label className="max-sm:sr-only">Remove</Label>
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            <div className="tw-mt-4">
                <Label>SCORM .zip File URL :- </Label>
                <br />
                <CopyLink value={fileURL} />
            </div>
        </div>
    );
};

const EmbedUpload = ({ setContentData, contentData }) => {
    const [fileURL, setFileURL] = useState(contentData?.content_url || "");
    const [title, setTitle] = useState(contentData?.lecture_data?.title || "");

    useEffect(() => {
        setContentData({
            ...contentData,
            content_url: fileURL,
            lecture_data: {
                title: title,
            },
        });
    }, [fileURL, title]);

    const onRemoveFile = () => {
        setFileURL("");
    };
    return (
        <div>
            <Label>Embed URL here :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">Mention your embedded URL here. </p>
            <div className="tw-space-y-3">
                <div className="tw-space-y-1">
                    <Label>Paste Embedded URL</Label>
                    <Textarea
                        onChange={(e) => setFileURL(e.target.value)}
                        value={fileURL}
                        rows="5"
                        placeholder={`https://www.abc.com/embed/abcdefghijklmnopqrestuvwxyz`}
                    />
                </div>
                {fileURL && (
                    <div className="tw-text-right">
                        <Button variant="outline" className="aspect-square tw-rounded-xl" onClick={onRemoveFile}>
                            <X className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                            <Label className="max-sm:sr-only">Remove URL</Label>
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
};

export function CopyLink({ value }) {
    const [copied, setCopied] = useState(false);
    const inputRef = useRef(null);

    const handleCopy = () => {
        if (inputRef.current) {
            navigator.clipboard.writeText(value);
            setCopied(true);
            setTimeout(() => setCopied(false), 1500);
        }
    };

    return (
        <div className="tw-space-y-2">
            <div className="tw-relative">
                <Input value={value} className="tw-pe-9" type="text" readOnly />
                <TooltipProvider delayDuration={0}>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <button
                                onClick={handleCopy}
                                className="tw-absolute tw-inset-y-0 tw-end-0 tw-flex tw-h-full tw-w-9 tw-items-center tw-justify-center tw-rounded-e-lg tw-border tw-border-transparent tw-text-muted-foreground/80 tw-outline-offset-2 tw-transition-colors hover:tw-text-foreground focus-visible:tw-text-foreground focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-ring/70 disabled:tw-pointer-events-none disabled:tw-cursor-not-allowed"
                                aria-label={copied ? "Copied" : "Copy to clipboard"}
                                disabled={copied}
                            >
                                <div
                                    className={cn(
                                        "transition-all",
                                        copied ? "tw-scale-100 tw-opacity-100" : "tw-scale-0 tw-opacity-0",
                                    )}
                                >
                                    <Check
                                        className="tw-stroke-emerald-500"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                </div>
                                <div
                                    className={cn(
                                        "tw-absolute tw-transition-all",
                                        copied ? "tw-scale-0 tw-opacity-0" : "tw-scale-100 tw-opacity-100",
                                    )}
                                >
                                    <Copy size={16} strokeWidth={2} aria-hidden="true" />
                                </div>
                            </button>
                        </TooltipTrigger>
                        <TooltipContent className="tw-border tw-border-input tw-bg-popover tw-px-2 tw-py-1 tw-text-xs tw-text-muted-foreground">
                            Copy to clipboard
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
            <a href={value} target="_blank" className="tw-text-xs tw-text-primary tw-underline tw-underline-offset-2">
                Go to URL
            </a>
        </div>
    );
}
