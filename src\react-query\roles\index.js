import { tanstack<PERSON>pi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetRoleListing = () => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["role-listing", { userId }],
        queryFn: async () => (await tanstackApi.get("role/listing-role")).data,
    });
};

export const useUpdateRole = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.put("role/update-role", data)).data,
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["role-listing"] }),
    });
};

export const useCreateRole = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("role/create-role", data)).data,
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["role-listing"] }),
    });
};
