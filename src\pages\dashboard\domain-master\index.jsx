import { Badge } from "@/components/ui/badge";
import {
    Bread<PERSON>rumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const DomainMaster = () => {
    const navigate = useNavigate();
    const [rolesList, setRolesList] = useState([]);
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(7);
    const [domainTypes, setDomainTypes] = useState([]);

    const [filterState, setFilterState] = useState({
        search: "",
        role: "",
        domain_type: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.domain_name?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const role = filterState?.role ? item?.role === filterState?.role : true; // Allow all items if no subCategory filter
            const domainType = filterState?.domain_type ? item?.domain_type === filterState?.domain_type : true; // Allow all items if no subCategory filter

            return matchesSearch && role && domainType; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            role: "",
            domain_type: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getDomainData();
        getDomainTypes();
    }, []);

    const getDomainData = async () => {
        await tanstackApi
            .get("auth/get-domains")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const getDomainTypes = async () => {
        await tanstackApi
            .get("domain-type/all")
            .then((res) => {
                setDomainTypes(res?.data?.data);
            })
            .catch((err) => {
                setDomainTypes([]);
            });
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Domain Users</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button className="tw-px-2 tw-py-1" onClick={() => navigate(`/dashboard/domain-create`)}>
                    <i className="fa-solid fa-plus"></i> New Domain
                </Button>
            </div>
            <div className="page_filters tw-mt-2 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Search
                    </label>
                    <input
                        value={filterState?.search}
                        name="search"
                        onChange={onFilterChange}
                        className="tw-text-sm"
                        style={{ minWidth: "300px" }}
                        type="text"
                        placeholder="Search by domain name ..."
                    />
                </div>
                {/* <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Role
                    </label>
                    <select value={filterState?.role} name="role" onChange={onFilterChange} className="tw-text-sm">
                        <option value=""> - Choose Role - </option>
                        {rolesList?.map((lang, idx) => (
                            <option value={lang?.role_code} key={idx}>
                                {lang?.display_name}
                            </option>
                        ))}
                    </select>
                </div> */}
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Domain Type
                    </label>
                    <select
                        value={filterState?.domain_type}
                        name="domain_type"
                        onChange={onFilterChange}
                        className="tw-text-sm"
                    >
                        <option value=""> - Choose domain type - </option>
                        {domainTypes?.map((type, idx) => (
                            <option className="tw-capitalize" value={type?.type} key={idx}>
                                {type?.display_name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>Logo</th>
                            <th>Domain Name</th>
                            <th>Person Name</th>
                            <th>Email</th>
                            {/* <th>Role</th> */}
                            <th>Domain Type</th>
                            <th>Created On</th>
                            <th>Reports</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>
                                    <img
                                        src={row?.organizationDetails?.organization_info?.about?.company_logo}
                                        className="tw-h-[50px]"
                                        alt=""
                                    />
                                </td>
                                <td>{row?.domain_name}</td>
                                <td>{`${row?.first_name} ${row?.last_name}`}</td>
                                <td>{row?.email}</td>
                                {/* <td>
                                    <Badge variant={"outline"}>{row?.role}</Badge>
                                </td> */}
                                <td>
                                    <Badge variant={"outline"}>{row?.domain_type}</Badge>
                                </td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td>
                                    <button
                                        className="selected_btn"
                                        onClick={() => navigate(`/dashboard/domain-reports/${row?.id}`)}
                                    >
                                        <i className="fa-solid fa-chart-bar"></i> All Reports
                                    </button>
                                </td>
                                <td>
                                    <button
                                        className="selected_btn"
                                        onClick={() => navigate(`/dashboard/domain-create/${row?.id}`)}
                                    >
                                        <i className="fa-regular fa-edit"></i> Edit
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination className="">
                        <PaginationContent>
                            {/* Previous Button */}
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page Numbers with Ellipses */}
                            {currentPage > 3 && (
                                <>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                            1
                                        </PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                </>
                            )}

                            {getVisiblePages().map((page) => (
                                <PaginationItem key={page}>
                                    <PaginationLink
                                        href="#"
                                        isActive={page === currentPage}
                                        onClick={() => handlePageChange(page)}
                                    >
                                        {page}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {currentPage < totalPages - 2 && (
                                <>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                            {totalPages}
                                        </PaginationLink>
                                    </PaginationItem>
                                </>
                            )}

                            {/* Next Button */}
                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </div>
    );
};

export default DomainMaster;
