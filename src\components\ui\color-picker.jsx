import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useForwardedRef } from "@/hooks/use-forward-ref";
import { cn } from "@/lib/utils";
import { forwardRef, useMemo, useState } from "react";
import { RgbaColorPicker } from "react-colorful";

function hexToRgba(hex) {
    hex = hex.replace("#", "");
    let r = 255,
        g = 255,
        b = 255,
        a = 1;
    if (hex.length === 6) {
        r = parseInt(hex.substring(0, 2), 16);
        g = parseInt(hex.substring(2, 4), 16);
        b = parseInt(hex.substring(4, 6), 16);
    } else if (hex.length === 8) {
        r = parseInt(hex.substring(0, 2), 16);
        g = parseInt(hex.substring(2, 4), 16);
        b = parseInt(hex.substring(4, 6), 16);
        a = parseInt(hex.substring(6, 8), 16) / 255;
    }
    return { r, g, b, a };
}

function rgbaToHex({ r, g, b, a }) {
    const toHex = (x) => {
        const hex = Math.round(x).toString(16);
        return hex.length === 1 ? `0${hex}` : hex;
    };
    const alpha = Math.round(a * 255);
    if (alpha === 255) {
        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    }
    return `#${toHex(r)}${toHex(g)}${toHex(b)}${toHex(alpha)}`;
}

const ColorPicker = forwardRef(
    ({ disabled, value, onChange, onInput, onBlur, name, className, ...props }, forwardedRef) => {
        const ref = useForwardedRef(forwardedRef);
        const [open, setOpen] = useState(false);

        const parsedColor = useMemo(() => {
            try {
                return hexToRgba(value || "#FFFFFF");
            } catch (error) {
                return { r: 255, g: 255, b: 255, a: 1 };
            }
        }, [value]);

        const backgroundColor = `rgba(${parsedColor.r}, ${parsedColor.g}, ${parsedColor.b}, ${parsedColor.a})`;

        return (
            <Popover onOpenChange={setOpen} open={open}>
                <PopoverTrigger asChild disabled={disabled} onBlur={onBlur}>
                    <Button
                        {...props}
                        className={cn("tw-block", className)}
                        name={name}
                        onClick={() => setOpen(true)}
                        size="icon"
                        style={{
                            backgroundColor,
                        }}
                        variant="outline"
                    >
                        <div />
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="tw-z-[2000] tw-w-full">
                    <RgbaColorPicker color={parsedColor} onChange={(newColor) => onChange(rgbaToHex(newColor))} />
                    <Input
                        maxLength={9}
                        onChange={(e) => {
                            onChange(e?.currentTarget?.value);
                        }}
                        ref={ref}
                        value={value}
                    />
                </PopoverContent>
            </Popover>
        );
    },
);
ColorPicker.displayName = "ColorPicker";

export { ColorPicker };
