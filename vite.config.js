import react from "@vitejs/plugin-react";
import { fileURLToPath } from "url";
import { defineConfig } from "vite";

// https://vite.dev/config/
export default defineConfig({
    plugins: [react()],
    server: {
        port: 3000,
    },
    resolve: {
        alias: {
            "@": fileURLToPath(new URL("./src", import.meta.url)),
        },
    },

    css: {
        preprocessorOptions: {
            sass: {
                silenceDeprecations: ["legacy-js-api"],
                api: "modern-compiler",
            },
        },
    },
});
