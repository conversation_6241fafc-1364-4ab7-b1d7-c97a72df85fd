import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { Clock, Languages, ShieldCheck, Users } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useGetLanguageList } from "@/react-query/language";

const defaultValue = {
    team_name: "",
};

const MySubGroups = () => {
    const navigate = useNavigate();
    const params = useParams();
    const [datalist, setDatalist] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [filter, setFilter] = useState(defaultValue);
    const getLanguages = useGetLanguageList();
    const languages = getLanguages.data?.data.data ?? [];

    useEffect(() => {
        if (params?.group_id !== undefined) getUserSubGroupData(params?.group_id);
    }, [params]);

    const getUserSubGroupData = async (groupID) => {
        if (localStorage.getItem("is_trainer") == "true") {
            await tanstackApi
                .post("user-subgroup/list-subgroup-trainer")
                .then((res) => {
                    const data = res?.data?.data?.filter((dt) => dt?.lms_user_subgroup?.group_id == Number(groupID));
                    setDatalist(data);
                    setFilteredData(data);
                })
                .catch((err) => {
                    setDatalist([]);
                    setFilteredData([]);
                });
        } else {
            await tanstackApi
                .post("user-subgroup/get-my-subgroups")
                .then((res) => {
                    const data = res?.data?.data?.filter((dt) => dt?.group_id == Number(groupID));
                    setDatalist(data);
                    setFilteredData(data);
                })
                .catch((err) => {
                    setDatalist([]);
                    setFilteredData([]);
                });
        }
    };

    const handleSearch = () => {
        const { team_name } = filter;
        if (team_name) {
            const filter = datalist?.filter((item) => {
                if (localStorage.getItem("is_trainer") == "true")
                    return item?.lms_user_subgroup?.name?.toLowerCase().includes(team_name.toLowerCase());
                return item?.name?.toLowerCase().includes(team_name.toLowerCase());
            });
            setFilteredData(filter);
        }
    };
    const handleClear = () => {
        setFilter(defaultValue);
        setFilteredData(datalist);
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/user-groups">My Groups</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{params?.group_name}</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <div className="tw-flex tw-gap-2">
                    <Button
                        variant="outline"
                        className="tw-px-2 tw-py-1"
                        onClick={() => navigate(`/dashboard/user-groups`)}
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                </div>
            </div>
            {/* <br /> */}
            <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label htmlFor="">Team Name</label>
                    <input
                        style={{ minWidth: "300px" }}
                        value={filter?.team_name}
                        onChange={(e) => setFilter({ ...filter, team_name: e.target.value })}
                        type="text"
                        placeholder="Search by name ..."
                    />
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={handleSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={handleClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-5 tw-p-0">
                {localStorage.getItem("is_trainer") == "true"
                    ? filteredData?.map((group, index) => (
                          <div key={index} className="tw-w-[350px] tw-rounded-xl tw-border-[1px] tw-p-1">
                              <div className="tw-p-2">
                                  <div className="tw-flex tw-items-center tw-gap-2">
                                      <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                          {group?.lms_user_subgroup?.name}
                                      </h2>
                                  </div>
                                  <p className="tw-mt-2 tw-line-clamp-3">{group?.chapter_discription}</p>
                                  <div className="tw-mt-1 tw-space-y-2">
                                      <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                          <Users size={18} /> {group?.lms_user_subgroup?.user_count}/
                                          {group?.lms_user_subgroup?.user_restriction} Users
                                      </span>{" "}
                                      <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                          <Languages size={18} />{" "}
                                          <Badge variant={"outline"}>
                                              {
                                                  languages?.find(
                                                      (dt) => dt?.id == group?.lms_user_subgroup?.language_id,
                                                  )?.name
                                              }
                                          </Badge>
                                      </span>{" "}
                                      <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                          <Clock size={18} /> Created on{" "}
                                          {moment(group?.lms_user_subgroup?.createdAt).format("LL")}
                                      </span>{" "}
                                      {localStorage.getItem("is_trainer") == "false" && (
                                          <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                              <ShieldCheck size={18} />{" "}
                                              {group?.lms_trainer_subgroups_mappings?.length > 0 ? (
                                                  group?.lms_trainer_subgroups_mappings?.map((trainer, idx) => (
                                                      <>
                                                          <a className="tw-font-semibold tw-underline">
                                                              {trainer?.lms_user?.first_name}{" "}
                                                              {trainer?.lms_user?.last_name}
                                                          </a>
                                                          {"  "}
                                                      </>
                                                  ))
                                              ) : (
                                                  <a className="tw-text-sm tw-text-slate-400">
                                                      No trainers assigned Yet!
                                                  </a>
                                              )}
                                          </span>
                                      )}
                                  </div>
                              </div>
                          </div>
                      ))
                    : filteredData?.map((group, index) => (
                          <div key={index} className="tw-w-[350px] tw-rounded-xl tw-border-[1px] tw-p-1">
                              <div className="tw-p-2">
                                  <div className="tw-flex tw-items-center tw-gap-2">
                                      <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                          {group?.name}
                                      </h2>
                                  </div>
                                  <p className="tw-mt-2 tw-line-clamp-3">{group?.chapter_discription}</p>
                                  <div className="tw-mt-1 tw-space-y-2">
                                      <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                          <Users size={18} /> {group?.user_count}/{group?.user_restriction} Users
                                      </span>{" "}
                                      <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                          <Languages size={18} />{" "}
                                          <Badge variant={"outline"}>
                                              {languages?.find((dt) => dt?.id == group?.language_id)?.name}
                                          </Badge>
                                      </span>{" "}
                                      <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                          <Clock size={18} /> Created on {moment(group?.createdAt).format("LL")}
                                      </span>{" "}
                                      {localStorage.getItem("is_trainer") == "false" && (
                                          <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                              <ShieldCheck size={18} />{" "}
                                              {group?.lms_trainer_subgroups_mappings?.length > 0 ? (
                                                  group?.lms_trainer_subgroups_mappings?.map((trainer, idx) => (
                                                      <>
                                                          <a className="tw-font-semibold tw-underline">
                                                              {trainer?.lms_user?.first_name}{" "}
                                                              {trainer?.lms_user?.last_name}
                                                          </a>
                                                          {"  "}
                                                      </>
                                                  ))
                                              ) : (
                                                  <a className="tw-text-sm tw-text-slate-400">
                                                      No trainers assigned Yet!
                                                  </a>
                                              )}
                                          </span>
                                      )}
                                  </div>
                              </div>
                          </div>
                      ))}
            </div>
        </div>
    );
};

export default MySubGroups;
