import {
    AlertDialog,
    AlertDialog<PERSON><PERSON>,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialog<PERSON>ooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { InteractionProvider, useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import InteractionSetting from "@/pages/dashboard/interactions/create/setting";
import InteractionTabSetting from "@/pages/dashboard/interactions/create/tab-setting";
import { Expandable } from "@/pages/dashboard/interactions/preview/expandable";
import { Process } from "@/pages/dashboard/interactions/preview/process";
import { Reveal } from "@/pages/dashboard/interactions/preview/reveal";
import { Steps } from "@/pages/dashboard/interactions/preview/steps";
import { Timeline } from "@/pages/dashboard/interactions/preview/timeline";
import { quizz_template } from "@/react-query";
import { useFileUpload } from "@/react-query/common";
import { useAddTimelineLog } from "@/react-query/common/timeline";
import { useCreateInteraction, useGetInteraction, useUpdateInteraction } from "@/react-query/interactions";
import { Cog, Edit, File, PlusCircle, Save, Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

function hasDuplicateLabels(data) {
    const seenLabels = new Set();
    for (const item of data) {
        if (seenLabels.has(item.label)) return true;
        seenLabels.add(item.label);
    }
    return false;
}

function CreateInteractions() {
    const params = useParams();
    const router = useNavigate();
    const { content, setContent, tabList, setCurrentTab, setTabList } = useEditInteraction();
    const [setting, setSetting] = useState(false);
    const [tabSetting, setTabSetting] = useState(false);
    const interaction = useGetInteraction(params?.id);
    const template = quizz_template.useGetParticularTemplate(content?.template_id);

    const removeImage = () => {
        setContent((prev) => ({
            ...prev,
            structure: {
                ...prev.structure,
                question_thumbnail: "",
            },
        }));
    };

    console.log(content.structure.question_thumbnail);

    useEffect(() => {
        if (params?.id && interaction.isSuccess) {
            let data = interaction.data?.data;
            if (data.structure.sections && data.structure.sections.length > 0) {
                data.structure.database = data.structure.sections.map((data) => {
                    if (data.name) {
                        data.label = data.name;
                        delete data.name;
                    }
                    if (data.content) {
                        data.description = data.content;
                        delete data.content;
                    }
                    return data;
                });
                delete data.structure.sections;
            }
            setContent(data);
            setTabList(data.structure.database);
        }
    }, [params, interaction.status]);

    const components = {
        Accordion: Expandable,
        Process: Process,
        Steps: Steps,
        Timeline: Timeline,
        Reveal: Reveal,
    };

    const InteractionComponent = content?.type ? components[content?.type] : null;
    const createInteraction = useCreateInteraction();
    const updateInteraction = useUpdateInteraction();
    const timeline = useAddTimelineLog();
    const upload = useFileUpload();

    const onImageChange = async (e) => {
        try {
            if (e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                setContent((prev) => ({
                    ...prev,
                    structure: {
                        ...prev.structure,
                        question_thumbnail: response?.fileUrl,
                    },
                }));
            }
        } catch (error) {
            setContent((prev) => ({
                ...prev,
                structure: {
                    ...prev.structure,
                    question_thumbnail: "",
                },
            }));
        }
    };

    const saveHandler = async (type = "save") => {
        if (content.title == null) return toast.error("Please enter a title");
        if (content.template_id == null) {
            content.template_id = 154;
        }
        if (params?.id) {
            content.is_draft = type == "draft";
            delete content.user_id;
            return updateInteraction.mutate(content, {
                onSuccess: (data) => {
                    let payload = {
                        user_id: window.localStorage.getItem("userId"),
                        event: "interactions",
                        log: `${content?.title} updated successfully`,
                    };
                    timeline.mutate(payload);
                    toast.success(data.message ?? "Interaction Updated");
                },
                onError: (error) => {
                    toast.error(error.response.data.message);
                },
            });
        }
        if (hasDuplicateLabels(content.structure.database)) return toast.error("Duplicate labels are not allowed");
        createInteraction.mutate(content, {
            onSuccess: (data) => {
                let payload = {
                    user_id: window.localStorage.getItem("userId"),
                    event: "interactions",
                    log: `${content?.title} created successfully`,
                };
                timeline.mutate(payload);
                toast.success(data.message ?? "Interaction Created");
                // setLocalInteractions(defaultData);
            },
            onError: (error) => {
                toast.error(error.response.data.message);
            },
        });
    };

    return (
        <>
            <InteractionSetting open={setting} setOpen={setSetting} />
            <InteractionTabSetting open={tabSetting} setOpen={setTabSetting} />
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-square-pen"></i> Create Interactions
                </h4>
                <div className="tw-flex tw-gap-2">
                    <Button className="tw-px-2 tw-py-1" variant="secondary" onClick={() => router(-1)}>
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button variant="secondary" onClick={() => saveHandler("draft")}>
                        <File /> Draft
                    </Button>
                    <Button onClick={() => saveHandler("save")}>
                        <Save /> Save
                    </Button>
                </div>
            </div>
            <div className="tw-grid tw-h-[90%] tw-grid-cols-[200px_1fr] tw-grid-rows-1 tw-gap-4">
                <div className="tw-grid tw-grid-rows-[80px_1fr] tw-gap-3">
                    <div
                        onClick={() => {
                            if (tabList?.length > 4) return toast.error("Maximum Tabs reached");
                            setContent((prev) => ({
                                ...prev,
                                structure: {
                                    ...prev.structure,
                                    database: [
                                        ...prev.structure.database,
                                        {
                                            label: `Tab ${prev.structure.database.length + 1}`,
                                            description: "New Tab Description",
                                            image_src: "",
                                            x: "",
                                            y: "",
                                            annotion_icon: "",
                                            bg_sound: "",
                                        },
                                    ],
                                },
                            }));
                        }}
                        className="tw-flex tw-h-20 tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-2 tw-text-lg tw-font-medium tw-text-slate-400"
                    >
                        <PlusCircle /> Add New
                    </div>
                    <div className="tw-flex tw-h-full tw-flex-col tw-gap-3 tw-overflow-hidden">
                        <div className="tw-flex tw-flex-col tw-gap-3 tw-overflow-y-auto">
                            {tabList?.map((data, idx) => {
                                return (
                                    <div
                                        className="tw-flex tw-h-fit tw-min-h-20 tw-shrink-0 tw-grow tw-cursor-pointer tw-flex-col tw-items-center tw-justify-center tw-gap-3 tw-rounded-lg tw-border-2 tw-p-2 tw-text-lg tw-font-medium"
                                        key={idx}
                                    >
                                        <div className="tw-text-center tw-leading-[1]">{data.label}</div>
                                        <div className="tw-flex tw-gap-2">
                                            <Button
                                                onClick={() => {
                                                    setCurrentTab(idx);
                                                    setTabSetting(true);
                                                }}
                                                variant="outline"
                                                size="icon"
                                                className="!tw-size-8"
                                            >
                                                <Edit className="!tw-size-3" />
                                            </Button>
                                            <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                    <Button variant="destructive" size="icon" className="!tw-size-8">
                                                        <X className="!tw-size-3" />
                                                    </Button>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent>
                                                    <AlertDialogHeader>
                                                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                        <AlertDialogDescription>
                                                            This action cannot be undone.
                                                        </AlertDialogDescription>
                                                    </AlertDialogHeader>
                                                    <AlertDialogFooter>
                                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                        <AlertDialogAction
                                                            onClick={() => {
                                                                toast.error("Tab removed");
                                                                const prevData = [...content.structure.database];
                                                                prevData.splice(idx, 1);
                                                                setContent((prev) => ({
                                                                    ...prev,
                                                                    structure: {
                                                                        ...prev.structure,
                                                                        database: prevData,
                                                                    },
                                                                }));
                                                            }}
                                                        >
                                                            Continue
                                                        </AlertDialogAction>
                                                    </AlertDialogFooter>
                                                </AlertDialogContent>
                                            </AlertDialog>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
                <div className="tw-relative tw-h-[90%] tw-w-full tw-rounded-lg tw-border-2">
                    {tabList.length > 0 ? (
                        <>
                            <div className="tw-absolute tw-right-3 tw-top-3 tw-z-50">
                                <Button
                                    onClick={() => setSetting(true)}
                                    variant="secondary"
                                    className="tw-px-2 tw-py-1"
                                >
                                    Edit <Cog />
                                </Button>
                            </div>
                            <div
                                className="tw-relative tw-grid tw-h-full tw-w-full tw-gap-y-4 tw-overflow-hidden tw-rounded-xl tw-bg-cover tw-bg-no-repeat tw-py-10"
                                style={{ backgroundImage: `url(${template.data?.data?.background})` }}
                            >
                                <div className="tw-mx-auto tw-grid tw-h-full  tw-max-h-full tw-w-[80%] tw-max-w-[80%] tw-grid-rows-[1fr] tw-gap-4 tw-overflow-hidden">
                                    {InteractionComponent && (
                                        <InteractionComponent
                                            interactions={content}
                                            template={template}
                                            setData={setContent}
                                        />
                                    )}
                                </div>
                            </div>
                        </>
                    ) : (
                        <div className="tw-flex tw-size-full tw-items-center tw-justify-center">
                            <Button onClick={() => setSetting(true)} variant="secondary" className="tw-px-2 tw-py-1">
                                Edit <Cog />
                            </Button>
                        </div>
                    )}
                    {content?.type == "Reveal" && (
                        <>
                            {content.structure.question_thumbnail ? (
                                <Button
                                    variant="outline"
                                    className="aspect-square max-sm:p-0 tw-absolute tw-left-5 tw-top-5"
                                    onClick={removeImage}
                                >
                                    <X
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <Label className="max-sm:sr-only">Remove Icon</Label>
                                </Button>
                            ) : (
                                <Label
                                    className={cn(
                                        buttonVariants({ variant: "outline" }),
                                        "aspect-square max-sm:p-0 tw-absolute tw-left-5 tw-top-5 tw-h-10",
                                    )}
                                >
                                    <Upload
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <input
                                        onChange={onImageChange}
                                        type="file"
                                        style={{ display: "none" }}
                                        id="bg_image"
                                        accept="image/*"
                                    />
                                    <Label htmlFor="bg_image" className="max-sm:sr-only">
                                        {upload.isPending ? "Uploading..." : "Upload Question Thumbnail"}
                                    </Label>
                                </Label>
                            )}
                        </>
                    )}
                </div>
            </div>
        </>
    );
}

export default function CreateInteractionsWrapper() {
    return (
        <InteractionProvider>
            <CreateInteractions />
        </InteractionProvider>
    );
}
