import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const CreateCourse = ({ open, setOpen }) => {
    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-lg">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">How do you want to create this course</h1>
                        </DialogTitle>
                        <DialogDescription>
                            Select below details according to your course convenience. You can create course as
                            individually or with trainers.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-flex-col-2 tw-mt-3 tw-flex tw-gap-10">
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox id="terms1" />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="terms1"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Enable Auto Enroll
                                    </label>
                                </div>
                            </div>
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox id="terms2" />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="terms2"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Enable Re-Enroll
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Course Type</Label>
                                <Select>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose course type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Combined Course">Combined Course</SelectItem>
                                        <SelectItem value="SCORM Course">SCORM Course</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-1">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Individually / Trainers</Label>
                                <Select>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Define Ownership" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Individually">Individually</SelectItem>
                                        <SelectItem value="Trainers">Trainers</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="tw-my-2 tw-flex tw-flex-wrap tw-gap-2">
                            <Badge variant="outline" className="tw-cursor-pointer">
                                Wranor Calsburg
                            </Badge>
                            <Badge variant="" className="tw-cursor-pointer">
                                Arthur Morgan
                            </Badge>
                            <Badge variant="outline" className="tw-cursor-pointer">
                                Pooja Shah
                            </Badge>
                            <Badge variant="outline" className="tw-cursor-pointer">
                                Wranor Calsburg
                            </Badge>
                            <Badge variant="" className="tw-cursor-pointer">
                                Arthur Morgan
                            </Badge>
                            <Badge variant="outline" className="tw-cursor-pointer">
                                Pooja Shah
                            </Badge>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                Close
                            </Button>
                        </DialogClose>
                        <Button type="submit">Create Course</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default CreateCourse;
