import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import WorkAssignments from "./WorkAssignments";
import WorkHomeworks from "./WorkHomeworks";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const tabsData = [{ name: "Assignments" }, { name: "Homeworks" }];

const AssignedWork = () => {
    const [allAssignedWork, setAllAssignedWork] = useState([]);
    const [activeTab, setActiveTab] = useState("Assignments");
    const [UserGroups, setUserGroups] = useState([]);

    useEffect(() => {
        getAssignedsData();
        getUserGroupData();
    }, []);

    const getAssignedsData = async () => {
        await tanstackApi
            .post("homework/list-homeworks-trainer", { is_public: false })
            .then((res) => {
                setAllAssignedWork(res?.data?.data);
            })
            .catch((err) => {
                setAllAssignedWork([]);
            });
    };

    const getUserGroupData = async () => {
        await tanstackApi
            .post("user-groups/get-my-groups")
            .then((res) => {
                setUserGroups(res?.data?.data);
            })
            .catch((err) => {
                setUserGroups([]);
            });
    };

    return (
        <div className="">
            <div>
                <div className="pageHeader">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Assigned Work</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
                <div className="page_tabs">
                    {tabsData?.map((tab, idx) => (
                        <p
                            key={idx}
                            onClick={() => setActiveTab(tab?.name)}
                            className={activeTab == tab?.name ? "active" : ""}
                        >
                            {tab?.name}
                        </p>
                    ))}
                </div>
                {activeTab == "Assignments" && (
                    <WorkAssignments allAssignedWork={allAssignedWork} activeTab={activeTab} UserGroups={UserGroups} />
                )}
                {activeTab == "Homeworks" && (
                    <WorkHomeworks allAssignedWork={allAssignedWork} activeTab={activeTab} UserGroups={UserGroups} />
                )}
            </div>
        </div>
    );
};

export default AssignedWork;
