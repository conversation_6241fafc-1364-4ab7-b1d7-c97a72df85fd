import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import SumbittedAssignments from "./SumbittedAssignments";
import SumbittedHomeworks from "./SumbittedHomeworks";
import SumbittedQuizzes from "./SumbittedQuizzes";

import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const tabsData = [
    {
        name: "Assignments",
    },
    {
        name: "Homeworks",
    },
    {
        name: "Quizzes",
    },
];

const Submissions = () => {
    const [allSubmissions, setAllSubmissions] = useState([]);
    const [activeTab, setActiveTab] = useState("Assignments");
    const [assginedData, setAssginedData] = useState([]);

    useEffect(() => {
        getSubmissionsData();
        getAssignedsData();
    }, []);

    const getSubmissionsData = async (formData) => {
        await tanstackApi
            .post("homework/submissions/get-submissions", formData)
            .then((res) => {
                setAllSubmissions(res?.data?.data);
            })
            .catch((err) => {
                setAllSubmissions([]);
            });
    };

    const getAssignedsData = async () => {
        await tanstackApi
            .post("homework/list-homeworks-trainer", { is_public: false })
            .then((res) => {
                setAssginedData(res?.data?.data);
            })
            .catch((err) => {
                setAssginedData([]);
            });
    };

    return (
        <div className="">
            <div>
                <div className="pageHeader">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Submissions by learners</BreadcrumbPage>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>{activeTab}</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
                <div className="page_tabs">
                    {tabsData?.map((tab, idx) => (
                        <p
                            key={idx}
                            onClick={() => setActiveTab(tab?.name)}
                            className={activeTab == tab?.name ? "active" : ""}
                        >
                            {tab?.name}
                        </p>
                    ))}
                </div>
                {activeTab == "Assignments" && (
                    <SumbittedAssignments
                        allSubmissions={allSubmissions}
                        activeTab={activeTab}
                        assginedData={assginedData}
                    />
                )}
                {activeTab == "Homeworks" && (
                    <SumbittedHomeworks
                        allSubmissions={allSubmissions}
                        activeTab={activeTab}
                        assginedData={assginedData}
                    />
                )}
                {activeTab == "Quizzes" && <SumbittedQuizzes allSubmissions={allSubmissions} activeTab={activeTab} />}
            </div>
        </div>
    );
};

export default Submissions;
