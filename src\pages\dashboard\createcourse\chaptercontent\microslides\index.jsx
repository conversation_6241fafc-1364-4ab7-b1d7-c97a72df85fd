import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from "@/components/ui/context-menu";
import { cn } from "@/lib/utils";
import { Copy, Trash } from "lucide-react";
import { useEffect } from "react";

export default function MicroCourseSlide({
    onMountSlide,
    formatNumber,
    content,
    template,
    type,
    currentId,
    contentArray,
    setContentArray,
    onSetDelete,
    index,
    onRemoveSlide,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
}) {
    const urlId = `#slide_${index}`;

    useEffect(() => {
        if (urlId == currentId) {
            const element = document.querySelector(`#slide_preview_${index}`);
            element.scrollIntoView({ behavior: "smooth" });
        }
    }, [currentId]);

    const makeCopy = (e) => {
        const oldData = { ...content };
        oldData.content_order = contentArray.length + 1;
        if (oldData.id) delete oldData.id;
        setContentArray((prev) => [...prev, oldData]);
    };

    return (
        <ContextMenu>
            <ContextMenuTrigger asChild>
                <div
                    id={`slide_preview_${index}`}
                    onClick={() => onMountSlide(index)}
                    style={{ backgroundImage: `url(${template?.background || content?.background})` }}
                    className={cn(
                        "tw-relative tw-grid tw-cursor-pointer tw-gap-2 tw-rounded-xl tw-border-2 tw-bg-cover tw-bg-center tw-bg-no-repeat tw-p-2 tw-shadow-sm",
                    )}
                    draggable
                    onDragStart={() => handleDragStart(index)}
                    onDragOver={() => handleDragOver(index)}
                    onDragEnd={handleDragEnd}
                    key={`slide_preview_${index}`}
                >
                    <div
                        className={cn(
                            "tw-absolute tw-inset-0 tw-size-full tw-rounded-lg",
                            urlId == currentId ? "tw-bg-green-100" : "tw-bg-transparent",
                        )}
                    />
                    <div className="tw-relative tw-z-10 tw-flex tw-flex-col tw-items-start tw-gap-2">
                        <div className="tw-text-md tw-flex tw-h-[30px] tw-w-[30px] tw-items-center tw-justify-center tw-rounded-lg tw-border-2 tw-bg-white tw-font-bold tw-text-slate-500 tw-shadow-sm">
                            {formatNumber(index + 1)}
                        </div>
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <div className="tw-text-sm tw-font-medium">{type}</div>
                        </div>
                    </div>
                </div>
            </ContextMenuTrigger>
            <ContextMenuContent className="tw-font-lexend">
                <ContextMenuItem onClick={makeCopy} className="tw-flex tw-gap-2">
                    <Copy className="tw-size-4" /> Copy
                </ContextMenuItem>
                <ContextMenuItem
                    onClick={() => {
                        if (content.id) {
                            onSetDelete(content, index);
                        } else {
                            onRemoveSlide(index);
                        }
                    }}
                    className="tw-flex tw-gap-2"
                >
                    <Trash className="tw-size-4" /> Remove
                </ContextMenuItem>
            </ContextMenuContent>
        </ContextMenu>
    );
}
