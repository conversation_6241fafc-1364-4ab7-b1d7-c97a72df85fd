import SlideHeader, { AddContentFunction } from "@/pages/dashboard/createcourse/chaptercontent/slides/common";
import { useInView } from "framer-motion";
import { useEffect, useRef } from "react";

export default function SCORM({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    onSetDelete,
    setCurrentId,
    setContentArray,
    contentArray,
    setOpenIconDialog,
}) {
    const containerRef = useRef(null);
    const inView = useInView(containerRef, { amount: 0.8 });
    useEffect(() => {
        if (inView) setCurrentId(`#slide_${index}`);
    }, [inView]);

    return (
        <div
            ref={containerRef}
            className="tw-grid tw-h-[60vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${template?.background || content?.background})` }}
        >
            <SlideHeader
                index={index}
                content={content}
                contentArray={contentArray}
                onSlideEdit={onSlideEdit}
                onSetDelete={onSetDelete}
                onRemoveSlide={onRemoveSlide}
                setOpenIconDialog={setOpenIconDialog}
                setContentArray={setContentArray}
            />
            {content?.content_url == null ? (
                <AddContentFunction onSlideEdit={onSlideEdit} content={content} index={index} />
            ) : (
                <div className="tw-p-3">
                    <embed className="tw-h-full tw-w-full" src={content?.content_url} type="" />
                </div>
            )}
        </div>
    );
}
