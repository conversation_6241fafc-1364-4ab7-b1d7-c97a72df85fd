import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "sonner";

const AssignUsersToPath = ({ pathData, getLearningPath }) => {
    const params = useParams();
    const [usersList, setUsersList] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState([]);

    useEffect(() => {
        getUsers();
    }, []);

    const getUsers = async (payload) => {
        await tanstackApi
            .get("users/get-users")
            .then((res) => {
                setUsersList(res?.data?.data?.filter((dt) => dt?.is_trainer == 0)?.reverse());
            })
            .catch((err) => {
                setUsersList([]);
            });
    };

    useEffect(() => {
        if (pathData?.usersGroups !== undefined && usersList?.length > 0) {
            const validUserIds = pathData.usersGroups
                .filter((group) => group.user_id && usersList.some((user) => user.id === group.user_id))
                .map((group) => group.user_id);
            setSelectedUsers(validUserIds);
        }
    }, [pathData, usersList]);

    const onCourseSelection = (item) => {
        if (selectedUsers?.includes(item)) {
            setSelectedUsers(selectedUsers?.filter((dt) => dt !== item));
        } else {
            setSelectedUsers([...selectedUsers, item]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (selectedUsers?.length == 0) {
            toast?.warning("Users", {
                description: "Please select some users for group.",
            });
            return false;
        }

        if (params?.path_id !== undefined) {
            const payload = {
                learning_path_id: params?.path_id,
                data: selectedUsers?.map((dt) => {
                    return {
                        group_id: null,
                        user_id: dt,
                    };
                }),
            };

            await tanstackApi
                .post("learning-path/assign-users", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "learning path",
                        log: `${selectedUsers?.length} users assigned to ${pathData?.name} successfully.`,
                    });
                    toast.success("Users Assigned Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    getLearningPath(params?.path_id);
                    // navigate(`/dashboard/learning-path-view/${params?.path_id}`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will assign this learning path to selected user groups.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Assign Users to Learning path</CardTitle>
                            <CardDescription>
                                Click on select button to select any user for learning path. Click Add users when
                                you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{selectedUsers?.length} User Selected</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Profile</th>
                                    <th>User Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {usersList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td style={{ width: "70px" }}>
                                            <Avatar>
                                                <AvatarImage src={row.picture_url} />
                                                <AvatarFallback>
                                                    {row.first_name[0]}
                                                    {row.last_name[0]}
                                                </AvatarFallback>
                                            </Avatar>
                                        </td>
                                        <td>
                                            {row.first_name} {row.last_name}
                                        </td>
                                        <td>{row?.email}</td>
                                        <td>{row?.role}</td>
                                        <td>
                                            {selectedUsers?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                        {selectedUsers?.length > 0 && (
                            <Button onClick={() => setOpenAlert(true)}>
                                <i className="fa-solid fa-floppy-disk"></i> Add Users
                            </Button>
                        )}
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AssignUsersToPath;
