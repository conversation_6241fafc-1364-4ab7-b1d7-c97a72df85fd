import { Button } from "@/components/ui/button";
import { Surface } from "@/components/ui/surface";
import { Tooltip } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { forwardRef } from "react";

const ToolbarWrapper = forwardRef(
    ({ shouldShowContent = true, children, isVertical = false, className, ...rest }, ref) => {
        const toolbarClassName = cn(
            "tw-inline-flex tw-h-full tw-gap-0.5 tw-leading-none tw-text-black",
            isVertical ? "tw-flex-col tw-p-2" : "tw-flex-row tw-items-center tw-p-1",
            className,
        );

        return (
            shouldShowContent && (
                <Surface className={toolbarClassName} {...rest} ref={ref}>
                    {children}
                </Surface>
            )
        );
    },
);

ToolbarWrapper.displayName = "Toolbar";

const ToolbarDivider = forwardRef(({ horizontal, className, ...rest }, ref) => {
    const dividerClassName = cn(
        "tw-bg-neutral-200 dark:tw-bg-neutral-800",
        horizontal
            ? "tw-my-1 tw-h-[1px] tw-w-full tw-min-w-[1.5rem] first:tw-mt-0 last:tw-mt-0"
            : "tw-mx-1 tw-h-full tw-min-h-[1.5rem] tw-w-[1px] first:tw-ml-0 last:tw-mr-0",
        className,
    );

    return <div className={dividerClassName} ref={ref} {...rest} />;
});

ToolbarDivider.displayName = "Toolbar.Divider";

const ToolbarButton = forwardRef(
    (
        {
            children,
            buttonSize = "icon",
            variant = "ghost",
            className,
            tooltip,
            tooltipShortcut,
            activeClassname,
            ...rest
        },
        ref,
    ) => {
        const buttonClass = cn("tw-w-auto tw-min-w-[2rem] tw-gap-1 tw-px-2", className);

        const content = (
            <Button
                activeClassname={activeClassname}
                className={buttonClass}
                variant={variant}
                buttonSize={buttonSize}
                ref={ref}
                {...rest}
            >
                {children}
            </Button>
        );

        if (tooltip) {
            return (
                <Tooltip title={tooltip} shortcut={tooltipShortcut}>
                    {content}
                </Tooltip>
            );
        }

        return content;
    },
);

ToolbarButton.displayName = "ToolbarButton";

export const Toolbar = {
    Wrapper: ToolbarWrapper,
    Divider: ToolbarDivider,
    Button: ToolbarButton,
};
