import { useGetQuizList } from "@/react-query/quizz";
import moment from "moment";
import { useMemo, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const defaultFilterState = {
    quiz_title: "",
    type: "",
    course_id: "",
    schedule_date: "",
};

const AssignedQuizzes = () => {
    const navigate = useNavigate();
    const quizData = useGetQuizList();
    const quizList = quizData.data?.data ?? [];
    const loginToken = localStorage.getItem("login_token");
    const typeOptions = useMemo(() => {
        return [...new Set(quizList.map((quiz) => quiz.type))];
    }, [quizList]);
    const [filterState, setFilterState] = useState(defaultFilterState);
    const [appliedFilterState, setAppliedFilterState] = useState(defaultFilterState);

    const filteredQuizList = useMemo(() => {
        if (Object.values(filterState).every((val) => val === "")) return quizList;
        return quizList.filter((quiz) => {
            const matchesSearch = filterState?.quiz_title
                ? quiz.title.toLowerCase().includes(filterState.quiz_title.toLowerCase())
                : true;
            const matchesType = filterState?.type ? quiz.type === filterState.type : true;
            const matchesSchedule = filterState?.schedule_date
                ? quiz.schedule_date === filterState.schedule_date
                : true;
            return matchesSearch && matchesType && matchesSchedule;
        });
    }, [quizList, filterState]);

    const handleSearchClear = () => {
        setFilterState(defaultFilterState);
        setAppliedFilterState(defaultFilterState);
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setAppliedFilterState((prev) => ({ ...prev, [name]: value }));
    };

    const onSearch = () => setFilterState(appliedFilterState);

    const onRedirect = (row) => {
        navigate(`/dashboard/quiz-create/${row?.id}`);
    };

    return (
        <>
            <div>
                <div className="tw-flex tw-justify-between">
                    <h4 className="tw-text-xl tw-font-semibold">
                        <i className="fa-solid fa-gamepad"></i> Assigned Quizzes
                    </h4>
                </div>
                <div className="page_filters tw-mt-5 tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label htmlFor="">Quiz Title</label>
                        <input
                            type="text"
                            name="quiz_title"
                            value={appliedFilterState?.quiz_title}
                            onChange={onChangeHandle}
                            placeholder="Search by quiz title ..."
                        />
                    </div>
                    <div className="input_group">
                        <label htmlFor="">Type</label>
                        <select id="" name="type" value={appliedFilterState?.type} onChange={onChangeHandle}>
                            <option value=""> - Choose Type - </option>
                            {typeOptions.map((type) => (
                                <option key={type} value={type}>
                                    {type}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="input_group">
                        <label htmlFor="">Schedule Date</label>
                        <input
                            type="date"
                            name="schedule_date"
                            value={appliedFilterState?.schedule_date}
                            onChange={onChangeHandle}
                        />
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={handleSearchClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>Quiz</th>
                                <th>Title</th>
                                <th>Type</th>
                                <th>Access</th>
                                <th>Course</th>
                                <th>Question</th>
                                <th>Creation Date</th>
                                <th>Schedule Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredQuizList.length == 0 ? (
                                <tr>
                                    <td colSpan={9}>
                                        <div className="tw-flex tw-h-40 tw-items-center tw-justify-center">
                                            <h2 className="tw-text-md tw-line-clamp-2 tw-font-semibold">
                                                No Quizzes Found
                                            </h2>
                                        </div>
                                    </td>
                                </tr>
                            ) : (
                                filteredQuizList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td onClick={() => onRedirect(row)}>
                                            <div className="table_image_alpha">
                                                <img
                                                    src={row?.thumbnail_img || "/assets/course-placeholder.png"}
                                                    alt=""
                                                />
                                            </div>
                                        </td>
                                        <td>{row?.title}</td>
                                        <td>{row?.type}</td>
                                        <td>
                                            <div className="chips_group">
                                                <p>{row?.is_public ? "Public" : "Private"}</p>
                                            </div>
                                        </td>
                                        <td>
                                            {row?.lms_course?.course_title?.length > 30
                                                ? `${row?.lms_course?.course_title?.slice(0, 30)} ...`
                                                : row?.lms_course?.course_title}
                                        </td>
                                        <td>{row?.components?.length}</td>
                                        <td>{moment(row?.createdAt).format("LL")}</td>
                                        <td>{moment(row?.schedule_date).format("LL")}</td>
                                        <td>
                                            <Link to={`/dashboard/quiz-create/${row?.id}`}>
                                                <button className="selected_btn">
                                                    <i className="fa-solid fa-edit"></i> Edit
                                                </button>
                                            </Link>{" "}
                                            <Link
                                                to={`https://lms-course-builder.vercel.app/dashboard/quiz-config/${row?.id}?token=${loginToken}`}F
                                                target="_blank"
                                            >
                                                <button className="selected_btn">
                                                    <i className="fa-solid fa-gears"></i> Configure
                                                </button>
                                            </Link>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </>
    );
};

export default AssignedQuizzes;
