import { Button, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { useLogin } from "@/react-query/auth/login";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

let sectors = [
    { value: "technology", name: "Technology" },
    { value: "finance", name: "Finance" },
    { value: "healthcare", name: "Healthcare" },
    { value: "retail", name: "Retail" },
    { value: "education", name: "Education" },
    { value: "manufacturing", name: "Manufacturing" },
    { value: "energy", name: "Energy" },
    { value: "real_estate", name: "Real Estate" },
    { value: "transportation", name: "Transportation" },
    { value: "telecommunications", name: "Telecommunications" },
    { value: "hospitality", name: "Hospitality" },
    { value: "food_beverage", name: "Food & Beverage" },
    { value: "media_entertainment", name: "Media & Entertainment" },
    { value: "agriculture", name: "Agriculture" },
    { value: "non_profit", name: "Non-Profit" },
    { value: "government", name: "Government" },
    { value: "consulting", name: "Consulting" },
    { value: "construction", name: "Construction" },
    { value: "legal", name: "Legal" },
];

const formSchema = z.object({
    company_logo: z.string().url(),
    company_name: z.string({ required_error: "Company name is required" }).trim().min(1, "Company name is required"),
    employe_size: z.coerce.number().default(2),
    company_sector: z.string(),
});

const SignupSteps = () => {
    const loginFn = useLogin();
    const navigate = useNavigate();
    const [isPending, setIsPending] = useState(false);

    const [organizationDetails, setOrganizationDetails] = useState({
        company_logo: "",
        company_name: "",
        employe_size: "2",
        company_sector: "",
    });

    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const user = useSelector((state) => state.AuthReducer?.onUserData);

    useEffect(() => {
        if (Object.keys(user ?? {}).length > 0) {
            if (user?.role_category === "sub-user") {
                navigate("/dashboard");
            } else if (user?.role_category === "user") {
                navigate(user?.organizationDetails?.signup_completed === true ? "/dashboard/" : "/signup-steps");
            } else if (user?.role_category === "administrator") {
                navigate("/dashboard/");
            }
        }
    }, [user]);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setOrganizationDetails({ ...organizationDetails, [name]: value });
        if (name == "company_logo") {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "ORGANIZATION-PROFILE");
            onFileUpload(data);
        }
    };

    const RemoveImage = () => {
        setOrganizationDetails({ ...organizationDetails, company_logo: "" });
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setOrganizationDetails({ ...organizationDetails, company_logo: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setOrganizationDetails({ ...organizationDetails, company_logo: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setOrganizationDetails({ ...organizationDetails, company_logo: "" });
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();
        try {
            setIsPending(true);
            const data = formSchema.safeParse(organizationDetails);
            if (!data.success) return toast.error(data.error.issues[0].message);

            const updatedData = (
                await tanstackApi.put("organization/update-signup-steps", {
                    step: "define",
                    data: { role_code: "company" },
                })
            ).data;

            const orgData = updatedData.data.organization_info;

            const payload = {
                step: "about",
                data: {
                    company_logo: orgData.about.company_logo,
                    company_name: orgData.about.company_name,
                    employe_size: orgData.about.employe_size,
                    company_sector: orgData.about.company_sector,
                },
            };

            localStorage.setItem(
                "org_data",
                JSON.stringify({
                    ...orgData,
                    about: payload,
                    define: {
                        role_code: "company",
                    },
                }),
            );

            await tanstackApi.put("organization/update-signup-steps", { ...payload }).then((res) => {
                toast.success("Organization Registered", {
                    description: res?.data?.message,
                });
                loginFn.mutate({
                    email: localStorage.getItem("username"),
                    password: localStorage.getItem("password"),
                });
            });
        } catch (error) {
            toast.error("Something went wrong", {
                description: error?.response?.data?.message,
            });
        } finally {
            setIsPending(false);
        }
    };

    return (
        <div className="tw-flex tw-h-[100vh] tw-items-center tw-justify-center">
            <Card>
                <CardHeader>
                    <CardTitle>Organization details</CardTitle>
                    <CardDescription>
                        Add Organization title, banner etc here. Click save when you&apos;re done.
                    </CardDescription>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-mt-0 tw-flex tw-flex-col tw-items-center tw-gap-2">
                            <div className="tw-h-[200px] tw-w-[400px] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                                <img
                                    className="tw-h-full tw-w-full tw-rounded-md tw-object-cover"
                                    src={organizationDetails?.company_logo || "/assets/thumbnail.png"}
                                />
                            </div>
                            <div className="tw-flex tw-gap-2">
                                {!organizationDetails?.company_logo && (
                                    <Label
                                        htmlFor="company_logo"
                                        className={cn(
                                            buttonVariants({ variant: "outline" }),
                                            "aspect-square max-sm:p-0 tw-cursor-pointer",
                                        )}
                                    >
                                        <Upload
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <input
                                            onChange={onChangeHandle}
                                            type="file"
                                            style={{ display: "none" }}
                                            id="company_logo"
                                            name="company_logo"
                                            accept="image/*"
                                        />
                                        <div className="max-sm:sr-only">
                                            {load ? "Uploading" : "Upload Organization Logo"}{" "}
                                            {load ? `${uploaded}%` : null}
                                        </div>
                                    </Label>
                                )}
                                {organizationDetails?.company_logo && (
                                    <Button
                                        variant="outline"
                                        className="aspect-square max-sm:p-0"
                                        onClick={RemoveImage}
                                    >
                                        <X
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <Label className="max-sm:sr-only">Remove</Label>
                                    </Button>
                                )}
                            </div>
                        </div>
                        <div className="tw-space-y-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="company_name">Organization Name</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={organizationDetails?.company_name}
                                    name="company_name"
                                    id="company_name"
                                    placeholder="Enter Organization name here"
                                />
                            </div>
                        </div>
                        <div className="tw-space-y-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Organization Type</Label>
                                <SelectNative
                                    onChange={onChangeHandle}
                                    value={organizationDetails?.company_sector}
                                    name="company_sector"
                                >
                                    <option value="">- Sector -</option>
                                    {sectors?.map((section, idx) => (
                                        <option value={section?.value} key={idx}>
                                            {section?.name}
                                        </option>
                                    ))}
                                </SelectNative>
                            </div>
                        </div>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        <Button disabled={isPending} variant="outline" onClick={() => navigate("/")}>
                            <i className="fa-solid fa-left-long"></i> Back to Login
                        </Button>

                        <Button disabled={isPending} onClick={onDataSubmit}>
                            <i className="fa-solid fa-circle-check"></i> Go To Dashboard
                        </Button>
                    </div>
                </CardFooter>
            </Card>
        </div>
    );
};

export default SignupSteps;
