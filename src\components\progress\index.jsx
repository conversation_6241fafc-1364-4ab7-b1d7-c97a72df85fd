import Box from "@mui/material/Box";
import CircularProgress from "@mui/material/CircularProgress";
import Typography from "@mui/material/Typography";

export function ProgressCustom({ progress }) {
    return (
        <Box sx={{ position: "relative", display: "inline-flex" }}>
            <CircularProgress
                sx={{
                    color: "#00AFF0", // Replace with your custom color
                }}
                variant="determinate"
                value={progress}
            />
            <Box
                sx={{
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: "absolute",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <Typography variant="caption" component="div" sx={{ color: "#000" }}>
                    {`${Math.round(progress)}%`}
                </Typography>
            </Box>
        </Box>
    );
}
