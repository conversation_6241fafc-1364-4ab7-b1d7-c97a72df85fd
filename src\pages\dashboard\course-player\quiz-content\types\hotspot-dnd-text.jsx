import ContentSlideLayout from "@/pages/dashboard/course-player/quiz-content/layouts/ContentSlideLayout";
import { useEffect, useRef, useState } from "react";
import Draggable from "react-draggable";
import { toast } from "sonner";

export default function HotspotDNDText({ handleNextSlide, handlePrevSlide, className, data, sequence, template }) {
    const containerRef = useRef(null);
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
    const [items, setItems] = useState([]);
    const [zones, setZones] = useState([]);

    const isWithinZone = (itemPosition, zone) => {
        const tolerance = 50; // Tolerance for snapping
        const isXWithin = Math.abs(itemPosition.x - zone.x) <= tolerance;
        const isYWithin = Math.abs(itemPosition.y - zone.y) <= tolerance;
        return isXWithin && isYWithin;
    };

    const updateContainerSize = () => {
        if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            setContainerSize({ width: rect.width, height: rect.height });
        }
    };

    useEffect(() => {
        updateContainerSize();
    }, []);

    useEffect(() => {
        window.addEventListener("resize", updateContainerSize);
        return () => {
            window.removeEventListener("resize", updateContainerSize);
        };
    }, []);

    const getAbsolutePosition = (relativeX, relativeY) => {
        return {
            x: relativeX * containerSize.width,
            y: relativeY * containerSize.height,
        };
    };

    const handleStop = (e, data, index) => {
        const updatedItems = [...items];
        updatedItems[index].x = data.x / containerSize.width; // Save relative x
        updatedItems[index].y = data.y / containerSize.height; // Save relative y

        const itemAbsolutePos = { x: data.x, y: data.y };

        // Find the nearest zone (if any)
        const nearestZone = zones
            .map((zone) => ({
                ...zone,
                ...getAbsolutePosition(zone.x, zone.y), // Convert zone to absolute position
            }))
            .find((zone) => isWithinZone(itemAbsolutePos, zone));

        if (nearestZone) {
            const isCorrect = updatedItems[index].correct === nearestZone.zone;

            if (isCorrect) {
                toast.success(`✅ Item "${updatedItems[index].name}" placed in the correct zone: ${nearestZone.zone}.`);
            } else {
                toast.error(`❌ Item "${updatedItems[index].name}" is in the wrong zone: ${nearestZone.zone}.`);
            }
        } else {
            // alert(
            //     `🚫 Item "${updatedItems[index].name}" was dropped outside of any zone.`
            // );
        }

        setItems(updatedItems);
    };
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data: data?.name ?? "Complete the gaps using the appropriate words from the box.",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div
                className="tw-relative tw-grid tw-grid-cols-1 tw-grid-rows-1"
                style={{ height: "100%", aspectRatio: "12 / 7" }}
            >
                <img
                    src={data?.question_thumbnail.replace("/assets", "/quiz")}
                    className="tw-h-full tw-w-full tw-object-contain"
                    alt=""
                />
                {items?.map((item, index) => {
                    const { x, y } = getAbsolutePosition(item.x, item.y);
                    return (
                        <Draggable key={index} position={{ x, y }} onStop={(e, data) => handleStop(e, data, index)}>
                            <div
                                className="tw-absolute tw-cursor-pointer tw-rounded"
                                style={{
                                    width: "100px",
                                    height: "50px",
                                    backgroundColor: "#add8e6",
                                    textAlign: "center",
                                    lineHeight: "50px",
                                }}
                            >
                                {item.name}
                            </div>
                        </Draggable>
                    );
                })}
            </div>
        </ContentSlideLayout>
    );
}
