import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { tanstackApi } from "@/react-query/api";
import { FolderOpen } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const QuestionPoolForQuiz = ({ open, setOpen, setComponentsArray, componentsArray, onSlideEdit }) => {
    const navigate = useNavigate();
    const [poolList, setPoolList] = useState([]);
    const [selectedItems, setSelectedItems] = useState([]);
    const [poolID, setPoolID] = useState(null);
    const [poolData, setPoolData] = useState(null);

    useEffect(() => {
        getPoolList();
    }, []);

    useEffect(() => {
        if (poolID?.id !== undefined) {
            getPoolData(poolID?.id);
        }
    }, [poolID]);

    const getPoolData = async (payload) => {
        await tanstackApi
            .get(`question-pool/get/${payload}`)
            .then((res) => {
                setPoolData(res?.data?.data);
            })
            .catch((err) => {
                setPoolData(null);
            });
    };

    const getPoolList = async () => {
        await tanstackApi
            .get("question-pool/list", { params: { is_public: localStorage.getItem("level") == "levelOne" } })
            .then((res) => {
                setPoolList(res?.data?.data);
            })
            .catch((err) => {
                setPoolList([]);
            });
    };

    const onQuestionSelection = (questionID) => {
        if (selectedItems?.includes(questionID)) {
            setSelectedItems(selectedItems?.filter((dt) => dt !== questionID));
        } else {
            setSelectedItems([...selectedItems, questionID]);
        }
    };

    const onImportDone = (e) => {
        e.preventDefault();

        if (selectedItems?.length == 0) {
            toast.warning("Please select questions");
            return false;
        }

        let data = poolData?.questions
            ?.filter((dt) => selectedItems?.includes(dt?.id))
            ?.map((dt) => dt?.options?.[0]?.option_json);
        setComponentsArray([...componentsArray, ...data]);
        setOpen(false);
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-5xl">
                    {poolData ? (
                        <div>
                            <div>
                                <DialogHeader>
                                    <DialogTitle>
                                        <h1 className="tw-text-2xl">Select Questions from pools</h1>
                                    </DialogTitle>
                                    <DialogDescription>
                                        Import questions from question pools mentioned below. And click on save button.
                                    </DialogDescription>
                                </DialogHeader>
                            </div>
                        </div>
                    ) : (
                        <div className="tw-flex tw-items-end tw-justify-between">
                            <div>
                                <DialogHeader>
                                    <DialogTitle>
                                        <h1 className="tw-text-2xl">Question pools</h1>
                                    </DialogTitle>
                                    <DialogDescription>
                                        Import questions from question pools mentioned below. And click on save button.
                                    </DialogDescription>
                                </DialogHeader>
                            </div>
                            <div>
                                <Button onClick={() => navigate(`/dashboard/question-pool`)} variant="outline">
                                    <i className="fa-solid fa-square-arrow-up-right"></i> Question Pool
                                </Button>
                            </div>
                        </div>
                    )}
                    {poolData == null && (
                        <div className="tw-flex tw-justify-center">
                            <div className="tw-grid tw-w-full tw-grid-cols-1 tw-gap-3">
                                {poolList?.map((content, idx) => (
                                    <div
                                        key={idx}
                                        onClick={() => setPoolID(content)}
                                        className="tw-flex tw-cursor-pointer tw-items-center tw-gap-4 tw-rounded-xl tw-border-[1px] tw-p-3 hover:tw-bg-slate-100"
                                    >
                                        <FolderOpen size={30} className="tw-text-slate-400" />
                                        <div className="tw-space-y-2">
                                            <Label className="tw-font-lexend tw-text-slate-600">{content?.name}</Label>
                                            <div className="tw-flex tw-flex-wrap tw-gap-1">
                                                {content?.tags?.map((tag, idx) => (
                                                    <Badge
                                                        variant={"outline"}
                                                        className="tw-font-mono tw-text-slate-400"
                                                        key={idx}
                                                    >
                                                        {tag}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                    {poolData !== null && (
                        <>
                            <div className="tw-flex tw-items-center tw-justify-between tw-gap-2">
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    <FolderOpen size={27} className="tw-text-slate-800" />
                                    <Label className="tw-font-lexend tw-text-slate-800">{poolData?.name}</Label>
                                </div>
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    <Button variant="outline" onClick={() => setPoolData(null)}>
                                        <i className="fa-solid fa-left-long"></i> Back
                                    </Button>
                                    <Button onClick={onImportDone}>
                                        <i className="fa-solid fa-circle-check"></i> Import to Quiz
                                    </Button>
                                </div>
                            </div>
                            <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Question</th>
                                            <th>Type</th>
                                            <th>Diffculty</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {poolData?.questions?.map((row, idx) => (
                                            <tr key={idx}>
                                                <td>{row.question}</td>
                                                <td>{row?.question_type}</td>
                                                <td>
                                                    <Badge variant="outline" className={"tw-capitalize"}>
                                                        {row?.difficulty_level}
                                                    </Badge>
                                                </td>
                                                <td>
                                                    {selectedItems?.includes(row?.id) ? (
                                                        <Button onClick={() => onQuestionSelection(row?.id)}>
                                                            Selected
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            onClick={() => onQuestionSelection(row?.id)}
                                                            variant="outline"
                                                        >
                                                            Choose
                                                        </Button>
                                                    )}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default QuestionPoolForQuiz;
