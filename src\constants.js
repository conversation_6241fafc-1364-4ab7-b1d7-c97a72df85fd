export const CONSTANTS = {
    getAPI: function () {
        // return "https://devapifarmsanta.infoware.xyz/"; //Development
        // return "https://lms.infoware.xyz/api/"; //UAT
        return "https://appapi.amieyaa.com/api/"; //Pre Production
    },

    getImageAPI: function () {
        return "https://s3.ap-south-1.amazonaws.com/infowarelms/";
    },

    getDateTimeFormate: function (date) {
        const month = date?.slice(5, 7);
        const cYear = date?.slice(0, 4);
        const cDate = date?.slice(8, 10);
        let cMonth = "";
        var fullInfo = "";
        if (month === "01") {
            cMonth = "Jan";
        } else if (month === "02") {
            cMonth = "Feb";
        } else if (month === "03") {
            cMonth = "Mar";
        } else if (month === "04") {
            cMonth = "Apr";
        } else if (month === "05") {
            cMonth = "May";
        } else if (month === "06") {
            cMonth = "Jun";
        } else if (month === "07") {
            cMonth = "Jul";
        } else if (month === "08") {
            cMonth = "Aug";
        } else if (month === "09") {
            cMonth = "Sep";
        } else if (month === "10") {
            cMonth = "Oct";
        } else if (month === "11") {
            cMonth = "Nov";
        } else if (month === "12") {
            cMonth = "Dec";
        }
        if (date) {
            return (fullInfo = `${cMonth} ${cDate}, ${cYear}`);
        } else {
            return "";
        }
    },

    getTimeFormate: function (date) {
        const cHr = date?.slice(11, 13);
        const cMi = date?.slice(14, 16);
        var cTime = "";
        if (cHr === "13") {
            cTime = `1:${cMi} PM`;
        } else if (cHr === "14") {
            cTime = `2:${cMi} PM`;
        } else if (cHr === "15") {
            cTime = `3:${cMi} PM`;
        } else if (cHr === "16") {
            cTime = `4:${cMi} PM`;
        } else if (cHr === "17") {
            cTime = `5:${cMi} PM`;
        } else if (cHr === "18") {
            cTime = `6:${cMi} PM`;
        } else if (cHr === "19") {
            cTime = `7:${cMi} PM`;
        } else if (cHr === "20") {
            cTime = `8:${cMi} PM`;
        } else if (cHr === "21") {
            cTime = `9:${cMi} PM`;
        } else if (cHr === "22") {
            cTime = `10:${cMi} PM`;
        } else if (cHr === "23") {
            cTime = `11:${cMi} PM`;
        } else if (cHr === "24") {
            cTime = `12:${cMi} AM`;
        } else {
            cTime = `${cHr}:${cMi} AM`;
        }
        if (date) {
            return cTime;
        }
    },

    dateGetter: function (d) {
        const dateObj = new Date(d * 1000).toDateString().split(" ");
        const dateString = `${dateObj[2]} ${dateObj[1]}, ${dateObj[3]}`;
        return dateString;
    },

    timeGetter: function (d, t) {
        const dateTimeObj = new Date(d * 1000);
        const dateObj = dateTimeObj.toDateString().split(" ");
        const dateString = t
            ? `${
                  dateTimeObj.toLocaleTimeString().split(":")[0] > "11"
                      ? dateTimeObj.toLocaleTimeString().split(":")[0] - 12
                      : dateTimeObj.toLocaleTimeString().split(":")[0]
              } : ${dateTimeObj.toLocaleTimeString().split(":")[1]} ${dateTimeObj.toLocaleTimeString().split(":")[0] > "11" ? " PM" : " AM"}`
            : `${dateObj[1]} ${dateObj[2]}, ${dateObj[3]} ${dateTimeObj.toLocaleTimeString()}${dateTimeObj.toLocaleTimeString().split(":")[0] > "11" ? " PM" : " AM"}`;
        return dateString;
    },
};
