import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const AddCoursesToBundle = ({ bundleData, getBundle }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [courseList, setCourseList] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedCourses, setSelectedCourses] = useState([]);

    useEffect(() => {
        getCourses();
    }, []);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    useEffect(() => {
        if (bundleData !== null) {
            setSelectedCourses(bundleData?.course_list || []);
        }
    }, [bundleData]);

    const onCourseSelection = (item) => {
        if (selectedCourses?.includes(item)) {
            setSelectedCourses(selectedCourses?.filter((dt) => dt !== item));
        } else {
            setSelectedCourses([...selectedCourses, item]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (params?.bundle_id !== undefined) {
            const payload = {
                course_bundle_id: params?.bundle_id,
                step: "course_bundle_courses",
                data: {
                    course_list: selectedCourses,
                },
            };

            await tanstackApi
                .put("course-bundle/creation/set-details", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "course bundles",
                        log: `${selectedCourses?.length} courses added to ${bundleData?.name} successfully.`,
                    });
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    getBundle(params?.bundle_id);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected courses in your bundle&apos;s details.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Select courses for bundle</CardTitle>
                            <CardDescription>
                                Click on select button to select any course in bundle. Click save when you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{selectedCourses?.length} Course Selected</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Banner</th>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Category</th>
                                    <th>Access</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {courseList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td
                                            style={{ width: "130px" }}
                                            onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}
                                        >
                                            <div className="table_image_alpha">
                                                <img
                                                    src={row?.course_banner_url || "/assets/course-placeholder.png"}
                                                    alt=""
                                                />
                                            </div>
                                        </td>
                                        <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                            {row?.course_title}
                                        </td>
                                        <td>{row?.is_scorm ? "SCORM" : "Combined"}</td>
                                        <td>{row?.lms_course_category?.category_name}</td>
                                        <td>{row?.is_public ? "Public" : "Private"}</td>
                                        <td>
                                            {selectedCourses?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        <Button onClick={() => setOpenAlert(true)}>
                            <i className="fa-solid fa-floppy-disk"></i> Update
                        </Button>
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AddCoursesToBundle;
