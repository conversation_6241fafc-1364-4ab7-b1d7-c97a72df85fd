import { motion, useInView } from "framer-motion";
import { useRef } from "react";

import ContentSlideLayout from "../layouts/ContentSlideLayout";

const LongAnswer = ({ handleNextSlide, handlePrevSlide, className, data, sequence, template }) => {
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { y: "-50%", scaleY: 0.5 },
        inView: { y: 0, scaleY: 1 },
    };
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data:
                    data?.name ??
                    "In real life engineers need to make many alterations in order to end up with a final product. Thus, redesign and make the appropriate modifications and improvements on your model",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div ref={ref} className="tw-relative tw-z-10 tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem]">
                <motion.div
                    variants={variants}
                    initial="initial"
                    animate={inView ? "inView" : "initial"}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    className="tw-relative"
                >
                    <label
                        htmlFor="textarea"
                        className="tw-rounded-md tw-bg-white tw-px-3 tw-text-left tw-text-3xl tw-text-[#FE5C96]"
                        style={{
                            color: data?.styles?.question?.color,
                            fontFamily: data?.styles?.question?.fontFamily,
                            fontSize: data?.styles?.question?.fontSize,
                            lineHeight: 1.5,
                        }}
                    >
                        Write your ideas here
                    </label>
                    <textarea
                        id="textarea"
                        rows={6}
                        className="tw-w-full tw-resize-none tw-rounded-md tw-bg-transparent tw-p-2 tw-text-[24px] tw-text-black"
                        style={{
                            color: data?.styles?.answer?.color,
                            fontFamily: data?.styles?.answer?.fontFamily,
                            fontSize: data?.styles?.answer?.fontSize,
                            backgroundColor: data?.styles?.answer?.backgroundColor,
                            borderColor: data?.styles?.answer?.borderColor,
                            borderWidth: data?.styles?.answer?.borderWidth,
                            borderStyle: data?.styles?.answer?.borderStyle,
                        }}
                    />
                </motion.div>
            </div>
        </ContentSlideLayout>
    );
};

export default LongAnswer;
