import courseImg from "@/assets/images/courseImg.png";
import AlertSnackbar from "@/components/alert";
import DeleteModal from "@/components/modal/delete";
import { PermissionGranted } from "@/lib/helpers/utility";
import { fetchCoursesList, updateCourseStatus } from "@/redux/course/action";
import { onReEnrollRequest } from "@/redux/users/action";
import DateRangeIcon from "@mui/icons-material/DateRange";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import ViewListIcon from "@mui/icons-material/ViewList";
import WindowIcon from "@mui/icons-material/Window";
import { Box, Button, FormControl, IconButton, InputLabel, MenuItem, Select } from "@mui/material";
import Grid from "@mui/material/Grid";
import Pagination from "@mui/material/Pagination";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const CourseList = () => {
    const navigate = useNavigate();
    const [toogleView, setToogleView] = useState(1);
    const [search, setSearch] = useState("");
    const [filteredArray, setFilteredArray] = useState([]);
    const [deleteModal, setDeleteModal] = useState(false);
    const [courseId, setCourseId] = useState("");
    const [recordsCountOnTable, setRecordsCountOnTable] = useState(10);
    const [allCoursesList, setAllCoursesList] = useState([]);

    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(fetchCoursesList());
    }, []);

    const coursesList = useSelector((state) => state.CourseReducers.coursesList);
    const AlertInfo = useSelector((state) => state.alertReducer)?.alertInfoObj;

    useEffect(() => {
        setFilteredArray(coursesList);
    }, [coursesList]);

    useEffect(() => {
        const filtered = coursesList?.filter((course) => {
            if (search === "") {
                return course;
            } else if (course?.course_title?.toLowerCase()?.includes(search?.toLowerCase())) {
                return course;
            }
        });
        setFilteredArray(filtered);
    }, [search]);

    const [pageCount, setPageCount] = useState(0);

    useEffect(() => {
        if (filteredArray && filteredArray.length > 0) {
            var modulas = filteredArray.length % recordsCountOnTable;
            var count = (filteredArray.length - modulas) / recordsCountOnTable;
            if (modulas !== 0) {
                setPageCount(count + 1);
            } else {
                setPageCount(count);
            }

            var data = filteredArray
                ?.map((ptnt, idx) => {
                    return { ...ptnt, number: idx + 1 };
                })
                ?.slice(0, recordsCountOnTable * 1);
            setAllCoursesList(data);
        } else {
            setAllCoursesList([]);
            setPageCount(0);
        }
    }, [filteredArray]);

    const onPageChange = (e, number) => {
        var data = filteredArray
            ?.map((ptnt, idx) => {
                return { ...ptnt, number: idx + 1 };
            })
            ?.slice(recordsCountOnTable * (number - 1), recordsCountOnTable * number);
        setAllCoursesList(data);
    };

    const onReEnroll = (course) => {
        var data = {
            courseId: course?.id,
        };
        dispatch(onReEnrollRequest(data));
    };

    const onRedirectToCourse = (course) => {
        if (localStorage.getItem("level") === "levelTwo") {
            window.location.href = `/dashboard/course-details/${course?.id}`;
        } else {
            if (course?.is_scorm) {
                window.location.href = `/dashboard/course-details/play-scorm/${course?.id}`;
            } else {
                navigate(`/course-details/view/${course?.id}/${0}/${0}`);
            }
        }
    };

    const handleCreateCourse = () => {
        navigate("/dashboard/course/creation");
        return;
    };

    const onHandleChange = (e, id) => {
        var data = {
            course_id: parseInt(id),
            status: e.target.value,
        };
        dispatch(updateCourseStatus(data));
    };
    return (
        <Box container sx={{ width: 1 }} style={{ padding: "0 1.4rem 1rem 1.4rem" }}>
            <div style={{ width: "100%", display: "flex", gap: "1rem" }}>
                {/* <input type="text" id="standard-basic"/> */}
                <div className="search">
                    <input
                        type="text"
                        className="searchTerm"
                        placeholder="Search Courses by Name"
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                    />
                    <button type="submit" className="searchButton">
                        <i className="fa fa-search"></i>
                    </button>
                </div>
                {/* <TextField value={search} onChange={(e) => setSearch(e.target.value)} id="standard-basic" style={{width: '27%'}} label="Search Courses by Name/ instructor name" fullWidth variant="standard" /> */}
                <div
                    onClick={() => setToogleView(1)}
                    style={{
                        display: "flex",
                        alignItems: "center",
                        color: toogleView === 1 ? "var(--input-primary-color)" : "rgba(104, 104, 104, 0.6)",
                        cursor: "pointer",
                    }}
                >
                    <Button
                        variant="text"
                        style={{ color: toogleView === 1 ? "var(--input-primary-color)" : "rgba(104, 104, 104, 0.6)" }}
                    >
                        <ViewListIcon />
                        &nbsp;List View
                    </Button>
                </div>

                <div
                    onClick={() => setToogleView(2)}
                    style={{
                        display: "flex",
                        alignItems: "center",
                        color: toogleView === 2 ? "var(--input-primary-color)" : "rgba(104, 104, 104, 0.6)",
                        cursor: "pointer",
                    }}
                >
                    <Button
                        variant="text"
                        style={{ color: toogleView === 2 ? "var(--input-primary-color)" : "rgba(104, 104, 104, 0.6)" }}
                    >
                        <WindowIcon />
                        &nbsp;Grid View
                    </Button>
                </div>
                {PermissionGranted("COURSES", "CREATE") && localStorage.getItem("role_category") == "user" && (
                    <>
                        <div className="align_self_center margin_all_auto margin_right_zero">
                            <span onClick={handleCreateCourse} className="btn_2 cc_btn_2 cc_cta_btn_main">
                                {"Create Your Course".toUpperCase()}
                            </span>
                        </div>
                    </>
                )}
            </div>

            <AlertSnackbar AlertInfo={AlertInfo} />
            {PermissionGranted("COURSES", "READ") && (
                <>
                    <Box sx={{ display: "flex", justifyContent: "right" }} mt={2}>
                        <Pagination
                            variant="outlined"
                            shape="rounded"
                            color="primary"
                            count={pageCount}
                            onChange={onPageChange}
                        />
                    </Box>

                    <Box container sx={{ width: 1 }} style={{ padding: "0.8rem 0" }}>
                        {/* <ReactSortable list={filteredArray} setList={setFilteredArray}> */}
                        {toogleView === 1 &&
                            allCoursesList.slice(0, 10)?.map((course, indx) => (
                                <div key={indx} className="courses_list_card">
                                    <div className="left">
                                        <a
                                            onClick={() => {
                                                onRedirectToCourse(course);
                                            }}
                                        >
                                            <img
                                                src={
                                                    course?.course_banner_url !== ""
                                                        ? course?.course_banner_url
                                                        : courseImg
                                                }
                                                style={{ height: "135px" }}
                                                alt=""
                                                loading="lazy"
                                            />
                                        </a>
                                        <div className="container">
                                            <div className="top_div">
                                                <div>
                                                    <h6
                                                        style={{
                                                            color: "rgba(0, 86, 210, 1)",
                                                            letterSpacing: "1px",
                                                            textTransform: "capitalize",
                                                        }}
                                                    >
                                                        {course?.course_title}
                                                    </h6>
                                                    {/* <span style={{ color: 'rgba(0, 187, 7, 1)' }}>{course?.lms_course_settings?.[0]?.course_accessed_by}</span> */}
                                                </div>
                                                <div>
                                                    <p
                                                        style={{
                                                            marginTop: "0.3rem",
                                                            letterSpacing: "1px",
                                                            color: "rgba(0, 86, 210, 1)",
                                                        }}
                                                    >
                                                        {course?.lms_course_category?.category_name}
                                                    </p>
                                                </div>
                                            </div>
                                            {/* <div>
                                <p style={{color: 'rgba(132, 132, 132, 0.7)', letterSpacing: '0.7px', margin: '0.5rem 0', lineHeight: '21.09px'}}>Gain confidence in your ability to understand and communicate financial results. Apply now. Learn to build, interpret, and analyze financial statements to drive decision-making.</p>
                            </div> */}
                                            <section>
                                                {/* <div>
                                    <p style={{color: 'rgba(132, 132, 132, 0.9)'}}><AccountCircleIcon style={{color: 'rgba(0, 86, 210, 1)'}}/>
                                        &nbsp;Learners: 528
                                    </p>
                                </div> */}
                                                {course?.lms_course_settings?.[0]?.expiration_date ? (
                                                    <div>
                                                        <p style={{ color: "rgba(132, 132, 132, 0.9)" }}>
                                                            <DateRangeIcon style={{ color: "rgba(0, 86, 210, 1)" }} />
                                                            Expiry Date :{" "}
                                                            {moment(
                                                                course?.lms_course_settings?.[0]?.expiration_date,
                                                            ).format("L")}
                                                        </p>
                                                    </div>
                                                ) : (
                                                    ""
                                                )}
                                                {/* <div>
                                    <p style={{color: 'rgba(132, 132, 132, 0.9)'}}><AccountCircleIcon style={{color: 'rgba(0, 86, 210, 1)'}}/>
                                        &nbsp;Users&nbsp;Enrolled:&nbsp;198&nbsp;Users
                                    </p>
                                </div>
                                <div>
                                    <p style={{color: 'rgba(132, 132, 132, 0.9)'}}><AccountCircleIcon style={{color: 'rgba(0, 86, 210, 1)'}}/>
                                        &nbsp;Learners: 528
                                    </p>
                                </div>
                                <div>
                                    <p style={{color: 'rgba(132, 132, 132, 0.9)'}}><AccountCircleIcon style={{color: 'rgba(0, 86, 210, 1)'}}/>
                                        &nbsp;10&nbsp;Weeks&nbsp;Courses&nbsp;/&nbsp;12&nbsp;Modules
                                    </p>
                                </div>
                                <div>
                                    <p style={{color: 'rgba(132, 132, 132, 0.9)'}}><AccountCircleIcon style={{color: 'rgba(0, 86, 210, 1)'}}/>
                                        &nbsp;Users&nbsp;Enrolled:&nbsp;198&nbsp;Users
                                    </p>
                                </div> */}
                                            </section>
                                        </div>
                                    </div>
                                    <div
                                        className="right"
                                        style={{
                                            marginRight: ".7rem",
                                            width: "150px",
                                            display: "flex",
                                            alignItems: "center",
                                        }}
                                    >
                                        {course.is_active !== true && (
                                            <div>
                                                {/* <Button style={{fontSize: '0.8rem'}} variant='text' small>View&nbsp;Report</Button> */}
                                                {localStorage.getItem("level") === "levelThree" && (
                                                    <Button
                                                        style={{ fontSize: "0.8rem" }}
                                                        variant="outlined"
                                                        small
                                                        onClick={() => onReEnroll(course)}
                                                    >
                                                        Re-Enroll
                                                    </Button>
                                                )}
                                                <IconButton
                                                // onClick={(e) => openDelete(course?.id)}
                                                >
                                                    <MoreVertIcon />
                                                </IconButton>
                                            </div>
                                        )}
                                        {/* {course?.
                                    
                                } */}
                                        <DeleteModal
                                            dataID={courseId}
                                            section="course-list"
                                            open={deleteModal}
                                            setOpen={setDeleteModal}
                                        />
                                        {/* {localStorage.getItem('level') === 'levelTwo' && <span style={{ color: 'rgba(0, 187, 7, 1)', letterSpacing: '1px', textTransform: 'capitalize', fontWeight:'500' }}>{course?.course_status}</span>} */}
                                        {localStorage.getItem("level") === "levelTwo" && (
                                            <div>
                                                <Grid style={{ width: "150px" }}>
                                                    <FormControl sx={{ width: "100%" }} size="small" variant="outlined">
                                                        <InputLabel id="demo-select-small">Course Status</InputLabel>
                                                        <Select
                                                            sx={{ width: "100%" }}
                                                            labelId="demo-select-small"
                                                            id="demo-select-small"
                                                            label="Course Status"
                                                            // name='user_type_code'
                                                            value={course?.course_status}
                                                            onChange={(e) => onHandleChange(e, course?.id)}
                                                        >
                                                            <MenuItem value="">
                                                                <em>- Select -</em>
                                                            </MenuItem>
                                                            <MenuItem value={"completed"}>
                                                                <p
                                                                    style={{
                                                                        color: "rgba(0, 187, 7, 1)",
                                                                        letterSpacing: "1px",
                                                                        textTransform: "capitalize",
                                                                        fontWeight: "500",
                                                                    }}
                                                                >
                                                                    Published
                                                                </p>
                                                            </MenuItem>
                                                            <MenuItem value={"pending"}>
                                                                <p
                                                                    style={{
                                                                        color: "#FF9800",
                                                                        letterSpacing: "1px",
                                                                        textTransform: "capitalize",
                                                                        fontWeight: "500",
                                                                    }}
                                                                >
                                                                    Pending
                                                                </p>
                                                            </MenuItem>
                                                            <MenuItem value={"deleted"}>
                                                                <p
                                                                    style={{
                                                                        color: "#BF3131",
                                                                        letterSpacing: "1px",
                                                                        textTransform: "capitalize",
                                                                        fontWeight: "500",
                                                                    }}
                                                                >
                                                                    Deleted
                                                                </p>
                                                            </MenuItem>
                                                            <MenuItem value={"hold"}>
                                                                <p
                                                                    style={{
                                                                        color: "#0766AD",
                                                                        letterSpacing: "1px",
                                                                        textTransform: "capitalize",
                                                                        fontWeight: "500",
                                                                    }}
                                                                >
                                                                    Hold
                                                                </p>
                                                            </MenuItem>
                                                            {/* {allRoles ? allRoles.map((role, index) => (
                                            )) : null} */}
                                                        </Select>
                                                    </FormControl>
                                                </Grid>
                                            </div>
                                        )}

                                        {/* {(course?.lms_course_settings.length > 0?.course?.lms_course_settings[0]?.pricing_details ==="") || (course?.lms_course_settings.length > 0?.course?.lms_course_settings[0]?.pricing_details?.is_free_course) ?
                                <div style={{ display: "flex", justifyContent: "flex-end", alignItems: "flex-end", height: "100%" ,}}>
                                    <p style={{ width: '100%', textAlign: 'right', marginRight: '0.5rem',color:'#34e363', fontWeight:'600'}}>Free Course&nbsp; */}
                                        {/* <b style={{ color: 'rgba(0, 86, 210, 1)' }}>
                                            {course?.lms_course_settings?.[0]?.pricing_details?.price_type}
                                            &nbsp;{course?.lms_course_settings?.[0]?.pricing_details?.course_price}
                                        </b> */}
                                        {/* </p>
                                </div>
                                    : 
                                <div style={{ display: "flex", justifyContent: "flex-end", alignItems: "flex-end", height: "100%" ,}}>
                                    <p >&nbsp;
                                        <b style={{ color: 'rgba(0, 86, 210, 1)' }}>
                                            {course?.lms_course_settings?.length>0?.course?.lms_course_settings[0]?.pricing_details?.price_type}
                                            &nbsp;{course?.lms_course_settings?.length>0?.course?.lms_course_settings[0]?.pricing_details !== null && course?.lms_course_settings[0]?.pricing_details !== "" ? (!course?.lms_course_settings[0]?.pricing_details?.is_free_course ? `Price : ${course?.lms_course_settings[0]?.pricing_details?. course_price} ${course?.lms_course_settings[0]?.pricing_details?.price_type}`: "This course is free!"): "Free Course"}
                                        </b>
                                    </p>
                                </div>} */}
                                    </div>
                                </div>
                            ))}
                        {/* </ReactSortable> */}
                        {toogleView === 2 && (
                            <div className="grid_container">
                                {/* <ReactSortable list={filteredArray} setList={setFilteredArray} className='grid_container'> */}
                                {allCoursesList?.map((course, indx) => (
                                    <a
                                        onClick={() => {
                                            onRedirectToCourse(course);
                                        }}
                                        className="course_list_grid"
                                        key={indx}
                                    >
                                        <div
                                            className="top"
                                            style={{
                                                boderRadius: "10px 10px 0 0",
                                                overflow: "hidden",
                                                backgroundImage: `url(${course?.course_banner_url == null ? courseImg : course?.course_banner_url})`,
                                            }}
                                        >
                                            <IconButton id="menu_icon">
                                                <MoreVertIcon />
                                            </IconButton>
                                            {/* <p>Price:&nbsp;{
                                            course?.lms_course_settings?.[0]?.pricing_details?.price_type}
                                            &nbsp;{course?.lms_course_settings?.[0]?.pricing_details?.course_price}
                                        </p> */}
                                        </div>
                                        <section>
                                            <div className="top_s">
                                                <h5>
                                                    {course?.course_title !== null ? (
                                                        <>
                                                            {course?.course_title?.slice(0, 20)}{" "}
                                                            {course?.course_title?.length > 20 ? "..." : ""}
                                                        </>
                                                    ) : (
                                                        "Undefined"
                                                    )}
                                                </h5>
                                                <p style={{ color: "rgba(0, 187, 7, 1)" }}>
                                                    {course?.lms_course_settings?.[0]?.course_accessed_by}
                                                </p>
                                            </div>
                                            {/* <p style={{ color: 'rgba(132, 132, 132, 1)' }}>Category:&nbsp;<b style={{ color: 'rgba(0, 86, 210, 1)' }}>{course?.lms_course_category?.category_name}</b></p> */}
                                            <p style={{ color: "rgba(132, 132, 132, 0.7)" }}>
                                                {course?.course_description}
                                            </p>
                                            <span>
                                                {course?.lms_course_settings?.[0]?.expiration_days ? (
                                                    <>{course?.lms_course_settings?.[0]?.expiration_days}&nbsp;Days</>
                                                ) : (
                                                    ""
                                                )}
                                            </span>
                                            {/* <div className="sec_a">
                                            <p><AccountCircleIcon style={{ color: 'var(--input-primary-color)' }} />&nbsp;Learner: 528</p>
                                            <p><AccountCircleIcon style={{ color: 'var(--input-primary-color)' }} />&nbsp;Instructor: 2</p>
                                        </div> */}
                                            <footer>
                                                <Button
                                                    variant="text"
                                                    style={{ color: "rgba(106, 106, 106, 1)", fontSize: "0.9rem" }}
                                                >
                                                    Edit
                                                </Button>
                                                {/* <Button variant='text' style={{ fontSize: '0.9rem' }}>View Report</Button> */}
                                            </footer>
                                        </section>
                                    </a>
                                ))}
                                {/* </ReactSortable> */}
                            </div>
                        )}
                    </Box>
                </>
            )}
        </Box>
    );
};

export default CourseList;
