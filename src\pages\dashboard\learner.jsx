import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import UserCoursesDash from "@/pages/dashboard/user/course";
import UserKPISDash from "@/pages/dashboard/user/kpi";
import LearnerProfileDash from "@/pages/dashboard/user/profile";
import UserScheduleDash from "@/pages/dashboard/user/schdeule";
import { useGetUsersLeaderboard } from "@/react-query/users";
import { fetchDashStatsReq } from "@/redux/dashboard/dash/action";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

const DashboardLearner = () => {
    const dispatch = useDispatch();
    // const dashboardStats = useSelector((state) => state.DashReducer.dashData);
    // const leaderboard = useGetUsersLeaderboard();
    // const userId = localStorage.getItem("userId");
    // const [leaderboardList, setLeaderboardList] = useState([]);
    const [badgeDeatils, setBadgeDeatils] = useState(null);

    useEffect(() => {
        dispatch(fetchDashStatsReq());
    }, []);

    // useEffect(() => {
    //     if (leaderboard.isSuccess) {
    //         const top10 = leaderboard.data.data.slice(0, 10).map((user, index) => ({ ...user, rank: index + 1 }));
    //         const currentUserIndex = leaderboard.data.data.findIndex((user) => user.id == userId);
    //         const currentUser = leaderboard.data.data[currentUserIndex];
    //         if (currentUserIndex >= 10) {
    //             setLeaderboardList([...top10, { ...currentUser, rank: currentUserIndex + 1 }]);
    //         } else {
    //             setLeaderboardList(top10);
    //         }
    //     }
    // }, [leaderboard.status]);

    useEffect(() => {
        const fetchData = async () => {
            setTimeout(async () => {
                dispatch(fetchDashStatsReq());
            }, 10000);
        };

        fetchData();
    }, []);
    return (
        <div className="level_three_dash tw-grid tw-grid-cols-[auto_327px] tw-gap-4">
            <div className="main_dash tw-overflow-hidden">
                <div className="tw-flex tw-flex-col tw-justify-between tw-gap-3">
                    <div>
                        <h1 className="tw-font-mono tw-text-xl tw-font-semibold">Welcome back, LMS Dashboard 👋</h1>
                        <p className="tw-text-sm tw-text-slate-600">
                            There is the latest update for the of below mentioned modules.
                        </p>
                    </div>
                </div>
                {/* <UserKPISDash DashData={dashboardStats || null} badgeDeatils={badgeDeatils || null} /> */}
                <UserCoursesDash />
                <UserScheduleDash />
            </div>
            <LearnerProfileDash setBadgeDeatils={setBadgeDeatils || null} />
            {/* <div className="tw-col-span-2 tw-mt-0">
                <div className="tw-flex tw-items-center tw-justify-between tw-px-3">
                    <h1 className="tw-font-lexend tw-text-lg tw-font-medium tw-text-gray-700">
                        #. Learner&apos;s Leaderboard :-
                    </h1>
                    <h1 className="tw-text-md tw-font-normal tw-text-gray-600">Top 10 Learners</h1>
                </div>
                <div className="tw-mt-3 tw-overflow-hidden tw-rounded-3xl tw-border-[1px] tw-font-lexend">
                    <div className="tw-grid tw-grid-cols-[70px_70px_1fr_1fr_1fr_1fr] tw-bg-slate-50">
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Rank</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Profile</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Name</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Level</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Points</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Achievement</div>
                    </div>
                    <div>
                        {leaderboardList.map((data, index) => {
                            let badge = data?.user_badges[data?.user_badges?.length - 1];
                            return (
                                <div
                                    key={data?.id}
                                    className={cn(
                                        "tw-border-b-[1px] tw-p-2 tw-px-4",
                                        data?.id == userId && "tw-bg-blue-100",
                                    )}
                                >
                                    <div className="tw-grid tw-grid-cols-[70px_70px_1fr_1fr_1fr_1fr] tw-items-center">
                                        <div className="tw-font-bold">#{data.rank}</div>
                                        <div>
                                            <Avatar>
                                                <AvatarImage src={data?.picture_url} />
                                                <AvatarFallback>
                                                    {data.first_name[0]}
                                                    {data.last_name[0]}
                                                </AvatarFallback>
                                            </Avatar>
                                        </div>
                                        <div className="tw-font-lexend">{`${data.first_name} ${data.last_name}`}</div>
                                        <div>
                                            <img
                                                src={`/assets/level-${badge?.badge_details?.level}.png`}
                                                className="tw-h-[40px]"
                                                alt=""
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-3">
                                            <i className="fa-solid fa-dice-d6 tw-text-red-600"></i>{" "}
                                            <p className="tw-font-lexend tw-text-lg tw-font-medium">
                                                {data?.points?.toLocaleString()}
                                            </p>
                                        </div>

                                        <div className="tw-flex tw-items-center tw-gap-4">
                                            <img src={badge?.badge_details?.icon_url} className="tw-h-[60px]" alt="" />
                                            <Label>{badge?.badge_details?.name}</Label>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div> */}
        </div>
    );
};

export default DashboardLearner;
