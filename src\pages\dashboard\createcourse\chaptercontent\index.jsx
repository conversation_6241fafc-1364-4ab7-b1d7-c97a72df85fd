import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useSidebar } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import SlideContent from "@/pages/dashboard/createcourse/chaptercontent/SlideContent";
import SlideSettings from "@/pages/dashboard/createcourse/chaptercontent/SlideSettings";
import MicroCourseSlide from "@/pages/dashboard/createcourse/chaptercontent/microslides";
import Assignment from "@/pages/dashboard/createcourse/chaptercontent/slides/Assignment";
import Audio from "@/pages/dashboard/createcourse/chaptercontent/slides/Audio";
import Embed from "@/pages/dashboard/createcourse/chaptercontent/slides/Embed";
import HTML from "@/pages/dashboard/createcourse/chaptercontent/slides/HTML";
import Homework from "@/pages/dashboard/createcourse/chaptercontent/slides/Homework";
import Interactions from "@/pages/dashboard/createcourse/chaptercontent/slides/Interactions";
import Quiz from "@/pages/dashboard/createcourse/chaptercontent/slides/Quiz";
import SCORM from "@/pages/dashboard/createcourse/chaptercontent/slides/SCORM";
import Video from "@/pages/dashboard/createcourse/chaptercontent/slides/Video";
import { iconsList } from "@/pages/dashboard/createcourse/chaptercontent/slides/icon-list";
import { tanstackApi } from "@/react-query/api";
import { useBulkUpdateCourse, useViewCourses } from "@/react-query/courses";
import { ArrowBigLeft, PackagePlus, Plus, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const formatNumber = (num) => num?.toString()?.padStart(2, "0");

const preview_mapping = {
    HTML: {
        component: MicroCourseSlide,
        type: "HTML",
    },
    ASSIGNMENT: {
        component: MicroCourseSlide,
        type: "Assignment",
    },
    HOMEWORK: {
        component: MicroCourseSlide,
        type: "Homework",
    },
    QUIZ: {
        component: MicroCourseSlide,
        type: "Quiz",
    },
    ZIP: {
        component: MicroCourseSlide,
        type: "SCORM",
    },
    EMBED: {
        component: MicroCourseSlide,
        type: "Embed",
    },
    MP4: {
        component: MicroCourseSlide,
        type: "Video",
    },
    MP3: {
        component: MicroCourseSlide,
        type: "Audio",
    },
    INTERACTIONS: {
        component: MicroCourseSlide,
        type: "Interactions",
    },
};

const content_mapping = {
    HTML: HTML,
    ASSIGNMENT: Assignment,
    HOMEWORK: Homework,
    QUIZ: Quiz,
    ZIP: SCORM,
    EMBED: Embed,
    MP4: Video,
    MP3: Audio,
    INTERACTIONS: Interactions,
};

const ChapterContent = () => {
    const params = useParams();
    const navigate = useNavigate();
    const { setOpen: sideBarOpen } = useSidebar();
    const [contentArray, setContentArray] = useState([]);
    const [courseData, setCourseData] = useState(null);
    const [open, setOpen] = useState(false);
    const [openSheet, setOpenSheet] = useState(false);
    const [openAlert, setOpenAlert] = useState(false);
    const [Chapter, setChapter] = useState(null);
    const [selectedContent, setSelectedContent] = useState(null);
    const [INDEX, setINDEX] = useState(null);
    const [template, setTemplate] = useState(null);
    const fetchCourse = useViewCourses(params?.course_id);
    const updateCourse = useBulkUpdateCourse(params?.course_id);
    const [currentId, setCurrentId] = useState(null);
    const [openIconDialog, setOpenIconDialog] = useState(false);

    useEffect(() => {
        if (fetchCourse.status === "success") setCourseData(fetchCourse.data.data);
    }, [fetchCourse.status]);

    const onAddContent = () => setOpen(true);

    const onSlideEdit = (data, c_index) => {
        setOpenSheet(true);
        setINDEX(c_index);
        setSelectedContent(data);
    };

    const onRemoveSlide = (index) => {
        let options = [...contentArray];
        options?.splice(index, 1);
        setContentArray(options);
    };

    useEffect(() => sideBarOpen(false), []);

    useEffect(() => {
        if (courseData != null) {
            let chapters = courseData?.courseChapters?.find((dt) => dt?.id == Number(params?.chapter_id));
            setChapter(chapters);
            const chapter_content = chapters?.lms_course_chapter_contents?.sort(
                (a, b) => Number(a?.content_order) - Number(b?.content_order),
            );
            if (chapter_content.length == 0 && contentArray.length == 0) setOpen(true);
            if (contentArray.length == 0) {
                setContentArray(chapter_content);
            }
        }
    }, [courseData, fetchCourse]);

    useEffect(() => {
        if (courseData?.lms_template) setTemplate(courseData?.lms_template);
    }, [courseData]);

    const onMountSlide = (slide_id) => {
        window.location.href = `#slide_${slide_id}`;
    };

    const onUpdateChapter = (type = "update") => {
        const lecturesAll = contentArray.map((data, idx) => {
            return {
                lecture_title: data.lecture_title,
                content_url: data.content_url,
                content_order: idx + 1,
                is_completion_required: data.is_completion_required,
                content_type: data.content_type,
                lecture_data: data.lecture_data,
                icon: data.icon,
                background: data.background,
                quiz_id: data.quiz_id,
                chapter_id: data.chapter_id,
                homework_id: data.homework_id,
                interaction_id: data.interaction_id,
                is_draft: type == "update",
                id: data?.id || undefined,
            };
        });

        bulkContentOrderUpdate({
            bulk_update_chapter_content: lecturesAll,
        });
    };

    const [deleteData, setDeleteData] = useState({
        contentID: null,
        contentIndex: null,
    });

    const onSetDelete = (content, index) => {
        setDeleteData({
            contentID: content?.id,
            contentIndex: index,
        });
        setOpenAlert(true);
    };

    const onContentDelete = async () => {
        await tanstackApi
            .delete("course/creation/delete-chapter-content", {
                data: {
                    chapter_content_id: deleteData?.contentID,
                },
            })
            .then((res) => {
                fetchCourse.refetch();
                toast.success("Deleted Successfully", {
                    description: res?.data?.message,
                });
                fetchCourse.refetch();
                onRemoveSlide(deleteData?.contentIndex);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const [draggedItemIndex, setDraggedItemIndex] = useState(null);

    const handleDragStart = (index) => {
        setDraggedItemIndex(index);
    };

    const handleDragOver = (index) => {
        const draggedOverItemIndex = index;
        if (draggedItemIndex === draggedOverItemIndex) return;

        const updatedItems = [...contentArray];
        const draggedItem = updatedItems[draggedItemIndex];

        // Remove the item being dragged and insert it at the new index
        updatedItems.splice(draggedItemIndex, 1);
        updatedItems.splice(draggedOverItemIndex, 0, draggedItem);

        setDraggedItemIndex(draggedOverItemIndex);
        setContentArray(updatedItems);
    };

    const handleDragEnd = () => {
        setDraggedItemIndex(null);
    };

    const bulkContentOrderUpdate = async (payload) => {
        updateCourse.mutate(payload, {
            onSuccess: () => {
                fetchCourse.refetch();
                navigate(0);
                toast.success("Updated", {
                    description: "Chapter updated successfully",
                });
            },
            onError: (error) => {
                toast.error("Something went wrong", {
                    description: error?.data?.data?.details?.[0]?.message,
                });
            },
        });
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Slide, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible, this slide will be deleted permanantly .
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onContentDelete}>Yes Delete it!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            <SlideContent
                open={open}
                setOpen={setOpen}
                setContentArray={setContentArray}
                contentArray={contentArray}
                Chapter={Chapter}
                INDEX={INDEX}
                onSlideEdit={onSlideEdit}
            />
            <div className="tw-grid tw-h-full tw-max-h-dvh tw-grid-cols-12 tw-grid-rows-[50px_1fr] tw-gap-4">
                <div className="tw-col-span-12 tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-5">
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-flex tw-items-center tw-gap-2">
                            <div className="tw-flex tw-h-[47px] tw-w-[50px] tw-items-center tw-justify-center tw-rounded-2xl tw-border-slate-700 tw-text-2xl tw-font-bold tw-text-slate-700">
                                {formatNumber(Chapter?.chapter_order)} .
                            </div>
                            <h1 className="tw-font-mono tw-text-2xl tw-font-semibold tw-text-gray-700">
                                {Chapter?.chapter_title}
                            </h1>
                        </div>
                        <div className="tw-flex tw-items-center tw-gap-2">
                            <Button asChild variant="outline">
                                <Link to={`/dashboard/view-course/${params?.course_id}`}>
                                    <ArrowBigLeft /> Back
                                </Link>
                            </Button>
                            <Button
                                type="button"
                                variant="secondary"
                                disabled={updateCourse.isPending || fetchCourse.isLoading}
                                onClick={(e) => onUpdateChapter("draft")}
                            >
                                {updateCourse.isPending ?? "Loading"}
                                <Save /> Save Draft
                            </Button>
                            <Button
                                type="button"
                                disabled={updateCourse.isPending || fetchCourse.isLoading}
                                onClick={(e) => onUpdateChapter("save")}
                            >
                                {updateCourse.isPending ?? "Loading"}
                                <Save /> Save Chapter
                            </Button>
                        </div>
                    </div>
                </div>
                <div className="tw-col-span-12 tw-grid tw-h-full tw-w-full tw-grid-cols-[10rem_1fr] tw-gap-3 tw-overflow-hidden">
                    <div className="tw-grid tw-h-full tw-w-full tw-grid-rows-[1fr_5rem] tw-gap-3 tw-overflow-hidden">
                        <div className="custom_scrollbar tw-flex tw-w-full tw-flex-col tw-gap-3 tw-overflow-y-auto">
                            {contentArray?.map((content, index) => {
                                const compData = preview_mapping[content?.content_type];
                                if (!compData) return null;
                                return (
                                    <compData.component
                                        key={index}
                                        onMountSlide={onMountSlide}
                                        content={content}
                                        formatNumber={formatNumber}
                                        contentArray={contentArray}
                                        setContentArray={setContentArray}
                                        template={template}
                                        type={compData.type}
                                        currentId={currentId}
                                        index={index}
                                        onSetDelete={onSetDelete}
                                        onRemoveSlide={onRemoveSlide}
                                        handleDragStart={handleDragStart}
                                        handleDragOver={handleDragOver}
                                        handleDragEnd={handleDragEnd}
                                    />
                                );
                            })}
                        </div>
                        <div className="tw-h-full tw-w-full">
                            <Button
                                variant="reset"
                                className="tw-flex tw-h-full tw-w-full tw-flex-col tw-border-2 tw-border-dashed"
                                onClick={onAddContent}
                            >
                                <PackagePlus className="tw-text-gray-400" size={40} />
                                <h1 className="tw-leading-0 tw-text-xs tw-font-semibold tw-text-gray-400">
                                    Add <br /> Slide
                                </h1>
                            </Button>
                        </div>
                    </div>
                    <div className="tw-grid tw-size-full tw-grid-cols-[10fr_4fr] tw-grid-rows-1 tw-gap-5 tw-overflow-hidden">
                        <div className="custom_scrollbar tw-h-full tw-space-y-4 tw-overflow-y-auto tw-pr-1">
                            {contentArray.length === 0 ? (
                                <div className="tw-flex tw-h-[40vh] tw-items-center tw-justify-center tw-rounded-lg tw-border-2 tw-border-dashed">
                                    <Button variant="secondary" onClick={onAddContent}>
                                        <Plus size={16} strokeWidth={2} aria-hidden="true" />
                                        <p>Add Slide</p>
                                    </Button>
                                </div>
                            ) : (
                                contentArray?.map((content, index) => {
                                    const Component = content_mapping[content?.content_type];
                                    return Component ? (
                                        <Component
                                            onRemoveSlide={onRemoveSlide}
                                            onSlideEdit={onSlideEdit}
                                            index={index}
                                            content={content}
                                            setContentArray={setContentArray}
                                            selectedContent={selectedContent}
                                            setSelectedContent={setSelectedContent}
                                            contentArray={contentArray}
                                            template={template}
                                            onSetDelete={onSetDelete}
                                            setCurrentId={setCurrentId}
                                            setOpenIconDialog={setOpenIconDialog}
                                        />
                                    ) : null;
                                })
                            )}
                        </div>
                        <div className="custom_scrollbar tw-h-full tw-overflow-hidden tw-pr-2">
                            <SlideSettings
                                open={openSheet}
                                setOpen={setOpenSheet}
                                setContentArray={setContentArray}
                                contentArray={contentArray}
                                selectedContent={selectedContent}
                                setSelectedContent={setSelectedContent}
                                INDEX={INDEX}
                            />
                        </div>
                    </div>
                </div>
            </div>
            <Dialog open={openIconDialog} onOpenChange={setOpenIconDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Choose Icon</DialogTitle>
                    </DialogHeader>
                    <div className="tw-grid tw-grid-cols-5 tw-gap-3">
                        {iconsList?.map((icon, idx) => (
                            <div
                                className={cn(
                                    "tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-md tw-p-2",
                                    contentArray[INDEX]?.icon == icon?.icon && "tw-bg-slate-100",
                                )}
                                key={idx}
                                onClick={() => {
                                    setContentArray((prev) => {
                                        prev[INDEX].icon = icon?.icon;
                                        return [...prev];
                                    });
                                    setOpenIconDialog(false);
                                }}
                            >
                                <img className="tw-w-[50px]" src={icon?.icon} alt={icon?.icon} />
                            </div>
                        ))}
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default ChapterContent;
