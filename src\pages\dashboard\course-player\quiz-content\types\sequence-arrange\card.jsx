import { cn } from "@/lib/utils";
import { ItemTypes } from "@/pages/dashboard/course-player/quiz-content/types/sequence-arrange/type";
import { memo } from "react";
import { useDrag, useDrop } from "react-dnd";

export const Card = memo(function Card({ id, children, moveCard, findCard }) {
    const originalIndex = findCard(id).index;
    const [{ isDragging }, drag] = useDrag(
        () => ({
            type: ItemTypes.CARD,
            item: { id, originalIndex },
            collect: (monitor) => ({
                isDragging: monitor.isDragging(),
            }),
            end: (item, monitor) => {
                const { id: droppedId, originalIndex } = item;
                const didDrop = monitor.didDrop();
                if (!didDrop) {
                    moveCard(droppedId, originalIndex);
                }
            },
        }),
        [id, originalIndex, moveCard],
    );
    const [_, drop] = useDrop(
        () => ({
            accept: ItemTypes.CARD,
            hover({ id: draggedId }) {
                if (draggedId !== id) {
                    const { index: overIndex } = findCard(id);
                    moveCard(draggedId, overIndex);
                }
            },
        }),
        [findCard, moveCard],
    );
    const opacity = isDragging ? 0 : 1;

    return (
        <div
            ref={(node) => drag(drop(node))}
            className={cn(
                "leading-7 tw-flex tw-h-[3.5rem] tw-cursor-grab tw-items-center tw-gap-[1rem] tw-rounded-[0.5rem] tw-bg-[color] tw-p-0.5 tw-text-[22px] tw-leading-[22px]",
            )}
            style={{ opacity }}
        >
            {children}
        </div>
    );
});
