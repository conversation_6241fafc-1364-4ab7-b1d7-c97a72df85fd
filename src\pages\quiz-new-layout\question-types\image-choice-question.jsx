"use client";

import { cn } from "@/lib/utils";
import { Check, X } from "lucide-react";
import { useEffect, useState } from "react";

export default function ImageChoiceQuestion({ question, onAnswerSubmit, showCorrectAnswer, savedAnswer }) {
    const [selectedOption, setSelectedOption] = useState(null);

    const handleOptionSelect = (optionIndex) => {
        setSelectedOption(optionIndex);
        const isCorrect = optionIndex === question.correctAnswer;
        onAnswerSubmit(optionIndex, isCorrect);
    };

    return (
        <div className="tw-grid tw-grid-cols-2 tw-gap-4">
            {question.options.map((option, index) => {
                const isSelected = selectedOption === index;
                const isCorrect = index === question.correctAnswer;
                const showCorrect = showCorrectAnswer && isCorrect;

                return (
                    <button
                        key={index}
                        onClick={() => handleOptionSelect(index)}
                        className={cn(
                            "tw-flex tw-flex-col tw-items-center tw-rounded-md tw-p-2 tw-transition-colors",
                            isSelected ? "tw-border-2 tw-border-green-500 tw-bg-green-700/50" : "",
                        )}
                    >
                        <div className="tw-relative tw-mb-2 tw-aspect-square tw-w-full tw-overflow-hidden tw-rounded-md">
                            <img
                                src={option.imageUrl || "/placeholder.svg"}
                                alt={option.label}
                                className="tw-h-full tw-w-full tw-object-cover"
                            />

                            {showCorrect && !isSelected && (
                                <div className="tw-absolute tw-right-2 tw-top-2 tw-flex tw-h-8 tw-w-8 tw-items-center tw-justify-center tw-rounded-full tw-bg-slate-800/80">
                                    <Check className="tw-text-green-400" />
                                </div>
                            )}
                        </div>
                        <span className="tw-text-sm tw-font-medium">{option.label}</span>
                    </button>
                );
            })}
        </div>
    );
}
