import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { tanstackApiFormdata } from "@/react-query/api";
import { CircleX, ImagePlus } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const tabsList = [
    {
        name: "Dropzones",
        icon: "fa-solid fa-vector-square",
    },
    {
        name: "Draggables",
        icon: "fa-solid fa-hand-back-fist",
    },
];

const DragDropSettings = ({ setContentData, contentData }) => {
    const [currentTab, setCurrentTab] = useState("Dropzones");

    const [hotspotDropzones, setHotspotDropzones] = useState(contentData?.hotspotDropzone || []);
    const [hotspots, setHotspots] = useState(contentData?.hotspots || []);

    const [dropzones, setDropzones] = useState(contentData?.dropzone || []);
    const [draggableOptions, setDraggableOptions] = useState(contentData?.draggable_options || []);

    useEffect(() => {
        if (hotspots?.length > 0) {
            setContentData({ ...contentData, hotspots: hotspots });
        }
    }, [hotspots]);

    useEffect(() => {
        if (dropzones?.length > 0) {
            setContentData({ ...contentData, dropzone: dropzones });
        }
    }, [dropzones]);

    useEffect(() => {
        if (draggableOptions?.length > 0) {
            setContentData({ ...contentData, draggable_options: draggableOptions });
        }
    }, [draggableOptions]);

    const onDropzoneOptionChange = (name, value, idx) => {
        let optionData = [...hotspotDropzones];
        optionData[idx][name] = value;
        setHotspotDropzones(optionData);
        if (name == "src") {
            const formData = new FormData();
            var file = value;
            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");
            onHotspotDropzoneMediaUpload(formData, idx);
        }
    };

    const RemoveDropzoneOption = (idx) => {
        const arr = [...hotspotDropzones];
        arr.splice(idx, 1);
        setHotspotDropzones(arr);
        setContentData({ ...contentData, hotspotDropzone: arr });
    };

    const DropzoneOptionAdd = () => {
        let opt = { x: "", y: "", src: "", zone: "" };
        setHotspotDropzones([...hotspotDropzones, opt]);
        setContentData({ ...contentData, hotspotDropzone: [...hotspotDropzones, opt] });
    };

    const onHotspotDropzoneMediaUpload = async (formData, index) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData)
            .then((res) => {
                let optionData = [...hotspotDropzones];
                optionData[index]["src"] = res?.data?.fileUrl;
                setHotspotDropzones(optionData);
            })
            .catch((err) => {
                return "";
            });
    };

    const onRemoveZoneImage = (index) => {
        let optionData = [...hotspotDropzones];
        optionData[index]["src"] = "";
        setHotspotDropzones(optionData);
    };

    /////////////DND Hotspot Items//////////////////////////////

    const onDNDHotspotOptionChange = (name, value, idx) => {
        let optionData = [...hotspots];
        optionData[idx][name] = value;
        setHotspots(optionData);

        if (name == "src") {
            const formData = new FormData();
            var file = value;
            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");

            onHotspotMediaUpload(formData, idx);
        }
    };

    const RemoveDNDHotspotOption = (idx) => {
        const arr = [...hotspots];
        arr.splice(idx, 1);
        setHotspots(arr);
    };

    const DNDHotspotOptionAdd = () => {
        let opt = { x: "", y: "", src: "", name: "", correct: "" };
        setHotspots([...hotspots, opt]);
    };

    const onHotspotMediaUpload = async (formData, index) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData)
            .then((res) => {
                let optionData = [...hotspots];
                optionData[index]["src"] = res?.data?.fileUrl;
                setHotspots(optionData);
            })
            .catch((err) => {
                return "";
            });
    };

    const onRemoveItemImage = (index) => {
        let optionData = [...hotspots];
        optionData[index]["src"] = "";
        setHotspots(optionData);
    };

    /////////////////// Dropzones //////////////////////////////

    const onDropzonesChange = (name, value, idx) => {
        let optionData = [...dropzones];

        if (name == "src") {
            const formData = new FormData();
            var file = value;
            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");

            onDropzoneMediaUpload(formData, idx);
        } else {
            optionData[idx][name] = value;
            optionData[idx]["id"] = value;
            setDropzones(optionData);
        }
    };

    const RemoveDropzone = (idx) => {
        const arr = [...dropzones];
        arr.splice(idx, 1);
        setDropzones(arr);
    };

    const DropzoneAdd = () => {
        let opt = { id: "", zone: "", src: "" };

        if (dropzones?.length < 4) {
            setDropzones([...dropzones, opt]);
        } else {
            toast.warning(`You can add only 4 options here.`);
        }
    };

    const onDropzoneMediaUpload = async (formData, index) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData)
            .then((res) => {
                let optionData = [...dropzones];
                optionData[index]["src"] = res?.data?.fileUrl;
                optionData[index]["id"] = res?.data?.fileUrl;
                setDropzones(optionData);
            })
            .catch((err) => {
                return "";
            });
    };

    const onRemoveDropzoneItemImage = (index) => {
        let optionData = [...dropzones];
        optionData[index]["src"] = "";
        setDropzones(optionData);
    };

    ///////////// Draggable Items//////////////////////////////

    const onDraggableChange = (name, value, idx) => {
        let optionData = [...draggableOptions];
        optionData[idx]["id"] = `image${idx + 1}`;
        optionData[idx][name] = value;
        setDraggableOptions(optionData);

        if (name == "src") {
            const formData = new FormData();
            var file = value;
            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");

            onDraggableMediaUpload(formData, idx);
        }
    };

    const RemoveDraggable = (idx) => {
        const arr = [...draggableOptions];
        arr.splice(idx, 1);
        setDraggableOptions(arr);
    };

    const DraggableAdd = () => {
        let opt = { id: "", src: "", zone: "" };

        if (draggableOptions?.length < 5) {
            setDraggableOptions([...draggableOptions, opt]);
        } else {
            toast.warning(`You can add only 5 options here.`);
        }
    };

    const onDraggableMediaUpload = async (formData, index) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData)
            .then((res) => {
                let optionData = [...draggableOptions];
                optionData[index]["src"] = res?.data?.fileUrl;
                setDraggableOptions(optionData);
            })
            .catch((err) => {
                return "";
            });
    };

    const onRemoveDraggableImage = (index) => {
        let optionData = [...draggableOptions];
        optionData[index]["src"] = "";
        setDraggableOptions(optionData);
    };

    return (
        <div>
            <Label>Drag & Drop Options Creation :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">
                Please define dropzones & draggable items as per the question&apos;s requirement here.
            </p>
            <div>
                <div className="tw-grid tw-w-[305px] tw-grid-cols-2 tw-items-center tw-justify-between tw-rounded-[50px] tw-bg-gray-100 tw-px-1 tw-py-1">
                    {tabsList?.map((tab, idx) => (
                        <p
                            key={idx}
                            onClick={() => setCurrentTab(tab?.name)}
                            className={`${tab?.name == currentTab && "active_subtab"} tw-bg-gray tw-cursor-pointer tw-rounded-[50px] tw-p-2 tw-px-5 tw-text-center tw-text-sm tw-font-medium`}
                        >
                            <i className={tab?.icon}></i> {tab?.name}
                        </p>
                    ))}
                </div>
            </div>
            {contentData?.componentTypeId == "hotspot_dnd" && (
                <>
                    {currentTab == "Dropzones" && (
                        <>
                            <div className="tw-mt-5 tw-grid tw-grid-cols-[150px_auto_100px] tw-gap-2">
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Image</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Dropzone</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                                </div>
                            </div>
                            <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                                {hotspotDropzones?.map((option, idx) => (
                                    <div
                                        key={idx}
                                        className="tw-grid tw-grid-cols-[150px_auto_100px] tw-items-center tw-gap-2"
                                    >
                                        <div className="">
                                            <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                                <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                                    <img
                                                        className="tw-h-full tw-w-full tw-object-contain"
                                                        src={option?.src || "/assets/thumbnail-alpha.png"}
                                                        alt=""
                                                    />
                                                </div>
                                                <div className="tw-flex tw-items-center tw-justify-between">
                                                    <CircleX
                                                        size={18}
                                                        className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                        onClick={() => onRemoveZoneImage(idx)}
                                                    />
                                                    <input
                                                        type="file"
                                                        onChange={(e) =>
                                                            onDropzoneOptionChange("src", e.target.files[0], idx)
                                                        }
                                                        accept="image/*"
                                                        id={`dropzone_${idx}`}
                                                        style={{ display: "none" }}
                                                    />

                                                    <label
                                                        htmlFor={`dropzone_${idx}`}
                                                        className="tw-flex tw-items-center tw-justify-center"
                                                    >
                                                        <Badge
                                                            variant="outline"
                                                            className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                        >
                                                            <ImagePlus size={16} /> Choose
                                                        </Badge>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <Input
                                                id="label"
                                                placeholder="Define dropzone label here"
                                                onChange={(e) => onDropzoneOptionChange("zone", e?.target?.value, idx)}
                                                value={option?.zone}
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {hotspotDropzones?.length > 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={() => RemoveDropzoneOption(idx)}
                                                >
                                                    <i className="fa-solid fa-xmark"></i>
                                                </Button>
                                            )}
                                            {hotspotDropzones?.length == idx + 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={DropzoneOptionAdd}
                                                >
                                                    <i className="fa-solid fa-plus"></i>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                    {currentTab == "Draggables" && (
                        <>
                            <div className="tw-mt-5 tw-grid tw-grid-cols-[1fr_1fr_100px] tw-gap-2">
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Draggable Item</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Dropzone</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                                </div>
                            </div>
                            <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                                {hotspots?.map((option, idx) => (
                                    <div
                                        key={idx}
                                        className="tw-grid tw-grid-cols-[1fr_1fr_100px] tw-items-center tw-gap-2"
                                    >
                                        <div className="">
                                            <Input
                                                id="label"
                                                placeholder="Define option label here"
                                                onChange={(e) =>
                                                    onDNDHotspotOptionChange("name", e?.target?.value, idx)
                                                }
                                                value={option?.name}
                                            />
                                        </div>
                                        <div>
                                            <SelectNative
                                                id="correct"
                                                onChange={(e) =>
                                                    onDNDHotspotOptionChange("correct", e.target.value, idx)
                                                }
                                                value={option?.correct}
                                            >
                                                <option value=""> - Zone - </option>
                                                {hotspotDropzones?.map((data) => (
                                                    <option key={data?.zone} value={data?.zone}>
                                                        {data?.zone}
                                                    </option>
                                                ))}
                                            </SelectNative>
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {hotspots?.length > 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={() => RemoveDNDHotspotOption(idx)}
                                                >
                                                    <i className="fa-solid fa-xmark"></i>
                                                </Button>
                                            )}
                                            {hotspots?.length == idx + 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={DNDHotspotOptionAdd}
                                                >
                                                    <i className="fa-solid fa-plus"></i>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                </>
            )}
            {contentData?.componentTypeId == "hotspot_dnd_image" && (
                <>
                    {currentTab == "Dropzones" && (
                        <>
                            <div className="tw-mt-5 tw-grid tw-grid-cols-[150px_auto_100px] tw-gap-2">
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Image</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Dropzone</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                                </div>
                            </div>
                            <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                                {hotspotDropzones?.map((option, idx) => (
                                    <div
                                        key={idx}
                                        className="tw-grid tw-grid-cols-[150px_auto_100px] tw-items-center tw-gap-2"
                                    >
                                        <div className="">
                                            <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                                <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                                    <img
                                                        className="tw-h-full tw-w-full tw-object-contain"
                                                        src={option?.src || "/assets/thumbnail-alpha.png"}
                                                        alt=""
                                                    />
                                                </div>
                                                <div className="tw-flex tw-items-center tw-justify-between">
                                                    <CircleX
                                                        size={18}
                                                        className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                        onClick={() => onRemoveZoneImage(idx)}
                                                    />
                                                    <input
                                                        type="file"
                                                        onChange={(e) =>
                                                            onDropzoneOptionChange("src", e.target.files[0], idx)
                                                        }
                                                        accept="image/*"
                                                        id={`dropzone_${idx}`}
                                                        style={{ display: "none" }}
                                                    />

                                                    <label
                                                        htmlFor={`dropzone_${idx}`}
                                                        className="tw-flex tw-items-center tw-justify-center"
                                                    >
                                                        <Badge
                                                            variant="outline"
                                                            className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                        >
                                                            <ImagePlus size={16} /> Choose
                                                        </Badge>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <Input
                                                id="label"
                                                placeholder="Define dropzone label here"
                                                onChange={(e) => onDropzoneOptionChange("zone", e?.target?.value, idx)}
                                                value={option?.zone}
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {hotspotDropzones?.length > 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={() => RemoveDropzoneOption(idx)}
                                                >
                                                    <i className="fa-solid fa-xmark"></i>
                                                </Button>
                                            )}
                                            {hotspotDropzones?.length == idx + 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={DropzoneOptionAdd}
                                                >
                                                    <i className="fa-solid fa-plus"></i>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                    {currentTab == "Draggables" && (
                        <>
                            <div className="tw-mt-5 tw-grid tw-grid-cols-[150px_1fr_100px] tw-gap-2">
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Image</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Text</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                                </div>
                            </div>
                            <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                                {hotspots?.map((option, idx) => (
                                    <div
                                        key={idx}
                                        className="tw-grid tw-grid-cols-[150px_1fr_100px] tw-items-center tw-gap-2"
                                    >
                                        <div className="">
                                            <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                                <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                                    <img
                                                        className="tw-h-full tw-w-full tw-object-contain"
                                                        src={option?.src || "/assets/thumbnail-alpha.png"}
                                                        alt=""
                                                    />
                                                </div>
                                                <div className="tw-flex tw-items-center tw-justify-between">
                                                    <CircleX
                                                        size={18}
                                                        className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                        onClick={() => onRemoveItemImage(idx)}
                                                    />
                                                    <input
                                                        type="file"
                                                        onChange={(e) =>
                                                            onDNDHotspotOptionChange("src", e.target.files[0], idx)
                                                        }
                                                        accept="image/*"
                                                        id={`draggable_${idx}`}
                                                        style={{ display: "none" }}
                                                    />

                                                    <label
                                                        htmlFor={`draggable_${idx}`}
                                                        className="tw-flex tw-items-center tw-justify-center"
                                                    >
                                                        <Badge
                                                            variant="outline"
                                                            className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                        >
                                                            <ImagePlus size={16} /> Choose
                                                        </Badge>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="tw-grid tw-grid-cols-1 tw-gap-2">
                                            <div className="">
                                                <Input
                                                    id="label"
                                                    placeholder="Define option label here"
                                                    onChange={(e) =>
                                                        onDNDHotspotOptionChange("name", e?.target?.value, idx)
                                                    }
                                                    value={option?.name}
                                                />
                                            </div>
                                            <div>
                                                <SelectNative
                                                    id="correct"
                                                    onChange={(e) =>
                                                        onDNDHotspotOptionChange("correct", e.target.value, idx)
                                                    }
                                                    value={option?.correct}
                                                >
                                                    <option value=""> - Zone - </option>
                                                    {hotspotDropzones?.map((data) => (
                                                        <option key={data?.zone} value={data?.zone}>
                                                            {data?.zone}
                                                        </option>
                                                    ))}
                                                </SelectNative>
                                            </div>
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {hotspots?.length > 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={() => RemoveDNDHotspotOption(idx)}
                                                >
                                                    <i className="fa-solid fa-xmark"></i>
                                                </Button>
                                            )}
                                            {hotspots?.length == idx + 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={DNDHotspotOptionAdd}
                                                >
                                                    <i className="fa-solid fa-plus"></i>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                </>
            )}

            {contentData?.componentTypeId == "dnd_image_box" && (
                <>
                    {currentTab == "Dropzones" && (
                        <>
                            <div className="tw-mt-5 tw-grid tw-grid-cols-[150px_auto_100px] tw-gap-2">
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Image</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Dropzone</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                                </div>
                            </div>
                            <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                                {dropzones?.map((option, idx) => (
                                    <div
                                        key={idx}
                                        className="tw-grid tw-grid-cols-[150px_auto_100px] tw-items-center tw-gap-2"
                                    >
                                        <div className="">
                                            <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                                <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                                    <img
                                                        className="tw-h-full tw-w-full tw-object-contain"
                                                        src={option?.src || "/assets/thumbnail-alpha.png"}
                                                        alt=""
                                                    />
                                                </div>
                                                <div className="tw-flex tw-items-center tw-justify-between">
                                                    <CircleX
                                                        size={18}
                                                        className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                        onClick={() => onRemoveDropzoneItemImage(idx)}
                                                    />
                                                    <input
                                                        type="file"
                                                        onChange={(e) =>
                                                            onDropzonesChange("src", e.target.files[0], idx)
                                                        }
                                                        accept="image/*"
                                                        id={`dropzone_img_${idx}`}
                                                        style={{ display: "none" }}
                                                    />

                                                    <label
                                                        htmlFor={`dropzone_img_${idx}`}
                                                        className="tw-flex tw-items-center tw-justify-center"
                                                    >
                                                        <Badge
                                                            variant="outline"
                                                            className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                        >
                                                            <ImagePlus size={16} /> Choose
                                                        </Badge>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="tw-p-1">
                                            <Input
                                                id="zone"
                                                maxLength="20"
                                                placeholder="Define zone label here"
                                                onChange={(e) => onDropzonesChange("zone", e?.target?.value, idx)}
                                                value={option?.zone}
                                            />
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {dropzones?.length > 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={() => RemoveDropzone(idx)}
                                                >
                                                    <i className="fa-solid fa-xmark"></i>
                                                </Button>
                                            )}
                                            {dropzones?.length == idx + 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={DropzoneAdd}
                                                >
                                                    <i className="fa-solid fa-plus"></i>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                    {currentTab == "Draggables" && (
                        <>
                            <div className="tw-mt-5 tw-grid tw-grid-cols-[150px_1fr_100px] tw-gap-2">
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Image</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Text</h1>
                                </div>
                                <div>
                                    <h1 className="tw-text-sm tw-font-medium tw-text-slate-400">Action</h1>
                                </div>
                            </div>
                            <div className="custom_scrollbar tw-mt-3 tw-h-[25vw] tw-space-y-3 tw-overflow-y-auto">
                                {draggableOptions?.map((option, idx) => (
                                    <div
                                        key={idx}
                                        className="tw-grid tw-grid-cols-[150px_1fr_100px] tw-items-center tw-gap-2"
                                    >
                                        <div className="">
                                            <div className="tw-flex tw-w-full tw-flex-col tw-gap-1 tw-rounded-xl tw-border-[1px] tw-p-1">
                                                <div className="tw-relative tw-h-[80px] tw-w-full tw-overflow-hidden tw-rounded-lg tw-border-[1px] tw-border-dashed">
                                                    <img
                                                        className="tw-h-full tw-w-full tw-object-cover"
                                                        src={option?.src || "/assets/thumbnail-alpha.png"}
                                                        alt=""
                                                    />
                                                </div>
                                                <div className="tw-flex tw-items-center tw-justify-between">
                                                    <CircleX
                                                        size={18}
                                                        className="tw-cursor-pointer tw-text-red-200 hover:tw-text-red-400"
                                                        onClick={() => onRemoveDraggableImage(idx)}
                                                    />
                                                    <input
                                                        type="file"
                                                        onChange={(e) =>
                                                            onDraggableChange("src", e.target.files[0], idx)
                                                        }
                                                        accept="image/*"
                                                        id={`draggable_option_${idx}`}
                                                        style={{ display: "none" }}
                                                    />

                                                    <label
                                                        htmlFor={`draggable_option_${idx}`}
                                                        className="tw-flex tw-items-center tw-justify-center"
                                                    >
                                                        <Badge
                                                            variant="outline"
                                                            className="tw-flex tw-cursor-pointer tw-items-center tw-gap-1 tw-text-slate-400 hover:tw-text-slate-600"
                                                        >
                                                            <ImagePlus size={16} /> Choose
                                                        </Badge>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="tw-grid tw-grid-cols-1 tw-gap-2">
                                            <div>
                                                <SelectNative
                                                    id="zone"
                                                    onChange={(e) => onDraggableChange("zone", e.target.value, idx)}
                                                    value={option?.zone}
                                                >
                                                    <option value=""> - Zone - </option>
                                                    {dropzones?.map((data) => (
                                                        <option key={data?.zone} value={data?.zone}>
                                                            {data?.zone}
                                                        </option>
                                                    ))}
                                                </SelectNative>
                                            </div>
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {draggableOptions?.length > 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={() => RemoveDraggable(idx)}
                                                >
                                                    <i className="fa-solid fa-xmark"></i>
                                                </Button>
                                            )}
                                            {draggableOptions?.length == idx + 1 && (
                                                <Button
                                                    variant="outline"
                                                    className="tw-rounded-xl"
                                                    onClick={DraggableAdd}
                                                >
                                                    <i className="fa-solid fa-plus"></i>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                </>
            )}
        </div>
    );
};

export default DragDropSettings;
