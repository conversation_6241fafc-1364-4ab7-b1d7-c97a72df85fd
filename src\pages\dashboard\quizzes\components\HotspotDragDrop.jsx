import { colorsList } from "@/data/colors";
import { isUrl } from "@/lib/utils";
import QuizContainer from "@/pages/dashboard/quizzes/components/container";
import { Move } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import Draggable from "react-draggable";

const HotspotDragDrop = (props) => {
    const { content, index, setComponentsArray, componentsArray } = props;
    const containerRef = useRef(null);
    const [circles, setCircles] = useState(content?.hotspots || []);
    const [dropzones, setDropzones] = useState(content?.hotspotDropzone || []);
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
    const data = componentsArray[index];

    useEffect(() => {
        if (content?.hotspots?.length > 0) setCircles(content?.hotspots);
    }, [content?.hotspots]);

    useEffect(() => {
        if (content?.hotspotDropzone?.length > 0) setDropzones(content?.hotspotDropzone);
    }, [content?.hotspotDropzone]);

    useEffect(() => {
        const updateContainerSize = () => {
            if (containerRef.current) {
                const rect = containerRef.current.getBoundingClientRect();
                setContainerSize({ width: rect.width, height: rect.height });
            }
        };

        updateContainerSize();
        window.addEventListener("resize", updateContainerSize);

        return () => {
            window.removeEventListener("resize", updateContainerSize);
        };
    }, []);

    const clampPosition = (x, y) => {
        return {
            x: Math.min(Math.max(x, 0), containerSize.width),
            y: Math.min(Math.max(y, 0), containerSize.height),
        };
    };

    // Convert relative positions to absolute positions
    const getAbsolutePosition = (relativeX, relativeY) => {
        if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
        return clampPosition(relativeX * containerSize.width, relativeY * containerSize.height);
    };

    // Convert absolute positions to relative positions
    const getRelativePosition = (data, absoluteX, absoluteY) => {
        if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
        return {
            ...data,
            x: absoluteX / containerSize.width,
            y: absoluteY / containerSize.height,
        };
    };

    // Handle draggable stop for hotspots
    const handleStop = (index, pos) => {
        setCircles((prevCircles) =>
            prevCircles.map((circle, i) => (i === index ? { ...getRelativePosition(circle, pos.x, pos.y) } : circle)),
        );
    };

    const updateSize = (index, size) => {
        setDropzones((prevZones) => {
            const newZones = [...prevZones];
            newZones[index] = { ...newZones[index], ...size };
            return newZones;
        });
    };

    // Handle draggable stop for dropzones
    const handleStopDropzone = (index, pos) => {
        setDropzones((prevZones) =>
            prevZones.map((zone, i) => (i === index ? { ...getRelativePosition(zone, pos.x, pos.y) } : zone)),
        );
    };

    useEffect(() => {
        let options = [...componentsArray];
        options[index].hotspotDropzone = dropzones;
        options[index].hotspots = circles;
        setComponentsArray(options);
    }, [circles, dropzones]);

    return (
        <QuizContainer {...props} type="hotspot_dragdrop" empty={content?.hotspotDropzone?.length === 0}>
            <div className="tw-flex tw-items-center tw-justify-center tw-px-10 tw-py-7">
                <div
                    className="tw-overflow-hidden tw-rounded-lg tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{
                        backgroundImage: `url(${content?.question_thumbnail})`,
                        position: "relative",
                        // aspectRatio: 12/7,
                        width: "600px",
                        height: "350px",
                    }}
                    ref={containerRef}
                >
                    {/* Render Hotspots */}
                    {circles.map((position, c_index) => {
                        const { x, y } = getAbsolutePosition(position.x, position.y);
                        return (
                            <Draggable
                                key={`circle-${c_index}`}
                                bounds="parent"
                                position={{ x, y }}
                                onStop={(e, data) => handleStop(c_index, data)}
                            >
                                <div
                                    className="tw-absolute tw-z-10 tw-cursor-grab tw-rounded-md tw-bg-gray-100 tw-px-5 tw-py-2 tw-text-lg"
                                    style={{ width: "max-content", background: colorsList[c_index] }}
                                >
                                    <p
                                        className="tw-font-mono tw-font-medium"
                                        style={{
                                            color: data?.styles?.answer?.color,
                                            fontFamily: data?.styles?.answer?.fontFamily,
                                            fontSize: data?.styles?.answer?.fontSize,
                                        }}
                                    >
                                        {position.name}
                                    </p>
                                </div>
                            </Draggable>
                        );
                    })}

                    {/* Render Dropzones */}
                    {dropzones.map((position, d_index) => {
                        return (
                            <HotspotDraggable
                                key={`dropzone-${d_index}`}
                                handleStopDropzone={handleStopDropzone}
                                updateSize={updateSize}
                                d_index={d_index}
                                getAbsolutePosition={getAbsolutePosition}
                                position={position}
                            />
                        );
                    })}
                </div>
            </div>
        </QuizContainer>
    );
};

export default HotspotDragDrop;

const HotspotDraggable = ({ handleStopDropzone, d_index, position, getAbsolutePosition, updateSize }) => {
    const { x, y } = getAbsolutePosition(position.x, position.y);
    const ref = useRef(null);

    useEffect(() => {
        if (!ref.current) return;
        const observer = new ResizeObserver((entries) => {
            for (let entry of entries) {
                const { height } = entry.contentRect;
                const width = height * 1;
                updateSize(d_index, { width, height });
                ref.current.style.width = `${width}px`;
            }
        });

        observer.observe(ref.current);
        return () => observer.disconnect();
    }, [d_index]);

    return (
        <Draggable
            key={`dropzone-${d_index}`}
            bounds="parent"
            handle=".handle"
            position={{ x, y }}
            onStop={(e, data) => {
                handleStopDropzone(d_index, data);
            }}
        >
            <div
                className="tw-absolute tw-aspect-square tw-min-w-[50px] tw-rounded-md tw-border-[2px] tw-border-dashed tw-border-gray-200 tw-bg-transparent"
                ref={ref}
                style={{
                    resize: "vertical",
                    overflow: "hidden",
                    width: position.width > 0 ? position.width : 50,
                    height: position.height > 0 ? position.height : 50,
                }}
            >
                <div className="handle tw-absolute tw-right-0 tw-top-0 tw-z-50 tw-cursor-grab">
                    <Move className="tw-size-3" />
                </div>
                <p className="tw-absolute tw-left-0 tw-top-0 tw-z-50 tw-text-sm tw-leading-none tw-text-black">
                    {position?.zone}
                </p>
                <div className="tw-relative tw-size-full">
                    {isUrl(position?.src) && (
                        <img
                            src={position?.src}
                            alt=""
                            className="tw-absolute tw-inset-0 tw-size-full tw-select-none tw-object-contain"
                        />
                    )}
                </div>
            </div>
        </Draggable>
    );
};
