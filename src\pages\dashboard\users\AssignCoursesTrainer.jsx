import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const AssignCoursesTrainer = ({ open, setOpen, groupTeamData, editData }) => {
    const navigate = useNavigate();
    const [assignedCourses, setAssignedCourses] = useState([]);

    const [openAlert, setOpenAlert] = useState(false);
    const [courseList, setCourseList] = useState([]);

    const [selectedCourses, setSelectedCourses] = useState([]);

    useEffect(() => {
        getCourses();
        if (editData) {
            getAssignedCourses();
        }
    }, [editData]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getAssignedCourses = async () => {
        await tanstackApi
            .post("learner-permission/assigned-courses-to-trainers", {
                userId: editData,
            })
            .then((res) => {
                setAssignedCourses(res?.data?.assignedCourses);
            })
            .catch((err) => {
                setAssignedCourses([]);
            });
    };

    useEffect(() => {
        if (assignedCourses?.length > 0) {
            if (courseList.filter((dt) => assignedCourses.includes(dt?.id)).length > 0) {
                setSelectedCourses(assignedCourses?.map((dt) => dt?.course_id) || []);
            }
        }
    }, [assignedCourses]);

    const onCourseSelection = (courseId) => {
        if (selectedCourses?.includes(courseId)) {
            setSelectedCourses(selectedCourses?.filter((dt) => dt !== courseId));
        } else {
            setSelectedCourses([...selectedCourses, courseId]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        let options = assignedCourses?.map((dt) => dt?.course_id) || [];
        let removeData = options?.filter((item) => !selectedCourses?.includes(item));

        const payload = {
            userId: editData,
            courseIds: selectedCourses,
            deletedIds: removeData,
        };
        await tanstackApi
            .post("learner-permission/add-courses-to-trainers", { ...payload })
            .then((res) => {
                toast.success(`${selectedCourses?.length} Courses Assigned`, {
                    description: res?.data?.message,
                });
                setOpenAlert(false);
                setOpen(false);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Add Members, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected members in your Team&apos;s details.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-5xl">
                    <DialogHeader>
                        <div className="tw-flex tw-items-end tw-justify-between">
                            <div>
                                <DialogTitle>
                                    <h1 className="tw-text-2xl">Assign Courses to Trainer</h1>
                                </DialogTitle>
                                <DialogDescription>
                                    Select the courses & press save button to apply changes.
                                </DialogDescription>
                            </div>
                            <div>
                                <p className="tw-font-mono tw-text-sm tw-font-semibold">
                                    {selectedCourses?.length} Course selected
                                </p>
                            </div>
                        </div>
                    </DialogHeader>
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Banner</th>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Category</th>
                                    <th>Access</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {courseList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td
                                            style={{ width: "130px" }}
                                            onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}
                                        >
                                            <div className="table_image_alpha">
                                                <img
                                                    src={row?.course_banner_url || "/assets/course-placeholder.png"}
                                                    alt=""
                                                />
                                            </div>
                                        </td>
                                        <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                            {row?.course_title}
                                        </td>
                                        <td>{row?.is_scorm ? "SCORM" : "Combined"}</td>
                                        <td>{row?.lms_course_category?.category_name}</td>
                                        <td>{row?.is_public ? "Public" : "Private"}</td>
                                        <td>
                                            {selectedCourses?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                Close
                            </Button>
                        </DialogClose>
                        <Button onClick={() => setOpenAlert(true)}>Assign Courses</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default AssignCoursesTrainer;
