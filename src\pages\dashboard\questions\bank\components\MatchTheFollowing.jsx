import { DndContext, useDraggable, useDroppable } from "@dnd-kit/core";
import { motion } from "framer-motion";
import { useState } from "react";

const MatchTheFollowing = ({ onSlideEdit, onRemoveSlide, index, content, template }) => {
    const matcher = ["#FFE4E1", "#98FB98", "#87CEEB", "#DDA0DD", "#FFE4E1", "#98FB98", "#87CEEB", "#DDA0DD"];

    const [optionList, setOptionList] = useState(content?.matchOptions);
    const [answerList, setAnswerList] = useState(content?.answerKey ?? Array(content?.matchOptions.length).fill({}));

    function handleDragEnd(event) {
        const { active, over } = event;
        if (!over) return;
        const value = active.data.current.label;
        const container = over.id;
        const option = content?.matchOptions?.find((dt, i) => (value ? dt.text == value : active.id == i));

        if (container === "option") {
            const exists = optionList.find((arr) => arr?.text === option.text);
            if (exists) return;
            const newDraggableOptions = [...optionList, option];
            setOptionList(newDraggableOptions);
            setAnswerList(answerList?.filter((dt) => dt?.text !== option.text));
            return;
        }

        const exists = answerList.find((arr) => arr?.text === option.text);
        const newIndex = Number(container.split("-")[1]);
        const newBlanks = [...answerList];
        if (exists) {
            const findIndex = answerList.findIndex((arr) => arr?.text === option.text);
            newBlanks[newIndex] = exists;
            newBlanks[findIndex] = answerList[newIndex];
        } else {
            newBlanks[newIndex] = option;
        }
        setAnswerList(newBlanks);
        setOptionList(optionList?.filter((dt) => dt?.text !== option.text));
    }

    return (
        <div
            className="tw-grid tw-min-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"

            // style={{ backgroundImage: `url(${template?.background||content?.question_background})` }}
        >
            <div className="tw-px-10 tw-py-7">
                <div className="tw-grid tw-h-full tw-min-h-[20rem] tw-w-full tw-grid-cols-1 tw-gap-6">
                    <DndContext onDragEnd={handleDragEnd}>
                        <div className="tw-grid tw-grid-cols-4 tw-grid-rows-[1fr_minmax(40px,_auto)] tw-gap-x-6">
                            {content?.matchOptions?.map((option, index) => (
                                <motion.div
                                    key={index}
                                    initial={{
                                        opacity: 0.5,
                                        scale: 0.2,
                                    }}
                                    whileInView={{
                                        scale: 1,
                                        opacity: 1,
                                    }}
                                    transition={{
                                        duration: 0.25,
                                        delay: index * 0.1,
                                        ease: "easeInOut",
                                    }}
                                    className="tw-h-full tw-rounded-lg tw-bg-teal-200 tw-px-8 tw-py-2 tw-text-center tw-font-mono tw-text-[40px] tw-leading-[40px] tw-text-white"
                                >
                                    {option?.imageLabel && (
                                        <img src={option?.imageLabel} alt="" className="tw-h-20 tw-w-full" />
                                    )}
                                    {option?.label}
                                </motion.div>
                            ))}
                            {content?.matchOptions.map((ans, idx) => {
                                return (
                                    <DropZone key={idx} current={idx} blanks={answerList} matcher={matcher}>
                                        <div
                                            className="tw-h-full tw-w-full tw-rounded-lg tw-border tw-border-dashed tw-border-gray-600"
                                            style={{ backgroundColor: matcher[idx] }}
                                        />
                                    </DropZone>
                                );
                            })}
                        </div>
                        <div className="tw-flex tw-min-w-16 tw-items-center tw-justify-center tw-gap-2">
                            <OptionDropZone>
                                {optionList.map((opt, idx) => {
                                    return (
                                        <DropItem key={idx} index={`option-${idx}`} label={opt?.text}>
                                            <div
                                                className="tw-flex tw-h-fit tw-w-fit tw-items-center tw-justify-center tw-rounded-lg tw-border-gray-600 tw-px-10 tw-py-2 tw-font-mono tw-text-[35px] tw-leading-[35px]"
                                                style={{ backgroundColor: matcher[Number(opt.id) - 1] }}
                                            >
                                                {opt?.text}
                                            </div>
                                        </DropItem>
                                    );
                                })}
                            </OptionDropZone>
                        </div>
                    </DndContext>
                </div>
            </div>
        </div>
    );
};

export default MatchTheFollowing;

function OptionDropZone({ children }) {
    const { setNodeRef } = useDroppable({
        id: "option",
    });

    return (
        <div
            ref={setNodeRef}
            className="tw-relative tw-z-50 tw-flex tw-min-h-[3rem] tw-w-full tw-min-w-16 tw-cursor-grab tw-items-center tw-justify-center tw-gap-6 tw-rounded-xl tw-border"
        >
            {children}
        </div>
    );
}

function DropItem({ index, label, children }) {
    const { attributes, listeners, setNodeRef, transform } = useDraggable({
        id: index,
        data: { label },
    });

    return (
        <motion.div
            initial={{
                opacity: 0.5,
                scale: 0.2,
            }}
            whileInView={{
                scale: 1,
                opacity: 1,
            }}
            transition={{
                duration: 0.25,
                delay: index * 0.1,
                ease: "easeInOut",
            }}
            ref={setNodeRef}
            style={{
                x: transform?.x,
                y: transform?.y,
            }}
            {...listeners}
            {...attributes}
        >
            {children}
        </motion.div>
    );
}

function DropZone({ blanks, current, matcher }) {
    const { setNodeRef } = useDroppable({
        id: `answer-${current}`,
    });

    return (
        <div
            ref={setNodeRef}
            className="tw-mr-1 tw-inline-block tw-min-h-8 tw-min-w-20 tw-rounded-md tw-border-2 tw-border-dashed tw-border-black tw-px-3 tw-py-0.5 !tw-text-[40px] !tw-leading-[40px]"
        >
            {blanks[Number(current)]?.text ? (
                <DropItem index={Number(current)} label={blanks[Number(current)]?.text}>
                    <div
                        className="tw-flex tw-h-fit tw-w-full tw-cursor-grab tw-items-center tw-justify-center tw-rounded-lg tw-border-gray-600 tw-px-7 tw-py-2 tw-font-mono tw-text-[35px]"
                        style={{ backgroundColor: matcher[Number(blanks[Number(current)].id) - 1] }}
                    >
                        {blanks[Number(current)]?.text}
                    </div>
                </DropItem>
            ) : (
                blanks[Number(current)]?.text
            )}
        </div>
    );
}
