import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTable } from "@/pages/dashboard/users/control-panel/data-table";
import { useGetCoursesBundle, useGetUserAssignCourseBundle, useGetUserCourseBundle } from "@/react-query/courses";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function AssignCourseBundle({ userId }) {
    const bundles = useGetCoursesBundle();
    const assignedBunlde = useGetUserCourseBundle({ userId });
    const update = useGetUserAssignCourseBundle({ userId });
    const [search, setSearch] = useState([]);
    const [courseBundle, setCourseBundle] = useState([]);
    const [selectedBundle, setSelectedBundle] = useState([]);
    const [assigned, setAssigned] = useState([]);
    const [delted, setDelted] = useState([]);
    const [assignedBundleID, setAssignedBundleID] = useState([]);
    const [alreadyAssignedBundle, setAlreadyAssignedBundle] = useState([]);

    const selectBundle = (id) => {
        if (delted.includes(id)) {
            const fitered = delted.filter((dt) => dt !== id);
            setDelted(fitered);
        }
        if (selectedBundle.includes(id)) {
            setSelectedBundle([...selectedBundle]);
        } else {
            if (!assignedBundleID.includes(id)) {
                setSelectedBundle([...selectedBundle, id]);
            }
        }

        if (assigned.includes(id)) {
            setAssigned([...assigned]);
        } else {
            setAssigned([...assigned, id]);
        }
    };

    const unSelectBundleHandler = (id) => {
        if (assignedBundleID.includes(id)) {
            if (!delted.includes(id)) {
                setDelted([...delted, id]);
            }
        }
        if (assigned.includes(id)) {
            const filtered = assigned.filter((course) => course !== id);
            setAssigned(filtered);
        }

        if (selectedBundle.includes(id)) {
            const filtered = selectedBundle.filter((course) => course !== id);
            setSelectedBundle(filtered);
        }
    };

    const assignBundleHandler = () => {
        const payload = {
            userId,
            courseBundleIds: selectedBundle,
            deletedIds: delted,
        };
        update.mutate(payload);
    };

    useEffect(() => {
        if (update.status == "success") {
            toast.success("Courses Bundle Assigned", {
                description: update.data.message,
            });
            setSelectedBundle([]);
            setDelted([]);
        }
        if (update.status == "error")
            toast.error("Something went wrong", {
                description: update.error.response?.data?.message,
            });
    }, [update.status]);

    useEffect(() => {
        if (bundles.status == "success") setCourseBundle(bundles.data.data);
    }, [bundles.status]);

    useEffect(() => {
        if (assignedBunlde.status == "success") setAlreadyAssignedBundle(assignedBunlde.data.courseBundles);
    }, [assignedBunlde.status]);

    useEffect(() => {
        setAssigned(alreadyAssignedBundle?.map((c) => c?.course_bundle_id));
        setAssignedBundleID(alreadyAssignedBundle?.map((c) => c?.course_bundle_id));
    }, [alreadyAssignedBundle]);

    if (bundles.isLoading) return <div>Loading...</div>;

    const columns = [
        {
            accessorKey: "name",
            header: "Title",
        },
        {
            accessorKey: "description",
            header: "Description",
            cell: ({ row }) => <p className="tw-line-clamp-3">{row.getValue("description")}</p>,
        },
        {
            accessorKey: "createdAt",
            header: "Created On",
            cell: ({ row }) => format(row.getValue("createdAt"), "PP"),
        },
        {
            accessorKey: "id",
            header: "Action",
            cell: ({ row }) => {
                const isAssigned = assigned?.includes(row.getValue("id"));
                const Icon = isAssigned ? DoneAllIcon : ControlPointIcon;
                return (
                    <Button
                        variant={isAssigned ? "primary" : "outline"}
                        onClick={() => {
                            if (isAssigned) return unSelectBundleHandler(row.getValue("id"));
                            selectBundle(row.getValue("id"));
                        }}
                    >
                        <Icon /> {isAssigned ? "Assigned" : "Select"}
                    </Button>
                );
            },
        },
    ];
    return (
        <div>
            <div className="tw-my-3 tw-flex tw-items-center tw-justify-between tw-gap-2 tw-font-lexend">
                <div className="tw-w-full">
                    <Input
                        placeholder="Search Courses..."
                        type="search"
                        value={search}
                        onChange={(event) => setSearch(event.target.value)}
                        className="tw-w-full tw-max-w-lg"
                    />
                </div>
                <div className="tw-flex tw-w-1/4 tw-items-center tw-justify-end tw-gap-3">
                    {selectedBundle?.length > 0 && <p>{selectedBundle?.length} Course Selected</p>}
                    {(delted?.length > 0 || selectedBundle.length > 0) && (
                        <Button variant="success" onClick={assignBundleHandler}>
                            {delted?.length > 0 ? "Update Bundle" : "Assign Bundle"}
                        </Button>
                    )}
                </div>
            </div>
            <DataTable columns={columns} data={courseBundle} search={search} />
        </div>
    );
}
