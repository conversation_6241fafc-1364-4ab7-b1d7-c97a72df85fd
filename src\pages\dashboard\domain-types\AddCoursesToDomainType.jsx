import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const AddCoursesToDomainType = ({ getUserGroups }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [courseList, setCourseList] = useState([]);
    const [groupCourses, setGroupCourses] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedCourses, setSelectedCourses] = useState([]);

    useEffect(() => {
        getCourses();
        if (params?.group_id !== undefined) {
            getGroupCourses({ group_id: params?.group_id });
        }
    }, [params]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getGroupCourses = async (payload) => {
        await tanstackApi
            .post("user-groups/list-courses", { ...payload })
            .then((res) => {
                setGroupCourses(res?.data?.data?.filter((dt) => dt?.course_id !== null));
            })
            .catch((err) => {
                setGroupCourses([]);
            });
    };

    useEffect(() => {
        if (groupCourses !== null) {
            setSelectedCourses(groupCourses?.map((dt) => dt?.course_id) || []);
        }
    }, [groupCourses]);

    const onCourseSelection = (item) => {
        if (selectedCourses?.includes(item)) {
            setSelectedCourses(selectedCourses?.filter((dt) => dt !== item));
        } else {
            setSelectedCourses([...selectedCourses, item]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (selectedCourses?.length == 0) {
            toast?.warning("Courses", {
                description: "Please select some Courses for Bundle.",
            });
            return false;
        }

        if (params?.domain_type_id !== undefined) {
            const payload = {
                domain_type: params?.domain_type_id,
                course_ids: selectedCourses,
            };

            await tanstackApi
                .post("domain-course/assign-domain-type", { ...payload })
                .then((res) => {
                    toast.success("Assigned Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected courses to all the domains which having &quot;
                            {params?.domain_type_id}&quot; domain type.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Assign courses to domain type - {params?.domain_type_id}</CardTitle>
                            <CardDescription>
                                Click on select button to select any course for {params?.domain_type_id} . Click on Add
                                Course button below when you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{selectedCourses?.length} Course Selected</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Banner</th>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Category</th>
                                    <th>Access</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {courseList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td
                                            style={{ width: "130px" }}
                                            onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}
                                        >
                                            <div className="table_image_alpha">
                                                <img
                                                    src={row?.course_banner_url || "/assets/course-placeholder.png"}
                                                    alt=""
                                                />
                                            </div>
                                        </td>
                                        <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                            {row?.course_title}
                                        </td>
                                        <td>{row?.is_scorm ? "SCORM" : "Combined"}</td>
                                        <td>{row?.lms_course_category?.category_name}</td>
                                        <td>{row?.is_public ? "Public" : "Private"}</td>
                                        <td>
                                            <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                {selectedCourses?.includes(row?.id) ? "Selected" : "Choose"}Choose
                                            </Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                {selectedCourses?.length > 0 && (
                    <CardFooter>
                        <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                            {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                            <Button onClick={() => setOpenAlert(true)}>
                                <i className="fa-solid fa-floppy-disk"></i> Add Courses
                            </Button>
                        </div>
                    </CardFooter>
                )}
            </Card>
        </>
    );
};

export default AddCoursesToDomainType;
