import Pagination from "@/components/table/pagination";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import AssignmentsForm from "./AssignmentsForm";

const ITEMS_PER_PAGE = 7;

const filterStateDefault = {
    search: "",
    course_id: "",
};

const AssignmentMaster = () => {
    const [searchParams] = useSearchParams();
    const [DataList, setDataList] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [currentPage, setCurrentPage] = useState(Number(searchParams.get("page") || 1));
    const [open, setOpen] = useState(searchParams.get("type") == "create" ? true : false);
    const [editData, setEditData] = useState(null);
    const [filteredData, setFilteredData] = useState([]);
    const [courseList, setCourseList] = useState([]);
    const [filterState, setFilterState] = useState(filterStateDefault);

    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    useEffect(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        setTableData(filteredData.slice(startIndex, endIndex));
    }, [currentPage, filteredData]);

    const onSearch = () => {
        const data = DataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.homework_title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const subscription = filterState?.course_id ? item?.course_id == filterState?.course_id : true; // Allow all items if no subCategory filter

            return matchesSearch && subscription; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
        handlePageChange(1);
    };

    const onClear = () => {
        setFilteredData(DataList);
        setFilterState(filterStateDefault);
        handlePageChange(1);
    };

    useEffect(() => {
        getHomeworkData();
        getCourses();
    }, []);

    useEffect(() => {
        setFilteredData(DataList);
    }, [DataList]);

    const getHomeworkData = async () => {
        await tanstackApi
            .post("homework/list", {
                is_public: localStorage.getItem("level") == "levelOne" ? true : false,
                is_assignment: true,
            })
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const onAddHomework = (data) => {
        setEditData(null);
        setOpen(true);
    };

    const onEditHomework = (data) => {
        setEditData(data);
        setOpen(true);
    };

    return (
        <div>
            <AssignmentsForm open={open} setOpen={setOpen} editData={editData} getHomeworkData={getHomeworkData} />
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-laptop-file"></i> Assignments
                </h4>
                <Button className="tw-px-2 tw-py-1" onClick={onAddHomework}>
                    <i className="fa-solid fa-plus"></i> Create Assignment
                </Button>
            </div>
            <br />
            <div className="page_filters tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Title
                    </label>
                    <input
                        value={filterState?.search}
                        name="search"
                        onChange={onFilterChange}
                        className="tw-text-sm"
                        style={{ minWidth: "300px" }}
                        type="text"
                        placeholder="Search by title ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Course
                    </label>
                    <select
                        className="tw-text-sm"
                        value={filterState?.course_id}
                        name="course_id"
                        onChange={onFilterChange}
                    >
                        <option value=""> - Choose Course - </option>
                        {courseList?.map((type, idx) => (
                            <option value={type?.id} key={idx}>
                                {type?.course_title}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table className="tw-font-lexend">
                    <thead>
                        <tr>
                            <th>Assignment Title</th>
                            <th>Points</th>
                            <th>Access</th>
                            <th>Related Course</th>
                            <th>Submission Date</th>
                            <th>Created On</th>
                            <th>Submission & Remarks</th>
                        </tr>
                    </thead>
                    <tbody className="tw-font-lexend">
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>{row?.homework_title}</td>
                                <td>
                                    <b>{row?.homework_points}</b>
                                </td>
                                <td>
                                    <Badge variant={"outline"}>{row?.is_public ? "Public" : "Private"}</Badge>
                                </td>
                                <td>{row?.lms_course?.course_title}</td>
                                <td>{moment(row?.submission_date).format("LL")}</td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td>
                                    <button className="selected_btn" onClick={() => onEditHomework(row)}>
                                        <i className="fa-regular fa-edit"></i> Edit assignment
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">
                        {ITEMS_PER_PAGE}
                    </p>
                </div>
                <div>
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        handlePageChange={handlePageChange}
                        itemsPerPage={ITEMS_PER_PAGE}
                    />
                </div>
            </div>
        </div>
    );
};

export default AssignmentMaster;
