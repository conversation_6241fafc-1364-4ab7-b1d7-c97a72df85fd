import { <PERSON><PERSON> } from "@/components/ui/button";
import { tanstack<PERSON>pi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
// import QuestionTypeForm from "./QuestionTypeForm";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { utils, writeFileXLSX } from "xlsx";
import { Badge } from "@/components/ui/badge";

const dropdownOptions = [
    { label: "Homework", value: "homework" },
    { label: "Shop", value: "shop" },
    { label: "User", value: "user" },
    { label: "Interactions", value: "interactions" },
    { label: "Quiz", value: "quiz" },
    { label: "User Groups", value: "user groups" },
    { label: "Course", value: "course" },
    { label: "Course Bundles", value: "course bundles" },
    { label: "Teams", value: "teams" },
    { label: "Sessions", value: "sessions" },
    { label: "Assignments", value: "assignments" },
    { label: "Question Pool", value: "question pool" },
    { label: "Learning Path", value: "learning path" },
    { label: "Login", value: "login" },
    { label: "Certificate", value: "certificate" },
    { label: "Signup", value: "signup" },
];

const TimelineLogs = () => {
    const params = useParams();
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(11);

    const [filterState, setFilterState] = useState({
        fromDate: "",
        toDate: "",
        event: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        if (filteredData?.length > 0) {
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;
            let options = filteredData.slice(startIndex, endIndex);
            setTableData(options);
        }
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        if (filterState?.fromDate && filterState?.toDate) {
            const from = new Date(filterState?.fromDate);
            const to = new Date(filterState?.toDate);

            const data = dataList.filter((item) => {
                from.setHours(0, 0, 0, 0);
                to.setHours(23, 59, 59, 999); //

                const recordDate = new Date(item.createdAt);
                recordDate.setHours(0, 0, 0, 0);

                const dateRangeData = recordDate >= from && recordDate <= to;

                const matchesEvent = filterState?.event ? item?.event === filterState?.event : true; // Allow all items if no subCategory filter

                return dateRangeData && matchesEvent; // Both must match
            });
            // setPageNumber(1);
            setFilteredData(data);
        } else {
            const data = dataList.filter((item) => {
                const matchesEvent = filterState?.event ? item?.event === filterState?.event : true; // Allow all items if no subCategory filter

                return matchesEvent; // Both must match
            });
            // setPageNumber(1);
            setFilteredData(data);
        }
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            fromDate: "",
            toDate: "",
            event: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getReports();
    }, []);

    const getReports = async () => {
        await tanstackApi
            .get("users/get-timeline-log", { params: { user_id: params?.domain_id } })
            .then((res) => {
                setDataList(res?.data?.data?.timeline_data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    function excelConverter() {
        var finalData = filteredData?.map((row, index) => {
            return {
                Event: row?.event,
                Summary: row?.log,
                "Created On": moment(row?.createdAt).format("LL"),
            };
        });
        const wb = utils.book_new();
        utils.book_append_sheet(wb, utils.json_to_sheet(finalData));
        writeFileXLSX(wb, `Timeline Logs - ${filterState?.fromDate} to ${filterState?.toDate}.xlsx`);
    }

    return (
        <>
            <div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                        <div className="input_group">
                            <label className="tw-text-sm" htmlFor="">
                                From
                            </label>
                            <input
                                className="tw-text-sm"
                                type="date"
                                onChange={onFilterChange}
                                value={filterState?.fromDate}
                                name="fromDate"
                            />
                        </div>
                        <div className="input_group">
                            <label className="tw-text-sm" htmlFor="">
                                To
                            </label>
                            <input
                                className="tw-text-sm"
                                type="date"
                                onChange={onFilterChange}
                                value={filterState?.toDate}
                                name="toDate"
                            />
                        </div>
                        <div className="input_group">
                            <label className="tw-text-sm" htmlFor="">
                                Event
                            </label>
                            <select
                                className="tw-text-sm"
                                onChange={onFilterChange}
                                value={filterState?.event}
                                name="event"
                            >
                                <option value=""> - Select Event - </option>
                                {dropdownOptions?.map((data, idx) => (
                                    <option value={data?.value} key={idx}>
                                        {data?.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="filter_controls">
                            <button className="search_btn" onClick={onSearch}>
                                <i className="fa-solid fa-magnifying-glass"></i> Search
                            </button>
                            <button className="clear_btn" onClick={onClear}>
                                <i className="fa-solid fa-xmark"></i> Clear
                            </button>
                        </div>
                    </div>
                    <Button className="tw-px-2 tw-py-1" onClick={excelConverter}>
                        <i className="fa-solid fa-download"></i> Export Data
                    </Button>
                </div>
                {/* <br /> */}

                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>Event</th>
                                <th>Summary</th>
                                <th>Creation On</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>
                                        <Badge variant={"outline"} className="tw-capitalize">
                                            {row?.event}
                                        </Badge>
                                    </td>
                                    <td>{row?.log}</td>
                                    <td>{moment(row?.createdAt).format("LL")}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination className="">
                            <PaginationContent>
                                {/* Previous Button */}
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {/* Page Numbers with Ellipses */}
                                {currentPage > 3 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                1
                                            </PaginationLink>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    </>
                                )}

                                {getVisiblePages().map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href="#"
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {currentPage < totalPages - 2 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                {totalPages}
                                            </PaginationLink>
                                        </PaginationItem>
                                    </>
                                )}

                                {/* Next Button */}
                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </>
    );
};

export default TimelineLogs;
