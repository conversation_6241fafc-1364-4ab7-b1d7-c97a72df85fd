import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MultipleSelector from "@/components/ui/multiselect";
import { SelectNative } from "@/components/ui/select-native";
import { UpdateModule } from "@/redux/module/action";
import { useEffect, useId, useState } from "react";
import { useDispatch } from "react-redux";

export default function ModulesForm({ open, setOpen, selected }) {
    const permissionList = [
        { value: "CREATE", label: "Create" },
        { value: "READ", label: "Read" },
        { value: "UPDATE", label: "Update" },
        { value: "DELETE", label: "Delete" },
    ];

    const dispatch = useDispatch();
    const formId = useId();

    const [selectedTrainers, setSelected<PERSON>rainers] = useState([]);
    const [optionsArray, setOptionsArray] = useState(permissionList);

    const [data, setData] = useState({
        isActive: true,
        display_name: "",
        category_code: "administrator",
    });

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setData({ ...data, [name]: value });
    };

    useEffect(() => {
        if (selected !== null) {
            setData({
                display_name: selected.display_name,
                isActive: Boolean(selected.is_active),
                id: selected.id,
            });

            const updatedOptions = permissionList.filter((permission) =>
                selected?.possible_permissions?.includes(permission.value),
            );

            setOptionsArray(permissionList);
            setSelectedTrainers(updatedOptions);
        } else {
            setData({
                display_name: "",
                category_code: "administrator",
                isActive: "true",
            });

            setOptionsArray(permissionList);
            setSelectedTrainers([]);
        }
    }, [selected]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        const perm = selectedTrainers?.map((itm) => itm?.value);

        if (selected !== null) {
            const payload = {
                display_name: data?.display_name,
                is_active: data?.isActive === "true" ? true : false,
                possible_permissions: perm,
                category_code: [data?.category_code],
                id: selected.id,
            };
            dispatch(UpdateModule(payload));
            setOpen(false);
        } else {
            const payload = {
                display_name: data?.display_name,
                category_code: [data?.category_code],
                possible_permissions: perm,
                is_active: data?.isActive === "true" ? true : false,
            };
            setOpen(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="tw-z-[1000]">
                <DialogHeader>
                    <DialogTitle>{selected ? "Edit Module" : "Create Module"}</DialogTitle>
                </DialogHeader>
                <div>
                    <form id={formId} onSubmit={handleSubmit} className="tw-space-y-3">
                        <Input type="hidden" name="id" id="id" value={data?.id} />

                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="display_name">Module Name</Label>
                            <Input
                                type="text"
                                name="display_name"
                                value={data?.display_name}
                                onChange={onChangeHandle}
                                id="display_name"
                                placeholder="Module Name"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label>Options</Label>
                            <MultipleSelector
                                commandProps={{
                                    label: "Select Permission",
                                }}
                                defaultOptions={optionsArray}
                                value={selectedTrainers} // Ensure selected options are prefilled
                                onChange={(e) => setSelectedTrainers(e)}
                                placeholder="Select Options"
                                hideClearAllButton
                                hidePlaceholderWhenSelected
                                emptyIndicator={<p className="text-center text-sm">No results found</p>}
                            />
                        </div>
                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="category_code">Category Code</Label>
                            <SelectNative
                                name="category_code"
                                id="category_code"
                                value={data?.category_code}
                                onChange={onChangeHandle}
                                placeholder=""
                            >
                                <option value="administrator">Administrator</option>
                                <option value="user">User</option>
                            </SelectNative>
                        </div>
                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="is_active">Module Status</Label>
                            <SelectNative
                                name="isActive"
                                id="is_active"
                                value={data?.isActive}
                                onChange={onChangeHandle}
                                placeholder=""
                            >
                                <option value="true">Active</option>
                                <option value="false">In Active</option>
                            </SelectNative>
                        </div>
                    </form>
                </div>
                <DialogFooter>
                    <Button variant="secondary" onClick={() => setOpen(false)}>
                        Cancel
                    </Button>
                    <Button variant="success" form={formId} type="submit">
                        Save
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
