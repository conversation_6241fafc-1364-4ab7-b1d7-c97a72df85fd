import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const AddCoursesToGroup = ({ getUserGroups }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [courseList, setCourseList] = useState([]);
    const [domainCourses, setDomainCourses] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedCourses, setSelectedCourses] = useState([]);

    useEffect(() => {
        if (courseList?.length > 0 && domainCourses?.length > 0) {
            const matchedCourses = courseList.filter((course) =>
                domainCourses.some((domainCourse) => domainCourse.course_id === course.id),
            );
            setSelectedCourses([...new Set(matchedCourses?.map((dt) => dt?.id))]);
        }
    }, [courseList, domainCourses]);

    useEffect(() => {
        getCourses();
        if (params?.domain_id !== undefined) {
            getDomainCourses();
        }
    }, [params]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getDomainCourses = async () => {
        await tanstackApi
            .post("domain-course/list-assigned-courses-and-bundles", { domain_id: params?.domain_id })
            .then((res) => {
                setDomainCourses(res?.data?.data);
            })
            .catch((err) => {
                setDomainCourses([]);
            });
    };

    const onCourseSelection = (item) => {
        setSelectedCourses([
            ...new Set(
                selectedCourses?.includes(item)
                    ? selectedCourses?.filter((dt) => dt !== item)
                    : [...selectedCourses, item],
            ),
        ]);
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (params?.domain_id !== undefined) {
            const payload = {
                user_id: params?.domain_id,
                course_ids: selectedCourses,
            };

            await tanstackApi
                .post("domain-course/assign-course", { ...payload })
                .then((res) => {
                    toast.success("Assigned Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    // getUserGroups(params?.domain_id);
                    // navigate(`/dashboard/course-bundle`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected courses in your domain&apos;s course listing.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Assign Courses to domain</CardTitle>
                            <CardDescription>
                                Click on select button to select any course for domain. Click save when you&apos;re
                                done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{selectedCourses?.length} Course Selected</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Banner</th>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Category</th>
                                    <th>Access</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {courseList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td
                                            style={{ width: "130px" }}
                                            onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}
                                        >
                                            <div className="table_image_alpha">
                                                <img
                                                    src={row?.course_banner_url || "/assets/course-placeholder.png"}
                                                    alt=""
                                                />
                                            </div>
                                        </td>
                                        <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                            {row?.course_title}
                                        </td>
                                        <td>{row?.is_scorm ? "SCORM" : "Combined"}</td>
                                        <td>{row?.lms_course_category?.category_name}</td>
                                        <td>{row?.is_public ? "Public" : "Private"}</td>
                                        <td>
                                            {selectedCourses?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>
                                                    <i className="fa-regular fa-circle-check"></i> Assigned
                                                </Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                        <Button onClick={() => setOpenAlert(true)}>
                            <i className="fa-solid fa-floppy-disk"></i> Add Courses
                        </Button>
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AddCoursesToGroup;
