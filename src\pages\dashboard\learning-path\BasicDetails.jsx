import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { SelectNative } from "@/components/ui/select-native";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const BasicDetails = ({ pathData, getLearningPath }) => {
    const params = useParams();
    const navigate = useNavigate();

    const AudienceTypes = ["PUBLIC", "PRIVATE"];

    const [pathDetail, setPathDetail] = useState({
        name: "",
        code: "",
        description: "",
        certificate_id: null,
        is_enforce_sequence: true,
        status: "Active",
        learning_path_logo_url: "",
        learning_path_status: "PUBLIC",
        learning_path_details: {
            test: "test",
        },
    });

    const [certificateList, setCertificateList] = useState([]);

    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        const allowedChars = /^[a-zA-Z0-9 ]*$/;
        if (name == "name") {
            if (!allowedChars.test(value)) {
                e.preventDefault();
            } else {
                setPathDetail({ ...pathDetail, [name]: value });
            }
        } else {
            setPathDetail({ ...pathDetail, [name]: value });
        }
    };

    useEffect(() => {
        getCertificates();
    }, []);

    useEffect(() => {
        if (pathData !== null) {
            setPathDetail({
                name: pathData?.name,
                code: pathData?.code,
                description: pathData?.description,
                certificate_id: pathData?.certificate_id,
                is_enforce_sequence: pathData?.is_enforce_sequence,
                learning_path_logo_url: pathData?.learning_path_logo_url,
                status: pathData?.status,
                learning_path_status: pathData?.learning_path_status,
                learning_path_details: {
                    test: "learning_path_details",
                },
            });
        } else {
            setPathDetail({
                name: "",
                code: "",
                description: "",
                certificate_id: null,
                is_enforce_sequence: true,
                status: "Active",
                learning_path_logo_url: "",
                learning_path_status: "PUBLIC",
                learning_path_details: {
                    test: "test",
                },
            });
        }
    }, [pathData]);

    const onClearData = () => {
        setPathDetail({
            name: "",
            code: "",
            description: "",
            certificate_id: "",
            is_enforce_sequence: false,
            status: "Active",
            learning_path_logo_url: "",
            learning_path_status: "PUBLIC",
            learning_path_details: {
                test: "test",
            },
        });
    };

    const RemoveImage = () => {
        setPathDetail({ ...pathDetail, learning_path_logo_url: "" });
    };

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "LEARNING-PATHS");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setPathDetail({ ...pathDetail, learning_path_logo_url: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setPathDetail({ ...pathDetail, learning_path_logo_url: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setPathDetail({ ...pathDetail, learning_path_logo_url: "" });
            });
    };

    const getCertificates = async (type) => {
        await tanstackApi
            .get(`certificate/get-certificates/learning-path`)
            .then((res) => {
                setCertificateList(res?.data?.data?.certificateList);
            })
            .catch((err) => {
                setCertificateList([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!pathDetail?.learning_path_logo_url) {
            toast?.warning("Path Banner", {
                description: "Path Banner is required",
            });
            return false;
        }
        if (!pathDetail?.name) {
            toast?.warning("Path Name", {
                description: "Path name is required",
            });
            return false;
        } else if (!pathDetail?.certificate_id) {
            toast?.warning("Certificate", {
                description: "Path Certificate selection is required",
            });
            return false;
        }

        if (params?.path_id !== undefined) {
            const payload = {
                id: params?.path_id,
                name: pathDetail?.name,
                code: pathDetail?.code || "ABC123",
                description: pathDetail?.description || "No Description Yet!",
                certificate_id: pathDetail?.certificate_id,
                is_enforce_sequence: pathDetail?.is_enforce_sequence,
                learning_path_logo_url: pathDetail?.learning_path_logo_url,
                status: pathDetail?.status,
                learning_path_status: pathDetail?.learning_path_status,
                learning_path_details: {
                    test: "learning_path_details",
                },
            };

            await tanstackApi
                .put("learning-path/update", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "learning path",
                        log: `${pathDetail?.name} details updated successfully.`,
                    });
                    toast.success("Learning path created", {
                        description: res?.data?.message,
                    });

                    getLearningPath(params?.path_id);
                    // navigate(`/dashboard/learning-path-view/${params?.path_id}`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                name: pathDetail?.name,
                code: pathDetail?.code || "ABC123",
                description: pathDetail?.description || "No Description Yet!",
                certificate_id: pathDetail?.certificate_id,
                is_enforce_sequence: pathDetail?.is_enforce_sequence,
                learning_path_logo_url: pathDetail?.learning_path_logo_url,
                status: pathDetail?.status,
                learning_path_status: pathDetail?.learning_path_status,
                learning_path_details: {
                    test: "learning_path_details",
                },
            };

            await tanstackApi
                .post("learning-path/create", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "learning path",
                        log: `${pathDetail?.name} details added successfully.`,
                    });
                    toast.success("Learning path created", {
                        description: res?.data?.message,
                    });

                    // getLearningPath({ course_id: params?.path_id });
                    navigate(`/dashboard/learning-path-view/${res?.data?.data?.id}`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Path basic details</CardTitle>
                <CardDescription>
                    Add learning path title, banner , decription etc here. Click save when you&apos;re done.
                </CardDescription>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[1.5fr_2fr] tw-gap-0">
                    <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                        <div className="tw-aspect-[2/1] tw-w-[90%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-md tw-object-cover"
                                src={pathDetail?.learning_path_logo_url || "/assets/thumbnail.png"}
                            />
                        </div>
                        <div className="tw-flex tw-gap-2">
                            {pathDetail?.learning_path_logo_url == "" && (
                                <Label
                                    htmlFor="learning_path_logo_url"
                                    className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                                >
                                    <Upload
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <input
                                        onChange={onImageChange}
                                        type="file"
                                        style={{ display: "none" }}
                                        id="learning_path_logo_url"
                                        accept="image/*"
                                    />
                                    <div className="max-sm:sr-only">
                                        {load ? "Uploading" : "Upload Image"} {load ? `${uploaded}%` : null}
                                    </div>
                                </Label>
                            )}
                            {pathDetail?.learning_path_logo_url !== "" && (
                                <Button variant="outline" className="aspect-square max-sm:p-0" onClick={RemoveImage}>
                                    <X
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <Label className="max-sm:sr-only">Remove</Label>
                                </Button>
                            )}
                        </div>
                    </div>
                    <div className="tw-space-y-3">
                        <div className="tw-grid tw-grid-cols-[1fr_150px] tw-gap-2">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Learning Path Name *</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={pathDetail?.name}
                                    name="name"
                                    id="name"
                                    placeholder="Enter learning path name here"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="code">Path Code</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={pathDetail?.code}
                                    name="code"
                                    id="code"
                                    placeholder="eg: ABC123"
                                />
                            </div>
                        </div>

                        <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                            <div className="tw-w-full tw-space-y-1">
                                <Label htmlFor="certificate_id">Certificate *</Label>
                                <SelectNative
                                    onChange={onChangeHandle}
                                    value={pathDetail?.certificate_id}
                                    name="certificate_id"
                                >
                                    <option value="Active">- Choose Certificate -</option>
                                    {certificateList?.map((data, idx) => (
                                        <option key={idx} value={data?.id}>
                                            {data?.title}
                                        </option>
                                    ))}
                                </SelectNative>
                            </div>
                            <div className="tw-w-full tw-space-y-1">
                                <Label htmlFor="status">Status</Label>
                                <SelectNative onChange={onChangeHandle} value={pathDetail?.status} name="status">
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </SelectNative>
                            </div>
                        </div>

                        <div className="tw-space-y-1">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                onChange={onChangeHandle}
                                value={pathDetail?.description}
                                name="description"
                                className="tw-text-sm"
                                placeholder="Define learning path description here."
                            />
                        </div>
                        <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                            <div className="tw-space-y-3">
                                <h1 className="tw-text-xl tw-font-semibold">Path Access</h1>
                                <div className="tw-flex-col-2 tw-mt-3 tw-flex tw-gap-10">
                                    <RadioGroup
                                        value={pathDetail?.learning_path_status}
                                        onValueChange={(e) =>
                                            onChangeHandle({ target: { value: e, name: "learning_path_status" } })
                                        }
                                        className="tw-flex"
                                    >
                                        <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                                            {AudienceTypes?.map((data, idx) => (
                                                <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                                    <RadioGroupItem
                                                        value={data}
                                                        id={data}
                                                        checked={pathDetail?.learning_path_status == data}
                                                    />
                                                    <Label
                                                        htmlFor={data}
                                                        className="tw-cursor-pointer tw-font-normal tw-capitalize"
                                                    >
                                                        {data?.toLowerCase()}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                    </RadioGroup>
                                </div>
                            </div>
                            <div className="tw-space-y-3">
                                <h1 className="tw-text-xl tw-font-semibold">Path Sequence</h1>
                                <div className="tw-items-top tw-flex tw-space-x-2">
                                    <Checkbox
                                        id="is_enforce_sequence"
                                        checked={pathDetail?.is_enforce_sequence}
                                        onCheckedChange={(e) =>
                                            onChangeHandle({ target: { value: e, name: "is_enforce_sequence" } })
                                        }
                                        name="is_enforce_sequence"
                                    />
                                    <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                        <label
                                            htmlFor="is_enforce_sequence"
                                            className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                        >
                                            Enforce Sequence
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" variant="outline" onClick={onClearData}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> {params?.path_id ? "Update" : "Save"}
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default BasicDetails;
