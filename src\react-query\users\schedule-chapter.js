import { tanstackApi } from "@/react-query/api";
import { useQuery } from "@tanstack/react-query";

export const useGetScheduledChapters = ({ params }) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["scheduled-chapter", { params, userId }],
        queryFn: async () => (await tanstackApi.get("course/creation/list-chapter", { params })).data,
        enabled: !!params,
    });
};

export const useGetTrainerScheduledChapters = ({ params }) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["scheduled-chapter", "trainer", { params, userId }],
        queryFn: async () => (await tanstackApi.get("chapter-class/list-trainer", { params })).data,
        enabled: !!params,
    });
};
