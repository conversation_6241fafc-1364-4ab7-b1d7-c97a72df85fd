import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion, useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

export default function TemplateTwo() {
    const [index, setIndex] = useState(0);
    const [start, setStart] = useState(false);
    const bgVariants = {
        initial: { rotate: 0 },
        animate: { rotate: [360, 0] },
    };

    const slides = [
        { id: "17b47063-47f7-5d31-9d3f-2d5f4d02e69d", comp: FirstSlide },
        { id: "c268b58d-5297-57d1-be19-a76b0da1bb3b", comp: SecondSlide },
        { id: "1c643a12-44b3-5a14-84f5-f0d10b5222ab", comp: ThirdSlide },
        { id: "47397587-0fe9-50b8-8ca0-cf52275a1a07", comp: FourthSlide },
        { id: "d7e6045f-9bef-51ec-afbc-f6a572ecf046", comp: FifthSlide },
        { id: "5b3b63b0-06d0-5405-9a83-d2e08653708d", comp: SixthSlide },
        { id: "7b3b63b0-06d0-5405-9a83-d2e08653708d", comp: SeventhSlide },
        { id: "8b3b63b0-06d0-5405-9a83-d2e08653708d", comp: EightSlide },
        { id: "9b3b63b0-06d0-5405-9a83-d2e08653708d", comp: NinthSlide },
        { id: "10b3b63b0-06d0-5405-9a83-d2e08653708d", comp: TenthSlide },
        { id: "11b3b63b0-06d0-5405-9a83-d2e08653708d", comp: EleventhSlide },
    ];

    const handleNextSlide = () => {
        if (index === slides.length - 1) return toast.error("NO Slide Available");
        if (index == 0 && start === false) setStart(true);
        setIndex((prevIndex) => prevIndex + 1);
    };
    const handlePrevSlide = () => {
        if (index === 0) return;
        if (index === 1) setStart(false);
        setIndex((prevIndex) => prevIndex - 1);
    };

    return (
        <div className="ContentQuizz tw-relative tw-h-full tw-w-full tw-overflow-hidden tw-rounded-[10px] tw-bg-[#5cdde8] tw-bg-cover tw-bg-center tw-bg-no-repeat !tw-font-lazyDog">
            <motion.div
                variants={bgVariants}
                initial="initial"
                transition={{ duration: 10, ease: "easeInOut", repeat: Infinity }}
                className="tw-absolute tw-inset-0 tw-z-0 tw-h-[100%] tw-bg-cover tw-bg-center tw-bg-no-repeat"
                style={{
                    backgroundImage: start ? `url('/quiz/background 1.gif')` : "url('/quiz/Frame.png')",
                }}
            ></motion.div>
            <motion.div
                initial={{ x: 0 }}
                animate={{
                    x: `calc(${index * -100}% - ${index * 2}rem)`,
                }}
                transition={{ duration: 1.5, ease: "easeInOut" }}
                className="tw-relative tw-z-10 tw-flex tw-h-full tw-w-full tw-items-center tw-gap-8"
            >
                {slides.map((slide, idx) => {
                    return (
                        <SlideContainer
                            key={idx}
                            slide={slide}
                            handleNextSlide={handleNextSlide}
                            handlePrevSlide={handlePrevSlide}
                        />
                    );
                })}
            </motion.div>
        </div>
    );
}

function SlideContainer({ slide, handleNextSlide, handlePrevSlide }) {
    return (
        <slide.comp
            className="tw-min-w-full tw-flex-grow"
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
        />
    );
}

function MainSlideLayout({ handleNextSlide, className, children }) {
    return (
        <div className={cn("tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center", className)}>
            <div className="tw-relative tw-h-[60%] tw-w-[80%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-[#FFFFFF]">
                <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center">
                    {children}
                    <button
                        onClick={handleNextSlide}
                        className="tw-absolute tw-bottom-[-10%] tw-right-[-2%] tw-aspect-square tw-w-[122px] tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-text-4xl"
                    >
                        Start
                    </button>
                </div>
                <img src="/quiz/Blitz.png" alt="" className="tw-absolute tw-right-[-8%] tw-top-[-22%] tw-z-[5]" />
                <img
                    src="/quiz/Group 59468.png"
                    alt=""
                    className="tw-absolute tw-bottom-[-18%] tw-left-[-11%] tw-z-[5]"
                />
                <img
                    src="/quiz/Frame 1597883374.png"
                    alt=""
                    className="tw-absolute tw-left-[-11.3%] tw-top-[-32.6%] tw-z-[-5]"
                />
                <div className="tw-absolute tw--left-10 tw-top-10 tw-z-[-10] tw-h-[102%] tw-w-[102%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-[#FE5C96]"></div>
            </div>
        </div>
    );
}

function GrowStarGroup({ className }) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            className={cn("tw-absolute tw-bottom-[5%] tw-right-[-5%] tw-z-0 tw-aspect-[1/1.5] tw-w-[150px]", className)}
            viewBox="0 0 221 188"
            fill="none"
        >
            <motion.path
                initial={{ scale: 1 }}
                animate={{
                    scale: 1.15,
                    transition: {
                        duration: 1,
                        delay: 0.4,
                        repeat: Infinity,
                        repeatType: "reverse",
                    },
                }}
                d="M29.817 58.8539C33.4435 60.6678 37.0691 62.8626 40.1111 65.8989C41.6098 67.4199 42.9267 69.1084 44.0356 70.9307C45.1144 72.7238 46.0307 74.6089 46.7739 76.5638C48.3144 80.6729 49.1504 84.9237 49.799 89.0891C49.969 90.1226 50.1223 91.1556 50.2666 92.1851L50.6819 95.2679L46.8983 95.737C46.2917 90.8879 45.8165 85.9051 46.2328 80.8503C46.6293 75.8179 47.9577 70.6898 50.6526 66.1982C51.329 65.0689 52.0901 63.9921 52.9296 62.9771C53.7713 61.9432 54.7066 60.9886 55.7238 60.1252C55.9715 59.8981 56.2407 59.7012 56.5042 59.4957C56.7712 59.2956 57.0282 59.0786 57.3048 58.8937C57.8565 58.5212 58.4038 58.137 58.9814 57.8133C60.1144 57.142 61.2954 56.5544 62.5148 56.0553C63.7123 55.5623 64.9363 55.1358 66.1812 54.7777C67.4142 54.4349 68.652 54.1181 69.8865 53.87C72.3581 53.3473 74.8139 52.9949 77.2355 52.6485L77.4743 57.2178L75.3164 57.1716C72.9716 57.1278 70.6057 57.0027 68.2215 56.8108C65.7809 56.6362 63.357 56.2795 60.9701 55.7436C58.4537 55.1839 56.0425 54.2302 53.8263 52.918C52.6814 52.214 51.6229 51.3793 50.6723 50.4311C49.7255 49.4775 48.9014 48.4105 48.219 47.2546C46.8708 44.9964 46.0441 42.6336 45.3373 40.3609C44.6237 38.0741 44.0294 35.7521 43.5568 33.4042C43.0723 31.0602 42.7263 28.6901 42.5205 26.3058C42.4214 25.1142 42.3441 23.9212 42.3068 22.7269L42.2793 21.8314C42.2722 21.533 42.2798 21.2339 42.2546 20.936C42.2201 20.3395 42.2127 19.7421 42.1907 19.1447L46.259 19.3365C45.917 23.6951 45.5512 28.1282 44.7076 32.5829C44.5925 33.1387 44.4872 33.697 44.3631 34.2515L43.9457 35.9124L43.4691 37.5666L42.927 39.2101C42.1587 41.4175 41.178 43.5458 39.9986 45.5654C37.7179 49.3831 34.5899 52.6304 30.854 55.0586C29.4097 56.0255 27.8296 56.7742 26.1651 57.2803C25.5572 57.4536 24.9365 57.5784 24.3087 57.6535C24.029 57.6839 23.7653 57.7036 23.5178 57.7128L23.335 57.7181L23.1871 57.6996C23.0832 57.6855 22.983 57.6719 22.8865 57.6588C21.6896 57.4847 21.025 57.2773 21.9472 56.3747C22.0806 56.2494 22.2217 56.1326 22.3696 56.0247C22.4469 55.9648 22.5562 55.897 22.6259 55.8403C22.6907 55.7863 22.7584 55.73 22.829 55.6713C23.1109 55.4385 23.4517 55.2109 23.8278 54.9416C24.2007 54.6659 24.6342 54.4038 25.0895 54.0797C25.3205 53.9262 25.5618 53.7659 25.8135 53.5986C26.0604 53.4245 26.3189 53.2477 26.5888 53.0683C27.9859 52.1143 29.3186 51.0703 30.5787 49.943C31.8656 48.8098 33.0628 47.5796 34.1597 46.2632C35.6547 44.4478 36.9114 42.4501 37.8994 40.3183C38.898 38.1412 39.6926 35.877 40.2729 33.5543C41.5186 28.8732 42.0818 23.9494 42.6103 18.9867L42.6107 18.9839C42.6546 18.5727 42.8548 18.1938 43.1702 17.9248C43.4856 17.6559 43.8924 17.5171 44.3072 17.537C44.722 17.557 45.1135 17.734 45.4014 18.032C45.6894 18.3299 45.852 18.7262 45.856 19.1397C45.8634 19.9448 45.8367 20.7514 45.8934 21.5558C45.9446 22.36 46.0054 23.1631 46.0872 23.9642C46.2435 25.567 46.4757 27.1601 46.7409 28.7449C47.2655 31.9125 48.0344 35.0352 49.0408 38.0852C49.6845 40.171 50.5206 42.193 51.5385 44.1254C52.0172 44.9988 52.5816 45.8229 53.2233 46.5856C53.8496 47.3022 54.5433 47.9575 55.295 48.5425C56.0006 49.0682 56.7496 49.5334 57.5342 49.933C58.3549 50.3352 59.202 50.6817 60.0697 50.97C61.8856 51.547 63.7518 51.9536 65.6437 52.1845C69.5188 52.6866 73.5462 52.7599 77.5969 52.9088L77.6207 52.9097C78.1281 52.9283 78.61 53.1362 78.9708 53.492C79.3315 53.8478 79.5447 54.3257 79.5682 54.8307C79.5916 55.3358 79.4235 55.8311 79.0972 56.2186C78.7709 56.606 78.3103 56.8571 77.8067 56.9222C74.7313 57.3196 71.7052 57.7354 68.7971 58.41C65.901 59.0992 63.0965 60.0025 60.6199 61.4202C59.5635 62.021 58.566 62.7191 57.6405 63.5053C56.7208 64.3092 55.8782 65.1967 55.1237 66.1561C53.6176 68.1403 52.4335 70.3475 51.6148 72.6972C50.7768 75.0814 50.2362 77.5591 50.0052 80.0746C49.7651 82.6316 49.7117 85.2025 49.8455 87.7671C49.953 90.2638 50.2081 92.7752 50.5039 95.2956L50.5058 95.3123C50.5588 95.7643 50.4296 96.2188 50.1465 96.5761C49.8635 96.9334 49.4497 97.1644 48.9958 97.2185C48.542 97.2725 48.0851 97.1451 47.7254 96.8643C47.3657 96.5835 47.1326 96.1721 47.077 95.7204C46.5801 91.6724 46.0556 87.659 45.1157 83.7901C44.6741 81.8645 44.1099 79.9689 43.4265 78.1147C42.7442 76.2934 41.8912 74.54 40.879 72.8778C40.6133 72.476 40.3665 72.0592 40.0871 71.6678L39.2234 70.5122C38.6159 69.7688 37.9699 69.0574 37.2879 68.381C35.8766 67.0343 34.3299 65.8357 32.6722 64.804C31.8375 64.2763 30.977 63.7788 30.0981 63.3046C29.6587 63.0676 29.215 62.8361 28.767 62.6099L28.0924 62.2739L27.7552 62.1093L27.4348 61.9664C27.0168 61.7663 26.5582 61.6043 26.1126 61.429C25.649 61.2786 25.1958 61.1077 24.7199 60.9782C23.7832 60.6881 22.8158 60.468 21.8492 60.2594C19.9742 59.8702 18.0719 59.6257 16.1592 59.528C14.3053 59.4371 12.4759 59.5457 10.9555 60.0699L10.5185 58.3389L10.5171 58.2245H10.5188H10.5295H10.5438H10.5725L10.6298 58.2249C10.6669 58.2256 10.705 58.2265 10.7585 58.2238L11.0543 58.2142L11.6561 58.1894C12.0597 58.1714 12.461 58.1536 12.8601 58.1358C13.6612 58.1041 14.4526 58.0728 15.2342 58.0418C16.8 57.9854 18.3286 57.9517 19.8222 57.9203C20.3303 57.9012 20.8392 57.921 21.3443 57.9794C21.5402 57.9951 21.6573 58.0538 21.838 58.0532C21.9556 58.0499 22.0714 58.0825 22.17 58.1466L22.4664 57.802C22.4246 57.9593 22.3665 58.0286 22.3942 58.0427C22.4131 58.0568 22.516 58.0123 22.6101 57.8619C22.6215 57.8438 22.6287 57.8235 22.6313 57.8023C22.6338 57.7811 22.6316 57.7596 22.6249 57.7394C22.6181 57.7191 22.6069 57.7006 22.5921 57.6852C22.5773 57.6697 22.5593 57.6578 22.5393 57.6501C22.5193 57.6424 22.4979 57.6392 22.4765 57.6407C22.4551 57.6423 22.4344 57.6485 22.4157 57.6589C22.397 57.6694 22.3809 57.6839 22.3685 57.7012C22.3561 57.7186 22.3477 57.7385 22.3439 57.7595L22.2515 58.2789C22.2343 58.375 22.104 58.5218 21.8622 58.6849C21.6943 58.8212 21.5114 58.9381 21.3171 59.0334C20.7181 59.325 20.0864 59.5445 19.4353 59.6873C18.0249 60.0103 16.6044 60.2959 15.1627 60.5674C14.4423 60.7026 13.717 60.8327 12.9866 60.9578L11.8811 61.1402L11.3131 61.2268L11.0174 61.2685C10.9063 61.2831 10.7402 61.2979 10.6037 61.3096C10.1146 61.3515 9.62624 61.2218 9.22316 60.9429C8.82007 60.664 8.52768 60.2535 8.39663 59.7825C8.26559 59.3115 8.30416 58.8097 8.50566 58.364C8.70716 57.9184 9.05888 57.557 9.4999 57.3424L9.6409 57.2748C10.7769 56.7484 11.9836 56.3889 13.2232 56.2075C13.7974 56.1381 14.3831 56.0488 14.9462 56.0283C15.2298 56.0128 15.5163 55.9898 15.798 55.9825L16.6388 55.9758C18.8699 55.9977 21.0923 56.2563 23.2683 56.7472C24.3679 56.9834 25.4548 57.2742 26.5252 57.6187L27.337 57.8884L28.1524 58.1973C28.4221 58.2933 28.6997 58.4236 28.9738 58.5422L29.3853 58.7237L29.752 58.8997C29.7731 58.8838 29.7929 58.8691 29.817 58.8539Z"
                fill="#FE5C96"
            />
            <motion.path
                initial={{ scale: 1 }}
                animate={{
                    scale: 1.5,
                    transition: {
                        duration: 1.3,
                        delay: 0.2,
                        repeat: Infinity,
                        repeatType: "reverse",
                    },
                }}
                d="M164.386 71.6117C166 73.5383 167.302 75.7042 168.243 78.0311C168.359 78.3225 168.483 78.6097 168.589 78.9042L168.873 79.8018C168.963 80.1021 169.068 80.3971 169.144 80.7015L169.352 81.6201C169.415 81.9275 169.501 82.2293 169.544 82.5407L169.672 83.4744L169.735 83.9409C169.756 84.0949 169.777 84.2504 169.788 84.4216C169.814 84.7572 169.827 85.104 169.818 85.4649C169.809 86.1138 169.703 86.7577 169.504 87.3753C169.379 87.7352 169.213 88.0794 169.009 88.4008C168.781 88.7435 168.505 89.0517 168.189 89.3162L168.167 89.3349C167.905 89.5535 167.583 89.6882 167.244 89.7213C166.904 89.7545 166.562 89.6846 166.263 89.5208C165.963 89.3569 165.721 89.1069 165.567 88.8034C165.413 88.4999 165.355 88.1572 165.401 87.8203C165.612 86.2594 165.913 84.7117 166.301 83.1847C166.932 80.6319 167.911 78.1767 169.209 75.8871C170.539 73.5464 172.277 71.4602 174.342 69.7264C176.457 67.9872 178.899 66.686 181.525 65.8992C184.223 65.1006 187.075 64.9671 189.836 65.5103C190.242 65.5936 190.611 65.8003 190.894 66.1018C191.176 66.4034 191.357 66.7848 191.413 67.1934C191.468 67.602 191.394 68.0176 191.202 68.3828C191.01 68.7479 190.708 69.0446 190.339 69.2318L190.303 69.2501C189.404 69.6922 188.441 69.9907 187.45 70.1346C186.538 70.2619 185.615 70.2985 184.696 70.2438C183.815 70.2004 182.976 70.0964 182.169 69.9812C181.364 69.8634 180.597 69.7488 179.763 69.5973C178.565 69.3965 177.391 69.0662 176.265 68.612C175.648 68.3543 175.058 68.0368 174.504 67.6639C173.938 67.2795 173.416 66.8351 172.946 66.3386C172.074 65.3872 171.362 64.3022 170.836 63.1252C170.349 62.038 169.967 60.9073 169.695 59.7481C168.596 55.2564 168.688 50.5573 169.964 46.1121L169.966 46.1056C170.078 45.7182 170.301 45.3717 170.608 45.1089C170.915 44.8461 171.292 44.6785 171.694 44.6266C172.095 44.5747 172.503 44.6408 172.867 44.8169C173.231 44.9929 173.536 45.2712 173.743 45.6173C174.393 46.7418 174.745 48.0124 174.766 49.3097C174.787 50.4469 174.645 51.5813 174.346 52.6789C174.213 53.2085 174.042 53.6981 173.878 54.193C173.695 54.6811 173.517 55.1718 173.3 55.6451C172.887 56.5993 172.401 57.5211 171.849 58.4027C169.652 61.9635 166.468 64.8157 162.68 66.6156C160.27 67.7402 157.654 68.358 154.993 68.4305C153.95 68.4666 152.907 68.3241 151.912 68.0092C151.71 67.9419 151.521 67.8724 151.345 67.7993C151.17 67.719 151.007 67.6369 150.856 67.5531C150.581 67.4065 150.325 67.2266 150.093 67.0176C149.485 66.4718 149.6 66.0325 150.205 65.7453C150.811 65.4642 151.907 65.3157 153.396 65.167L154.11 65.0903L154.832 64.9942C155.315 64.9208 155.804 64.8598 156.292 64.7688C157.265 64.5975 158.227 64.3685 159.172 64.083C160.998 63.5179 162.711 62.6433 164.237 61.4979C167.322 59.1895 169.562 55.9388 170.616 52.2429C170.868 51.4008 170.995 50.5268 170.995 49.6483C171.006 48.9232 170.811 48.2098 170.431 47.5909L173.676 47.1661C173.345 48.4737 173.139 49.8092 173.059 51.1552C172.904 53.6404 173.245 56.1958 173.674 58.5438C173.972 60.2172 174.494 61.8012 175.341 63.019C175.74 63.6046 176.246 64.1105 176.832 64.5108C177.472 64.9154 178.168 65.2228 178.898 65.4229C179.731 65.652 180.577 65.831 181.432 65.959C182.343 66.1055 183.249 66.2526 184.102 66.3393C185.817 66.5232 187.398 66.4534 188.606 65.864L189.023 69.2075L188.424 69.0943L188.125 69.0355L187.821 68.9999L187.214 68.9266L186.6 68.8959C185.205 68.8431 183.81 69.0058 182.464 69.3783C181.095 69.761 179.782 70.3211 178.559 71.0445C177.328 71.769 176.192 72.6423 175.176 73.6445C174.151 74.6619 173.24 75.7877 172.46 77.0019C170.319 80.296 169.056 84.1967 168.532 88.1833L166.162 86.9123C166.304 86.7938 166.411 86.6398 166.472 86.4663C166.57 86.196 166.627 85.9123 166.639 85.625C166.657 85.2681 166.648 84.9102 166.61 84.5548L166.44 83.3656C166.381 82.9684 166.301 82.5748 166.198 82.1866L166.063 81.5987C166.021 81.4015 165.944 81.2139 165.886 81.0208C165.758 80.6387 165.649 80.2495 165.507 79.8714L165.042 78.7499C164.368 77.2681 163.53 75.8657 162.543 74.569C161.533 73.2809 160.39 72.1019 159.133 71.0516C157.545 69.759 155.816 68.6475 153.98 67.738C153.87 67.6805 153.761 67.6184 153.65 67.5639L153.311 67.4156L152.638 67.1122C152.196 66.8887 151.736 66.7404 151.285 66.5597L150.608 66.2909C150.387 66.2116 150.168 66.1402 149.947 66.0846C149.511 65.9495 149.044 65.9475 148.607 66.0789C148.5 66.1196 148.403 66.1696 148.326 66.2042C148.325 66.2049 148.323 66.2059 148.322 66.2072C148.321 66.2085 148.32 66.21 148.319 66.2116L148.311 66.2362C148.282 66.3258 148.288 66.423 148.328 66.5083C148.364 66.5865 148.416 66.6565 148.48 66.714C148.597 66.8175 148.742 66.8827 148.897 66.9008C148.97 66.9106 149.045 66.9049 149.115 66.884C149.179 66.8718 149.241 66.8551 149.302 66.8339C149.405 66.7953 149.48 66.7709 149.522 66.7981C149.563 66.8236 149.575 66.904 149.515 67.041C149.47 67.1248 149.411 67.2 149.34 67.2631C149.255 67.3645 149.144 67.4413 149.019 67.4857C148.72 67.6065 148.386 67.6172 148.079 67.516C147.852 67.4411 147.649 67.309 147.488 67.1325C147.373 67.0045 147.282 66.8566 147.22 66.6962C147.148 66.506 147.116 66.3035 147.124 66.1006C147.13 65.9467 147.181 65.7977 147.269 65.6715L147.492 65.3539C147.609 65.1869 147.745 65.0348 147.899 64.9008C147.985 64.8261 148.076 64.7578 148.171 64.6964C148.273 64.6393 148.379 64.5893 148.488 64.5466C149.003 64.352 149.55 64.2577 150.1 64.2689C150.53 64.2708 150.959 64.3083 151.383 64.3811L152.548 64.5657L152.841 64.6158L153.128 64.6881L153.705 64.836C155.258 65.2382 156.759 65.8221 158.175 66.5755C160.514 67.8792 162.598 69.5926 164.325 71.6333C164.345 71.6261 164.363 71.6197 164.386 71.6117Z"
                fill="#FE5C96"
            />
            <motion.path
                initial={{ scale: 1 }}
                animate={{
                    scale: 1.1,
                    transition: {
                        duration: 1,
                        repeat: Infinity,
                        repeatType: "reverse",
                    },
                }}
                d="M105.929 142.454L107.386 143.266L108.788 144.171L109.136 144.399L109.471 144.649L110.139 145.149C110.361 145.318 110.586 145.48 110.803 145.654L111.436 146.2C112.283 146.924 113.088 147.694 113.848 148.508L114.94 149.771C115.298 150.196 115.614 150.653 115.95 151.093C117.241 152.886 118.336 154.812 119.215 156.837C120.066 158.859 120.711 160.961 121.14 163.112C121.238 163.684 121.357 164.252 121.431 164.827L121.623 166.557L121.708 168.297C121.738 168.876 121.7 169.458 121.698 170.038C121.622 172.363 121.29 174.673 120.707 176.926L120.25 178.511C120.119 178.966 119.827 179.359 119.428 179.617C119.029 179.875 118.55 179.981 118.079 179.915C117.607 179.849 117.176 179.616 116.864 179.259C116.551 178.901 116.379 178.443 116.379 177.97L116.38 177.878C116.384 172.763 116.373 167.489 117.36 162.191C117.846 159.509 118.64 156.892 119.726 154.392C120.837 151.822 122.359 149.448 124.233 147.364C126.156 145.3 128.415 143.574 130.915 142.259C133.341 140.998 135.893 139.994 138.53 139.263C139.826 138.894 141.125 138.579 142.429 138.301C143.737 138.025 145.076 137.783 146.459 137.633C147.873 137.466 149.299 137.424 150.72 137.507C152.211 137.594 153.684 137.872 155.104 138.332C155.572 138.484 155.982 138.776 156.277 139.169C156.572 139.561 156.738 140.034 156.753 140.524C156.768 141.014 156.63 141.496 156.359 141.905C156.089 142.314 155.698 142.63 155.24 142.81L155.188 142.83C152.952 143.658 150.588 144.092 148.202 144.11C145.671 144.125 143.143 143.925 140.646 143.511C138.17 143.169 135.718 142.669 133.307 142.015C132.061 141.671 130.837 141.252 129.642 140.759C128.411 140.262 127.223 139.665 126.091 138.974C124.929 138.268 123.839 137.452 122.836 136.536C121.837 135.616 120.934 134.599 120.138 133.5C118.625 131.377 117.397 129.066 116.484 126.626C114.69 121.938 113.582 117.018 113.193 112.017C113.148 111.393 113.109 110.769 113.087 110.144C113.073 109.831 113.063 109.519 113.059 109.206C113.054 108.891 113.043 108.599 113.049 108.215C113.049 107.373 113.174 106.535 113.42 105.729C113.589 105.196 113.841 104.693 114.168 104.239C114.557 103.702 115.061 103.256 115.642 102.935L115.667 102.921C115.952 102.767 116.264 102.669 116.587 102.635C116.909 102.601 117.236 102.63 117.547 102.721C117.858 102.812 118.148 102.963 118.4 103.166C118.653 103.369 118.862 103.619 119.018 103.903C119.184 104.21 119.285 104.548 119.312 104.896C120.041 114.861 117.458 124.788 111.961 133.146C110.681 135.088 109.164 136.865 107.444 138.435C106.564 139.227 105.621 139.947 104.624 140.589C103.623 141.233 102.564 141.784 101.461 142.234C99.7109 142.973 97.8161 143.31 95.9174 143.218C94.7003 143.174 93.5118 142.838 92.4522 142.24C91.3125 141.517 91.5382 141.018 92.5309 140.786C93.0356 140.641 93.7407 140.551 94.6265 140.383C95.6969 140.184 96.7531 139.916 97.7887 139.581C99.3782 139.046 100.881 138.283 102.25 137.317C103.674 136.302 104.974 135.125 106.126 133.811C107.788 131.89 109.216 129.779 110.378 127.522C111.582 125.257 112.56 122.879 113.298 120.424C114.784 115.512 115.317 110.363 114.866 105.252L117.802 106.822C117.803 106.824 117.807 106.83 117.803 106.823L117.792 106.807C117.794 106.81 117.796 106.813 117.798 106.816C117.804 106.824 117.81 106.829 117.807 106.825V106.831C117.79 106.855 117.776 106.881 117.765 106.908C117.689 107.088 117.639 107.278 117.618 107.472C117.579 107.776 117.564 108.083 117.572 108.39L117.616 109.552C117.696 111.132 117.864 112.706 118.094 114.269C118.567 117.39 119.357 120.455 120.453 123.417C121.21 125.521 122.198 127.535 123.398 129.423C123.971 130.31 124.613 131.151 125.319 131.939C125.659 132.317 126.017 132.679 126.393 133.022C126.583 133.19 126.76 133.369 126.955 133.53L127.539 134.012C129.29 135.348 131.252 136.384 133.345 137.078C135.559 137.823 137.832 138.382 140.14 138.749C142.453 139.177 144.793 139.444 147.143 139.549C149.325 139.654 151.508 139.339 153.571 138.621L153.645 142.599C151.603 141.907 149.296 141.794 146.927 142.032C145.742 142.143 144.539 142.338 143.324 142.571C142.106 142.808 140.897 143.074 139.709 143.38C137.162 144.005 134.706 144.947 132.397 146.185C130.152 147.384 128.162 149.003 126.534 150.953C124.92 152.955 123.646 155.206 122.76 157.616C121.847 160.085 121.21 162.646 120.86 165.253C120.271 169.359 120.208 173.6 120.154 177.899L116.668 177.329C118.99 170.515 118.18 162.66 114.705 156.272C114.478 155.878 114.276 155.468 114.029 155.087L113.286 153.943C113.033 153.565 112.744 153.214 112.475 152.847C112.336 152.668 112.207 152.479 112.059 152.307L111.611 151.794C111.016 151.107 110.386 150.45 109.725 149.826C109.031 149.237 108.358 148.619 107.606 148.096C107.246 147.815 106.871 147.552 106.484 147.309C106.096 147.066 105.718 146.807 105.323 146.574L104.118 145.911C103.719 145.683 103.29 145.514 102.877 145.311C101.206 144.537 99.4577 143.938 97.6615 143.523C93.3814 142.523 88.937 142.437 84.6211 143.271L84.576 141.565C87.1604 142.348 89.8732 142.622 92.5628 142.372C94.0895 142.229 94.8644 142.188 94.858 142.515C94.8478 142.66 94.6461 142.91 94.2114 143.195C93.9477 143.368 93.6717 143.521 93.3856 143.654C93.2185 143.735 93.0362 143.817 92.837 143.894C92.7375 143.932 92.6351 143.976 92.5271 144.01C92.4183 144.042 92.3058 144.073 92.1896 144.103C89.4921 144.809 86.674 144.93 83.9254 144.459C83.4705 144.38 83.0553 144.151 82.7461 143.81C82.4368 143.468 82.2513 143.033 82.2189 142.575C82.1866 142.116 82.3094 141.659 82.5677 141.278C82.826 140.897 83.205 140.613 83.6443 140.471L83.8186 140.415C86.5492 139.549 89.3983 139.109 92.2644 139.112C94.6178 139.105 96.9622 139.404 99.2382 140C101.53 140.59 103.75 141.428 105.86 142.497C105.882 142.482 105.903 142.468 105.929 142.454Z"
                fill="#FE5C96"
            />
        </svg>
    );
}

function ContentSlideLayout({ handleNextSlide, handlePrevSlide, className, question, children }) {
    const ref = useRef(null);
    const inView = useInView(ref);
    const slideVariants = {
        initial: { scale: 0.5 },
        animate: { scale: 1 },
        exit: { scale: 0.5 },
    };

    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        if (!isOpen && inView) {
            setTimeout(() => {
                setIsOpen(true);
            }, 1000);
        }
    }, [inView]);

    useEffect(() => {
        let timer;

        if (inView && isOpen) {
            timer = setTimeout(() => {
                setIsOpen(false);
            }, 5000);
        }

        return () => clearTimeout(timer);
    }, [inView, isOpen]);

    return (
        <AnimatePresence mode="wait">
            <motion.div
                ref={ref}
                variants={slideVariants}
                initial="initial"
                whileInView={inView ? "animate" : "initial"}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                exit="exit"
                className={cn("tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center", className)}
            >
                <div className="tw-relative tw-min-h-[60%] tw-w-[80%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-[#FFFFFF]">
                    <div className="tw-mx-auto tw-flex tw-h-[90%] tw-w-[90%] tw-flex-col tw-items-center tw-justify-center tw-py-12">
                        <div className="tw-flex tw-w-full tw-items-center tw-justify-between tw-gap-6 tw-pt-6">
                            {/* <div className="tw-flex tw-items-start tw-gap-2 tw-text-[45px] tw-leading-[45px] tw-text-[#333333]">
                <p>{question?.index}. </p>
                <p>{question?.data}</p>
              </div> */}
                            <div>
                                <Popover open={isOpen} onOpenChange={setIsOpen}>
                                    <PopoverTrigger asChild>
                                        <button className="tw-absolute tw-right-5 tw-top-5 tw-z-40 tw-aspect-square tw-w-[90px] tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FE5C96]">
                                            <i className="fa-solid fa-info tw-text-[40px] tw-leading-[40px] tw-text-[#333333]"></i>
                                        </button>
                                    </PopoverTrigger>
                                    <PopoverContent
                                        side="left"
                                        className="!tw-w-full tw-max-w-[80rem] !tw-border-4 !tw-border-[#3D3D3D] !tw-bg-white tw-px-8 !tw-font-lazyDog tw-text-black tw-shadow-sm tw-backdrop-blur"
                                    >
                                        <div className="tw-flex tw-items-start tw-gap-4 tw-text-[40px] tw-leading-[40px]">
                                            <p>{question?.index}. </p>
                                            <p>{question?.data}</p>
                                        </div>
                                        <p className="tw-mt-4 tw-text-[28px] tw-leading-[28px]">
                                            <b>Note: </b>
                                            {question?.specialInstruction}
                                        </p>
                                    </PopoverContent>
                                </Popover>
                            </div>
                        </div>
                        {question?.data && (
                            <img src="/quiz/Vector (2).png" alt="" className="tw-my-2 tw-h-[20px] tw-w-full" />
                        )}
                        {children}
                        <div className="tw-absolute tw-bottom-[-10%] tw-right-[-2%] tw-z-20">
                            <button
                                onClick={handlePrevSlide}
                                className="tw-aspect-square tw-w-[90px] tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FFD860]"
                            >
                                <i className="fa-solid fa-arrow-left tw-text-[40px] tw-leading-[40px] tw-text-[#333333]"></i>
                            </button>
                            <button
                                onClick={handleNextSlide}
                                className="tw-aspect-square tw-w-[90px] tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FFD860]"
                            >
                                <i className="fa-solid fa-arrow-right tw-text-[40px] tw-leading-[40px] tw-text-[#333333]"></i>
                            </button>
                        </div>
                    </div>
                    <motion.img
                        initial={{ y: 0 }}
                        animate={{ y: [-10, 10, -10] }}
                        transition={{ duration: 3, ease: "easeInOut", repeat: Infinity }}
                        src="/quiz/Vector (1).png"
                        alt=""
                        className="tw-absolute tw-left-[-5%] tw-top-[-8%] tw-z-[5]"
                    />
                    <motion.img
                        initial={{ scale: 1 }}
                        animate={{ scale: 1.25 }}
                        transition={{
                            duration: 3,
                            ease: "easeInOut",
                            repeat: Infinity,
                            repeatType: "reverse",
                        }}
                        src="/quiz/Group 59449.png"
                        alt=""
                        className="tw-absolute tw-bottom-[-2%] tw-left-[-0%] tw-z-[5]"
                    />
                    <motion.img
                        initial={{ rotate: 0 }}
                        animate={{
                            rotate: -360,
                        }}
                        transition={{ duration: 10, ease: "linear", repeat: Infinity }}
                        src="/quiz/Frame 1597883374.png"
                        alt=""
                        className="tw-absolute tw-right-[-170px] tw-top-[-170px] tw-z-[-5]"
                    />
                    <motion.img
                        initial={{ x: 0 }}
                        animate={{ x: [-10, 10, -10] }}
                        transition={{ duration: 3, ease: "easeInOut", repeat: Infinity }}
                        src="/quiz/Abstract-Scratch-Dash-Line-Rain--Streamline-Beveled-Scribbles.png"
                        alt=""
                        className="tw-absolute tw-bottom-[-50px] tw-right-[100px] tw-z-[5]"
                    />
                    <GrowStarGroup className="tw-right-[-100px] tw-top-[-120px]" />
                    <div className="tw-absolute tw--left-10 tw-top-10 tw-z-[-10] tw-h-[102%] tw-w-[102%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-[#FE5C96]"></div>
                </div>
            </motion.div>
        </AnimatePresence>
    );
}

function FirstSlide({ handleNextSlide, className }) {
    return (
        <MainSlideLayout handleNextSlide={handleNextSlide} className={className}>
            <div className="tw-absolute tw--top-12 tw-left-1/2 tw--translate-x-1/2 tw-border-[4px] tw-border-[#3D3D3D] tw-bg-[#FE5C96] tw-px-8 tw-py-3 tw-text-[42px] tw-leading-[42px] tw-text-white">
                Quiz
            </div>
            <h1 className="tw-mb-3 tw-text-[128px] tw-leading-[144px] tw-text-[#333333]">Quizzy Pop</h1>
            <img src="/quiz/Vector 189 (Stroke).png" alt="" className="tw-h-[15px] tw-w-[467px]" />
            <p className="tw-mt-16 tw-max-w-[70%] tw-text-center tw-text-[45px] tw-leading-[45px] tw-text-[#333333]">
                Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit
            </p>
        </MainSlideLayout>
    );
}

function SecondSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const [selected, setSelected] = useState(0);
    const options = ["A. Option 1", "B. Option 2", "C. Option 3", "D. Option 4"];
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data:
                    data?.question ??
                    "Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.",
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div className="tw-grid tw-h-[18rem] tw-w-full tw-grid-cols-2 tw-grid-rows-1 tw-gap-6">
                <div className="tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
                    <div className="tw-relative tw-h-[300px] tw-w-[420px]">
                        <img
                            src="/quiz/Rectangle 2716.png"
                            alt=""
                            className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw-object-contain"
                        />
                        <motion.img
                            initial={{ scale: 1, rotate: 1 }}
                            animate={{
                                scale: 1.1,
                            }}
                            transition={{
                                duration: 1,
                                repeat: Infinity,
                                repeatType: "reverse",
                            }}
                            src="/quiz/spark, sparkle, 26.png"
                            alt=""
                            className="tw-absolute tw-right-[-50px] tw-top-[-110px] tw-z-0 tw-aspect-[1/1.25] tw-w-[150px]"
                        />
                        <motion.img
                            initial={{ rotate: 10 }}
                            animate={{
                                rotate: -10,
                                transition: {
                                    duration: 2,
                                    repeat: Infinity,
                                    repeatType: "reverse",
                                },
                            }}
                            src="/quiz/Question Mark.png"
                            alt=""
                            className="tw-absolute tw-bottom-[-3%] tw-right-[-5%] tw-z-0 tw-aspect-[1/1.25] tw-w-[80px]"
                        />
                    </div>
                </div>
                <div className="tw-h-full tw-w-full">
                    <div className="tw-grid tw-grid-cols-1 tw-gap-4">
                        {options.map((option, index) => (
                            <motion.button
                                key={index}
                                initial={{ y: 100, opacity: 0.5 }}
                                whileInView={{ y: 0, opacity: 1 }}
                                transition={{
                                    duration: 0.25,
                                    delay: index * 0.1,
                                    ease: "easeInOut",
                                }}
                                className={cn(
                                    "tw-rounded-md tw-border tw-py-4 tw-text-[30px] tw-leading-[30px] tw-text-[#E08D67]",
                                    selected === index
                                        ? "tw-border-[#FE5C96] tw-bg-[#FE5C96] tw-text-white"
                                        : "tw-border-[#9C9C9C] tw-bg-white tw-text-[#E08D67]",
                                )}
                                onClick={() => setSelected(index)}
                            >
                                {option}
                            </motion.button>
                        ))}
                    </div>
                </div>
            </div>
        </ContentSlideLayout>
    );
}

function ThirdSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const images = ["/quiz/Frame 1597883343.png", "/quiz/Frame 1597883343 (1).png", "/quiz/Frame 1597883343 (2).png"];
    const [selected, setSelected] = useState(0);
    const handleClick = (idx) => setSelected(idx);
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data:
                    data?.question ??
                    "Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.",
                specailInstruction:
                    data?.specailInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div className="tw-mt-8 tw-grid tw-h-full tw-min-h-[25rem] tw-w-full tw-grid-cols-3 tw-grid-rows-1 tw-gap-6">
                {images.map((img, idx) => {
                    return (
                        <motion.div
                            initial={{ y: 100, opacity: 0.5, scale: 0.5 }}
                            whileInView={{ y: 0, opacity: 1, scale: 1 }}
                            transition={{
                                duration: 0.35,
                                delay: 0.15 * idx,
                                ease: "linear",
                            }}
                            onClick={() => handleClick(idx)}
                            className={cn(
                                "tw-relative tw-h-full tw-w-full tw-rounded-lg tw-border-[6px] tw-border-transparent",
                                selected === idx && "tw-border-[#FE5C96] tw-bg-[#FE5C96] tw-text-white",
                            )}
                            key={idx}
                        >
                            <div
                                className={cn(
                                    "tw-absolute tw-left-0 tw-top-0 tw-flex tw-size-10 tw-items-center tw-justify-center tw-rounded-br-lg tw-bg-[#FE5C96]",
                                    selected === idx ? "tw-z-10 tw-block" : "tw-hidden",
                                )}
                            >
                                <i className="fa-solid fa-check tw-text-[28px]"></i>
                            </div>
                            <img
                                src={img}
                                alt=""
                                className="tw-absolute tw-z-0 tw-h-full tw-w-full tw-rounded-lg tw-object-cover"
                            />
                        </motion.div>
                    );
                })}
            </div>
        </ContentSlideLayout>
    );
}

function FourthSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const [selected, setSelected] = useState(0);
    const options = ["A. Option 1", "B. Option 2", "C. Option 3", "D. Option 4"];
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data:
                    data?.question ??
                    "Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.",
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div className="tw-mt-8 tw-grid tw-h-full tw-min-h-[25rem] tw-w-full tw-grid-cols-1 tw-grid-rows-1 tw-gap-6">
                <div className="tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-4">
                    {options.map((option, index) => (
                        <motion.button
                            key={index}
                            initial={{ y: 100, opacity: 0.5 }}
                            whileInView={{ y: 0, opacity: 1 }}
                            transition={{
                                duration: 0.25,
                                delay: index * 0.1,
                                ease: "easeInOut",
                            }}
                            className={cn(
                                "tw-rounded-md tw-border tw-py-4 tw-text-[40px] tw-leading-[40px] tw-text-[#E08D67]",
                                selected === index
                                    ? "tw-border-[#FE5C96] tw-bg-[#FE5C96] tw-text-white"
                                    : "tw-border-[#9C9C9C] tw-bg-white tw-text-[#E08D67]",
                            )}
                            onClick={() => setSelected(index)}
                        >
                            {option}
                        </motion.button>
                    ))}
                </div>
            </div>
        </ContentSlideLayout>
    );
}

function FifthSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const options = ["Input", "Mechanism", "Joints", "Fixed", "Rigid Links"];
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data:
                    data?.question ??
                    "A linkage is a series of ...... is called............... connected with........ . A linkage .if it has two or more rigid links that move in respect to a rigid link. In order for a mechanism to work, we need to apply a force or that will cause another set to motion a certain point, which is called .................... point of the mechanism to move or produce a force, called ...........................",
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div className="tw-mt-8 tw-grid tw-h-full tw-min-h-[10rem] tw-w-full tw-grid-cols-1 tw-grid-rows-1 tw-gap-6">
                <div className="tw-flex tw-items-start tw-justify-center tw-gap-6">
                    {options.map((option, index) => (
                        <motion.button
                            key={index}
                            initial={{
                                opacity: 0.5,
                                scale: 0.2,
                            }}
                            whileInView={{
                                scale: 1,
                                opacity: 1,
                            }}
                            transition={{
                                duration: 0.25,
                                delay: index * 0.1,
                                ease: "easeInOut",
                            }}
                            className="tw-rounded-lg tw-bg-[#FE5C96] tw-px-8 tw-py-2 tw-text-[40px]"
                        >
                            {option}
                        </motion.button>
                    ))}
                </div>
            </div>
        </ContentSlideLayout>
    );
}

function SixthSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const options = ["Input", "Mechanism", "Joints", "Fixed"];
    const matcher = ["#FFE4E1", "#98FB98", "#87CEEB", "#DDA0DD"];
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data: data?.question ?? "Complete the gaps using the appropriate words from the box.",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div className="tw-mt-8 tw-grid tw-h-full tw-min-h-[20rem] tw-w-full tw-grid-cols-1 tw-gap-6">
                <div className="tw-grid tw-grid-cols-4 tw-grid-rows-[1fr_40px] tw-gap-x-6">
                    {options.map((option, index) => (
                        <motion.button
                            key={index}
                            initial={{
                                opacity: 0.5,
                                scale: 0.2,
                            }}
                            whileInView={{
                                scale: 1,
                                opacity: 1,
                            }}
                            transition={{
                                duration: 0.25,
                                delay: index * 0.1,
                                ease: "easeInOut",
                            }}
                            className="tw-h-full tw-rounded-lg tw-bg-[#FE5C96] tw-px-8 tw-py-2 tw-text-[40px]"
                        >
                            {option}
                        </motion.button>
                    ))}
                    {matcher.map((color, idx) => {
                        return (
                            <div
                                key={idx}
                                className="tw-h-full tw-w-full tw-rounded-lg tw-border tw-border-dashed"
                            ></div>
                        );
                    })}
                </div>
                <div className="tw-flex tw-items-center tw-justify-center tw-gap-4">
                    {matcher.map((color, idx) => {
                        return (
                            <div
                                key={idx}
                                className="tw-flex tw-h-fit tw-w-fit tw-items-center tw-justify-center tw-rounded-lg tw-px-7 tw-py-2 tw-text-[28px]"
                                style={{ backgroundColor: color }}
                            >
                                {color}
                            </div>
                        );
                    })}
                </div>
            </div>
        </ContentSlideLayout>
    );
}

function SeventhSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data: data?.question ?? "Complete the gaps using the appropriate words from the box.",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div className="tw-relative tw-grid tw-grid-cols-2 tw-pt-[1rem]">
                <div className="tw-flex tw-items-center tw-justify-center">
                    <div className="tw-relative tw-h-[410px] tw-w-[500px]">
                        <img
                            className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw-object-contain"
                            src="/quiz/img-eacf32e87aa00f07c75189f7c83349fc2ea87718 1.png"
                            alt=""
                        />
                        <DropZoneMark className="tw-left-[34.9%] tw-top-[20%]" />
                        <DropZoneMark className="tw-left-[46.5%] tw-top-[20%]" />
                        <DropZoneMark className="tw-left-[57.9%] tw-top-[20%]" />
                        <DropZoneMark className="tw-left-[34.9%] tw-top-[50%]" />
                        <DropZoneMark className="tw-left-[57.9%] tw-top-[50%]" />
                    </div>
                </div>
                <div className="tw-relative">
                    <div className="tw-absolute tw-left-[15%] tw-top-[10%] tw-flex tw-aspect-[1/1] tw-h-[6rem] tw-items-center tw-justify-center tw-rounded-full tw-bg-[#FECC5C] tw-text-[44px] tw-leading-[44px] tw-text-white">
                        1
                    </div>
                    <div className="tw-absolute tw-right-[20%] tw-top-[10%] tw-flex tw-aspect-[1/1] tw-h-[6rem] tw-items-center tw-justify-center tw-rounded-full tw-bg-[#FD5C5C] tw-text-[44px] tw-leading-[44px] tw-text-white">
                        2
                    </div>
                    <div className="tw-absolute tw-left-[10%] tw-top-[50%] tw-flex tw-aspect-[1/1] tw-h-[6rem] tw-items-center tw-justify-center tw-rounded-full tw-bg-[#E08D67] tw-text-[44px] tw-leading-[44px] tw-text-white">
                        3
                    </div>
                    <div className="tw-absolute tw-right-[30%] tw-top-[70%] tw-flex tw-aspect-[1/1] tw-h-[6rem] tw-items-center tw-justify-center tw-rounded-full tw-bg-[#FECC5C] tw-text-[44px] tw-leading-[44px] tw-text-white">
                        4
                    </div>
                    <div className="tw-absolute tw-left-[30%] tw-top-[30%] tw-flex tw-aspect-[1/1] tw-h-[6rem] tw-items-center tw-justify-center tw-rounded-full tw-bg-[#FD5C5C] tw-text-[44px] tw-leading-[44px] tw-text-white">
                        5
                    </div>
                </div>
            </div>
        </ContentSlideLayout>
    );
}

function DropZoneMark({ className }) {
    return (
        <motion.div
            initial={{ scale: 0.75 }}
            animate={{
                scale: 1.3,
            }}
            transition={{
                duration: 0.5,
                repeat: Infinity,
                repeatType: "reverse",
            }}
            className={cn(
                "tw-absolute tw-h-[40px] tw-w-[40px] tw-rounded-full tw-border-4 tw-border-red-500 tw-bg-white/50",
                className,
            )}
        />
    );
}

function EightSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const list = ["#FECC5C", "#FD5C5C", "#E08D67", "#FECC5C"];
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { x: "-50%", scaleX: 0.5 },
        inView: { x: 0, scaleX: 1 },
    };
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data: data?.question ?? "Complete the gaps using the appropriate words from the box.",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div className="tw-relative tw-flex tw-w-full tw-items-center tw-justify-center tw-overflow-hidden tw-pt-[3rem]">
                <div className="tw-grid tw-w-[calc(100%-20%)] tw-grid-cols-1 tw-gap-[1rem]" ref={ref}>
                    {list.map((color, idx) => {
                        return (
                            <motion.div
                                key={idx}
                                variants={variants}
                                initial="initial"
                                whileInView={inView ? "inView" : "initial"}
                                transition={{
                                    duration: 0.5,
                                    delay: idx * 0.2,
                                }}
                                className="tw-flex tw-h-[5rem] tw-items-center tw-gap-[1rem] tw-rounded-[0.5rem] tw-bg-[color] tw-p-[1.5rem] tw-text-[40px] tw-text-white"
                                style={{ backgroundColor: color }}
                            >
                                <div className="tw-aspect-[1/1] tw-h-full tw-rounded-[0.5rem] tw-bg-white"></div>
                                Dummy text with images
                            </motion.div>
                        );
                    })}
                </div>
            </div>
        </ContentSlideLayout>
    );
}

function NinthSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { y: "-50%", scaleY: 0.5 },
        inView: { y: 0, scaleY: 1 },
    };

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data:
                    data?.question ??
                    "In real life engineers need to make many alterations in order to end up with a final product. Thus, redesign and make the appropriate modifications and improvements on your model",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div ref={ref} className="tw-relative tw-z-10 tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem]">
                <motion.div
                    variants={variants}
                    initial="initial"
                    animate={inView ? "inView" : "initial"}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    className="tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center tw-pt-[2rem]"
                >
                    <label
                        htmlFor="textarea"
                        className="tw-absolute tw--left-2 tw-top-5 tw-rounded-md tw-bg-white tw-px-3 tw-text-4xl tw-text-[#FE5C96]"
                    >
                        Write your ideas here
                    </label>
                    <textarea
                        id="textarea"
                        rows={7}
                        className="tw-w-full tw-resize-none tw-rounded-md tw-border-2 tw-border-[#FE5C96] tw-bg-transparent tw-p-2 tw-text-[30px] tw-text-white tw-outline-none"
                    />
                </motion.div>
            </div>
        </ContentSlideLayout>
    );
}

function TenthSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const list = ["Motors", "Robot’s Arm", "Robot’s Head"];
    const options = [
        "/quiz/Rectangle 2736.png",
        "/quiz/Rectangle 2737.png",
        "/quiz/Rectangle 2738.png",
        "/quiz/Rectangle 2739.png",
    ];
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { opacity: 0.5, scale: 0.5 },
        inView: { opacity: 1, scale: 1 },
    };

    const optionsVariants = {
        initial: { scale: 0.5, opacity: 0.5 },
        inView: { scale: 1, opacity: 1 },
    };

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data:
                    data?.question ?? "Drag the image on correct placeholder make sure the label is correct for image.",
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div className="tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden tw-pt-[2rem]">
                <div
                    className="tw-grid tw-h-1/2 tw-w-[calc(100%-5%)] tw-grid-cols-3 tw-grid-rows-1 tw-gap-[1rem]"
                    ref={ref}
                >
                    {list.map((color, idx) => {
                        return (
                            <motion.div
                                key={idx}
                                variants={variants}
                                initial="initial"
                                whileInView={inView ? "inView" : "initial"}
                                transition={{
                                    duration: 0.5,
                                    delay: idx * 0.2,
                                }}
                                className="tw-flex tw-flex-col tw-items-center tw-gap-[1rem] tw-rounded-[0.5rem] tw-p-[1.5rem] tw-text-[28px] tw-text-black"
                            >
                                <div className="tw-h-[150px] tw-w-full tw-rounded-[0.5rem] tw-border-8 tw-border-dashed tw-border-[#FECC5C] tw-bg-[#FE5C96]"></div>
                                Dummy text with images
                            </motion.div>
                        );
                    })}
                </div>
                <div className="gap-4 tw-flex">
                    {options.map((option, index) => (
                        <motion.div
                            key={index}
                            variants={optionsVariants}
                            initial="initial"
                            whileInView={inView ? "inView" : "initial"}
                            transition={{
                                duration: 0.5,
                                delay: index * 0.1,
                                ease: "easeInOut",
                            }}
                            className="tw-flex tw-h-[5rem] tw-items-center tw-gap-[1rem] tw-rounded-[0.5rem] tw-bg-[color] tw-text-[40px] tw-text-white"
                        >
                            <img src={option} alt="" className="tw-h-full tw-w-full tw-object-contain" />
                        </motion.div>
                    ))}
                </div>
            </div>
        </ContentSlideLayout>
    );
}

function EleventhSlide({ handleNextSlide, handlePrevSlide, className, data }) {
    const list = [
        "A linkage is a series of",
        {},
        "is called",
        {},
        "connected with",
        {},
        "A linkage. if it has two or more rigid links that move in respect to a rigid link. In order for a mechanism to work, we need to apply a force or that will cause another set to motion a certain point, which is called",
        {},
        "point of the mechanism to move or produce a force",
    ];
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { scale: 0.5 },
        inView: { scale: 1 },
    };

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            question={{
                index: 1,
                data:
                    data?.question ?? "Drag the image on correct placeholder make sure the label is correct for image.",
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div className="tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden tw-pt-[2rem]">
                <div className="tw-flex tw-h-1/2 tw-w-[calc(100%-20%)] tw-flex-wrap tw-gap-[1rem]" ref={ref}>
                    {list.map((dt, idx) => {
                        const randomString = Math.random().toString(36).substring(2, 15);
                        return (
                            <motion.div
                                key={idx}
                                className="tw-inline-flex tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[40px] tw-text-black"
                            >
                                {typeof dt !== "string" ? (
                                    <motion.label
                                        key={idx}
                                        variants={variants}
                                        initial="initial"
                                        whileInView={inView ? "inView" : "initial"}
                                        transition={{
                                            duration: 0.5,
                                        }}
                                        htmlFor={`option-${randomString}`}
                                        className="p-1 tw-inline-flex tw-h-12 tw-rounded-md tw-bg-white"
                                    >
                                        <select
                                            key={idx}
                                            id={`option-${randomString}`}
                                            className="p-1 tw-w-full tw-text-[40px] tw-text-[#FE5C96] tw-outline-none"
                                        >
                                            <option value="">Choose</option>
                                            <option value="">Choose</option>
                                            <option value="">Choose</option>
                                            <option value="">Choose</option>
                                            <option value="">Choose</option>
                                        </select>
                                    </motion.label>
                                ) : (
                                    dt
                                )}
                            </motion.div>
                        );
                    })}
                </div>
            </div>
        </ContentSlideLayout>
    );
}
