import { clsx } from "clsx";
import { differenceInMinutes } from "date-fns";
import { extendTailwindMerge } from "tailwind-merge";
import { z } from "zod";

const twMerge = extendTailwindMerge({
    prefix: "tw-",
});

export function cn(...inputs) {
    return twMerge(clsx(inputs));
}

export function replacePlaceholders(sentence, matchOptions) {
    matchOptions.sort((a, b) => parseInt(a.correctIndex) - parseInt(b.correctIndex));
    let filledSentence = sentence.split("[field]");
    matchOptions.forEach((option, index) => {
        if (Number(option.correctIndex) === index + 1)
            filledSentence.splice(Number(option.correctIndex) + index, 0, option);
    });
    return filledSentence;
}

export const hexToRgba = (hex, alpha) => {
    // Remove the hash at the start if it's there

    if (hex.startsWith("var(")) {
        return `rgba(var(${hex.slice(4, -1)}), ${alpha})`; // Return the CSS variable with alpha
    }
    hex = hex.replace(/^#/, "");

    // Parse r, g, b values
    let r = parseInt(hex.substring(0, 2), 16);
    let g = parseInt(hex.substring(2, 4), 16);
    let b = parseInt(hex.substring(4, 6), 16);

    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const urlSchema = z.string().url();

export function isUrl(data) {
    return urlSchema.safeParse(data).success;
}

export function generateRandomString(length = 5) {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

export const generateDefaultForType = (typeKey, structure) => {
    const defaultValue = {};
    if (!structure[typeKey]) {
        console.warn(`Type ${typeKey} not found in structure`);
        return defaultValue;
    }
    ["question", "answer", "default", "selected"].forEach((section) => {
        if (structure[typeKey][section]) {
            defaultValue[section] = {};

            structure[typeKey][section].forEach((item) => {
                if (item.type === "color") {
                    defaultValue[section][item.name] = "#000000";
                } else if (item.type === "select") {
                    defaultValue[section][item.name] = item.options[0] || "";
                } else if (item.type === "text") {
                    defaultValue[section][item.name] = "40px";
                } else if (item.type === "border") {
                    defaultValue[section] = {
                        borderColor: "#000000",
                        borderWidth: "2px",
                        borderStyle: "solid",
                    };
                }
            });
        }
    });

    return defaultValue;
};

/**
 * Creates a function that matches paths against an array of route patterns
 * @param {string[]} routes - Array of route patterns to match against
 * @returns {function(string): boolean} A function that takes a path and returns true if it matches any route pattern
 * @example
 * const matcher = createRouteMatcher(['/home', '/about']);
 * matcher('/home') // returns true
 * matcher('/contact') // returns false
 */
export const createRouteMatcher = (routes) => {
    const regexRoutes = routes.map((route) => new RegExp(`^${route}$`));
    return (path) => regexRoutes.some((regex) => regex.test(path));
};

export function getSubmissionBadge(submissionDate) {
    const submission = new Date(submissionDate);
    const today = new Date();
    const differenceInMins = differenceInMinutes(submission, today);
    const daysRemaining = Math.floor(differenceInMins / (60 * 24)); // Convert mins to days
    const hoursRemaining = Math.floor((differenceInMins % (60 * 24)) / 60); // Convert mins to hours
    let message, badgeClass;

    if (differenceInMins < 0) {
        message = "Submission Expired";
        badgeClass = "tw-bg-red-500 tw-text-white";
    } else if (daysRemaining === 0 && hoursRemaining > 0) {
        message = `Due Today! ${hoursRemaining} hour${hoursRemaining > 1 ? "s" : ""} remaining`;
        badgeClass = "tw-bg-orange-500 tw-text-white";
    } else if (daysRemaining === 0) {
        message = "Due Today!";
        badgeClass = "tw-bg-orange-500 tw-text-white";
    } else if (daysRemaining <= 7) {
        message = `Due in ${daysRemaining} day${daysRemaining > 1 ? "s" : ""}`;
        badgeClass = "tw-bg-yellow-500 tw-text-white";
    } else if (daysRemaining <= 30) {
        const weeksRemaining = Math.ceil(daysRemaining / 7);
        message = `Due in ${weeksRemaining} week${weeksRemaining > 1 ? "s" : ""}`;
        badgeClass = "tw-bg-green-500 tw-text-white";
    } else {
        message = `Due in ${daysRemaining} days`;
        badgeClass = "tw-bg-blue-500 tw-text-white";
    }

    return { message, badgeClass };
}
