import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    B<PERSON><PERSON><PERSON><PERSON>Link,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button, buttonVariants } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import BulletList from "@tiptap/extension-bullet-list";
import { Color } from "@tiptap/extension-color";
import Document from "@tiptap/extension-document";
import Dropcursor from "@tiptap/extension-dropcursor";
import FontFamily from "@tiptap/extension-font-family";
import Image from "@tiptap/extension-image";
import ListItem from "@tiptap/extension-list-item";
import ListKeymap from "@tiptap/extension-list-keymap";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Typography from "@tiptap/extension-typography";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import "@vidstack/react/player/styles/base.css";
import "@vidstack/react/player/styles/plyr/theme.css";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";

const types = ["PDF", "HTML"];

const TermsConditions = () => {
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [selectedOption, setSelectedOption] = useState("HTML");
    const [fileUrl, setFileUrl] = useState("");
    const [htmlContent, setHtmlContent] = useState("");
    const [termsData, setTermsData] = useState(null);

    const handleChange = (value) => {
        setSelectedOption(value);
    };

    const onHandleChange = (value) => {
        setHtmlContent(value);
    };

    const onFileUpload = async (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "ORGANIZATION-SETTINGS");

            setLoad(true);
            await tanstackApiFormdata
                .post("common/upload-course-file", data, {
                    onUploadProgress: (data) => {
                        var percent = Math.round((data.loaded / data.total) * 100);
                        setUploaded(percent);
                    },
                })
                .then((res) => {
                    if (res.data.success) {
                        setLoad(false);
                        setFileUrl(res.data.fileUrl);
                    } else {
                        setLoad(false);
                        setFileUrl("");
                    }
                })
                .catch((err) => {
                    setLoad(false);
                    setFileUrl("");
                });
        }
    };

    useEffect(() => {
        if (termsData) {
            setSelectedOption(termsData?.dataType);
            setHtmlContent(termsData?.htmlContent);
            setFileUrl(termsData?.fileUrl);
        }
    }, [termsData]);

    useEffect(() => {
        if (localStorage.getItem("login_role") == "super-admin") {
            getSystemAdminTerms();
        } else {
            getDomainAdminTerms();
        }
    }, []);

    const getSystemAdminTerms = async () => {
        await tanstackApi
            .get("signup/terms-and-conditions")
            .then((res) => {
                setTermsData(res?.data?.termsAndCondition?.term_n_conditions);
            })
            .catch((err) => {});
    };

    const getDomainAdminTerms = async () => {
        await tanstackApi
            .get("organization/get-tems-and-conditions")
            .then((res) => {
                setTermsData(res?.data?.data);
            })
            .catch((err) => {});
    };

    const onUpdateTerms = async (e) => {
        e.preventDefault();
        if (selectedOption == "PDF") {
            if (!fileUrl) {
                toast.warning("PDF File", {
                    description: "PLease select terms & conditions file",
                });
                return false;
            }
            let payload = {
                dataType: "PDF",
                fileUrl: fileUrl,
            };
            await tanstackApi
                .put("organization/update-term-and-conditions", payload)
                .then((res) => {
                    if (res.data.success) {
                        toast.success("Updated Successfully", {
                            description: "Terms & conditions updated successfully",
                        });
                    }
                })
                .catch((err) => {});
        } else {
            if (!htmlContent) {
                toast.warning("HTMl Content", {
                    description: "PLease enter terms & conditions content.",
                });
                return false;
            }
            let payload = {
                dataType: "HTML",
                htmlContent: htmlContent,
            };
            await tanstackApi
                .put("organization/update-term-and-conditions", payload)
                .then((res) => {
                    if (res.data.success) {
                        toast.success("Updated Successfully", {
                            description: "Terms & conditions updated successfully",
                        });
                    }
                })
                .catch((err) => {});
        }
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Terms & conditions</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button onClick={onUpdateTerms} className="tw-px-2 tw-py-1">
                    <i className="fa-solid fa-check"></i> Update Terms
                </Button>
            </div>
            <div>
                <h1 className="tw-font-lexend tw-text-lg tw-font-semibold">
                    #. Terms & Conditions for Trainer & Learner :-
                </h1>
                <div className="tw-ml-5 tw-flex tw-items-center tw-space-y-3 tw-pt-3">
                    <RadioGroup value={selectedOption} onValueChange={handleChange} className="tw-flex">
                        <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                            {types?.map((data, idx) => (
                                <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                    <RadioGroupItem value={data} id={data} />
                                    <Label htmlFor={data} className="tw-cursor-pointer">
                                        {data}
                                    </Label>
                                </div>
                            ))}
                        </div>
                    </RadioGroup>
                </div>
                {selectedOption == "PDF" && (
                    <div className="tw-m-5 tw-space-y-3">
                        {fileUrl && (
                            <div className="tw-flex tw-flex-col tw-gap-0">
                                <div>
                                    <i className="fa-solid fa-paperclip"></i> <Label>File URL</Label>
                                </div>
                                <a
                                    href={fileUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="tw-cursor-pointer tw-text-sm hover:tw-underline"
                                >
                                    {fileUrl}
                                </a>
                            </div>
                        )}
                        <div>
                            <div className="tw-mt-2 tw-flex tw-gap-2">
                                {!fileUrl && (
                                    <Label
                                        htmlFor="tnc_file"
                                        variant="outline"
                                        className={cn(
                                            buttonVariants({ variant: "outline" }),
                                            "aspect-square tw-rounded-xl",
                                        )}
                                    >
                                        <Upload
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <input
                                            onChange={onFileUpload}
                                            name="attachment_url"
                                            type="file"
                                            style={{ display: "none" }}
                                            id="tnc_file"
                                            accept="application/pdf"
                                        />
                                        <div className="max-sm:sr-only">
                                            {load ? "Uploading" : "Upload Terms & conditions file"}{" "}
                                            {load ? `${uploaded}%` : null}
                                        </div>
                                    </Label>
                                )}

                                {fileUrl && (
                                    <Button
                                        variant="outline"
                                        className="aspect-square tw-rounded-xl"
                                        onClick={() => setFileUrl("")}
                                    >
                                        <X
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <Label className="max-sm:sr-only">Remove File</Label>
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>
                )}
                {selectedOption == "HTML" && (
                    <div className="tw-m-5 tw-h-full tw-overflow-hidden tw-rounded-lg tw-border-2">
                        <HTMLEditor
                            onHandleChange={onHandleChange}
                            value={htmlContent}
                            placeholder="Define terms & conditions here for domain users."
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default TermsConditions;

const HTMLEditor = ({ onHandleChange, value, placeholder }) => {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                text: {
                    HTMLAttributes: {
                        class: "tw-text-sm",
                    },
                },
            }),
            Color,
            Document,
            Paragraph,
            TextStyle.configure({
                HTMLAttributes: {},
            }),
            BulletList,
            ListItem,
            FontFamily.configure({
                types: ["textStyle"],
            }),
            Dropcursor,
            ListKeymap,
            Placeholder.configure({
                placeholder: placeholder,
            }),
            Image,
            TextAlign.configure({
                types: ["heading", "paragraph"],
            }),
            Typography,
        ],
        content: value,
        editorProps: {
            attributes: {
                spellcheck: "false",
            },
        },
        onUpdate: ({ editor }) => {
            onHandleChange(editor.getHTML());
        },
    });

    return <TipTapEditor editor={editor} />;
};

import BubbleMenu from "@/pages/dashboard/createcourse/chaptercontent/slides/editor/bubble-menu";
import MenuBar from "@/pages/dashboard/createcourse/chaptercontent/slides/editor/menu-bar";
import { EditorContent } from "@tiptap/react";
import { toast } from "sonner";

function TipTapEditor({ editor }) {
    return (
        <div className="tw-prose tw-relative tw-w-full tw-max-w-full tw-overflow-y-auto prose-h1:tw-my-2 prose-p:tw-mb-3 prose-p:tw-mt-2 prose-p:tw-leading-snug prose-img:tw-my-2 prose-hr:tw-my-4 prose-hr:tw-border-gray-500">
            <MenuBar editor={editor} />
            <EditorContent editor={editor} />
            <BubbleMenu editor={editor} />
        </div>
    );
}
