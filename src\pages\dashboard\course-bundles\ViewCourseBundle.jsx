import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Book, Clock, FilePenLine } from "lucide-react";
import moment from "moment";

const ViewCourseBundle = () => {
    const params = useParams();
    const router = useNavigate();
    const [bundleData, setBundleData] = useState(null);
    const [courseList, setCourseList] = useState([]);
    const [bundleCourses, setBundleCourses] = useState([]);

    useEffect(() => {
        getCourses();
        if (params?.bundle_id !== undefined) {
            getCourseBundle(params?.bundle_id);
        }
    }, [params]);

    const getCourseBundle = async (payload) => {
        await tanstackApi
            .get(`course-bundle/get-one-course-bundle/${payload}`)
            .then((res) => {
                setBundleData(res?.data?.data);
            })
            .catch((err) => {
                setBundleData(null);
            });
    };

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    useEffect(() => {
        if (courseList?.length > 0 && bundleData) {
            setBundleCourses(courseList?.filter((dt) => bundleData?.course_list?.includes(dt?.id)));
        }
    }, [courseList, bundleData]);

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/course-bundle">Course Bundles</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>View Course Bundles</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <div className="tw-flex tw-gap-2">
                    <Button
                        className="tw-px-2 tw-py-1"
                        variant="outline"
                        onClick={() => router(`/dashboard/course-bundle`)}
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button
                        className="tw-px-2 tw-py-1"
                        onClick={() => router(`/dashboard/course-bundle-create/${params?.bundle_id}`)}
                    >
                        <i className="fa-solid fa-edit"></i> Edit Bundle
                    </Button>
                </div>
                {/* </Link> */}
            </div>
            <div className="tw-grid tw-w-full tw-grid-cols-[400px_1fr] tw-gap-7 tw-border-secondary tw-p-2">
                <div className="tw-space-y-5">
                    <div className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-border-[1px]">
                        {bundleData?.logo_image_url ? (
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-object-cover tw-shadow-sm"
                                src={bundleData?.logo_image_url}
                            />
                        ) : (
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-xl tw-object-cover tw-shadow-sm"
                                src={"/assets/thumbnail.png"}
                            />
                        )}
                    </div>
                </div>
                <div>
                    <h1 className="tw-text-3xl tw-font-bold">{bundleData?.name}</h1>
                    <p className="tw-mt-4 tw-line-clamp-2 tw-font-mono tw-text-[16px] tw-font-semibold tw-text-slate-500">
                        {bundleData?.description}
                    </p>
                    <div className="tw-mt-4 tw-space-y-3">
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <Book size={18} /> <Badge variant={"outline"}>{bundleData?.is_courses_added} Courses</Badge>
                        </span>{" "}
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <FilePenLine size={18} /> Last Updated {moment(bundleData?.updatedAt).format("LL")}
                        </span>{" "}
                        <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-sm tw-font-normal">
                            <Clock size={18} /> Created On{" "}
                            {bundleData?.createdAt ? (
                                moment(bundleData?.createdAt).format("LL")
                            ) : (
                                <span className="tw-text-xs tw-text-slate-400"> _ _ / _ _ / _ _ _ _</span>
                            )}
                        </span>{" "}
                    </div>
                </div>
            </div>
            <br />
            <div className="tw-flex tw-flex-wrap tw-gap-5 tw-p-4">
                {bundleCourses?.map((step, idx) => (
                    <div
                        key={idx}
                        className="tw-w-[270px] tw-cursor-pointer tw-space-y-2 tw-rounded-xl tw-border-[1px] tw-p-2 hover:tw-bg-slate-50"
                        onClick={() => router(`/dashboard/view-course/${step?.id}`)}
                    >
                        <div className="tw-w-full">
                            <img
                                className="tw-h-[140px] tw-w-full tw-rounded-lg tw-object-cover tw-shadow-sm"
                                src={step?.course_banner_url || "/assets/thumbnail.png"}
                            />
                        </div>
                        <div>
                            <h1 className="tw-text-md tw-font-semibold">{step?.course_title}</h1>
                            <p className="tw-mt-1 tw-line-clamp-2 tw-text-sm tw-font-normal tw-text-slate-500">
                                {step?.course_description}
                            </p>
                        </div>
                        <div className="tw-mt-4 tw-space-y-3">
                            <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-xs tw-font-normal">
                                <Book size={16} />{" "}
                                <Badge variant={"outline"}>{step?.is_scorm ? "SCORM" : "Combined"}</Badge>
                            </span>{" "}
                            <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-xs tw-font-normal">
                                <FilePenLine size={16} /> Last Updated {moment(step?.updatedAt).format("LL")}
                            </span>{" "}
                            <span className="tw-flex tw-items-center tw-gap-1 tw-font-lexend tw-text-xs tw-font-normal">
                                <Clock size={16} /> Expiring On{" "}
                                {step?.lms_course_settings?.[0]?.expiration_date ? (
                                    moment(step?.lms_course_settings?.[0]?.expiration_date).format("LL")
                                ) : (
                                    <span className="tw-text-xs tw-text-slate-400"> _ _ / _ _ / _ _ _ _</span>
                                )}
                            </span>{" "}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default ViewCourseBundle;
