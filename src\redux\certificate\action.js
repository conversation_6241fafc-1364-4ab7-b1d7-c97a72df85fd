import {
    CERTIFICATE_RES,
    CREATE_CERTIFICATE,
    EDIT_CERTIFICATE,
    FETCH_CERTI_BACKGROUND_TYPES_REQ,
    FETCH_COMPLETED_CERTIFICATE,
    FETCH_UPLOADED_CERTIFICATE_REQ,
    GENERATE_CERTIFICATE,
    GET_CERTI_BACKGROUND_LIST,
    GET_COMPLETED_CERTIFICATE,
    GET_GENERATED_CERTIFICATE,
    GET_UPLOADED_CERTIFICATE_LIST,
    SET_CERTIFICATE_RES,
} from "@/redux-types";

export const fetchCertiBackgroundType = () => {
    return {
        type: FETCH_CERTI_BACKGROUND_TYPES_REQ,
    };
};

export const certificateBackgroundTypeList = (certificate) => {
    return {
        type: GET_CERTI_BACKGROUND_LIST,
        payload: certificate,
    };
};

export const fetchUploadedCertificate = (typeID) => {
    return {
        type: FETCH_UPLOADED_CERTIFICATE_REQ,
        payload: typeID,
    };
};

export const getCertificateCompleted = (data) => {
    return {
        type: FETCH_COMPLETED_CERTIFICATE,
        payload: data,
    };
};

export const getCertificateList = (data) => {
    return {
        type: GET_COMPLETED_CERTIFICATE,
        payload: data,
    };
};

export const uploadedCertificateList = (certificate) => {
    return {
        type: GET_UPLOADED_CERTIFICATE_LIST,
        payload: certificate,
    };
};

export const createNewCertificate = (certificateData) => {
    return {
        type: CREATE_CERTIFICATE,
        payload: certificateData,
    };
};

export const editCertificate = (certificateData) => {
    return {
        type: EDIT_CERTIFICATE,
        payload: certificateData,
    };
};

export const getCertificateRespoonse = (resData) => {
    return {
        type: CERTIFICATE_RES,
        payload: resData,
    };
};

export const setCertificateRes = (resData) => {
    return {
        type: SET_CERTIFICATE_RES,
        payload: resData,
    };
};

export const GenerateNewCertitifcate = (certificateData) => {
    return {
        type: GENERATE_CERTIFICATE,
        payload: certificateData,
    };
};

export const getGeneratedCertificate = (resData) => {
    return {
        type: GET_GENERATED_CERTIFICATE,
        payload: resData,
    };
};
