import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useState } from "react";

const Singlechoice = ({ onSlideEdit, content, setContentData }) => {
    const [selected, setSelected] = useState(content?.answerKey ?? content?.options[0]);

    const handleClick = (option) => {
        setSelected(option);
    };

    return (
        <div className="tw-grid tw-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat">
            <div className="tw-flex tw-items-center tw-justify-center tw-px-10 tw-py-7">
                <div className="tw-grid tw-h-[18rem] tw-w-full tw-grid-cols-2 tw-grid-rows-1 tw-gap-6">
                    <div className="tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
                        <div className="tw-relative tw-mt-7 tw-h-[300px] tw-w-[420px]">
                            <img
                                src={content?.question_thumbnail}
                                alt=""
                                className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw--rotate-3 tw-rounded-xl tw-object-cover"
                            />
                            <motion.img
                                initial={{ scale: 1, rotate: 1 }}
                                animate={{
                                    scale: 1.1,
                                }}
                                transition={{
                                    duration: 1,
                                    repeat: Infinity,
                                    repeatType: "reverse",
                                }}
                                src={"/quiz/spark, sparkle, 26.png"}
                                alt=""
                                className="tw-absolute tw-right-[-50px] tw-top-[-110px] tw-z-0 tw-aspect-[1/1.25] tw-w-[150px]"
                            />
                            <motion.img
                                initial={{ rotate: 10 }}
                                animate={{
                                    rotate: -10,
                                    transition: {
                                        duration: 2,
                                        repeat: Infinity,
                                        repeatType: "reverse",
                                    },
                                }}
                                src="/quiz/Question Mark.png"
                                alt=""
                                className="tw-absolute tw-bottom-[-3%] tw-right-[-5%] tw-z-0 tw-aspect-[1/1.25] tw-w-[80px]"
                            />
                        </div>
                    </div>
                    <div className="tw-h-full tw-w-full">
                        <div className="tw-grid tw-grid-cols-1 tw-gap-4">
                            {content?.options.map((option, index) => (
                                <motion.button
                                    key={index}
                                    initial={{ y: 100, opacity: 0.5 }}
                                    whileInView={{ y: 0, opacity: 1 }}
                                    transition={{
                                        duration: 0.25,
                                        delay: index * 0.1,
                                        ease: "easeInOut",
                                    }}
                                    onClick={() => handleClick(option)}
                                    className={cn(
                                        "tw-rounded-md tw-border tw-py-4 tw-font-mono tw-text-[30px] tw-leading-[30px] tw-text-[#E08D67]",
                                        selected?.label === option?.label
                                            ? "tw-border-[#FE5C96] tw-bg-[#FE5C96] tw-text-white"
                                            : "tw-border-[#9C9C9C] tw-bg-white tw-text-[#E08D67]",
                                    )}
                                    // onClick={() => handleClick(option)}
                                >
                                    {option?.label}
                                </motion.button>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Singlechoice;
