import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { tanstackApi } from "@/react-query/api";
import { useGetUsersLeaderboard } from "@/react-query/users";
import {
    Book,
    CalendarX2,
    GraduationCap,
    IdCard,
    PackageOpen,
    Route,
    ShieldCheck,
    ShieldX,
    User,
    UsersRound,
} from "lucide-react";
import { useEffect, useState } from "react";

const DomainDashboard = () => {
    const [dashData, setDashData] = useState(null);
    const [dashboard, setDashboard] = useState([
        {
            name: "Total Users",
            icon: User,
            suffix: "users",
            value: 0,
            api_key: "users",
            className: "tw-bg-green-50 tw-text-green-600",
        },
        {
            name: "Total Trainers",
            icon: IdCard,
            suffix: "trainers",
            value: 0,
            api_key: "trainers",
            className: "tw-bg-blue-50 tw-text-blue-600",
        },
        {
            name: "Available Courses",
            icon: Book,
            suffix: "courses",
            value: 0,
            api_key: "courses",
            className: "tw-bg-yellow-50 tw-text-yellow-600",
        },
        {
            name: "Total Groups / Classes",
            icon: UsersRound,
            suffix: "groups",
            value: 0,
            api_key: "groups",
            className: "tw-bg-red-50 tw-text-red-600",
        },
        {
            name: "Learning Paths",
            icon: Route,
            suffix: "paths",
            value: 0,
            api_key: "learningPaths",
            className: "tw-bg-purple-50 tw-text-purple-600",
        },
        {
            name: "Course bundles",
            icon: PackageOpen,
            suffix: "bundles",
            value: 0,
            api_key: "bundles",
            className: "tw-bg-pink-50 tw-text-pink-600",
        },
        {
            name: "Active Users",
            icon: ShieldCheck,
            suffix: "users",
            value: 0,
            api_key: "usersLoggedIn",
            className: "tw-bg-green-50 tw-text-green-600",
        },
        {
            name: "Inactive Users",
            icon: ShieldX,
            suffix: "users",
            value: 0,
            api_key: "usersNotLoggedIn",
            className: "tw-bg-red-50 tw-text-red-600",
        },
        {
            name: "Issued Certificates",
            icon: GraduationCap,
            suffix: "certificates",
            value: 0,
            api_key: "certificatesIssuedCount",
            className: "tw-bg-blue-50 tw-text-blue-600",
        },
        {
            name: "Expired Certificates",
            icon: CalendarX2,
            suffix: "certificates",
            value: 0,
            api_key: "expiredCertificatesCount",
            className: "tw-bg-red-50 tw-text-red-600",
        },
    ]);
    const leaderboard = useGetUsersLeaderboard();

    useEffect(() => {
        getCourses();
    }, []);

    useEffect(() => {
        if (dashData !== null) {
            let dash = dashboard?.map((dash, idx) => {
                return {
                    ...dash,
                    value: dashData[dash?.api_key],
                };
            });
            setDashboard(dash);
        }
    }, [dashData]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("dashboard")
            .then((res) => {
                setDashData(res?.data?.data);
            })
            .catch((err) => {
                setDashData(null);
            });
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <div>
                    <h1 className="tw-font-mono tw-text-xl tw-font-semibold">Welcome back, LMS Dashboard 👋</h1>
                    <p className="tw-text-sm tw-text-slate-600">
                        There is the latest update for the of below mentioned modules.
                    </p>
                </div>
            </div>
            <div className="tw-mt-5 tw-flex tw-flex-wrap tw-gap-3">
                {dashboard?.map((card, index) => (
                    <DashKPICard key={card.name} card={card} index={index} />
                ))}
            </div>
            <br />
            {/* <div className="tw-mt-0 tw-font-lexend">
                <div className="tw-flex tw-items-center tw-justify-between tw-px-3">
                    <h1 className="tw-font-lexend tw-text-lg tw-font-medium tw-text-gray-700">
                        #. Learner&apos;s Leaderboard :-
                    </h1>
                    <h1 className="tw-text-md tw-font-normal tw-text-gray-600">Top 10 Learners</h1>
                </div>
                <div className="tw-mt-3 tw-overflow-hidden tw-rounded-3xl tw-border-[1px]">
                    <div className="tw-grid tw-grid-cols-[70px_70px_1fr_1fr_1fr_1fr] tw-bg-slate-50">
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Rank</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Profile</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Name</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Level</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Points</div>
                        <div className="tw-px-4 tw-py-3 tw-font-lexend tw-text-sm tw-text-gray-600">Achievement</div>
                    </div>
                    <div>
                        {leaderboard?.data?.data
                            ?.slice(0, 10)
                            ?.map((user, index) => ({ ...user, rank: index + 1 }))
                            ?.map((data) => {
                                let badge = data?.user_badges[data?.user_badges?.length - 1];
                                return (
                                    <div key={data?.id} className={cn("tw-border-b-[1px] tw-p-2 tw-px-4")}>
                                        <div className="tw-grid tw-grid-cols-[70px_70px_1fr_1fr_1fr_1fr] tw-items-center">
                                            <div className="tw-font-bold">#{data.rank}</div>
                                            <div>
                                                <Avatar>
                                                    <AvatarImage src={data?.picture_url} />
                                                    <AvatarFallback>
                                                        {data.first_name[0]}
                                                        {data.last_name[0]}
                                                    </AvatarFallback>
                                                </Avatar>
                                            </div>
                                            <div className="tw-font-lexend">
                                                {`${data.first_name} ${data.last_name}`}
                                            </div>
                                            <div>
                                                <img
                                                    src={`/assets/level-${badge?.badge_details?.level}.png`}
                                                    className="tw-h-[40px]"
                                                    alt=""
                                                />
                                            </div>
                                            <div className="tw-flex tw-items-center tw-gap-3">
                                                <i className="fa-solid fa-dice-d6 tw-text-red-600"></i>{" "}
                                                <p className="tw-font-lexend tw-text-lg tw-font-medium">
                                                    {data?.points?.toLocaleString()}
                                                </p>
                                            </div>

                                            <div className="tw-flex tw-items-center tw-gap-4">
                                                <img
                                                    src={badge?.badge_details?.icon_url}
                                                    className="tw-h-[60px]"
                                                    alt=""
                                                />
                                                <Label>{badge?.badge_details?.name}</Label>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                    </div>
                </div>
            </div> */}
        </div>
    );
};

export default DomainDashboard;

const DashKPICard = ({ card }) => {
    const ICON = card?.icon;
    return (
        <div className={cn("p-3 tw-w-[280px] tw-cursor-pointer tw-rounded-2xl tw-border-[1px]", card.className)}>
            <div className="tw-mb-2 tw-flex tw-justify-end">
                <Label className="tw-text-slate-600">{card?.name}</Label>
            </div>
            <div className="tw-grid tw-grid-cols-[50px_auto] tw-items-center tw-gap-2">
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="p-2 tw-rounded-xl tw-bg-gray-100">
                        <ICON className="tw-text-teal-500" size={30} />
                    </div>
                </div>
                <div className="tw-flex tw-items-baseline tw-gap-2">
                    <h1 className="tw-text-3xl tw-font-bold tw-text-slate-800">{card?.value}</h1>
                    <small className="tw-text-[16px] tw-font-normal tw-capitalize tw-text-slate-500">
                        {card?.suffix}
                    </small>
                </div>
            </div>
        </div>
    );
};
