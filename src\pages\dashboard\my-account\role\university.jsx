import { tanstackApiFormdata } from "@/react-query/api";
import { AddUserAbout } from "@/redux/define-role/action";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";

let departmentSize = [
    { value: "computer_science", label: "Computer Science" },
    { value: "business_administration", label: "Business Administration" },
    { value: "mechanical_engineering", label: "Mechanical Engineering" },
    { value: "electrical_engineering", label: "Electrical Engineering" },
    { value: "civil_engineering", label: "Civil Engineering" },
    { value: "psychology", label: "Psychology" },
    { value: "biology", label: "Biology" },
    { value: "chemistry", label: "Chemistry" },
    { value: "physics", label: "Physics" },
    { value: "mathematics", label: "Mathematics" },
    { value: "law", label: "Law" },
    { value: "medicine", label: "Medicine" },
    { value: "nursing", label: "Nursing" },
    { value: "philosophy", label: "Philosophy" },
    { value: "english_literature", label: "English Literature" },
    { value: "history", label: "History" },
    { value: "political_science", label: "Political Science" },
    { value: "economics", label: "Economics" },
    { value: "sociology", label: "Sociology" },
    { value: "fine_arts", label: "Fine Arts" },
];

const MyProfileUniversityAbout = () => {
    const dispatch = useDispatch();
    const [orgDetails, setOrgDetails] = useState(JSON.parse(localStorage.getItem("org_data"))?.about);

    const [choosenOptions, setChoosenOptions] = useState(
        JSON.parse(localStorage.getItem("org_data"))?.about?.departments_list || [],
    );

    const onAboutChange = (e) => {
        const { name, value } = e.target;
        setOrgDetails({ ...orgDetails, [name]: value });
    };

    const onChooseOption = (option) => {
        if (choosenOptions?.includes(option)) {
            setChoosenOptions(choosenOptions?.filter((dt) => dt !== option));
        } else {
            setChoosenOptions([...choosenOptions, option]);
        }
    };

    const onMediaChange = (e) => {
        if (e.target.files[0]) {
            const formData = new FormData();
            var file = e.target.files[0];

            formData.append("file", file);
            formData.append("category", "ORGANIZATION-PROFILE");

            ImageUpload(formData);
        }
    };

    const ImageUpload = (data) => {
        tanstackApiFormdata.post("common/upload-file", data).then((res) => {
            if (res.data.success) {
                setOrgDetails({ ...orgDetails, company_logo: res?.data?.fileUrl });
            }
        });
    };

    const updateAboutSection = () => {
        let payload = {
            step: "about",
            data: {
                company_logo: orgDetails?.company_logo,
                university_name: orgDetails?.university_name,
                staff_members: orgDetails?.staff_members,
                location_situated: orgDetails?.location_situated,
                departments_list: choosenOptions,
            },
        };
        dispatch(AddUserAbout(payload));
        toast.success("Details Updated");
    };

    return (
        <>
            <div className="section_details">
                <div className="section_heading">
                    <h6>
                        <i className="fa-solid fa-building-columns"></i> About University
                    </h6>
                    <button onClick={updateAboutSection}>
                        <i className="fa-solid fa-square-pen"></i> Update Info
                    </button>
                </div>
                <div className="section_wrapper">
                    <div className="left">
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <label htmlFor="">University Name</label>
                                <input
                                    type="text"
                                    placeholder="Enter company name here"
                                    value={orgDetails?.university_name}
                                    onChange={onAboutChange}
                                    name="university_name"
                                />
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_text" style={{ flex: 1 }}>
                                <label htmlFor="">Location Situated</label>
                                <input
                                    type="text"
                                    placeholder="Enter University Location here"
                                    value={orgDetails?.location_situated}
                                    onChange={onAboutChange}
                                    name="location_situated"
                                />
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_select" style={{ flex: 1 }}>
                                <label htmlFor="">Team Staff Members count</label>
                                <select value={orgDetails?.staff_members} onChange={onAboutChange} name="staff_members">
                                    <option value=""> - Choose staff members count - </option>
                                    <option value="0-10">0 - 10</option>
                                    <option value="10-50">10 - 50</option>
                                    <option value="50-100">50 - 100</option>
                                    <option value="100-500">100 - 500</option>
                                    <option value="500-1000">500 - 1000</option>
                                </select>
                            </div>
                        </div>
                        <div className="input_group">
                            <div className="input_multiselect" style={{ flex: 1 }}>
                                <label htmlFor="">Your Company Sector</label>
                                <div className="options_wrapper">
                                    {departmentSize?.map((sectr, idx) => (
                                        <p
                                            key={idx}
                                            onClick={() => onChooseOption(sectr?.value)}
                                            className={choosenOptions?.includes(sectr?.value) ? "active" : ""}
                                        >
                                            {sectr?.label}
                                        </p>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="right">
                        <div className="org_logo">
                            <img src={orgDetails?.company_logo} alt="" id="" />
                        </div>
                        <input
                            onChange={onMediaChange}
                            type="file"
                            accept="image/*"
                            name="company_logo"
                            id="org_logo"
                        />
                        <label htmlFor="org_logo">
                            <i className="fa-solid fa-image"></i> Choose Logo
                        </label>
                    </div>
                </div>
            </div>
        </>
    );
};

export default MyProfileUniversityAbout;
