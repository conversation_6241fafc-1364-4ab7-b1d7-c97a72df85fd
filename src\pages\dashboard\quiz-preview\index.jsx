import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Introduction from "@/pages/dashboard/course-player/quiz-content/types/Introduction";
import QuizzResult from "@/pages/dashboard/quiz-preview/result";
import SlideWrapper from "@/pages/dashboard/quiz-preview/wrapper";
import { useGetQuizDetails } from "@/react-query/quizz";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

const QuizPreview = () => {
    const params = useParams();
    const [mode, setMode] = useState("instruction");
    const [isStarted, setIsStarted] = useState(false);
    const [template, setTemplate] = useState(null);
    const quizData = useGetQuizDetails(params?.quiz_id);
    const loginToken = localStorage.getItem("login_token");

    useEffect(() => {
        setTemplate(quizData.data?.data?.[0]);
    }, [quizData]);

    const [components, setComponents] = useState([]);

    useEffect(() => {
        if (template !== null && template !== undefined) {
            setComponents(
                template?.components?.map((dt, idx) => {
                    return {
                        index: idx,
                        answerKey: null,
                        points: Number(dt?.points),
                        ...dt,
                    };
                }),
            );
        }
    }, [template]);

    const startQuiz = (e) => {
        e.preventDefault();
        onChangeMode("components");
        setIsStarted(true);
    };

    const stopQuiz = () => {
        setIsStarted(false);
        onChangeMode("results");
    };

    const onChangeMode = (value) => setMode(value);

    const [slides, setSlides] = useState([]);

    const onUserReponseSave = (e, points, slides) => {
        e.preventDefault();
        stopQuiz();
        setSlides(slides);
    };

    return (
        <div className="tw-relative tw-grid tw-size-full tw-h-dvh tw-grid-rows-1">
            {mode == "instruction" && <Introduction template={template} AssessmentStart={startQuiz} />}
            {mode == "components" && (
                <SlideWrapper
                    template={template}
                    components={components}
                    isStarted={isStarted}
                    onUserReponseSave={onUserReponseSave}
                />
            )}
            {mode == "results" && <QuizzResult template={template} slides={slides} />}
            <Button size="icon" variant="secondary" asChild className={cn("tw-absolute tw-right-2 tw-top-2 tw-z-50")}>
                <Link
                    to={`https://lms-course-builder.vercel.app/dashboard/quiz-config/${params?.quiz_id}?token=${loginToken}`}
                    target="_blank"
                >
                    <X />
                </Link>
            </Button>
        </div>
    );
};

export default QuizPreview;
