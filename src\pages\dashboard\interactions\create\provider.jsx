import { createContext, useContext, useEffect, useState } from "react";

/**
 * Context for managing interactions.
 * @type {React.Context<{content: object, setContent: Function, tabList: Array, updateChanges: Function, currentTab: number, setCurrentTab: Function, setTabList: Function}>}
 */
const InteractionContext = createContext(undefined);

/**
 * Default data structure for interactions.
 * @constant {Object}
 */
const defaultData = {
    title: "Interactions",
    description: "Interactions Description",
    category: "Catalogue",
    type: "Accordion",
    is_draft: true,
    structure: {
        database: [
            {
                label: "Tab 1",
                description: "First Tab Description",
                image_src: "",
                x: "",
                y: "",
                annotion_icon: "",
                bg_sound: "",
            },
        ],
    },
    instructions: "",
    is_public: localStorage.getItem("level") === "levelOne",
};

/**
 * Interaction provider component to manage state.
 * @param {Object} props - Component properties.
 * @param {React.ReactNode} props.children - Child components.
 * @returns {JSX.Element} - Interaction context provider.
 */
export const InteractionProvider = ({ children }) => {
    const [content, setContent] = useState(defaultData);
    const [tabList, setTabList] = useState(content?.structure?.database);
    const [currentTab, setCurrentTab] = useState(0);

    /**
     * Updates interaction content.
     * @param {Object} data - Updated data.
     */
    const updateChanges = (data) => {
        setContent((prev) => ({ ...prev, ...data, structure: { ...prev.structure } }));
    };

    /**
     * Handles changes in the tab data.
     * @param {string} key - The key to be updated.
     * @param {string} value - The new value for the key.
     * @param {number} index - The index of the tab to be updated.
     */
    const handleTabChange = (key, value, index) => {
        setContent((prev) => {
            const oldData = [...prev.structure.database];
            oldData[index] = {
                ...oldData[index],
                [key]: value,
            };
            return {
                ...prev,
                structure: {
                    ...prev.structure,
                    database: oldData,
                },
            };
        });
    };

    useEffect(() => {
        if (content.structure) {
            const data = content.structure.database || content.structure.sections;
            setTabList(data);
        }
    }, [content]);

    const value = {
        content,
        setContent,
        tabList,
        updateChanges,
        currentTab,
        setCurrentTab,
        handleTabChange,
        setTabList,
    };

    return <InteractionContext.Provider value={value}>{children}</InteractionContext.Provider>;
};

/**
 * Custom hook to use the interaction context.
 * @throws {Error} If used outside of InteractionProvider.
 * @returns {{content: object, setContent: Function, tabList: Array, updateChanges: Function, currentTab: number, setCurrentTab: Function}} Interaction context values.
 */
export const useEditInteraction = () => {
    const context = useContext(InteractionContext);
    if (context === undefined) throw new Error("useEditInteraction must be used within a InteractionProvider");
    return context;
};
