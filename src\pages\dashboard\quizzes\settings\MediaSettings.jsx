import { Button, buttonVariants } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";

const MediaSettings = ({ setContentData, contentData, componentsArray, setComponentsArray }) => {
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [fileUrl, setFileUrl] = useState(contentData?.question_thumbnail);

    const [loadBG, setLoadBG] = useState(false);
    const [uploadedBG, setUploadedBG] = useState(null);
    const [fileUrlBG, setFileUrlBG] = useState(contentData?.question_background);

    console.log("thumbnail", load, uploaded, fileUrl);
    console.log("background", loadBG, uploadedBG, fileUrlBG);

    useEffect(() => {
        setContentData((prev) => ({
            ...prev,
            question_thumbnail: fileUrl,
        }));
    }, [fileUrl]);

    useEffect(() => {
        setContentData((prev) => ({
            ...prev,
            question_background: fileUrlBG,
        }));
    }, [fileUrlBG]);

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setFileUrl(res.data.fileUrl);
                } else {
                    setLoad(false);
                    setFileUrl("");
                }
            })
            .catch((err) => {
                setLoad(false);
                setFileUrl("");
            });
    };

    const onImageBGChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileBGUpload(data);
        }
    };

    const onFileBGUpload = async (formData) => {
        setLoadBG(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploadedBG(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoadBG(false);
                    setFileUrlBG(res.data.fileUrl);
                } else {
                    setLoadBG(false);
                    setFileUrlBG("");
                }
            })
            .catch((err) => {
                setLoadBG(false);
                setFileUrlBG("");
            });
    };

    return (
        <div className="tw-space-y-3">
            <div>
                <Label>Choose Thumbnail :-</Label>
                <p className="tw-mb-4 tw-text-sm tw-text-slate-400">File supported .png, .jpg & .webp </p>

                <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                    <div className="tw-aspect-[2/1] tw-w-[60%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                        <img
                            className="tw-aspect-[2/1] tw-w-full tw-rounded-md tw-object-cover"
                            src={fileUrl || "/assets/thumbnail2.png"}
                        />
                    </div>
                    <div className="tw-flex tw-gap-2">
                        <Label
                            htmlFor="question_thumbnail"
                            className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                        >
                            <Upload
                                className="opacity-60 sm:-ms-1 sm:me-2"
                                size={16}
                                strokeWidth={2}
                                aria-hidden="true"
                            />
                            <input
                                onChange={onImageChange}
                                type="file"
                                style={{ display: "none" }}
                                id="question_thumbnail"
                                accept="image/*"
                            />
                            <div className="max-sm:sr-only">
                                {load ? "Uploading" : "Upload Thumbnail Image"} {load ? `${uploaded}%` : null}
                            </div>
                        </Label>
                        {fileUrl && (
                            <Button
                                variant="outline"
                                className="aspect-square max-sm:p-0"
                                onClick={() => setFileUrl("")}
                            >
                                <X
                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                                <Label className="max-sm:sr-only">Remove</Label>
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            <div>
                <Label>Choose Background :-</Label>
                <p className="tw-mb-4 tw-text-sm tw-text-slate-400">File supported .png, .jpg & .webp </p>

                <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                    <div className="tw-aspect-[2/1] tw-w-[60%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                        <img
                            className="tw-aspect-[2/1] tw-w-full tw-rounded-md tw-object-cover"
                            src={fileUrlBG || "/assets/thumbnail2.png"}
                        />
                    </div>
                    <div className="tw-flex tw-gap-2">
                        <Label
                            htmlFor="question_background"
                            className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                        >
                            <Upload
                                className="opacity-60 sm:-ms-1 sm:me-2"
                                size={16}
                                strokeWidth={2}
                                aria-hidden="true"
                            />
                            <input
                                onChange={onImageBGChange}
                                type="file"
                                style={{ display: "none" }}
                                id="question_background"
                                accept="image/*"
                            />
                            <div className="max-sm:sr-only">
                                {loadBG ? "Uploading" : "Upload Background Image"} {loadBG ? `${uploadedBG}%` : null}
                            </div>
                        </Label>
                        {fileUrlBG && (
                            <Button
                                variant="outline"
                                className="aspect-square max-sm:p-0"
                                onClick={() => setFileUrlBG("")}
                            >
                                <X
                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                                <Label className="max-sm:sr-only">Remove</Label>
                            </Button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MediaSettings;
