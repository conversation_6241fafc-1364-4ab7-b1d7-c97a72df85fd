import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { DownloadCloudIcon, Trophy } from "lucide-react";
import { ExternalLink } from "lucide-react";
import { useCallback } from "react";

const CertificateCards = ({ title, marks, first_name, last_name, pdf_url, admin_name, issued_at }) => {
    const handleDownload = useCallback(() => {
        const link = document.createElement("a");
        link.href = pdf_url;
        link.download = `${first_name}_${last_name}__${title}_.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, [pdf_url]);
    return (
        <div className="tw-max-h-80 tw-w-full tw-basis-full tw-rounded-xl tw-border-t-[6px] tw-border-[#393E46] tw-bg-[#D9D9D9] md:tw-basis-1/2 lg:tw-basis-1/3 xl:tw-basis-1/4">
            <div className="tw-p-4">
                <div className="tw-flex tw-w-full tw-flex-col tw-gap-4 tw-p-2">
                    <div className="tw-flex tw-gap-2">
                        <div className="tw-flex tw-h-12 tw-w-12 tw-items-center tw-justify-center tw-rounded-full tw-bg-[#393E46]">
                            <Trophy size={25} color="white" />
                        </div>
                        <div className="tw-flex tw-flex-col">
                            <h3 title={title} className="tw-line-clamp-1 tw-text-xl tw-font-bold">
                                {title}
                            </h3>
                            <h4 className="tw-text-sm tw-text-gray-700">LMS</h4>
                        </div>
                    </div>
                    <div className="tw-text-xs">Issued at: {format(issued_at, "PP")}</div>
                    <h3 className="tw-text-xs">
                        Comprehensive course covering HTML, CSS, JavaScript, and modern web frameworks.
                    </h3>
                    <div
                        className={cn(
                            "tw-flex tw-h-12 tw-w-full tw-justify-between",
                            admin_name || marks ? "" : "tw-hidden",
                        )}
                    >
                        {admin_name && (
                            <div className="tw-h-fit tw-w-fit tw-rounded-3xl tw-bg-[#A8A8A8] tw-px-2 tw-py-1">
                                {admin_name}
                            </div>
                        )}
                        {marks && (
                            <div className="tw-h-fit tw-w-fit tw-rounded-3xl tw-bg-[#A8A8A8] tw-px-6 tw-py-1">
                                {marks}
                            </div>
                        )}
                    </div>
                    <hr className="tw-h-[1px] tw-w-full tw-transform tw-border-none tw-bg-[#A8A8A8]" />
                    <div className="tw-grid tw-grid-cols-2 tw-gap-3">
                        <Button asChild className="tw-items-center tw-justify-center tw-font-medium">
                            <a href={pdf_url} target="_blank" rel="noopener noreferrer">
                                <ExternalLink size={20} />
                                <h3 className="tw-sr-only tw-hidden xl:tw-not-sr-only xl:tw-block">View</h3>
                            </a>
                        </Button>
                        <Button onClick={handleDownload} className="tw-items-center tw-justify-center tw-font-medium">
                            <DownloadCloudIcon size={20} />
                            <h3 className="tw-sr-only tw-hidden xl:tw-not-sr-only xl:tw-block">Download</h3>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CertificateCards;
