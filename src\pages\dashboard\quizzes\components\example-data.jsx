export const quizExampleData = {
    singlechoice: {
        name: "Which of the following software could assist someone who cannot use their hands for computer input ?",
        type: "singlechoice",
        points: 100,
        options: [
            {
                src: "",
                label: "Video conferencing",
                isCorrect: false,
            },
            {
                src: "",
                label: "Speech recognition",
                isCorrect: true,
            },
            {
                src: "",
                label: "Audio digitizer",
                isCorrect: false,
            },
            {
                src: "",
                label: "Synthesizer",
                isCorrect: false,
            },
        ],
        dropzone: [],
        hotspots: [],
        question: "",
        feedbacks: {
            correctAns: "You have chosen the option",
            incorrectAns: "Chosen the wrong option",
            partiallyCorrectAns: "That's not exactly the correct option",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "repeat(1, 1fr)",
        componentTypeId: "singlechoice",
        hotspotDropzone: [],
        dropdown_options: [],
        draggable_options: [],
        styles: {
            answer: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
            },
            default: {
                borderColor: "#000000",
                borderStyle: "solid",
                borderWidth: "1px",
            },
            question: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
            },
            selected: {
                color: "#ffffff",
                borderColor: "#3e86f9",
                borderStyle: "solid",
                borderWidth: "1px",
                backgroundColor: "#3e86f9",
            },
        },
        question_thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1735199391_open_source_speech_recognition.png",
        specialInstruction: "",
        question_background: "",
    },
    singlechoice_media: {
        name: "Select the Odd one out of the following options",
        type: "singlechoice_media",
        points: 100,
        options: [
            {
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/super-admin/LMS4239/course-contents/1735028392_franchise.png",
                label: "image1",
                isCorrect: true,
            },
            {
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/super-admin/LMS4239/course-contents/1735028343_2810766.webp",
                label: "image2",
                isCorrect: false,
            },
            {
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/super-admin/LMS4239/course-contents/1735028348_pizza_piece_3378199_2810760.webp",
                label: "image3",
                isCorrect: false,
            },
        ],
        dropzone: [],
        hotspots: [],
        question: "",
        feedbacks: {
            correctAns: "You have chosen the option",
            incorrectAns: "Chosen the wrong option",
            partiallyCorrectAns: "That's not exactly the correct option",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "",
        componentTypeId: "singlechoice_media",
        hotspotDropzone: [],
        dropdown_options: [],
        draggable_options: [],
        styles: {
            answer: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
            },
            default: {
                borderColor: "#000000",
                borderStyle: "solid",
                borderWidth: "1px",
            },
            question: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
            },
            selected: {
                color: "#ffffff",
                borderColor: "#3e86f9",
                borderStyle: "solid",
                borderWidth: "2px",
                backgroundColor: "#3e86f9",
            },
        },
        question_thumbnail: "/assets/thumbnail.png",
        specialInstruction: "Choose the correct option out of below mentioned options",
        question_background: "",
    },
    dragdrop_media: {
        name: "Drag the image on correct placeholder make sure the label is correct for image.",
        type: "dnd_image_box",
        points: 70,
        options: [],
        dropzone: [
            {
                id: "BionicHand",
                zone: "BionicHand",
            },
            {
                id: "RobotHead",
                zone: "RobotHead",
            },
            {
                id: "HighTorqMotor",
                zone: "HighTorqMotor",
            },
        ],
        hotspots: [],
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "1fr 1.5fr",
        componentTypeId: "dnd_image_box",
        draggable_options: [
            {
                id: "image1",
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1731330205_rectangle_2738.png",
                zone: "HighTorqMotor",
            },
            {
                id: "image2",
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1731330355_rectangle_2739.png",
                zone: "HighTorqMotor",
            },
            {
                id: "image3",
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1731330374_rectangle_2736.png",
                zone: "BionicHand",
            },
            {
                id: "image4",
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1731330417_rectangle_2737.png",
                zone: "RobotHead",
            },
        ],
        question_thumbnail: "",
        specialInstruction: "Drag the image on correct placeholder make sure the label is correct for image.",
        question_background:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730972873_question_marks_background_78370_2896.jpg",
    },
    fill_in_the_blank: {
        name: "Drag the words & drop them into appropriate places.",
        type: "fillin_the_blank",
        points: 70,
        options: [],
        dropzone: [],
        hotspots: [],
        question:
            "A linkage is a series of [field] is called [field] connected with [field] A linkage .if it has two or more rigid links that move in respect to a rigid link. Which is called [field] point of the mechanism to move or produce a force, called [field]",
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [
            {
                label: "Input",
                correctIndex: 1,
            },
            {
                label: "Mechanism",
                correctIndex: 2,
            },
            {
                label: "Joints",
                correctIndex: 3,
            },
            {
                label: "Fixed",
                correctIndex: 4,
            },
            {
                label: "Rigid Links",
                correctIndex: 5,
            },
        ],
        matchOptions: [],
        optionColumns: "1fr 1fr 1fr",
        componentTypeId: "fillin_the_blank",
        draggable_options: [],
        question_thumbnail: "",
        specialInstruction: "Drage the below mentioned options into blank fields",
        question_background:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973000_personality_quiz.webp",
    },
    hotspot_dragdrop: {
        name: "Hotspot Drag & Drop question title here",
        type: "hotspot_dnd",
        points: 100,
        options: [],
        dropzone: [],
        hotspots: [
            {
                x: 0.7850000000000001,
                y: 0.5097142857142857,
                src: "",
                name: "Rotational",
                correct: "Output Motion",
            },
            {
                x: 0.755,
                y: 0.67,
                src: "",
                name: "Reciprocating",
                correct: "Input Motion",
            },
        ],
        question: "",
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "",
        componentTypeId: "hotspot_dnd",
        hotspotDropzone: [
            {
                x: 0.5183333333333333,
                y: 0,
                src: "",
                zone: "Output Motion",
            },
            {
                x: 0.24999999999999992,
                y: 0.6628571428571428,
                src: "",
                zone: "Input Motion",
            },
        ],
        dropdown_options: [],
        draggable_options: [],
        question_thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733484716_untitled_2024_11_08_1139.png",
        specialInstruction: "",
        question_background: "",
    },
    hotspot_dragdrop_media: {
        name: "Move the linkage according to the direction shown by the arrow of the input and mark the output direction by drawing an arrow inside the box.",
        type: "hotspot_dnd_image",
        points: "10",
        options: [],
        dropzone: [],
        hotspots: [
            {
                x: 0,
                y: 0.003061224489807403,
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733394938_screenshot_2024_12_05_155906.png",
                name: "",
                correct: "",
            },
            {
                x: 0,
                y: 0.5355591836735872,
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733394941_screenshot_2024_12_05_155912.png",
                name: "",
                correct: "",
            },
            {
                x: 0,
                y: 0.7804244897959461,
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733394671_screenshot_2024_12_05_155917.png",
                name: "",
                correct: "A",
            },
            {
                x: 0,
                y: 0.2817714285715134,
                src: "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733394676_screenshot_2024_12_05_155921.png",
                name: "",
                correct: "",
            },
        ],
        question: "",
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "",
        componentTypeId: "hotspot_dnd_image",
        hotspotDropzone: [
            {
                x: 0.5994444444444443,
                y: 0.06657142857142852,
                src: "",
                zone: "A",
            },
        ],
        dropdown_options: [],
        hotspotDropzones: [
            {
                x: 0.6461111111111111,
                y: 0.4037142857142857,
                src: "",
                zone: "A",
            },
        ],
        draggable_options: [],
        question_thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733394969_screenshot_2024_12_05_160602.png",
        specialInstruction: "Linkage 2",
        question_background: "",
    },
    long_answer: {
        name: "Thus, redesign and make the appropriate modifications and improvements on your model",
        type: "long_answer",
        points: 70,
        options: [],
        dropzone: [],
        hotspots: [],
        question: "In real life engineers need to make many alterations in order to end up with a final product. ",
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "1fr 1fr",
        componentTypeId: "long_answer",
        draggable_options: [],
        question_thumbnail: "",
        styles: {
            answer: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
            },
            question: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
                borderColor: "#3e86f9",
                borderStyle: "solid",
                borderWidth: "2px",
            },
        },
        specialInstruction: "Complete the gaps using the appropriate words from the box.",
        question_background:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730973000_personality_quiz.webp",
    },
    match_the_following: {
        name: "Match the following items with their descriptions:",
        type: "match_the_following",
        points: 100,
        options: [],
        dropzone: [],
        hotspots: [
            {
                x: "",
                y: "",
                src: "",
                name: "",
                correct: "",
            },
            {
                x: "",
                y: "",
                src: "",
                name: "",
                correct: "",
            },
        ],
        question: "Complete the gaps using the appropriate words from the box",
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [],
        matchOptions: [
            {
                id: "1",
                text: "sets the model into motion.",
                label: "motor",
                imageText: "",
                imageLabel:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733393540_img_3d23fb0ba76c4327d14ea74dbe990e6132b7f53f.png",
                correctIndex: "1",
            },
            {
                id: "2",
                text: "connects the controller with a peripheral.",
                label: "LED",
                imageText: "",
                imageLabel:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733393543_img_baff4394ebd5d66f69b35d2657c6fe92cbd1b86b.png",
                correctIndex: "3",
            },
            {
                id: "3",
                text: "is the brain of the robot.",
                label: "Cable",
                imageText: "",
                imageLabel:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733393546_img_dd4bd5039314bb6907b0bb6cedc3736f9d19befa.png",
                correctIndex: "4",
            },
            {
                id: "4",
                text: "emits light.",
                label: "PRO Controller",
                imageText: "",
                imageLabel:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733393549_img_548fb4dae602fc61ccd1d68b046a5cb9d0ee1d49.png",
                correctIndex: "2",
            },
        ],
        optionColumns: "",
        componentTypeId: "match_the_following",
        hotspotDropzone: [],
        draggable_options: [],
        question_thumbnail: "/assets/thumbnail.png",
        specialInstruction: "Complete the gaps using the appropriate words from the box.",
        question_background: "",
    },
    sequence_arrange: {
        name: "Write numbers under each icon to indicate the sequence you pressed the buttons to create the manual program for the goalkeeper.",
        type: "sequence_arrange",
        points: 100,
        options: [],
        dropzone: [],
        hotspots: [],
        question:
            "Throughout history, people have been trying to reach the peak of Mt. Everest. Put these historical events in the correct order.",
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [],
        matchOptions: [
            {
                text: "Setting To Right",
                label: "",
                imageText:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733483155_dd.png",
                imageLabel: "",
                correctIndex: 2,
            },
            {
                text: "Play",
                label: "",
                imageText:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733483159_download2.png",
                imageLabel: "",
                correctIndex: 1,
            },
            {
                text: "Power On / Off",
                label: "",
                imageText:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733483162_downloads.png",
                imageLabel: "",
                correctIndex: 4,
            },
            {
                text: "Stop",
                label: "",
                imageText:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733483165_download3.png",
                imageLabel: "",
                correctIndex: 3,
            },
            {
                text: "Setting To Left",
                label: "",
                imageText:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1733483169_download.png",
                imageLabel: "",
                correctIndex: "5",
            },
        ],
        optionColumns: "",
        componentTypeId: "sequence_arrange",
        hotspotDropzone: [],
        dropdown_options: [],
        draggable_options: [],
        question_thumbnail: "/assets/thumbnail.png",
        specialInstruction: "Arrange the options in the correct sequence mentioned below.",
        question_background: "",
    },
    string_dropdown: {
        name: "Read this abstract about Everest and choose one correct answer in each drop-down list.",
        type: "string_dropdown",
        points: 70,
        options: [],
        dropzone: [],
        hotspots: [],
        question:
            "The most {0} area of the mountain is often considered to be the Khumbu {1}, which is particularly dangerous due to the {2} movement of the icefall.",
        feedbacks: {
            correctAns: "You have dragged all the options perfectly",
            incorrectAns: "Dragged the wrong options at all places",
            partiallyCorrectAns: "That's not exactly the correct options",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "1.5fr 1fr",
        componentTypeId: "string_dropdown",
        dropdown_options: [
            {
                id: "label_0",
                correct: "beautiful",
                options: ["dangerous", "beautiful", "safe"],
            },
            {
                id: "label_1",
                correct: "Glacier",
                options: ["Ice Fall", "Glacier", "Peak"],
            },
            {
                id: "label_2",
                correct: "unstoppable",
                options: ["frequent", "unpredictable", "unstoppable"],
            },
        ],
        draggable_options: [],
        question_thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730974142_glaciers_jakobshavn_glacier_greenland___kertu_adobe_stock_photo_229290457.jpg",
        specialInstruction: "",
        question_background:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1730974045_pexels_mali_45987.jpg",
    },
    true_false: {
        name: "Which of the following software could assist someone who cannot use their hands for computer input ?",
        type: "true_false",
        points: 100,
        options: [
            {
                src: "",
                label: "Video conferencing",
                isCorrect: false,
            },
            {
                src: "",
                label: "Speech recognition",
                isCorrect: true,
            },
        ],
        dropzone: [],
        hotspots: [],
        question: "",
        feedbacks: {
            correctAns: "You have chosen the option",
            incorrectAns: "Chosen the wrong option",
            partiallyCorrectAns: "That's not exactly the correct option",
        },
        fillOptions: [],
        matchOptions: [],
        optionColumns: "repeat(1, 1fr)",
        componentTypeId: "singlechoice",
        hotspotDropzone: [],
        dropdown_options: [],
        draggable_options: [],
        styles: {
            answer: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
            },
            default: {
                borderColor: "#000000",
                borderStyle: "solid",
                borderWidth: "1px",
            },
            question: {
                color: "#000000",
                fontSize: "40px",
                fontFamily: "Lazy Dog",
            },
            selected: {
                color: "#ffffff",
                borderColor: "#3e86f9",
                borderStyle: "solid",
                borderWidth: "1px",
                backgroundColor: "#3e86f9",
            },
        },
        question_thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-contents/1735199391_open_source_speech_recognition.png",
        specialInstruction: "",
        question_background: "",
    },
};
