import { DndContext, useDraggable, useDroppable } from "@dnd-kit/core";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import ContentSlideLayout from "../layouts/ContentSlideLayout";

const DNDImagesAndBox = ({
    handleNextSlide,
    handlePrevSlide,
    className,
    data,
    sequence,
    onComponentAnswer,
    template,
}) => {
    const [images, setImages] = useState([]);
    const [droppedImages, setDroppedImages] = useState(null);

    useEffect(() => {
        if (data !== null) {
            setImages(data?.draggable_options);
            const result = data?.dropzone.reduce((acc, item) => {
                acc[item.id] = data?.answerKey?.[item.id] ?? [];
                return acc;
            }, {});
            setDroppedImages(result);
        }
    }, [data]);

    function getMatchedElements(data) {
        const matchedElements = [];
        for (const outerKey in data) {
            const elements = data[outerKey];
            if (elements.length > 0 && elements[0].zone === outerKey) {
                matchedElements.push(elements[0]);
            }
        }
        return matchedElements;
    }

    useEffect(() => {
        const ans = getMatchedElements(droppedImages);
        let ppq = data?.points / data?.dropzone?.length;
        let points = Math.round(ans?.length * ppq);

        onComponentAnswer(sequence, droppedImages, points);
    }, [droppedImages]);

    const onDragEnd = (event) => {
        const { active, over } = event;
        if (!over && !droppedImages) return;

        if (droppedImages[active.id] && droppedImages[over.id]) {
            return;
        }
        if (over.id == "option") {
            const exist = droppedImages[active.id][0];
            setDroppedImages((prev) => ({
                ...prev,
                [active.id]: [],
            }));
            setImages((prev) => [...prev, exist]);
            return;
        }

        const exist = droppedImages[over.id].find((dt) => dt.id === active.id);
        const dt = images.find((img) => img.id == active.id);

        if (exist) return;
        if (droppedImages[over.id].length == 1) return;
        setImages(images.filter((img) => img.id !== active.id));
        setDroppedImages((prev) => ({
            ...prev,
            [over.id]: [...prev[over.id], dt],
        }));
    };

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data:
                    data?.question ?? "Drag the image on correct placeholder make sure the label is correct for image.",
                specialInstruction:
                    data?.specialInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <DndContext onDragEnd={onDragEnd}>
                <div className="tw-relative tw-flex tw-h-full tw-w-full tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden">
                    <div
                        className="tw-grid tw-h-1/2 tw-w-full tw-gap-[3rem]"
                        style={{ gridTemplateColumns: `repeat(${data?.dropzone?.length},1fr)` }}
                    >
                        {data?.dropzone?.map((zone, idx) => {
                            return (
                                <DropZone
                                    current={zone}
                                    blanks={zone}
                                    key={zone.id}
                                    className="tw-relative tw-size-full"
                                >
                                    <div
                                        style={{
                                            backgroundImage: `url(${zone?.src})`,
                                            borderColor: data?.styles?.question?.borderColor || "#000",
                                            borderWidth: data?.styles?.question?.borderWidth || "3px",
                                            borderStyle: data?.styles?.question?.borderStyle,
                                        }}
                                        className="tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center tw-rounded-[0.5rem] tw-border-dashed tw-border-purple-500 tw-bg-contain tw-bg-center tw-bg-no-repeat"
                                    >
                                        <img
                                            src="/assets/drag_drop_media.png"
                                            className="tw-z-0 tw-size-16 tw-opacity-75"
                                        />
                                        {(!zone.src || zone?.src == "") && (
                                            <p
                                                className="tw-absolute tw-inset-0 tw-z-0 tw-mx-auto tw-flex tw-w-[80%] tw-items-center tw-justify-center tw-font-mono tw-text-[20px] tw-font-bold tw-text-black"
                                                style={{
                                                    color: data?.styles?.question?.color,
                                                    fontFamily: data?.styles?.question?.fontFamily,
                                                    fontSize: data?.styles?.question?.fontSize,
                                                }}
                                            >
                                                {zone.zone}
                                            </p>
                                        )}
                                        {droppedImages?.[zone.id]?.length > 0 && (
                                            <DropItem
                                                className="tw-absolute tw-inset-0 tw-size-full"
                                                key={zone.id}
                                                id={zone.id}
                                                index={idx}
                                            >
                                                <div className="tw-absolute tw-inset-0 tw-z-10 tw-flex tw-size-full tw-flex-wrap tw-gap-2">
                                                    {droppedImages[zone.id].map((img) => (
                                                        <img
                                                            key={img.id}
                                                            src={img.src || "/assets/thumbnail-beta.png"}
                                                            className="tw-absolute tw-inset-0 tw-size-full tw-rounded-[0.5rem] tw-object-cover"
                                                            alt={img.id}
                                                        />
                                                    ))}
                                                </div>
                                            </DropItem>
                                        )}
                                    </div>
                                </DropZone>
                            );
                        })}
                    </div>
                    <OptionDropZone>
                        <div className="tw-flex tw-items-center tw-justify-center tw-gap-4">
                            {images?.map((image, index) => (
                                <DropItem key={image.id} id={image.id} index={index}>
                                    <div className="tw-flex tw-h-[7rem] tw-cursor-grab tw-items-center tw-gap-[1rem] tw-rounded-[0.5rem] tw-text-white">
                                        <img
                                            src={image.src || "/assets/thumbnail-beta.png"}
                                            alt={image.id}
                                            className="tw-h-full tw-w-full tw-rounded-[0.5rem] tw-object-contain"
                                        />
                                    </div>
                                </DropItem>
                            ))}
                        </div>
                    </OptionDropZone>
                </div>
            </DndContext>
        </ContentSlideLayout>
    );
};

export default DNDImagesAndBox;

function DropItem({ id, children, index, className }) {
    const { attributes, listeners, setNodeRef, transform } = useDraggable({ id });

    return (
        <motion.div
            initial={{
                opacity: 0.5,
                scale: 0.2,
            }}
            whileInView={{
                scale: 1,
                opacity: 1,
            }}
            transition={{
                duration: 0.25,
                delay: index * 0.1,
                ease: "easeInOut",
            }}
            ref={setNodeRef}
            style={{
                x: transform?.x,
                y: transform?.y,
            }}
            className={className}
            {...listeners}
            {...attributes}
        >
            {children}
        </motion.div>
    );
}

function DropZone({ blanks, children, className }) {
    const { setNodeRef } = useDroppable({ id: blanks.id });

    return (
        <div ref={setNodeRef} className={className}>
            {children}
        </div>
    );
}

function OptionDropZone({ children }) {
    const { setNodeRef } = useDroppable({
        id: "option",
    });

    return (
        <div
            ref={setNodeRef}
            className="tw-relative tw-z-50 tw-mt-6 tw-w-full tw-min-w-32 tw-rounded-lg tw-border-2 tw-border-gray-400 tw-p-2"
        >
            {children}
        </div>
    );
}
