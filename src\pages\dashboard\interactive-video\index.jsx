import Pagination from "@/components/table/pagination";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { tanstackApiFormdata } from "@/react-query/api";
import {
    useCreateInteractiveVideo,
    useDeleteInteractiveVideo,
    useGetInteractiveVideo,
} from "@/react-query/interactive-video";
import { Upload, X } from "lucide-react";
import moment from "moment";
import { useEffect } from "react";
import { useRef, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { toast } from "sonner";

const ITEMS_PER_PAGE = 10;

export default function InteractiveVideo() {
    const fileInputRef = useRef(null);
    const [totalPages, setTotalPages] = useState(0);
    const [searchParams] = useSearchParams();
    const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get("page") || "1", 10));
    const offset = (currentPage - 1) * ITEMS_PER_PAGE;
    const limit = ITEMS_PER_PAGE;
    const interactions = useGetInteractiveVideo({ limit, offset });
    const addInteractiveVideo = useCreateInteractiveVideo();
    const [createOpen, setCreateOpen] = useState(false);
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);
    const [fileURL, setFileURL] = useState("");
    const [fileName, setFileName] = useState("");
    const [title, setTitle] = useState("");
    const [uploadResponse, setUploadResponse] = useState(null);
    const deleteInteractiveVideo = useDeleteInteractiveVideo();
    const loginToken = localStorage.getItem("login_token");

    const handlePageChange = (page) => setCurrentPage(page);

    useEffect(() => {
        if (interactions.isSuccess) setTotalPages(Math.ceil(interactions.data.pagination.total / ITEMS_PER_PAGE));
    }, [interactions]);

    const onFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            const maxSize = 25 * 1024 * 1024;
            if (file.size > maxSize) {
                fileInputRef.current.value = [];
                return toast.error("File size should be less than 25mb");
            }
            if (!title || title.trim() == "") {
                fileInputRef.current.value = [];
                return toast.error("Enter Title");
            }
            const data = new FormData();
            const imageData = file;
            data.append("title", title);
            data.append("file", imageData);
            data.append("category", "COURSE-CONTENTS");
            onFileUpload(data, imageData);
        }
    };

    const createVideo = () => {
        if (!title || title.trim() == "") return toast.error("Enter Title");
        addInteractiveVideo.mutate(
            { media_ids: [uploadResponse.data.id] },
            {
                onSuccess: (data) => {
                    toast.success(data.message);
                    setTitle("");
                    setFileURL("");
                    setFileName("");
                    setCreateOpen(false);
                    // setTotalPages(Math.ceil(interactions.data.pagination.total / pageSize))
                },
            },
        );
    };

    const onFileUpload = async (formData, imageData) => {
        try {
            setLoad(true);
            const res = (
                await tanstackApiFormdata.post("common/upload-course-file", formData, {
                    onUploadProgress: (data) => {
                        setUploaded(Math.round((data.loaded / data.total) * 100));
                    },
                })
            ).data;
            setUploadResponse(res);
            if (res.success) {
                setFileURL(res.fileUrl);
                setFileName(imageData?.name);
            }
        } catch (error) {
            setFileURL("");
        } finally {
            setLoad(false);
        }
    };

    const onRemoveFile = () => {
        setFileName("");
        setFileURL("");
    };

    return (
        <>
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-video"></i> Interactive Video
                </h4>
                <Button onClick={() => setCreateOpen(true)} className="tw-px-2 tw-py-1">
                    <i className="fa-solid fa-plus"></i> Create New
                </Button>
            </div>
            <br />

            <div className="custom_table tw-mt-2 tw-h-full">
                {interactions.isLoading && <>Loading...</>}
                <table>
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Creation Date</th>
                            <th>Expiration Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {interactions.data?.data?.map((row, idx) => (
                            <tr key={idx}>
                                <td>
                                    <p>{row?.primaryMedia?.title}</p>
                                </td>
                                <td>{row?.type}</td>
                                <td>{row?.status}</td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td>{moment(row?.updatedAt).format("LL")}</td>
                                <td className="tw-space-x-2">
                                    <Link
                                        to={`https://lms-course-builder.vercel.app/dashboard/interactive-video/${row?.id}?token=${loginToken}`}
                                        target="_blank"
                                    >
                                        <button className="selected_btn_alpha">
                                            <i className="fa-solid fa-pen-to-square"></i> Preview
                                        </button>
                                    </Link>
                                    <AlertDialog className="tw-pl-1">
                                        <AlertDialogTrigger>
                                            <button className="selected_btn_alpha">
                                                <i className="fa-solid fa-trash"></i> Delete
                                            </button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    This action cannot be undone.
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction
                                                    onClick={() => deleteInteractiveVideo.mutate({ id: row?.id })}
                                                >
                                                    Continue
                                                </AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                    itemsPerPage={ITEMS_PER_PAGE}
                />
            </div>
            <Dialog open={createOpen} onOpenChange={setCreateOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Create Interactive Video</DialogTitle>
                    </DialogHeader>
                    <div className="tw-space-y-3">
                        <div>
                            <Label>Interactive Video Title</Label>
                            <Input
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                placeholder="Enter video title here"
                                className="tw-mt-1"
                            />
                        </div>
                        <div className="tw-mt-2 tw-flex tw-flex-col tw-gap-2">
                            <div>
                                <Label>Select video file here to Generate Interactive Video</Label>
                                <p className="tw-text-xs">File supported .mp4</p>
                            </div>
                            <div className="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center tw-gap-3 tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2 tw-py-5">
                                <img className="tw-h-[50px] tw-w-[50px] tw-rounded-md" src="/assets/video.png" />
                                <p className="tw-text-md tw-text-slate-400">
                                    {fileName || "Upload Video .mp4 file (max 25mb)"}
                                </p>
                                <div className="tw-mt-2 tw-flex tw-gap-2">
                                    <Label
                                        htmlFor="video_file"
                                        className={cn(
                                            buttonVariants({ variant: "outline" }),
                                            "aspect-square max-sm:p-0",
                                        )}
                                    >
                                        <Upload
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <input
                                            ref={fileInputRef}
                                            onChange={onFileChange}
                                            type="file"
                                            style={{ display: "none" }}
                                            id="video_file"
                                            accept="video/*"
                                        />
                                        <div className="max-sm:sr-only">
                                            {load ? "Uploading" : "Upload .mp4 File"} {load ? `${uploaded}%` : null}
                                        </div>
                                    </Label>

                                    {fileURL && (
                                        <Button
                                            variant="outline"
                                            className="aspect-square tw-rounded-xl"
                                            onClick={onRemoveFile}
                                        >
                                            <X
                                                className="opacity-60 sm:-ms-1 sm:me-2"
                                                size={16}
                                                strokeWidth={2}
                                                aria-hidden="true"
                                            />
                                            <Label className="max-sm:sr-only">Remove</Label>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </div>
                        <Button onClick={createVideo} className="tw-mt-2">
                            Create Interactive Video
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}
