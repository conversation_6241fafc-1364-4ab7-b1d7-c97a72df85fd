import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMapEvents } from "react-leaflet";
// Fixes issue with default marker icons not displaying correctly
delete L.Icon.Default.prototype._getIconUrl;

L.Icon.Default.mergeOptions({
    iconRetinaUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
    iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
    shadowUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png",
});

// Custom icon for previously selected location
const customIcon = new L.Icon({
    iconUrl: "https://cdn-icons-png.flaticon.com/128/9757/9757873.png", // Custom icon URL
    iconSize: [35, 35], // Adjust size
    iconAnchor: [15, 40], // Adjust anchor point
    popupAnchor: [0, -40], // Adjust popup position if needed
});

const MapModal = ({ open, setOpen, scheduleDetails, setScheduleDetails }) => {
    const [position, setPosition] = useState(
        scheduleDetails?.latitude && scheduleDetails?.longitude
            ? { lat: scheduleDetails?.latitude, lng: scheduleDetails?.longitude }
            : null,
    );

    // Custom hook to capture map click
    const LocationMarker = () => {
        useMapEvents({
            click(e) {
                setPosition(e.latlng); // Set marker position on map click
            },
        });

        return position === null ? null : (
            <Marker position={position} /> // Default marker for new location
        );
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        setScheduleDetails({ ...scheduleDetails, latitude: position?.lat, longitude: position?.lng });
        setOpen(false);
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-5xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">Mark meeting location on map</h1>
                        </DialogTitle>
                        <DialogDescription>Drag the location marker to your desired location.</DialogDescription>
                    </DialogHeader>
                    <MapContainer
                        center={position || [20.5937, 78.9629]} // Center on the previous position if available
                        zoom={5}
                        style={{ height: "600px", width: "100%" }}
                    >
                        <TileLayer
                            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                        />

                        {/* Custom marker for previously selected location */}
                        {scheduleDetails?.latitude && scheduleDetails?.longitude && (
                            <Marker
                                position={[scheduleDetails?.latitude, scheduleDetails?.longitude]}
                                icon={customIcon} // Use custom icon here
                            />
                        )}

                        {/* Default marker for new clicks */}
                        <LocationMarker />
                    </MapContainer>
                    <DialogFooter className="sm:justify-start">
                        {/* <Button type="button" variant="secondary">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                        <Button onClick={onDataSubmit}>
                            <i className="fa-regular fa-floppy-disk"></i> Done
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default MapModal;
