import { Button, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const Details = ({ bundleData, getBundle }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [passwordShow, setPasswordShow] = useState(false);
    const [bundleDetails, setBundleDetails] = useState({
        name: "",
        description: "",
        password: "",
        logo_image_url: "",
        is_public: false,
    });

    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        const allowedChars = /^[a-zA-Z0-9 ]*$/;

        if (name == "name") {
            if (!allowedChars.test(value)) {
                e.preventDefault();
            } else {
                setBundleDetails({ ...bundleDetails, [name]: value });
            }
        } else {
            setBundleDetails({ ...bundleDetails, [name]: value });
        }

        if (name == "logo_image_url") {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-IMAGES");
            onFileUpload(data);
        }
    };

    useEffect(() => {
        if (bundleData !== null) {
            setBundleDetails({
                name: bundleData?.name,
                description: bundleData?.description,
                password: bundleData?.password,
                logo_image_url: bundleData?.logo_image_url,
            });
        } else {
            setBundleDetails({
                name: "",
                description: "",
                password: "",
                logo_image_url: "",
            });
        }
    }, [bundleData]);

    const onClearData = () => {
        setBundleDetails({
            name: "",
            description: "",
            password: "",
            logo_image_url: "",
        });
    };

    const RemoveImage = () => {
        setBundleDetails({ ...bundleDetails, logo_image_url: "" });
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setBundleDetails({ ...bundleDetails, logo_image_url: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setBundleDetails({ ...bundleDetails, logo_image_url: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setBundleDetails({ ...bundleDetails, logo_image_url: "" });
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!bundleDetails?.name) {
            toast?.warning("Bundle name", {
                description: "Bundle name is required",
            });
            return false;
        } else if (!bundleDetails?.password && params?.bundle_id == undefined) {
            toast?.warning("Password", {
                description: "Bundle password is required",
            });
            return false;
        } else if (!bundleDetails?.logo_image_url) {
            toast?.warning("Banner Image", {
                description: "Bundle Banner Image is required",
            });
            return false;
        }

        if (params?.bundle_id !== undefined) {
            const payload = {
                course_bundle_id: params?.bundle_id,
                step: "course_bundle_details",
                data: {
                    name: bundleDetails?.name,
                    description: bundleDetails?.description || "No Description Yet!",
                    password: bundleDetails?.password,
                    logo_image_url: bundleDetails?.logo_image_url,
                },
            };

            await tanstackApi
                .put("course-bundle/creation/set-details", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "course bundles",
                        log: `${bundleDetails?.name} updated successfully.`,
                    });
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });

                    getBundle(params?.bundle_id);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                name: bundleDetails?.name,
                description: bundleDetails?.description || "No Description Yet!",
                password: bundleDetails?.password,
                logo_image_url: bundleDetails?.logo_image_url,
                is_public: localStorage.getItem("level") == "levelOne" ? true : false,
            };

            await tanstackApi
                .post("course-bundle/creation/create-course-bundle", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "course bundles",
                        log: `${bundleDetails?.name} created successfully.`,
                    });
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                    navigate(`/dashboard/course-bundle`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Bundle basic details</CardTitle>
                <CardDescription>
                    Add bundle title, banner , decription etc here. Click save when you&apos;re done.
                </CardDescription>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[1.5fr_2fr] tw-gap-0">
                    <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                        <div className="tw-aspect-[2/1] tw-w-[80%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-md tw-object-cover"
                                src={bundleDetails?.logo_image_url || "/assets/thumbnail.png"}
                            />
                        </div>
                        <div className="tw-flex tw-gap-2">
                            {!bundleDetails?.logo_image_url && (
                                <Label
                                    htmlFor="logo_image_url"
                                    className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                                >
                                    <Upload
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <input
                                        onChange={onChangeHandle}
                                        type="file"
                                        style={{ display: "none" }}
                                        id="logo_image_url"
                                        name="logo_image_url"
                                        accept="image/*"
                                    />
                                    <div className="max-sm:sr-only">
                                        {load ? "Uploading" : "Upload Bundle Thumbnail"} {load ? `${uploaded}%` : null}
                                    </div>
                                </Label>
                            )}
                            {bundleDetails?.logo_image_url && (
                                <Button variant="outline" className="aspect-square max-sm:p-0" onClick={RemoveImage}>
                                    <X
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <Label className="max-sm:sr-only">Remove</Label>
                                </Button>
                            )}
                        </div>
                    </div>
                    <div className="tw-space-y-3">
                        <div className="tw-space-y-1">
                            <Label htmlFor="name">Bundle Name</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={bundleDetails?.name}
                                name="name"
                                id="name"
                                placeholder="Enter bundle name here"
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="password">Password</Label>
                            <div className="tw-relative tw-flex tw-rounded-md tw-border">
                                <Input
                                    className="tw-border-none"
                                    onChange={onChangeHandle}
                                    value={bundleDetails?.password}
                                    name="password"
                                    id="password"
                                    type={passwordShow ? "text" : "password"}
                                    placeholder="eg: 123@32$#SD"
                                />
                                <div className="tw-absolute tw-right-2 tw-top-2 tw-cursor-pointer">
                                    {!passwordShow ? (
                                        <i
                                            className="fa-solid fa-eye-slash"
                                            onClick={() => setPasswordShow(!passwordShow)}
                                        ></i>
                                    ) : (
                                        <i
                                            className="fa-solid fa-eye"
                                            onClick={() => setPasswordShow(!passwordShow)}
                                        ></i>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="tw-space-y-1">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                onChange={onChangeHandle}
                                value={bundleDetails?.description}
                                name="description"
                                className="tw-text-sm"
                                placeholder="Define bundle description here."
                            />
                        </div>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" variant="outline" onClick={onClearData}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> Update Details
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default Details;
