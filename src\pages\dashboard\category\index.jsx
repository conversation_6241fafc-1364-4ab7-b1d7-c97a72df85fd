import { Button } from "@/components/ui/button";
import { DataTable } from "@/pages/dashboard/roles/permission/data-table";
import { fetchCATEGORYREQ } from "@/redux/category/action";
import { format } from "date-fns";
import { Eye, Pencil, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import CategoryForm from "./form";

export default function CatgoryPage() {
    const dispatch = useDispatch();
    const AllCategory = useSelector((state) => state.CategoryReducers)?.categoryList;

    const listingCatgory = [];
    const [open, setOpen] = useState(false);
    const [selected, setSelected] = useState(null);

    useEffect(() => {
        dispatch(fetchCATEGORYREQ());
    }, []);

    const handleUpdate = (id) => {
        const data = AllCategory?.find((dt) => dt.id == id);
        setSelected(data);
        setOpen(true);
    };

    const columns = [
        {
            accessorKey: "id",
        },
        {
            accessorKey: "category_code",
            header: "Category Code",
        },
        {
            accessorKey: "category_name",
            header: "Category Name",
        },
        {
            accessorKey: "is_active",
            header: "Status",
            cell: ({ row }) => {
                const is_active = Boolean(row.getValue("is_active"));
                return <div className="text-right font-medium">{is_active ? "Active" : "In Active"}</div>;
            },
        },
        {
            accessorKey: "createdAt",
            header: "Create On",
            cell: ({ row }) => {
                return <div className="text-right font-medium">{format(row.getValue("createdAt"), "PP")}</div>;
            },
        },
        {
            accessorKey: "createdAt",
            header: "Sub Category ",
            cell: ({ row }) => {
                return (
                    <Link to={`/dashboard/sub-category/${row.getValue("id")}`}>
                        <Eye className="tw-text-blue-400" />
                    </Link>
                );
            },
        },
        {
            accessorKey: "created_by",
            header: "Actions",
            cell: ({ row }) => {
                return (
                    <div
                        className="text-right font-medium"
                        onClick={() => {
                            handleUpdate(row.getValue("id"));
                        }}
                    >
                        <Pencil className="tw-text-blue-400" />
                    </div>
                );
            },
        },
    ];

    if (listingCatgory?.isLoading) return <div>Loading...</div>;
    return (
        <div>
            <div className="tw-mb-3 tw-flex tw-items-center tw-justify-between">
                <div className="tw-font-lexend tw-text-3xl tw-font-medium">Categories</div>
                <Button
                    onClick={() => {
                        setSelected(null);
                        setOpen(true);
                    }}
                    variant="outline"
                >
                    <Plus />
                    Create Category
                </Button>
            </div>
            <DataTable columns={columns} data={AllCategory} />
            <CategoryForm open={open} setOpen={setOpen} selected={selected} />
        </div>
    );
}
