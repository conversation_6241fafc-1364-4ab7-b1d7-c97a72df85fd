import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

const contentTypes = [
    {
        title: "Quiz",
        contentType: "QUIZ",
        icon: "/assets/quiz.png",
    },
    {
        title: "SCORM",
        contentType: "ZIP",
        icon: "/assets/scorm.png",
    },
    {
        title: "Video",
        contentType: "MP4",
        icon: "/assets/video.png",
    },
    {
        title: "HTML",
        contentType: "HTML",
        icon: "/assets/html.png",
    },
    {
        title: "Assignment",
        contentType: "ASSIGNMENT",
        icon: "/assets/assignment.png",
    },
    {
        title: "Homework",
        contentType: "HOMEWORK",
        icon: "/assets/homework.png",
    },
    {
        title: "Interaction",
        contentType: "INTERACTIONS",
        icon: "/assets/interaction.png",
    },
    {
        title: "Audio",
        contentType: "MP3",
        icon: "/assets/audio.png",
    },
    {
        title: "Embed",
        contentType: "EMBED",
        icon: "/assets/embed.png",
    },
];

const SlideContent = ({ open, setOpen, setContentArray, contentArray, Chapter, onSlideEdit }) => {
    const onSelectContent = (content) => {
        let option = {
            chapter_id: Chapter?.id,
            lecture_title: "Add content title here.",
            lecture_data: null,
            content_order: contentArray?.length + 1,
            content_type: content?.contentType,
            content_url: null,
            is_questions_added: null,
            is_completion_required: false,
            is_active: true,
            html_content: null,
            quiz_id: null,
            homework_id: null,
            interaction_id: null,
            is_draft: true,
            icon: "",
            background: null,
            slide_type: null,
            quiz: null,
            homework: null,
            interaction: null,
        };
        setContentArray([...contentArray, option]);
        setOpen(false);

        onSlideEdit(option, contentArray?.length);
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-sm">
                    <div className="tw-flex tw-justify-center">
                        <div className="tw-grid tw-w-full tw-grid-cols-3">
                            {contentTypes?.map((content, idx) => (
                                <div
                                    key={idx}
                                    onClick={() => onSelectContent(content)}
                                    className="tw-flex tw-aspect-square tw-cursor-pointer tw-flex-col tw-items-center tw-gap-2 tw-rounded-xl tw-py-5 hover:tw-bg-slate-100"
                                >
                                    <img className="tw-w-[55px]" src={content?.icon} alt="" />
                                    <Label>{content?.title}</Label>
                                </div>
                            ))}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default SlideContent;
