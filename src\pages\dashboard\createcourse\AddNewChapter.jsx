import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { tanstackApi } from "@/react-query/api";
import { useCreateCourse, useUpdateCourse } from "@/react-query/courses";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "sonner";

const defaultValue = {
    chapter_title: "",
    chapter_discription: "",
    scorm_details: 0,
    chapter_points: 0,
    days: 0,
};

const AddNewChapter = ({ open, setOpen, editChapter, CourseData }) => {
    const queryClient = useQueryClient();
    const createCourse = useCreateCourse();
    const updateCourse = useUpdateCourse();
    const [chapterDetails, setChapterDetails] = useState({
        ...defaultValue,
        course_id: CourseData?.id,
        chapter_order: CourseData?.courseChapters?.length + 1,
    });

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        const allowedChars = /^[a-zA-Z0-9 ]*$/;
        if (name == "chapter_title") {
            if (!allowedChars.test(value)) {
                e.preventDefault();
            } else {
                setChapterDetails({ ...chapterDetails, [name]: value });
            }
        } else {
            setChapterDetails({ ...chapterDetails, [name]: value });
        }
    };

    useEffect(() => {
        if (editChapter !== null) {
            setChapterDetails({
                course_id: editChapter?.course_id,
                chapter_title: editChapter?.chapter_title,
                chapter_discription: editChapter?.chapter_discription,
                chapter_order: editChapter?.chapter_order,
                scorm_details: editChapter?.scorm_details,
                chapter_points: editChapter?.chapter_points,
                days: editChapter?.days,
            });
        } else {
            setChapterDetails({
                ...defaultValue,
                course_id: CourseData?.id,
                chapter_order: CourseData?.courseChapters?.length + 1,
            });
        }
    }, [editChapter]);

    const onClearData = () => {
        setChapterDetails({
            ...defaultValue,
            course_id: CourseData?.id,
            chapter_order: CourseData?.courseChapters?.length + 1,
        });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!chapterDetails.chapter_title) {
            toast.warning("Title", {
                description: `Chapter title is required.`,
            });

            return false;
        }

        if (CourseData.courseChapters.find((chapter) => chapter.chapter_title === chapterDetails.chapter_title)) {
            toast.warning("Title", {
                description: `Chapter title already exists.`,
            });
            return false;
        }

        if (!chapterDetails.chapter_points) {
            toast.warning("Points", {
                description: `Chapter points are required.`,
            });
            return false;
        }

        if (!chapterDetails.days) {
            toast.warning("Schedule Days", {
                description: `Chapter schedule days are required.`,
            });
            return false;
        }

        if (editChapter == null) {
            const payload = {
                course_id: CourseData?.id,
                chapter_title: chapterDetails?.chapter_title,
                chapter_discription: chapterDetails?.chapter_discription || "No Description yet!",
                chapter_order: chapterDetails?.chapter_order,
                lectureContents: [],
                scorm_details: chapterDetails?.scorm_details,
                chapter_points: chapterDetails?.chapter_points,
                days: chapterDetails?.days,
            };

            createCourse.mutate(payload, {
                onSuccess: () => {
                    toast.success("Saved Successfully", {
                        description: "Chapter saved successfully.",
                    });
                    queryClient.invalidateQueries({ queryKey: ["view-course"] });
                    setOpen(false);
                },
                onError: () => {
                    toast.error("Something went wrong", {
                        description: "Something went wrong.",
                    });
                },
            });
        } else {
            const payload = {
                chapter_id: editChapter?.id,
                course_id: CourseData?.id,
                chapter_title: chapterDetails?.chapter_title,
                chapter_discription: chapterDetails?.chapter_discription || "No Description yet!",
                chapter_order: chapterDetails?.chapter_order,
                lectureContents: editChapter?.lms_course_chapter_contents,
                scorm_details: chapterDetails?.scorm_details,
                chapter_points: chapterDetails?.chapter_points || 0,
                days: chapterDetails?.days || 1,
            };
            updateCourse.mutate(payload, {
                onSuccess: () => {
                    toast.success("Updated Successfully", {
                        description: "Chapter updated successfully.",
                    });
                    setOpen(false);
                },
                onError: () => {
                    toast.error("Something went wrong", {
                        description: "Something went wrong.",
                    });
                },
            });
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-4xl">
                    <DialogHeader>
                        <DialogTitle className="tw-text-2xl">Create a new chapter!</DialogTitle>
                        <DialogDescription>
                            Fill up below details according to your chapter convenience.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-2">
                        <div className="tw-grid tw-grid-cols-[80px_1fr] tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Order</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={chapterDetails?.chapter_order}
                                    name="chapter_order"
                                    type="number"
                                    id="name"
                                    placeholder="eg:01"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">
                                    Chapter Title <span className="tw-text-red-600">*</span>
                                </Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={chapterDetails?.chapter_title}
                                    name="chapter_title"
                                    id="name"
                                    placeholder="Enter chapter title here"
                                />
                            </div>
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="name">Description</Label>
                            <Textarea
                                onChange={onChangeHandle}
                                value={chapterDetails?.chapter_discription}
                                name="chapter_discription"
                                className="tw-text-sm"
                                placeholder="Enter chapter description here."
                            />
                        </div>

                        <div className="tw-grid tw-grid-cols-[170px_170px_auto] tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">
                                    Assign Points <span className="tw-text-red-600">*</span>
                                </Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={chapterDetails?.chapter_points}
                                    name="chapter_points"
                                    type="number"
                                    id="name"
                                    placeholder="Chapter points here"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">
                                    Duration (<small>Days</small>) <span className="tw-text-red-600">*</span>
                                </Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={chapterDetails?.days}
                                    name="days"
                                    type="number"
                                    id="name"
                                    placeholder="eg: 1,2 or 4"
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <Button type="button" variant="secondary" onClick={onClearData}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>
                        <Button type="submit" onClick={onDataSubmit}>
                            <i className="fa-solid fa-floppy-disk"></i> Save Chapter
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default AddNewChapter;
