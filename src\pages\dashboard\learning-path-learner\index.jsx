import { tanstackApi } from "@/react-query/api";
import { Clock, FolderOpen, Hash } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const LearningPathsLearner = () => {
    const navigate = useNavigate();
    const [dataList, setDataList] = useState([]);
    const [displayedData, setDisplayedData] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");

    useEffect(() => {
        getPathData();
    }, []);

    const getPathData = async () => {
        await tanstackApi
            .get("learning-path/listing")
            .then((res) => {
                const fetchedData = res?.data?.data || [];
                setDataList(fetchedData);
                setDisplayedData(fetchedData);
            })
            .catch((err) => {
                setDataList([]);
                setDisplayedData([]);
            });
    };

    const handleSearch = () => {
        if (searchTerm.trim() !== "") {
            const filtered = dataList.filter((path) => path?.name.toLowerCase().includes(searchTerm.toLowerCase()));
            setDisplayedData(filtered);
        } else {
            setDisplayedData(dataList);
        }
    };

    const handleClear = () => {
        setSearchTerm("");
        setDisplayedData(dataList);
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-signs-post"></i> Learning Paths
                </h4>
            </div>
            <div className="page_filters tw-mt-5 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label htmlFor="search">Path Name</label>
                    <input
                        id="search"
                        style={{ minWidth: "300px" }}
                        type="text"
                        placeholder="Search by name ..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={handleSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={handleClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-5 tw-p-0">
                {displayedData.length > 0 ? (
                    displayedData.map((path, index) => (
                        <div key={index} className="tw-w-[300px] tw-rounded-xl tw-border-[1px] tw-p-1">
                            <div
                                className="tw-h-[160px] tw-w-full tw-cursor-pointer tw-overflow-hidden tw-rounded-lg"
                                onClick={() => navigate(`/dashboard/learning-path-learner-view/${path?.id}`)}
                            >
                                <img
                                    className="tw-h-full tw-w-full tw-object-cover"
                                    src={path?.learning_path_logo_url || "/assets/thumbnail.png"}
                                    alt={path?.name}
                                />
                            </div>
                            <div className="tw-p-2">
                                <div
                                    className="tw-flex tw-items-center tw-gap-2"
                                    onClick={() => navigate(`/dashboard/learning-path-learner-view/${path?.id}`)}
                                >
                                    <h2 className="tw-leading-2 tw-text-md tw-line-clamp-1 tw-font-semibold">
                                        {path?.name}
                                    </h2>
                                </div>
                                <div
                                    className="tw-h-[40px]"
                                    onClick={() => navigate(`/dashboard/learning-path-learner-view/${path?.id}`)}
                                >
                                    <p className="tw-mt-2 tw-line-clamp-2 tw-text-sm tw-text-slate-500">
                                        {path?.description}
                                    </p>
                                </div>
                                <div className="tw-mt-3 tw-space-y-2">
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Hash size={18} /> {path?.code}
                                    </span>
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Clock size={18} /> Created on {moment(path?.createdAt).format("LL")}
                                    </span>
                                </div>
                                <div className="tw-mt-3 tw-grid tw-grid-cols-1 tw-gap-2">
                                    <div
                                        onClick={() => navigate(`/dashboard/learning-path-learner-view/${path?.id}`)}
                                        className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                    >
                                        <FolderOpen size={16} strokeWidth={2} aria-hidden="true" />
                                        View Learning Path
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="tw-flex tw-h-40 tw-w-full tw-items-center tw-justify-center tw-rounded-md tw-border tw-p-5 tw-text-center tw-text-slate-500">
                        No learning paths found.
                    </div>
                )}
            </div>
        </div>
    );
};

export default LearningPathsLearner;
