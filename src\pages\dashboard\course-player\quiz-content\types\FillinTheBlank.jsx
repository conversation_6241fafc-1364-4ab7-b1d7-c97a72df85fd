import { colorsList } from "@/data/colors";
import { replacePlaceholders } from "@/lib/utils";
import ContentSlideLayout from "@/pages/dashboard/course-player/quiz-content/layouts/ContentSlideLayout";
import { DndContext, useDraggable, useDroppable } from "@dnd-kit/core";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

const FillinTheBlank = ({
    handleNextSlide,
    handlePrevSlide,
    className,
    data,
    sequence,
    onComponentAnswer,
    template,
}) => {
    const [blanks, setBlanks] = useState([]);
    const [quizData, setQuizData] = useState([]);
    const [draggableOptions, setDraggableOptions] = useState([]);

    useEffect(() => {
        if (data?.fillOptions) {
            setBlanks(
                Array(data?.fillOptions?.length).fill({
                    label: "",
                    correctIndex: 0,
                }),
            );
            setQuizData(replacePlaceholders(data?.question, data?.fillOptions));
            setDraggableOptions(data?.fillOptions);
        }
    }, [data]);

    useEffect(() => {
        let ans = blanks.filter((dt, idx) => {
            return Number(dt?.correctIndex) == idx && blanks[idx]?.label === dt.label && dt.label !== "";
        });
        let ppq = data?.points / data?.fillOptions?.length;
        let points = Math.round(ans?.length * ppq);

        onComponentAnswer(
            sequence,
            blanks.filter((dt) => dt?.label != ""),
            points,
        );
    }, [blanks, draggableOptions]);

    function handleDragEnd(event) {
        const { active, over } = event;
        if (!over) return;
        const value = active.data.current;
        const container = over.id;

        const option = data?.fillOptions?.find((dt, i) => (value.label ? dt.label == value.label : active.id == i));

        if (container === "option") {
            const exists = draggableOptions.find((arr) => arr?.label === option.label);
            if (exists) return;
            const newDraggableOptions = [...draggableOptions, option];
            setDraggableOptions(newDraggableOptions);
            setBlanks(blanks?.filter((dt) => dt?.label !== option.label));
            return;
        }

        const exists = blanks.find((arr) => arr?.label === option.label);
        const newIndex = container.split("-")[1];
        const newBlanks = [...blanks];
        const inAnsExist = blanks[newIndex] && blanks[newIndex].label !== "";
        const options = draggableOptions;
        if (exists) {
            const findIndex = blanks.findIndex((arr) => arr?.label === option.label);
            newBlanks[newIndex] = exists;
            newBlanks[findIndex] = blanks[newIndex];
        } else if (inAnsExist) {
            const oldData = blanks[newIndex];
            newBlanks[newIndex] = option;
            options[options.length] = oldData;
        } else {
            newBlanks[newIndex] = option;
        }
        setBlanks(newBlanks);
        setDraggableOptions(options?.filter((dt) => dt?.label !== option.label));
    }

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data: data?.name ?? "",
                specialInstruction: data?.specialInstruction ?? "",
            }}
        >
            <DndContext onDragEnd={handleDragEnd}>
                <div className="tw-grid tw-w-full tw-grid-cols-1 tw-grid-rows-1 tw-gap-6">
                    <div className="tw-flex tw-items-start tw-justify-center tw-gap-6">
                        <label htmlFor="">
                            {quizData?.map((item, idx) => {
                                return typeof item === "string" ? (
                                    <span key={idx} style={{ ...data?.styles?.question, lineHeight: 1.75 }}>
                                        {item}
                                    </span>
                                ) : (
                                    <DropZone
                                        key={idx}
                                        current={item.correctIndex}
                                        item={item}
                                        styles={data?.styles}
                                        blanks={blanks}
                                    />
                                );
                            }) || "Component title here"}{" "}
                            <b>{data?.preference === "required" && "*"}</b>
                        </label>
                    </div>
                    <OptionDropZone>
                        {draggableOptions?.map((word, index) => (
                            <DropItem key={index} styles={data?.styles} index={`option-${index}`} word={word} />
                        ))}
                    </OptionDropZone>
                </div>
            </DndContext>
        </ContentSlideLayout>
    );
};

export default FillinTheBlank;

function DropZone({ item, blanks, current, styles }) {
    const { setNodeRef } = useDroppable({
        id: `answer-${current}`,
    });

    return (
        <div
            ref={setNodeRef}
            className="tw-mr-1 tw-inline-block tw-min-h-8 tw-min-w-20 tw-rounded-md"
            style={blanks[Number(item.correctIndex)]?.label ? { ...styles?.selected } : { ...styles?.default }}
        >
            {blanks[Number(item.correctIndex)]?.label ? (
                <DropItem index={Number(current)} styles={styles} word={blanks[Number(item.correctIndex)]}>
                    {blanks[Number(item.correctIndex)]?.label}
                </DropItem>
            ) : (
                blanks[Number(item.correctIndex)]?.label
            )}
        </div>
    );
}

function OptionDropZone({ children }) {
    const { setNodeRef } = useDroppable({
        id: "option",
    });

    return (
        <div
            ref={setNodeRef}
            className="tw-relative tw-z-50 tw-flex tw-min-h-[4rem] tw-min-w-16 tw-flex-wrap tw-items-center tw-justify-center tw-gap-4 tw-rounded-lg tw-border tw-p-1"
        >
            {children}
        </div>
    );
}

function DropItem({ index, word, styles }) {
    const id = typeof index === "number" ? index : index.split("-")[1];
    const { attributes, listeners, setNodeRef, transform } = useDraggable({
        id: index,
        data: {
            label: word.label,
        },
    });

    return (
        <motion.button
            initial={{
                opacity: 0.5,
                scale: 0.2,
            }}
            whileInView={{
                scale: 1,
                opacity: 1,
            }}
            transition={{
                duration: 0.25,
                delay: index * 0.1,
                ease: "easeInOut",
            }}
            ref={setNodeRef}
            style={{
                x: transform?.x,
                y: transform?.y,
                backgroundColor: colorsList[id],
                ...styles?.answer,
            }}
            {...listeners}
            {...attributes}
            className="!tw-cursor-grab tw-rounded-lg tw-px-6 tw-py-1"
        >
            {word?.label}
        </motion.button>
    );
}
