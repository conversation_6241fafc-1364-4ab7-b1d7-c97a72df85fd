import {
    FETCH_PERMISSION_REQ,
    GET_PERMISSION_LIST,
    UPDATE_MODULE_PERMISSION,
    UPDATE_SUB_MODULE_PERMISSION,
} from "@/redux-types";

export const fetchPermissionReq = (role_code) => {
    return {
        type: FETCH_PERMISSION_REQ,
        payload: role_code,
    };
};

export const getPermissionList = (permissionList) => {
    return {
        type: GET_PERMISSION_LIST,
        payload: permissionList,
    };
};

export const UpdateModulePermission = (permissionList) => {
    return {
        type: UPDATE_MODULE_PERMISSION,
        payload: permissionList,
    };
};

export const UpdateSubModulePermission = (permissionList) => {
    return {
        type: UPDATE_SUB_MODULE_PERMISSION,
        payload: permissionList,
    };
};
