import { ALERT_SNACKBAR_INFO } from "@/redux-types";

const initialState = {
    alertInfoObj: {},
    isLoading: true,
    error: null,
};

const alertReducer = (state = initialState, action) => {
    switch (action.type) {
        case ALERT_SNACKBAR_INFO:
            return {
                alertInfoObj: action.payload,
                isLoading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};
export default alertReducer;
