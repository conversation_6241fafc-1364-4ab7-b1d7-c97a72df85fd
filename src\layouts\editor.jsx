import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link, Outlet } from "react-router-dom";

export default function EditorLayout() {
    return (
        <div>
            <div className="tw-flex tw-items-center tw-justify-center tw-gap-5 tw-p-3">
                <Button asChild>
                    <Link to="/editor/lexical">Lexical</Link>
                </Button>
                <Button asChild>
                    <Link to="/editor/tip-tap">TipTap</Link>
                </Button>
            </div>
            <Outlet />
        </div>
    );
}
