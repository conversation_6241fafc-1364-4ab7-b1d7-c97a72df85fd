import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { usePagination } from "@/hooks/use-pagination";
import { useGetInvoiceList } from "@/react-query/invoice";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";

const ITEMS_PER_PAGE = 7;

const statuses = [
    { value: "PENDING", label: "Pending" },
    { value: "PAID", label: "Paid" },
];

const DomainOrders = () => {
    const navigate = useNavigate();
    const { data: invoiceList, isSuccess, isError } = useGetInvoiceList();
    const [dataList, setDataList] = useState([]);
    const [searchParams] = useSearchParams();
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [currentPage, setCurrentPage] = useState(Number(searchParams.get("page") || 1));

    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);
    const { pages } = usePagination({
        currentPage,
        totalPages,
        paginationItemsToDisplay: ITEMS_PER_PAGE,
    });

    useEffect(() => {
        if (isSuccess) {
            const list = invoiceList.data?.filter((dt) => dt?.customer?.is_domain);
            setDataList(list);
            setFilteredData(list);
        } else if (isError) {
            setDataList([]);
            setFilteredData([]);
        }
    }, [isSuccess, isError, invoiceList]);

    const [filterState, setFilterState] = useState({
        search: "",
        status: "",
        invoice_date: "",
    });

    useEffect(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        setTableData(filteredData.slice(startIndex, endIndex));
    }, [currentPage, filteredData]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
        navigate(`?page=${page}`);
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState((prev) => ({ ...prev, [name]: value }));
    };

    const onSearch = () => {
        const data = dataList.filter((item) => {
            const matchesName = filterState.search
                ? item.invoice_number.toLowerCase().includes(filterState.search.toLowerCase())
                : true;

            const statusMatch = filterState.status ? item.status === filterState.status : true;

            const invoiceDateMatch = filterState.invoice_date
                ? moment(item.invoice_date).format("DD/MMM/YYYY") ===
                  moment(filterState.invoice_date).format("DD/MMM/YYYY")
                : true;

            return matchesName && statusMatch && invoiceDateMatch;
        });
        setFilteredData(data);
        handlePageChange(1); // Reset to page 1 after search
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            status: "",
            invoice_date: "",
        });
        handlePageChange(1); // Reset to page 1 after clear
    };

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>All Orders</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
            </div>
            <div className="page_filters tw-mt-4 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="search">
                        Search
                    </label>
                    <input
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState.search}
                        name="search"
                        type="text"
                        placeholder="Search by invoice Id"
                        id="search"
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="invoice_date">
                        Invoice Date
                    </label>
                    <input
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState.invoice_date}
                        type="date"
                        name="invoice_date"
                        id="invoice_date"
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="status">
                        Status
                    </label>
                    <select
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState.status}
                        name="status"
                        id="status"
                    >
                        <option value=""> - All - </option>
                        {statuses.map((opn, idx) => (
                            <option value={opn.value} key={idx}>
                                {opn.label}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>Invoice ID</th>
                            <th>Customer</th>
                            <th>Type</th>
                            <th>Transaction ID</th>
                            <th>Particulars</th>
                            <th>Discount</th>
                            <th>Total Amount</th>
                            <th>Invoice Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData.map((row, idx) => (
                            <tr key={idx}>
                                <td>#.{row.invoice_number}</td>
                                <td>
                                    {row.customer.is_domain
                                        ? row.customer.domain_name
                                        : `${row.customer.first_name} ${row.customer.last_name}`}
                                </td>
                                <td>
                                    <Badge variant={row.customer.is_domain ? "default" : "outline"}>
                                        {row.customer.is_domain ? "Domain" : "Learner"}
                                    </Badge>
                                </td>
                                <td>{row.stripe_payment_id || "-"}</td>
                                <td>{row.invoice_items.length} Item</td>
                                <td>${row.discount}</td>
                                <td>
                                    <b>${row.total_amount}</b>
                                </td>
                                <td>{moment(row.invoice_date).format("LL")}</td>
                                <td>{row.status}</td>
                                <td>
                                    <Link to={`/dashboard/view-domain-order/${row.id}`}>
                                        <Button>
                                            <i className="fa-solid fa-eye"></i> View Order
                                        </Button>
                                    </Link>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">
                        {ITEMS_PER_PAGE}
                    </p>
                </div>
                <div>
                    <Pagination className="tw-mx-0 tw-w-[auto]">
                        <PaginationContent>
                            <PaginationItem>
                                <PaginationPrevious
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {pages.map((page, index) => {
                                const key = `${page}_${index}`;
                                if (page === -1) {
                                    return (
                                        <PaginationItem key={key}>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    );
                                }
                                return (
                                    <PaginationItem key={key}>
                                        <PaginationLink
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            })}

                            <PaginationItem>
                                <PaginationNext
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </div>
    );
};

export default DomainOrders;
