import {
    ADD_NEW_CATEGORY,
    ADD_NEW_SUB_CATEGORY,
    FETCH_CATEGORY_LIST_REQ,
    FETCH_SUB_CATEGORY_REQ,
    GET_CATEGORY_LIST,
    GET_SUB_CATEGORY_LIST,
    UPDATE_CATEGORY,
    UPDATE_SUB_CATEGORY,
} from "@/redux-types";

export const fetchCATEGORYREQ = () => {
    return {
        type: FETCH_CATEGORY_LIST_REQ,
    };
};

export const getAllCategories = (categoryList) => {
    return {
        type: GET_CATEGORY_LIST,
        payload: categoryList,
    };
};

export const addCategory = (categoryData, onclose) => {
    return {
        type: ADD_NEW_CATEGORY,
        payload: categoryData,
        onclose: onclose,
    };
};

export const updateCategory = (categoryData, onClose) => {
    return {
        type: UPDATE_CATEGORY,
        payload: categoryData,
        onClose: onClose,
    };
};

export const fetchSUBCATEGORYREQ = (categoryID) => {
    return {
        type: FETCH_SUB_CATEGORY_REQ,
        payload: categoryID,
    };
};

export const getSubCategory = (subCategoryList) => {
    return {
        type: GET_SUB_CATEGORY_LIST,
        payload: subCategoryList,
    };
};

export const addSubCategory = (subCategoryData) => {
    return {
        type: ADD_NEW_SUB_CATEGORY,
        payload: subCategoryData,
    };
};

export const updateSubCategory = (subCategoryData, onClose) => {
    return {
        type: UPDATE_SUB_CATEGORY,
        payload: subCategoryData,
        onClose: onClose,
    };
};
