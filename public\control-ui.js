window.addEventListener("message", (event) => {
    var method = event.data.method,
        data = event.data.data;
    switch (method) {
        case "debugControl":
            addToDebug(data);
            break;
        case "commitControl":
            setData(data);
            break;
        case "exitOK":
            document.getElementById("MainScormPlayer").removeChild(document.getElementById("cross_domain"));
            document.getElementById("MainScormPlayer").removeChild(statusElement);
            document.getElementById("MainScormPlayer").removeChild(bookmarkDataElement);
            break;
        case "updateTotalTime":
            index = data.index;
            totalTime = data.time;
            totalStatus = data.status;
            bookmarkData = data.bookmarkData;
            bookmarkDataElement.innerHTML = JSON.stringify(bookmarkData);
            statusElement.innerHTML =
                `<p> Status: ` +
                `<b>${ 
                data.status 
                } </b> ${ 
                index 
                } / ${ 
                bookmarkData.totalChapters 
                } (${ 
                bookmarkData.completedChapters 
                } completed) </p>`;
            break;
        case "setUI":
            statusElement.innerHTML =
                `<p> Status: ` +
                ` <b> ${ 
                data.status 
                } </b> ${ 
                index 
                } / ${ 
                bookmarkData.totalChapters 
                }time: ${ 
                totalTime 
                } (${ 
                bookmarkData.completedChapters 
                } completed) </p>`;
            break;
    }
});

var uptoDateData = {},
    totalTime,
    totalStatus,
    bookmarkData = {
        completedChapters: 0,
        index: 1,
        totalChapters: 1,
    },
    index,
    mainScormId,
    token,
    debugStrings = "",
    statusElement,
    bookmarkDataElement,
    addToDebug = (str) => {
        debugStrings = `${debugStrings  } \t${  str}`;
    },
    loadDataControl = (args) => {
        mainScormId = args.scormId;
        token = args.token;
        createIframe(args);
    },
    exitAPI = () => {
        sendMessage("exitAPI", null);
    },
    goNext = () => {
        sendMessage("goNext", null);
    },
    goPrevious = () => {
        sendMessage("goPrevious", null);
    },
    goSCO = (i) => {
        sendMessage("goSCO", i);
    },
    setData = async (data) => {
        uptoDateData[data.key] = data.value;
        if(totalStatus && totalTime) {
        uptoDateData[mainScormId] = uptoDateData[mainScormId] || {};
        uptoDateData[mainScormId]['cmi.core.total_time'] = totalTime;
        totalStatus = uptoDateData[mainScormId]['cmi.core.lesson_status'];
      }
        await fetch("https://appapi.amieyaa.com/api/course/scorm/update-scorm-details", {
            method: "PUT",
            headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
                authorization: `bearer ${token}`,
            },
            body: JSON.stringify({
                data: uptoDateData,
                scormId: mainScormId,
                otherData: {
                    time: totalTime,
                    status: totalStatus,
                },
            }),
        });
        uptoDateData = {};
    };
function createIframe(args) {
    statusElement = document.getElementsByClassName("sco_status")[0]; //
    bookmarkDataElement = document.getElementById("sco_status_box");
    const MainScormPlayer = document.getElementById("MainScormPlayer");
    if (!statusElement) {
        statusElement = document.createElement("div");
        statusElement.id = "sco_status";
        MainScormPlayer.appendChild(statusElement);
    }
    if (!bookmarkDataElement) {
        bookmarkDataElement = document.createElement("p");
        bookmarkDataElement.id = "sco_status_box";
        MainScormPlayer.appendChild(bookmarkDataElement);
    }
    bookmarkDataElement.style.visibility = "hidden";
    bookmarkDataElement.innerHTML = JSON.stringify(bookmarkData);

    const iframeElement = document.createElement("iframe");
    iframeElement.setAttribute("id", "cross_domain");
    iframeElement.setAttribute("width", args.width);
    iframeElement.setAttribute("height", args.height);
    iframeElement.setAttribute("src", "https://s3.ap-south-1.amazonaws.com/infowarelms/commonFiles/player.s3.html"); 
    iframeElement.setAttribute("title", "Player");
    iframeElement.onload = function () {
        sendMessage("loadDataControl", args);
    };
    MainScormPlayer.appendChild(iframeElement);
}

function sendMessage(method, details) { 
   document
    .getElementById("cross_domain")
    .contentWindow.postMessage({ method: method, data: details }, "https://s3.ap-south-1.amazonaws.com");
}
