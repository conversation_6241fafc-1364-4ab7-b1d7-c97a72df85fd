import {
    CERTIFICATE_RES,
    CREATE_CERTIFICATE,
    EDIT_CERTIFICATE,
    FETCH_CERTI_BACKGROUND_TYPES_REQ,
    FETCH_UPLOADED_CERTIFICATE_REQ,
    GET_CERTI_BACKGROUND_LIST,
    GET_COMPLETED_CERTIFICATE,
    GET_UPLOADED_CERTIFICATE_LIST,
    SET_CERTIFICATE_RES,
} from "@/redux-types";

const initialState = {
    //CERTIFICATE BACKGROUND TYPES LIST ARRAY
    certiBackgroundType: [],

    // UPLOADED CAERTIFICATE BASED ON BACKGROUND TYPE FILTER
    getCertifi: [],
    backgroundTypes: [],
    certificateData: {},
    editData: {},
    responseData: false,
};

const CertificateReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_CERTI_BACKGROUND_TYPES_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_CERTI_BACKGROUND_LIST:
            return {
                ...state,
                certiBackgroundType: action.payload,
            };
        case FETCH_UPLOADED_CERTIFICATE_REQ:
            return {
                ...state,
                isLoading: true,
                type: action.payload,
            };
        case GET_UPLOADED_CERTIFICATE_LIST:
            return {
                ...state,
                backgroundTypes: action.payload,
            };
        case CREATE_CERTIFICATE:
            return {
                ...state,
                certificateData: action.payload,
            };
        case GET_COMPLETED_CERTIFICATE:
            return {
                ...state,
                getCertifi: action.payload,
            };
        case EDIT_CERTIFICATE:
            return {
                ...state,
                editData: action.payload,
            };
        case CERTIFICATE_RES:
            return {
                ...state,
                responseData: action.payload,
            };
        case SET_CERTIFICATE_RES:
            return {
                ...state,
                responseData: action.payload,
            };
        default:
            return state;
    }
};
export default CertificateReducer;
