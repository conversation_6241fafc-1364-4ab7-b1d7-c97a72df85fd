import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTable } from "@/pages/dashboard/users/control-panel/data-table";
import { useGetCourses } from "@/react-query/courses";
import { useGetUserAssignCourse, useGetUsersEnrollListing } from "@/react-query/users/enroll";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import { format } from "date-fns";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

export default function AssignCourse({ userId }) {
    const courses = useGetCourses();
    const assignCourse = useGetUsersEnrollListing({ userId });
    const [courseList, setCourseList] = useState([]);
    const [search, setSearch] = useState([]);
    const [selectedCourse, setSelectedCourse] = useState([]);
    const [deletedCourses, setDeletedCourses] = useState([]);
    const [assignedCourseID, setAssignedCourseID] = useState([]);
    const [assignedCourses, setAssignedCourses] = useState([]);
    const updateAssignCourse = useGetUserAssignCourse();

    useEffect(() => {
        if (courses.status == "success")
            setCourseList(courses.data.data.filter((itm) => itm?.course_status === "completed"));
    }, [courses.status]);

    useEffect(() => {
        if (updateAssignCourse.status == "success") {
            toast.success("Courses Assigned", {
                description: updateAssignCourse.data.message,
            });
            setSelectedCourse([]);
            setDeletedCourses([]);
        }
        if (updateAssignCourse.status == "error")
            toast.error("Something went wrong", {
                description: updateAssignCourse.error.response?.data?.message,
            });
    }, [updateAssignCourse.status]);

    const assignCourseHandler = () => {
        updateAssignCourse.mutate({
            userId: userId,
            courseIds: selectedCourse,
            deletedIds: deletedCourses,
        });
    };

    const selectCourse = useCallback((id) => {
        if (deletedCourses?.includes(id)) setDeletedCourses((prev) => prev.filter((dt) => dt !== id));
        setSelectedCourse((prev) =>
            selectedCourse?.includes(id) ? prev : !assignedCourseID?.includes(id) ? [...prev, id] : prev,
        );

        setAssignedCourses((prev) => (assignedCourses?.includes(id) ? prev : [...prev, id]));
    }, []);

    const unSelectCourseHandler = (id) => {
        if (assignedCourseID.includes(id)) {
            if (!deletedCourses.includes(id)) {
                setDeletedCourses([...deletedCourses, id]);
            }
        }
        if (assignedCourses.includes(id)) {
            const filtered = assignedCourses.filter((course) => course !== id);
            setAssignedCourses(filtered);
        }

        if (selectedCourse.includes(id)) {
            const filtered = selectedCourse.filter((course) => course !== id);
            setSelectedCourse(filtered);
        }
    };

    useEffect(() => {
        if (assignCourse.status == "success") {
            setAssignedCourses(assignCourse.data.courses.map((itm) => itm.course_id));
            setAssignedCourseID(assignCourse.data.courses.map((itm) => itm.course_id));
        }
    }, [assignCourse.status]);

    if (courses.isLoading || assignCourse.isLoading) return <div>Loading...</div>;

    const columns = [
        {
            accessorKey: "course_banner_url",
            header: "Banner",
            cell: ({ row }) => <img src={row.getValue("course_banner_url")} className="tw-w-20" alt="" />,
        },
        {
            accessorKey: "course_title",
            header: "Course Name",
        },
        {
            accessorKey: "audience_type",
            header: "Audience Type",
        },
        {
            accessorKey: "createdAt",
            header: "Created On",
            cell: ({ row }) => format(row.getValue("createdAt"), "PP"),
        },
        {
            accessorKey: "id",
            header: "Action",
            cell: ({ row }) => (
                <>
                    {assignedCourses.includes(row.getValue("id")) ? (
                        <Button variant="primary" onClick={() => unSelectCourseHandler(row.getValue("id"))}>
                            <DoneAllIcon /> ASSIGNED
                        </Button>
                    ) : (
                        <Button variant="outline" onClick={() => selectCourse(row.getValue("id"))}>
                            <ControlPointIcon />
                            SELECT COURSE
                        </Button>
                    )}
                </>
            ),
        },
    ];

    return (
        <div>
            <div className="tw-my-3 tw-flex tw-items-center tw-justify-between tw-gap-2 tw-font-lexend">
                <div className="tw-w-full">
                    <Input
                        placeholder="Search Courses..."
                        type="search"
                        value={search}
                        onChange={(event) => setSearch(event.target.value)}
                        className="tw-w-full tw-max-w-lg"
                    />
                </div>
                <div className="tw-flex tw-w-1/4 tw-items-center tw-justify-end tw-gap-3">
                    {selectedCourse?.length > 0 && <p>{selectedCourse?.length} Course Selected</p>}
                    {(deletedCourses?.length > 0 || selectedCourse.length > 0) && (
                        <Button variant="success" onClick={assignCourseHandler}>
                            {deletedCourses?.length > 0 ? "Update Courses" : "Assign Courses"}
                        </Button>
                    )}
                </div>
            </div>
            <DataTable columns={columns} data={courseList} search={search} />
        </div>
    );
}
