import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import {
    <PERSON>alog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

const dataSchema = z.object({
    displayName: z
        .string()
        .min(1, "Display Name is required")
        .refine((val) => val.trim().length > 0, "Display Name cannot be empty"),
    display_icon: z
        .string()
        .min(1, "Display Icon is required")
        .refine((val) => val.trim().length > 0, "Display Icon cannot be empty"),
    description: z
        .string()
        .min(1, "Description is required")
        .refine((val) => val.trim().length > 0, "Description cannot be empty"),
    component_type: z
        .string()
        .min(1, "Component Type is required")
        .refine((val) => val.trim().length > 0, "Component Type cannot be empty"),
});

const defaultValue = {
    displayName: "",
    display_icon: "",
    description: "",
    component_type: "",
    type: "",
};

const QuestionTypeForm = ({ open, setOpen, editData, getQuestionTypes }) => {
    const params = useParams();

    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const [questionType, setQuestionType] = useState(defaultValue);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setQuestionType({ ...questionType, [name]: value });
    };

    useEffect(() => {
        if (editData !== null) {
            setQuestionType({
                displayName: editData?.displayName,
                display_icon: editData?.display_icon,
                description: editData?.description,
                component_type: editData?.component_type,
                type: editData?.component_type,
            });
        } else {
            setQuestionType(defaultValue);
        }
    }, [editData]);

    const RemoveImage = () => {
        setQuestionType({ ...questionType, display_icon: "" });
    };

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-IMAGES");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setQuestionType({ ...questionType, display_icon: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setQuestionType({ ...questionType, display_icon: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setQuestionType({ ...questionType, display_icon: "" });
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        const validate = dataSchema.safeParse(questionType);
        if (!validate.success) {
            toast.warning("Invalid Data", {
                description: validate.error.errors[0].message,
            });
            return false;
        }

        if (editData == null) {
            const payload = {
                ...validate.data,
                type: validate.data.component_type,
            };

            await tanstackApi
                .post("question-type/create", { ...payload })
                .then((res) => {
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                    getQuestionTypes();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                questionTypeId: editData?.id,
                ...validate.data,
                type: validate.data.component_type,
            };

            await tanstackApi
                .put("question-type/update", { ...payload })
                .then((res) => {
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    getQuestionTypes();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-[800px]">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">
                                {editData !== null ? "Update" : "Create a new"} question type!
                            </h1>
                        </DialogTitle>
                        <DialogDescription>
                            Fill up below details according to your question type convenience.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="tw-grid tw-grid-cols-[250px_1fr] tw-gap-5">
                        <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                            <div className="tw-aspect-[2/1] tw-w-[100%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                                <img
                                    className="tw-aspect-[1/1] tw-w-full tw-rounded-md tw-object-contain"
                                    src={questionType?.display_icon || "/assets/thumbnail-alpha.png"}
                                />
                            </div>
                            <div className="tw-flex tw-gap-2">
                                {!questionType?.display_icon && (
                                    <Button
                                        htmlFor="display_icon"
                                        className={cn(
                                            buttonVariants({ variant: "outline" }),
                                            "aspect-square max-sm:p-0 tw-rounded-xl",
                                        )}
                                    >
                                        <Upload
                                            className="opacity-60 sm:-ms-1 sm:me-2 tw-text-black"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <input
                                            onChange={onImageChange}
                                            type="file"
                                            style={{ display: "none" }}
                                            id="display_icon"
                                            accept="image/*"
                                        />
                                        <Label htmlFor="display_icon" className="tw-text-black">
                                            {load ? "Uploading" : "Upload Icon *"} {load ? `${uploaded}%` : null}
                                        </Label>
                                    </Button>
                                )}
                                {questionType?.display_icon && (
                                    <Button
                                        variant="outline"
                                        className="aspect-square max-sm:p-0"
                                        onClick={RemoveImage}
                                    >
                                        <X
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <Label className="max-sm:sr-only">Remove Icon</Label>
                                    </Button>
                                )}
                            </div>
                        </div>
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="displayName">Display Name *</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={questionType?.displayName}
                                        name="displayName"
                                        id="displayName"
                                        placeholder="Enter question type display name here"
                                    />
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="component_type">
                                        Component Type <small>(Inegration key)</small> *
                                    </Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={questionType?.component_type}
                                        name="component_type"
                                        id="component_type"
                                        placeholder="Define component type here"
                                    />
                                </div>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="description">Description *</Label>
                                <Textarea
                                    onChange={onChangeHandle}
                                    value={questionType?.description}
                                    name="description"
                                    className="tw-text-sm"
                                    placeholder="Enter question type description here."
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                <i className="fa-solid fa-xmark"></i> Close
                            </Button>
                        </DialogClose>
                        <Button type="submit" onClick={onDataSubmit}>
                            <i className="fa-solid fa-floppy-disk"></i> Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default QuestionTypeForm;
