import { tanstack<PERSON>pi } from "@/react-query/api";
import { timelineLog } from "@/react-query/common/timeline";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { onPermissionDataSave, onUserPersonalData } from "@/redux/auth/action";
import { useMutation } from "@tanstack/react-query";
import { useDispatch } from "react-redux";

export const useLogin = () => {
    const dispatch = useDispatch();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("auth/signin", data)).data,
        onSuccess: (data) => {
            const userData = data.data;
            const token = data.token;
            const permissions = JSON.stringify(userData.permissions);

            localStorage.setItem("permissions", permissions);
            localStorage.setItem("login_token", token);
            localStorage.setItem("userId", userData.id);
            localStorage.setItem("lms_fName", userData.first_name);
            localStorage.setItem("lms_lName", userData.last_name);
            localStorage.setItem("user_email", userData.email);
            localStorage.setItem("user_image", userData.picture_url);
            localStorage.setItem("domain_name", userData.domain_name);
            localStorage.setItem("user_code", userData.user_code);
            localStorage.setItem("user_loggedIn", true);
            localStorage.setItem("role_category", userData.role_category);
            localStorage.setItem("is_trainer", userData.is_trainer);
            localStorage.setItem("login_role", userData.role);
            document.cookie = `login_token=${token}; path=/; max-age=${60 * 60 * 24 * 7}`;

            if (userData.organizationDetails) {
                localStorage.setItem("org_data", JSON.stringify(userData.organizationDetails.organization_info));
                localStorage.setItem(
                    "terms_conditions",
                    JSON.stringify(userData.organizationDetails.term_n_conditions),
                );
            } else {
                localStorage.setItem("org_data", JSON.stringify({}));
                localStorage.setItem("terms_conditions", JSON.stringify({}));
            }

            localStorage.setItem("login_role", userData.role);

            switch (userData.role_category) {
                case "administrator":
                    localStorage.setItem("level", "levelOne");
                    break;
                case "user":
                    localStorage.setItem("level", "levelTwo");
                    break;
                case "sub-user":
                    localStorage.setItem("level", "levelThree");
                    if (userData.parent_user_id) localStorage.setItem("parent_user_id", userData.parent_user_id);
                    break;
            }

            timelineLog({
                user_id: userData.parent_user_id,
                event: "login",
                log: `${userData.first_name} ${userData.last_name} logged in successfully `,
            });

            dispatch(onUserPersonalData({ ...userData }));
            dispatch(onPermissionDataSave(permissions));
        },
        onError: (err) => {
            dispatch(AlertSnackInfo({ message: err.response.data.message, result: err.response.data.success }));
        },
    });
};
