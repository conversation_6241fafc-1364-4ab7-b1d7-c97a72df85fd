import { Input } from "@/components/ui/input";
import { SelectNative } from "@/components/ui/select-native";
import { CONSTANTS } from "@/constants";
import { tanstackApi } from "@/react-query/api";
import { addTimelineLog } from "@/redux/reports/action";
import { <PERSON><PERSON>, Tooltip } from "@mui/material";
import axios from "axios";
import { format } from "date-fns";
import { useEffect, useRef, useState } from "react";
import { BsImages } from "react-icons/bs";
import { RiLayout6Fill } from "react-icons/ri";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { useReactToPrint } from "react-to-print";
import { toast } from "sonner";

const tmArray = [
    {
        name: "Complex",
        type: "course_completion",
        orientation: "landscape",
        thumbnail: "/assets/certificates/template-2.png",
        data: `<div
    id="MainTemplate"
    className="landscape_design"
    style="
        background-image: url(<%=background_url%>);
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
    "
>
    <div className="template" style="margin: 0 auto; padding: 0px; scale:0.7;">
        <div style="text-align: center; margin-top: 20px; display: flex; justify-content: center">
            <img src="<%=logo%>" style="max-width: 150px" id="LOGO" />
        </div>
        <h1 style="font-size: 32px; text-align: center; margin-bottom: 20px">Certificate of Course Completion</h1>
        <p style="font-size: 20px; margin: 10px">
            This certifies that
            <strong id="STUDENT_NAME"> <%=student_name%> </strong> has successfully completed the course
            <strong id="COURSE_TITLE"> <%=course_title%> </strong>.
        </p>
        <p style="font-size: 18px; margin: 3px; text-align: center" id="mentor_name">
            This course has been completed under the guidance of
            <strong id="MENTOR_NAME"> <%=mentor_name%> </strong>.
        </p>
        <p style="font-size: 18px; margin: 3px; text-align: center" id="completion_date">
            The course commenced on
            <strong id="START_DATE"> <%=start_date%> </strong> and concluded on
            <strong id="COMPLETION_DATE"> <%=completion_date%> </strong>
        </p>

        <div style="margin-top: 15px">
            <div style="display: flex; align-items: center">
                <div>
                    <img
                        id="PICTURE_URL"
                        src="<%=picture_url%>"
                        alt="picture_url"
                        style="width: 120px; height: 120px; margin-right: 40px"
                    />
                </div>
                <div>
                    <p style="font-size: 17px; margin: 0px 10px">
                        Certificate ID:
                        <strong id="CERTIFICATE_ID"> <%=certificate_id%> </strong>
                    </p>
                    <p style="font-size: 17px; margin: 0px 10px">
                        Issue Date:
                        <strong id="CREATED_DATE"> <%=created_date%> </strong>
                    </p>
                    <p style="font-size: 17px; margin: 0px 10px" id="certificate_validity">
                        Expiry Date:
                        <strong id="EXPIRY_DATE"> <%=expiry_date%> </strong>
                    </p>
                </div>
            </div>

            <p style="font-size: 18px; margin: 10px">
                Congratulations on completing the course successfully and we wish you all the best for your future
                endeavors.
            </p>
        </div>
        <div style="margin-top: 10px; text-align: right">
            <div style="text-align: right; display: flex; justify-content: end">
                <img id="SIGNATURE" src="<%=sign%>" alt="signature" style="max-width: 120px" />
            </div>
            <p style="font-size: 18px; margin-bottom: 5px" id="admin_name">
                <strong id="ADMIN_NAME"><%=admin_name%></strong>
            </p>
        </div>
    </div>
</div>
`,
    },
    {
        name: "Complex",
        type: "course_completion",
        orientation: "landscape",
        thumbnail: "/assets/certificates/template-1.png",
        data: `<div
      id="MainTemplate"
      className="landscape_design"
      style="
        background-image: url('<%=background_url%>');
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
      "
    >
      <div className="template" style="margin: 0 auto; padding: 0px; scale:0.7;">
        <div style="text-align: center; margin-top: 20px;display: flex;justify-content: center;">
          <img src="<%=logo%>" style="max-width: 150px" id="LOGO" />
        </div>
        <h1 style="font-size: 32px; text-align: center; margin-bottom: 20px">
          Certificate of Learning Path Completion
        </h1>
        <p style="font-size: 20px; margin: 10px">
          This certifies that
          <strong id="STUDENT_NAME"> <%=student_name%> </strong> has
          successfully completed the Learning Path
          <strong id="COURSE_TITLE"> <%=course_title%> </strong>.
        </p>
        <p
          style="font-size: 18px; margin: 3px; text-align: center"
          id="mentor_name"
        >
          This Learning Path has been completed under the guidance of
          <strong id="MENTOR_NAME"> <%=mentor_name%> </strong>.
        </p>
        <p
          style="font-size: 18px; margin: 3px; text-align: center"
          id="completion_date"
        >
          The Learning Path commenced on
          <strong id="START_DATE"> <%=start_date%> </strong> and concluded on
          <strong id="COMPLETION_DATE"> <%=completion_date%> </strong>
        </p>

        <div style="margin-top: 15px">
          <div style="display: flex; align-items: center">
            <div>
              <img
                id="PICTURE_URL"
                src="<%=picture_url%>"
                alt="picture_url"
                style="width: 120px; height: 120px; margin-right: 40px"
              />
            </div>
            <div>
              <p style="font-size: 17px; margin: 0px 10px">
                Certificate ID:
                <strong id="CERTIFICATE_ID"> <%=certificate_id%> </strong>
              </p>
              <p style="font-size: 17px; margin: 0px 10px">
                Issue Date:
                <strong id="CREATED_DATE"> <%=created_date%> </strong>
              </p>
              <p
                style="font-size: 17px; margin: 0px 10px"
                id="certificate_validity"
              >
                Expiry Date:
                <strong id="EXPIRY_DATE"> <%=expiry_date%> </strong>
              </p>
            </div>
          </div>

          <p style="font-size: 18px; margin: 10px">
            Congratulations on completing the Learning Path successfully and we
            wish you all the best for your future endeavors.
          </p>
        </div>
        <div style="margin-top: 10px; text-align: right">
          <div style="text-align: right;display: flex;justify-content: end;">
            <img
              id="SIGNATURE"
              src="<%=sign%>"
              alt="signature"
              style="max-width: 120px"
            />
          </div>
          <p style="font-size: 18px; margin-bottom: 5px" id="admin_name">
            <strong id="ADMIN_NAME"><%=admin_name%></strong>
          </p>
        </div>
      </div>
    </div>`,
    },
];

const bgArray = [
    {
        name: "One",
        type: "course_completion",
        orientation: "landscape",
        thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249732_certificate_background_633874_85__1_.jpg",
        data: "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249732_certificate_background_633874_85__1_.jpg",
    },
    {
        name: "Two",
        type: "course_completion",
        orientation: "landscape",
        thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249770_tp204_background_10.webp",
        data: "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249770_tp204_background_10.webp",
    },
    {
        name: "Three",
        type: "course_completion",
        orientation: "landscape",
        thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249801_pngtree_geometric_certificate_background_picture_image_121517.jpg",
        data: "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249801_pngtree_geometric_certificate_background_picture_image_121517.jpg",
    },
    {
        name: "six",
        type: "course_completion",
        orientation: "landscape",
        thumbnail:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249853_pngtree_elegant_blue_gold_certificate_border_design_png_image_6015462.png",
        data: "https://s3.ap-south-1.amazonaws.com/infowarelms/university/LMS0831/certificates/1727249853_pngtree_elegant_blue_gold_certificate_border_design_png_image_6015462.png",
    },
];

const CertificateForm = () => {
    const navigate = useNavigate();
    const params = useParams();
    const dispatch = useDispatch();
    const [getLastCerti, setGetLastCerti] = useState([]);
    const AlertInfo = useSelector((state) => state.alertReducer)?.alertInfoObj;

    const [menu, setMenu] = useState("Background");
    const [content, setContent] = useState([]);
    const [orientation, setOrientation] = useState("Landscape");

    const [template, setTemplate] = useState("");
    const [background, setBackgorund] = useState("");
    const [logo, setLogo] = useState("");
    const [sign, setSign] = useState("");
    const [finalTemplate, setFinalTemplate] = useState("");
    const [customMedia, setCustomMedia] = useState("Company Logo");
    const [certiType, setCertiType] = useState("course-completion");

    const [certificate, setCertificate] = useState({
        student_name: "John Doe",
        certificate_main_text: "",
        admin_name: "Company Pvt Ltd.",
        logo_url: "",
        picture_url: "",
        background_url: "",
        course_title: "Express JS Mastery",
        total_marks: "500",
        marks: "350",
        mentor_name: "Edward Shaw",
        marks_percentile: "75",
        completion_date: "11/09/2023",
        start_date: "11/09/2023",
        certificate_id: "EXP4514JS",
        expiry_date: "",
        created_date: "",
        title: "",
        height: "799.748px",
        width: "1133.858px",
    });

    useEffect(() => {
        if (params?.certificate_type) {
            setCertiType(params?.certificate_type);
        }
    }, [params]);

    useEffect(() => {
        if (certiType !== "") {
            getCertificates(certiType);
        }
    }, [certiType]);

    const getCertificates = async (type) => {
        await tanstackApi
            .get(`certificate/get-certificates/${type}`)
            .then((res) => {
                setGetLastCerti(res?.data?.data?.certificateList?.reverse());
            })
            .catch((err) => {
                setGetLastCerti([]);
            });
    };

    const onHandleChange = (e) => {
        const { value, name } = e.target;
        setCertificate({ ...certificate, [name]: value });
    };

    useEffect(() => {
        if (params?.certificate_id !== undefined && getLastCerti !== undefined && getLastCerti?.length > 0) {
            var certificateDATA = getLastCerti?.filter((data) => data?.id === parseInt(params?.certificate_id))?.[0];
            var data = {
                student_name: certificateDATA?.preview_strings?.student_name,
                picture_url: certificateDATA?.preview_strings?.picture_url,
                certificate_main_text: "",
                admin_name: certificateDATA?.preview_strings?.admin_name,
                logo_url: logo,
                background_url: certificateDATA?.preview_strings?.background_url,
                course_title: certificateDATA?.preview_strings?.course_title,
                total_marks: certificateDATA?.preview_strings?.total_marks,
                marks: certificateDATA?.preview_strings?.marks,
                mentor_name: certificateDATA?.preview_strings?.mentor_name,
                marks_percentile: certificateDATA?.preview_strings?.marks_percentile,
                certificate_id: 20777,
                include_course_validity: certificateDATA?.preview_strings?.include_course_validity,
                include_logo_url: certificateDATA?.preview_strings?.include_logo_url,
                include_picture_url: certificateDATA?.preview_strings?.include_picture_url,
                include_marks: certificateDATA?.preview_strings?.include_marks,
                mentor_signature: "",
                admin_signature: "",
                start_date: certificateDATA?.preview_strings?.start_date,
                completion_date: certificateDATA?.preview_strings?.completion_date,
                expiry_date: format(certificateDATA?.preview_strings?.expiry_date, "PP"),
                created_date: format(certificateDATA?.preview_strings?.created_date, "PP"),
                is_system: certificateDATA?.is_system,
                title: certificateDATA?.title,
                height: certificateDATA?.height,
                width: certificateDATA?.width,
            };
            setCertificate(data);
            setBackgorund(certificateDATA?.background_image_url);
            setTemplate(
                certificateDATA?.certificate
                    ?.replaceAll(`width: 1133.858px`, `width: 768px`)
                    ?.replaceAll(`height: 799.748px`, `height: 538px`)
                    ?.replaceAll(`scale:1`, `scale:0.7`),
            );
            setLogo(certificateDATA?.preview_strings?.logo_url);
            setSign(certificateDATA?.preview_strings?.admin_signature);
            setCertiType(certificateDATA?.background_type);
        }
    }, [params, getLastCerti]);

    const CertificateRef = useRef();
    const templPayload = useRef();

    useEffect(() => {
        if (menu === "Background") {
            setContent(bgArray);
        } else {
            setContent(tmArray);
        }
    }, [menu]);

    const onMediaChange = (e) => {
        if (e.target.files[0]) {
            const formData = new FormData();
            var file = e.target.files[0];

            formData.append("file", file);
            formData.append("category", "CERTIFICATES");

            ImageUpload(formData);
        }
    };

    const ImageUpload = (data) => {
        axios
            .post(`${CONSTANTS.getAPI()}common/upload-file`, data, {
                headers: {
                    "Content-Type": "multipart/form-data",
                    Authorization: `Bearer ${
                        localStorage.getItem("auth_signup_token") || localStorage.getItem("login_token")
                    }`,
                },
            })
            .then((res) => {
                if (res.data.success) {
                    if (customMedia === "Company Logo") {
                        setLogo(res?.data?.fileUrl);
                    } else if (customMedia === "Signature") {
                        setSign(res?.data?.fileUrl);
                    } else if (customMedia === "Background") {
                        setBackgorund(res?.data?.fileUrl);
                    }
                }
            })
            .catch((err) => {});
    };

    const [isStudentName, setIsStudentName] = useState(true);
    const [mentorName, setMentorName] = useState(true);
    const [adminName, setAdminName] = useState(true);
    const [startDate, setStartDate] = useState(true);
    const [completionDate, setCompletionDate] = useState(true);
    const [certificateValidity, setCertificateValidity] = useState(true);
    const [marks, setMarks] = useState(true);
    const [marksPercentile, setMarksPercentile] = useState(true);
    const [profileUrl, setProfileUrl] = useState(true);

    useEffect(() => {
        if (profileUrl && document.getElementById("PICTURE_URL") !== null) {
            document.getElementById("PICTURE_URL").style.display = "block";
        } else if (document.getElementById("PICTURE_URL") !== null) {
            document.getElementById("PICTURE_URL").style.display = "none";
        }

        if (isStudentName && document.getElementById("student_name") !== null) {
            document.getElementById("student_name").style.display = "block";
        } else if (document.getElementById("student_name") !== null) {
            document.getElementById("student_name").style.display = "none";
        }

        if (mentorName && document.getElementById("mentor_name") !== null) {
            document.getElementById("mentor_name").style.display = "block";
        } else if (document.getElementById("mentor_name") !== null) {
            document.getElementById("mentor_name").style.display = "none";
        }

        if (adminName && document.getElementById("admin_name") !== null) {
            document.getElementById("admin_name").style.display = "block";
        } else if (document.getElementById("admin_name") !== null) {
            document.getElementById("admin_name").style.display = "none";
        }

        if (startDate && document.getElementById("start_date") !== null) {
            document.getElementById("start_date").style.display = "block";
        } else if (document.getElementById("start_date") !== null) {
            document.getElementById("start_date").style.display = "none";
        }

        if (completionDate && document.getElementById("completion_date") !== null) {
            document.getElementById("completion_date").style.display = "block";
        } else if (document.getElementById("completion_date") !== null) {
            document.getElementById("completion_date").style.display = "none";
        }

        if (certificateValidity && document.getElementById("certificate_validity") !== null) {
            document.getElementById("certificate_validity").style.display = "block";
        } else if (document.getElementById("certificate_validity") !== null) {
            document.getElementById("certificate_validity").style.display = "none";
        }
    }, [isStudentName, mentorName, adminName, startDate, completionDate, certificateValidity, profileUrl]);

    useEffect(() => {
        if (template) {
            var data = template
                .replaceAll(`<%=student_name%>`, certificate.student_name)
                .replaceAll(`<%=mentor_name%>`, certificate.mentor_name)
                .replaceAll(`<%=course_title%>`, certificate.course_title)
                .replaceAll(`<%=marks_percentile%>`, certificate.marks_percentile)
                .replaceAll(`<%=start_date%>`, certificate.start_date)
                .replaceAll(`<%=completion_date%>`, certificate.completion_date)
                .replaceAll(`<%=admin_name%>`, certificate.admin_name)
                .replaceAll(`<%=certificate_id%>`, certificate.certificate_id)
                .replaceAll(`<%=created_date%>`, certificate.created_date)
                .replaceAll(`<%=expiry_date%>`, certificate.expiry_date)
                .replaceAll(`<%=picture_url%>`, certificate.picture_url)
                .replaceAll(`<%=logo%>`, logo)
                .replaceAll(`<%=sign%>`, sign)
                .replaceAll(`<%=background_url%>`, background);

            setFinalTemplate(`${data}`);
        }
    }, [
        template,
        certificate.student_name,
        certificate.mentor_name,
        certificate.course_title,
        certificate.start_date,
        certificate.completion_date,
        certificate.certificate_id,
        certificate.created_date,
        certificate.expiry_date,
        certificate.admin_name,
        certificate.picture_url,
        background,
    ]);

    const handlePrint = useReactToPrint({
        content: () => document.getElementById("MainTemplate"),
        documentTitle: `Sample Certificate`,
    });

    const reverseData = () => {
        // document.getElementById("MainTemplate").style.backgroundImage = `url('<%=background_url%>')`;
        // document.getElementById("PICTURE_URL").src = "<%=picture_url%>";
        document.getElementById("STUDENT_NAME").innerHTML = "<%=student_name%>";
        document.getElementById("COURSE_TITLE").innerHTML = "<%=course_title%>";
        document.getElementById("MENTOR_NAME").innerHTML = "<%=mentor_name%>";
        document.getElementById("START_DATE").innerHTML = "<%=start_date%>";
        document.getElementById("COMPLETION_DATE").innerHTML = "<%=completion_date%>";
        document.getElementById("CERTIFICATE_ID").innerHTML = "<%=certificate_id%>";
        document.getElementById("CREATED_DATE").innerHTML = "<%=created_date%>";
        document.getElementById("EXPIRY_DATE").innerHTML = "<%=expiry_date%>";
        document.getElementById("ADMIN_NAME").innerHTML = "<%=admin_name%>";
        // document.getElementById("LOGO").src = "<%=logo%>";
        // document.getElementById("SIGNATURE").src = "<%=signature%>";
    };

    useEffect(() => {
        if (finalTemplate !== "") {
            // if (backgorund && document.getElementById("MainTemplate") !== null) {
            //   document.getElementById(
            //     "MainTemplate"
            //   ).style.backgroundImage = `url(${backgorund})`;
            // }

            if (logo && document.getElementById("LOGO") !== null) {
                document.getElementById("LOGO").src = logo;
            }

            if (sign && document.getElementById("SIGNATURE") !== null) {
                document.getElementById("SIGNATURE").src = sign;
            }
        }
    }, [background, logo, sign, finalTemplate]);

    const onTemplateSave = async (e) => {
        if (template == "") {
            return toast.error("Please select certificate template");
        } else if (background == "") {
            return toast.error("Please select certificate background");
        } else if (certificate?.expiry_date == "") {
            return toast.error("Please define certificate validity");
        } else if (certificate?.title == "") {
            return toast.error("Please enter certificate title");
        } else if (certificate?.height == "") {
            return toast.error("Please enter certificate height");
        } else if (certificate?.width == "") {
            return toast.error("Please enter certificate width");
        } else if (logo == "") {
            return toast.error("Please Add Organisation Logo");
        } else if (sign == "") {
            return toast.error("Please Add Authorised person's signature");
        }

        reverseData();

        var MyNode = document.getElementById("MainTemplate");
        var MyNodeHTML = MyNode.outerHTML
            .replaceAll("&lt;", "<")
            .replaceAll("&gt;", ">")
            .replaceAll("&quot;", '"')
            .replaceAll(`src="${certificate.picture_url}"`, `src="<%=picture_url%>"`)
            .replaceAll(`background-image: url(${background})`, `background-image: url(<%=background_url%>)`)
            .replaceAll(`scale:0.7`, `scale:1`)
            .replaceAll(`width: 768px`, `width: 1133.858px`)
            .replaceAll(`height: 538px`, `height: 799.748px`);

        if (!params?.certificate_id) {
            const payload = {
                background_type: certiType,
                is_system: localStorage.getItem("level") == "levelOne",
                // certificate: template.replaceAll("&lt;", "<").replaceAll("&gt;", ">").replaceAll("&quot;", '"').replaceAll("<%=logo%>", logo)?.replaceAll("scale: 0.7","scale: 1"),
                certificate: MyNodeHTML,
                background_image_url: background,
                certificate_orientation: "landscape",
                certificate_main_text: "",
                title: certificate.title,
                width: certificate.width,
                height: certificate.height,
                preview_strings: {
                    student_name: certificate?.student_name,
                    picture_url: "",
                    certificate_main_text: "",
                    admin_name: certificate?.admin_name,
                    logo_url: logo != "" ? logo : false,
                    background_url: background,
                    course_title: certificate?.course_title,
                    total_marks: certificate?.total_marks,
                    marks: certificate?.marks,
                    mentor_name: certificate?.mentor_name,
                    marks_percentile: certificate?.marks_percentile,
                    certificate_id: 987465,
                    include_course_validity: true,
                    include_logo_url: true,
                    include_picture_url: certificate?.include_picture_url,
                    include_marks: true,
                    mentor_signature: "",
                    admin_signature: sign,
                    start_date: certificate?.start_date,
                    completion_date: certificate?.completion_date,
                    expiry_date: certificate?.expiry_date,
                    created_date: new Date(),
                    sign: logo || sign || background ? sign : "",
                },
            };

            await tanstackApi
                .post(`certificate/creation/add-certificate`, { ...payload })
                .then((res) => {
                    dispatch(
                        addTimelineLog({
                            user_id: localStorage.getItem("userId"),
                            event: "certificate",
                            log: `${certificate.payload?.title} created successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
                        }),
                    );

                    toast.success("Created Successfully", {
                        description: res?.data?.message,
                    });
                    navigate(`/dashboard/certificate`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                id: parseInt(params?.certificate_id),
                background_type: certiType,
                // is_system: localStorage.getItem("level") == "levelOne",
                certificate: template.replaceAll("&lt;", "<").replaceAll("&gt;", ">").replaceAll("<%=logo%>", ""),
                background_image_url: background,
                certificate_orientation: "landscape",
                certificate_main_text: "",
                title: certificate.title,
                width: certificate.width,
                height: certificate.height,
                preview_strings: {
                    student_name: certificate?.student_name,
                    picture_url: "",
                    certificate_main_text: "",
                    admin_name: certificate?.admin_name,
                    logo_url: logo != "" ? logo : false,
                    background_url: background,
                    course_title: certificate?.course_title,
                    total_marks: certificate?.total_marks,
                    marks: certificate?.marks,
                    mentor_name: certificate?.mentor_name,
                    marks_percentile: certificate?.marks_percentile,
                    certificate_id: 20777,
                    include_course_validity: true,
                    include_logo_url: true,
                    include_picture_url: certificate?.include_picture_url,
                    include_marks: true,
                    mentor_signature: "",
                    admin_signature: sign,
                    start_date: certificate?.start_date,
                    completion_date: certificate?.completion_date,
                    expiry_date: certificate?.expiry_date,
                    created_date: certificate?.created_date,
                    sign: logo || sign || background ? sign : "",
                },
            };

            await tanstackApi
                .put(`certificate/creation/update-certificate`, { ...payload })
                .then((res) => {
                    dispatch(
                        addTimelineLog({
                            user_id: localStorage.getItem("userId"),
                            event: "certificate",
                            log: `${certificate.payload?.title} updated successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
                        }),
                    );

                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    navigate(`/dashboard/certificate`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <>
            <div className="certificate_module">
                <div className="tw-flex tw-justify-between tw-gap-4">
                    <div className="tw-flex tw-w-full tw-flex-grow tw-flex-wrap tw-gap-4">
                        <div>
                            <label htmlFor="">
                                Certificate Validity <b className="imp_required">*</b>
                            </label>
                            <Input
                                onChange={onHandleChange}
                                name="expiry_date"
                                value={certificate.expiry_date}
                                type="date"
                            />
                        </div>
                        <div>
                            <label htmlFor="">
                                Certificate Layout <b className="imp_required">*</b>
                            </label>
                            <SelectNative name="" id="" onChange={(e) => setOrientation(e.target.value)}>
                                <option value="Landscape">Landscape</option>
                                {/* <option value="Portrait">Portrait</option> */}
                            </SelectNative>
                        </div>
                        <div>
                            <label htmlFor="">
                                Title <b className="imp_required">*</b>
                            </label>
                            <Input
                                onChange={onHandleChange}
                                name="title"
                                value={certificate.title}
                                type="text"
                                placeholder="Certificate Title"
                            />
                        </div>

                        <div>
                            <label htmlFor="">
                                Height <b className="imp_required">*</b>
                            </label>
                            <Input
                                onChange={onHandleChange}
                                name="height"
                                value={certificate.height}
                                type="text"
                                placeholder="Height"
                                disabled
                            />
                        </div>
                        <div>
                            <label htmlFor="">
                                Width <b className="imp_required">*</b>
                            </label>
                            <Input
                                onChange={onHandleChange}
                                name="width"
                                value={certificate.width}
                                type="text"
                                placeholder="Width"
                                disabled
                            />
                        </div>

                        <div>
                            <label htmlFor="">
                                Custome Media <b className="imp_required">*</b>
                            </label>
                            <SelectNative
                                value={customMedia}
                                onChange={(e) => setCustomMedia(e.target.value)}
                                name=""
                                id=""
                            >
                                <option value="Company Logo">Logo *</option>
                                <option value="Signature">Signature *</option>
                                <option value="Background">Background</option>
                            </SelectNative>
                        </div>
                        <div>
                            <label htmlFor="">{customMedia} File</label>
                            <Input key={customMedia} onChange={onMediaChange} type="file" accept="image/*" />
                        </div>
                    </div>
                    <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
                        <Button variant="contained" size="small" href="/dashboard/certificate">
                            Back
                        </Button>
                        <div className="tw-flex tw-items-end tw-justify-center">
                            <button className="btn_1 cursor_pointer" onClick={onTemplateSave}>
                                {params?.certificate_id ? "Update" : "Save"}
                            </button>
                        </div>
                    </div>
                </div>
                <div className="certificate_contianer">
                    <div className="templetes">
                        <div className="menu">
                            <Tooltip title="Template">
                                <RiLayout6Fill onClick={() => setMenu("Template")} />
                            </Tooltip>
                            <Tooltip title="Backgrounds">
                                <BsImages onClick={() => setMenu("Background")} />
                            </Tooltip>
                        </div>
                        <div className="subMenu">
                            <p>
                                {menu} <b className="imp_required">*</b>
                            </p>
                            <div className="content_list">
                                {content.map((cnt, idx) => (
                                    <div
                                        className="content_card"
                                        key={idx}
                                        onClick={() =>
                                            menu === "Background" ? setBackgorund(cnt.data) : setTemplate(cnt.data)
                                        }
                                    >
                                        <img src={cnt.thumbnail} alt="" />
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    <div className="finalDesign">
                        <div dangerouslySetInnerHTML={{ __html: finalTemplate }}></div>
                    </div>

                    {/* <div className="inputAreas">
                        <p>Context</p>
                        <div className="controls">
                            <input
                                onChange={() => setProfileUrl(!profileUrl)}
                                checked={profileUrl}
                                type="checkbox"
                                name=""
                                id=""
                            />
                            <div>
                                <label htmlFor="">Profile URL</label>
                                <input
                                    name="picture_url"
                                    value={certificate.picture_url}
                                    onChange={onHandleChange}
                                    type="text"
                                    placeholder="profile url"
                                />
                            </div>
                        </div>
                        <div className="controls">
                            <input
                                onChange={() => setIsStudentName(!isStudentName)}
                                checked={isStudentName}
                                type="checkbox"
                                name=""
                                id=""
                            />
                            <div>
                                <label htmlFor="">Student Name</label>
                                <input
                                    name="student_name"
                                    value={certificate.student_name}
                                    onChange={onHandleChange}
                                    type="text"
                                    placeholder="Student Name"
                                />
                            </div>
                        </div>
                        <div className="controls">
                            <input
                                onChange={() => setMentorName(!mentorName)}
                                checked={mentorName}
                                type="checkbox"
                                name=""
                                id=""
                            />
                            <div>
                                <label htmlFor="">Mentor Name</label>
                                <input
                                    name="mentor_name"
                                    value={certificate.mentor_name}
                                    onChange={onHandleChange}
                                    type="text"
                                    placeholder="Mentor Name"
                                />
                            </div>
                        </div>
                        <div className="controls">
                            <input
                                onChange={() => setAdminName(!adminName)}
                                checked={adminName}
                                type="checkbox"
                                name=""
                                id=""
                            />
                            <div>
                                <label htmlFor="">Admin Name</label>
                                <input
                                    name="admin_name"
                                    value={certificate.admin_name}
                                    onChange={onHandleChange}
                                    type="text"
                                    placeholder="Admin Name"
                                />
                            </div>
                        </div>
                        <div className="controls">
                            <input
                                onChange={() => setStartDate(!startDate)}
                                checked={false}
                                type="checkbox"
                                name=""
                                id=""
                            />
                            <div>
                                <label htmlFor="">Start Date</label>
                                <input
                                    name="start_date"
                                    value={certificate.start_date}
                                    onChange={onHandleChange}
                                    type="date"
                                    placeholder="Start Date"
                                />
                            </div>
                        </div>
                        <div className="controls">
                            <input
                                onChange={() => setCompletionDate(!completionDate)}
                                checked={completionDate}
                                type="checkbox"
                                name=""
                                id=""
                            />
                            <div>
                                <label htmlFor="">Completion Date</label>
                                <input
                                    name="completion_date"
                                    value={certificate.completion_date}
                                    onChange={onHandleChange}
                                    type="date"
                                    placeholder="Completion Date"
                                />
                            </div>
                        </div>
                        <div className="controls">
                            <input type="checkbox" name="" id="" checked={false} />
                            <div>
                                <label htmlFor="">Course Title</label>
                                <input
                                    name="course_title"
                                    value={certificate.course_title}
                                    onChange={onHandleChange}
                                    type="text"
                                    placeholder="Course Title"
                                />
                            </div>
                        </div>

                        <div className="controls">
                            <input
                                onChange={() => setCertificateValidity(!certificateValidity)}
                                checked={certificateValidity}
                                type="checkbox"
                                name=""
                                id=""
                            />
                            <div>
                                <label htmlFor="">Certificate Validity</label>
                                <input
                                    name="expiry_date"
                                    value={certificate.expiry_date}
                                    onChange={onHandleChange}
                                    type="date"
                                    placeholder="Certificate Validity"
                                />
                            </div>
                        </div>
                    </div> */}
                </div>
            </div>
        </>
    );
};

export default CertificateForm;
