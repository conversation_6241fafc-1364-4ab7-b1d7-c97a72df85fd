import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FileSliders } from "lucide-react";
import { useEffect, useState } from "react";
import DragDropSettings from "./settings/DragDropSettings";
import FeedbackSettings from "./settings/FeedbackSettings";
import GeneralSettings from "./settings/GeneralSettings";
import MediaSettings from "./settings/MediaSettings";
import OptionsSettings from "./settings/OptionsSettings";

let content_mapping = {
    singlechoice: "Options",
    singlechoice_media: "Options",
    fillin_the_blank: "Options",
    dnd_image_box: "Drag & Drops",
    sequence_arrange: "Options",
    string_dropdown: "Options",
    match_the_following: "Options",
    hotspot_dnd: "Drag & Drops",
    hotspot_dnd_image: "Drag & Drops",
    long_answer: "General",
};

const label_mapping = {
    singlechoice: "Singlechoice",
    singlechoice_media: "Singlechoice media",
    fillin_the_blank: "Fill in the blanks",
    dnd_image_box: "Drag drop media",
    sequence_arrange: "Sequence arrange",
    string_dropdown: "String dropdown",
    match_the_following: "Match the following",
    hotspot_dnd: "Hotspot drag & drop",
    hotspot_dnd_image: "Hotspot drag & drop media",
    long_answer: "Long answer",
};

const QuestionSettings = ({ open, setOpen, contentData, setContentData }) => {
    const [defaultSlide, setDefaultSlide] = useState(content_mapping[contentData?.componentTypeId]);

    useEffect(() => {
        setDefaultSlide(content_mapping[contentData?.componentTypeId]);
    }, [contentData?.componentTypeId]);

    const onSaveChanges = (e) => {
        e.preventDefault();
        // let options = [...componentsArray];
        // options[INDEX] = contentData;
        // setComponentsArray(options);
        setOpen(false);
    };

    return (
        <>
            <Sheet open={open} onOpenChange={setOpen}>
                <SheetContent className="sm:!tw-max-w-2xl">
                    <SheetHeader>
                        <SheetTitle className="tw-flex tw-items-center tw-gap-3">
                            <FileSliders /> <p>Question Settings</p> -{" "}
                            <span className="tw-text-[16px] tw-text-slate-500">
                                {label_mapping?.[contentData?.componentTypeId]}
                            </span>{" "}
                        </SheetTitle>
                    </SheetHeader>
                    <br />
                    <div className="tw-flex tw-h-[93%] tw-flex-col tw-justify-between">
                        <Tabs defaultValue={"General"} className="" onValueChange={(e) => setDefaultSlide(e)}>
                            <TabsList className="tw-grid tw-w-full tw-grid-cols-5">
                                <TabsTrigger className="tw-gap-2" value="General">
                                    <i className="fa-solid fa-circle-info"></i> General
                                </TabsTrigger>
                                <TabsTrigger className="tw-gap-2" value="Options">
                                    <i className="fa-solid fa-list-check"></i> Options
                                </TabsTrigger>
                                <TabsTrigger className="tw-gap-2" value="Drag & Drops">
                                    <i className="fa-solid fa-grip-vertical"></i> Drag & Drops
                                </TabsTrigger>
                                <TabsTrigger className="tw-gap-2" value="Media">
                                    <i className="fa-solid fa-images"></i> Media
                                </TabsTrigger>
                                <TabsTrigger className="tw-gap-2" value="Feedbacks">
                                    <i className="fa-regular fa-comment-dots"></i> Feedbacks
                                </TabsTrigger>
                            </TabsList>
                            <TabsContent value="General">
                                <GeneralSettings setContentData={setContentData} contentData={contentData} />
                            </TabsContent>
                            <TabsContent value="Options">
                                <OptionsSettings setContentData={setContentData} contentData={contentData} />
                            </TabsContent>
                            <TabsContent value="Drag & Drops">
                                <DragDropSettings setContentData={setContentData} contentData={contentData} />
                            </TabsContent>
                            <TabsContent value="Media">
                                <MediaSettings setContentData={setContentData} contentData={contentData} />
                            </TabsContent>
                            <TabsContent value="Feedbacks">
                                <FeedbackSettings setContentData={setContentData} contentData={contentData} />
                            </TabsContent>
                        </Tabs>
                        <SheetFooter>
                            <Button variant="outline" onClick={() => setOpen(false)}>
                                <i className="fa-solid fa-xmark"></i> Cancel
                            </Button>
                            <Button type="submit" onClick={onSaveChanges}>
                                <i className="fa-solid fa-save"></i> Save changes
                            </Button>
                        </SheetFooter>
                    </div>
                </SheetContent>
            </Sheet>
        </>
    );
};

export default QuestionSettings;
