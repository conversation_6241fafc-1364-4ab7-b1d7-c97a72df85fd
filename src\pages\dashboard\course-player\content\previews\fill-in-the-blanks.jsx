import { useEffect, useRef, useState } from "react";

export default function FillInTheBlank({ data, index, onComponentAnswer, correctAns }) {
    const [blanks, setBlanks] = useState([]);
    const [quizData, setQuizData] = useState([]);

    useEffect(() => {
        if (data?.fillOptions) {
            setBlanks(Array(data?.fillOptions?.length).fill(""));
            setQuizData(replacePlaceholders(data?.question, data?.fillOptions));
        }
    }, [data]);

    useEffect(() => {
        let ans = data.fillOptions.filter(
            (dt, idx) => Number(dt?.correctIndex) === idx + 1 && blanks[idx + 1] === dt.label,
        );
        let ppq = data?.points / data?.fillOptions?.length;
        let points = Math.round(ans?.length * ppq);

        onComponentAnswer(index, ans, points);
    }, [blanks]);

    const handleDragStart = (event, option) => {
        event.dataTransfer.setData("text", JSON.stringify(option));
    };

    const handleDrop = (event, correctIndex) => {
        event.preventDefault();
        const option = JSON.parse(event.dataTransfer.getData("text"));
        const exists = blanks.findIndex((arr) => arr === option.label);

        const newBlanks = [...blanks];
        if (exists) {
            newBlanks[exists] = "";
        }
        newBlanks[correctIndex] = option.label;
        setBlanks(newBlanks);
    };

    const handleDragOver = (event) => {
        event.preventDefault();
    };

    return (
        <div className="comp_control">
            <div className="fill_in_the_blank">
                <div>
                    <label
                        htmlFor=""
                        style={{
                            fontSize: "1.5vw",
                            fontWeight: "600",
                            letterSpacing: "-0.005em",
                            width: "100%",
                            textAlign: "center",
                            fontFamily: "Lexend, sans-serif",
                        }}
                    >
                        {data?.name}
                    </label>
                </div>
                <div className="fillable_options">
                    {data.fillOptions?.map((word, idx) => (
                        <p
                            key={word.label}
                            draggable
                            onDragStart={(e) => handleDragStart(e, word)}
                            style={{
                                padding: "8px 20px",
                                border: "1px solid black",
                                borderRadius: "5px",
                                backgroundColor: "#333",
                                fontSize: "1vw",
                                color: "#fff",
                                cursor: "grab",
                            }}
                        >
                            {word?.label}
                        </p>
                    ))}
                </div>
                <label htmlFor="">
                    {quizData?.map((item, idx) => {
                        return typeof item === "string" ? (
                            <span key={idx}>{item}</span>
                        ) : (
                            <DropContainer
                                key={idx}
                                handleDrop={handleDrop}
                                current={item.correctIndex}
                                handleDragOver={handleDragOver}
                                item={item}
                                blanks={blanks}
                            />
                        );
                    }) || "Component title here"}{" "}
                    <b>{data?.preference === "required" && "*"}</b>
                </label>
            </div>
            {data?.specialInstruction && (
                <p className="special_instruction">
                    <i className="fa-solid fa-circle-info"></i>
                    <small>{data?.specialInstruction}</small>
                </p>
            )}
        </div>
    );
}

function DropContainer({ handleDrop, handleDragOver, item, blanks, current }) {
    const ref = useRef(null);

    return (
        <span
            ref={ref}
            onDrop={(e) => {
                handleDrop(e, item.correctIndex);
                ref.current.classList.remove("drop_active");
                ref.current.classList.add("dropped");
            }}
            onDragOver={(e) => {
                ref.current.classList.add("drop_active");
                handleDragOver(e);
            }}
            onDragLeave={() => {
                ref.current.classList.remove("drop_active");
            }}
            className={`drop_container ${!isEmpty(blanks[current]) ? "dropped" : ""}`}
            style={{
                minWidth: "80px",
                display: "inline-block",
                marginRight: "4px",
            }}
        >
            {blanks[Number(item.correctIndex)]}
        </span>
    );
}

function isEmpty(value) {
    if (value === null || value === undefined || value === "") {
        return true;
    }
    return false;
}

function replacePlaceholders(sentence, matchOptions) {
    matchOptions.sort((a, b) => parseInt(a.correctIndex) - parseInt(b.correctIndex));
    let filledSentence = sentence.split("[field]");
    matchOptions.forEach((option, index) => {
        if (Number(option.correctIndex) === index + 1)
            filledSentence.splice(Number(option.correctIndex) + index, 0, option);
    });
    return filledSentence;
}
