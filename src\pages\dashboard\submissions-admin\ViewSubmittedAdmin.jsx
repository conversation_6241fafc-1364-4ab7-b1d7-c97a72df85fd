import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";

const ViewSubmittedAdmin = () => {
    const params = useParams();

    const [database, setDatabase] = useState(null);

    useEffect(() => {
        if (params?.id !== undefined) {
            getSubmissionsData(params?.id);
        }
    }, [params]);

    const getSubmissionsData = async (workID) => {
        await tanstackApi
            .post("homework/submissions/get-all-submissions", {})
            .then((res) => {
                setDatabase(res?.data?.data?.find((dt) => dt?.id == Number(workID)));
            })
            .catch((err) => {
                setDatabase(null);
            });
    };

    const onDownloadHomework = () => {
        const url = database?.attachment_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `Attachment File : ${database?.homework_title}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    const onDownloadSubmission = () => {
        const url = database?.submission_attachment;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `Submission File : ${database?.homework_title}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    const tab = params?.type == "Assignment" ? "Assignments" : "Homeworks";

    return (
        <div className="">
            <div className="pageHeader">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/submissions-admin">Submission by Learners</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Check Remarks of {params?.type}</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>

                <Link to={`/dashboard/submissions-admin?tab=${tab}`}>
                    <Button className="">
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                </Link>
            </div>

            <br />
            <div className="content_body_homework">
                <div className="homework_title">
                    <h1>
                        <i className="fa-solid fa-laptop-file"></i> {database?.lms_course_homework?.homework_title}
                    </h1>
                </div>
                <div className="homework_description">
                    <p>{database?.lms_course_homework?.description}</p>
                </div>
                <div className="homework_details">
                    <div>
                        <p>
                            <i className="fa-solid fa-dice-d6"></i> Points
                        </p>
                        <h4>{database?.lms_course_homework?.homework_points}</h4>
                    </div>
                    <div>
                        <p>
                            {" "}
                            <i className="fa-solid fa-clock"></i> Submission
                        </p>
                        <h4>{moment(database?.lms_course_homework?.submission_date).format("LLL")}</h4>
                    </div>
                    <div>
                        <p>
                            <i className="fa-solid fa-paperclip"></i> Attachments
                        </p>
                        <h4>
                            <button onClick={onDownloadHomework}>
                                <i className="fa-solid fa-download"></i> Download
                            </button>
                        </h4>
                    </div>
                </div>
            </div>
            <div className="subission_details">
                <div className="tw-text-right">
                    Submitted on <strong>{moment(database?.created_at).format("LLL")}</strong>
                </div>
                <h5>#. Submission by user :-</h5>
                <p>{database?.submission}</p>
                <br />
                <h5>#. Submission Attachment:-</h5>
                <button onClick={onDownloadSubmission}>
                    <i className="fa-solid fa-download"></i> Download Attached File
                </button>
                <br />
                <br />
                <h5>
                    #. Evaluation Status: <b>{database?.evaluation_status}</b>
                </h5>
            </div>
        </div>
    );
};

export default ViewSubmittedAdmin;
