import { FILE_UPLOADED_RESPONSE, UPLOAD_FILE_CONTAINER, UPLOAD_FORM_FILE_CONTAINER } from "@/redux-types";

const initialState = {
    isLoading: false,
    fileResponse: {},
};

const imageUploadReducer = (state = initialState, action) => {
    switch (action.type) {
        case UPLOAD_FORM_FILE_CONTAINER:
            return {
                ...state,
                isLoading: true,
                payload: action.payload,
            };
        case UPLOAD_FILE_CONTAINER:
            return {
                ...state,
                isLoading: true,
                imageURL: action.payload,
            };
        case FILE_UPLOADED_RESPONSE:
            return {
                fileResponse: action.payload,
                isLoading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};
export default imageUploadReducer;
