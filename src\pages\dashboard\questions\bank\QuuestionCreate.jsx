import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>rumb<PERSON><PERSON>,
    <PERSON>read<PERSON>rumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import DragDropMedia from "./components/DragDropMedia";
import FillinTheBlanks from "./components/FillinTheBlanks";
import HotspotDragDrop from "./components/HotspotDragDrop";
import HotspotDragDropMedia from "./components/HotspotDragDropMedia";
import LongAnswer from "./components/LongAnswer";
import MatchTheFollowing from "./components/MatchTheFollowing";
import SequenceArrange from "./components/SequenceArrange";
import Singlechoice from "./components/Singlechoice";
import SinglechoiceMedia from "./components/SinglechoiceMedia";
import StringDropdown from "./components/StringDropdown";
import QuestionSettings from "./QuestionSettings";

const difficultyLeverl = ["easy", "hard", "medium"];

const contentTypes = [
    {
        title: "Singlechoice",
        contentType: "singlechoice",
        icon: "/assets/singlechoice.png",
        data: {
            name: "Singlechoice question title here",
            type: "singlechoice",
            question: "",
            points: 100,
            options: [
                {
                    src: "",
                    label: "Option A",
                    isCorrect: false,
                },
                {
                    src: "",
                    label: "Option B",
                    isCorrect: false,
                },
                {
                    src: "",
                    label: "Option C",
                    isCorrect: true,
                },
                {
                    src: "",
                    label: "Option D",
                    isCorrect: false,
                },
            ],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have chosen the option",
                incorrectAns: "Chosen the wrong option",
                partiallyCorrectAns: "That's not exactly the correct option",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "singlechoice",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Choose the correct option out of below mentioned options",
            question_background: "",
        },
    },
    {
        title: "Singlechoice Media",
        contentType: "singlechoice_media",
        icon: "/assets/singlechoice_media.png",
        data: {
            name: "Singlechoice with images your question title here",
            type: "singlechoice_media",
            question: "",
            points: 100,
            options: [
                {
                    src: "/assets/thumbnail.png",
                    label: "image1",
                    isCorrect: true,
                },
                {
                    src: "/assets/thumbnail.png",
                    label: "image2",
                    isCorrect: false,
                },
                {
                    src: "/assets/thumbnail.png",
                    label: "image3",
                    isCorrect: false,
                },
            ],
            dropzone: [],
            dropdown_options: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have chosen the option",
                incorrectAns: "Chosen the wrong option",
                partiallyCorrectAns: "That's not exactly the correct option",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "singlechoice_media",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Choose the correct option out of below mentioned options",
            question_background: "",
        },
    },
    {
        title: "Fill in the blanks",
        contentType: "fillin_the_blank",
        icon: "/assets/fillin_the_blanks.png",
        data: {
            name: "Fill in the blanks question title here",
            type: "fillin_the_blank",
            question:
                "Lorem ipsum dolor sit amet [field] consectetur [field] adipiscing elit [field]. Aenean linkage, si habet duo vel plures rigid links quae moventur respectu ad rigid link, vocatur [field] point mechanismi ut moveat vel vim generet, vocatur [field].",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [
                {
                    label: "Option A",
                    correctIndex: 1,
                },
                {
                    label: "Option B",
                    correctIndex: 2,
                },
                {
                    label: "Option C",
                    correctIndex: 3,
                },
                {
                    label: "Option D",
                    correctIndex: 4,
                },
            ],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "fillin_the_blank",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Drage the below mentioned options into blank fields",
            question_background: "",
        },
    },
    {
        title: "Drag & Drop Image",
        contentType: "dnd_image_box",
        icon: "/assets/drag_drop_media.png",
        data: {
            name: "Drag & Drop Image question title here",
            type: "dnd_image_box",
            question: "Drag the image on correct placeholder make sure the label is correct for image.",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [
                {
                    id: "Zone One",
                    zone: "Zone One",
                },
                {
                    id: "Zone Two",
                    zone: "Zone Two",
                },
                {
                    id: "Zone Three",
                    zone: "Zone Three",
                },
            ],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "dnd_image_box",
            draggable_options: [
                {
                    id: "image1",
                    src: "",
                    zone: "Zone One",
                },
                {
                    id: "image2",
                    src: "",
                    zone: "Zone Two",
                },
                {
                    id: "image3",
                    src: "",
                    zone: "Zone Three",
                },
                {
                    id: "image4",
                    src: "",
                    zone: "",
                },
            ],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Drag the image on correct placeholder make sure the label is correct for image.",
            question_background: "",
        },
    },
    {
        title: "Sequence Arrange",
        contentType: "sequence_arrange",
        icon: "/assets/sequence_arrange.png",
        data: {
            name: "Sequence arrange question title here",
            type: "sequence_arrange",
            question:
                "Throughout history, people have been trying to reach the peak of Mt. Everest. Put these historical events in the correct order.",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 2,
                    label: "",
                    imageLabel: "",
                },
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 1,
                    label: "",
                    imageLabel: "",
                },
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 4,
                    label: "",
                    imageLabel: "",
                },
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 3,
                    label: "",
                    imageLabel: "",
                },
            ],
            optionColumns: "",
            componentTypeId: "sequence_arrange",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Arrange the options in the correct sequence mentioned below.",
            question_background: "",
        },
    },
    {
        title: "String dropdown",
        contentType: "string_dropdown",
        icon: "/assets/dropdown.png",
        data: {
            name: "String dropdown question title here",
            type: "string_dropdown",
            question:
                "Lorem ipsum dolor sit amet {0} area montis saepe censetur Khumbu {1}, quod praesertim periculosum est ob {2} motum glaciei.",
            points: 100,
            options: [],
            dropdown_options: [
                {
                    id: "label_0",
                    correct: "Option B",
                    options: ["Option A", "Option B", "Option C"],
                },
                {
                    id: "label_1",
                    correct: "Option B",
                    options: ["Option A", "Option B", "Option C"],
                },
                {
                    id: "label_2",
                    correct: "Option B",
                    options: ["Option A", "Option B", "Option C"],
                },
            ],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "string_dropdown",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
    {
        title: "Match the following",
        contentType: "match_the_following",
        icon: "/assets/match_the_following.png",
        data: {
            name: "Match the following question title here",
            type: "match_the_following",
            question: "Complete the gaps using the appropriate words from the box",
            points: 100,
            options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [
                {
                    id: "1",
                    text: "Option 1",
                    label: "Label A",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 2,
                },
                {
                    id: "2",
                    text: "Option 2",
                    label: "Label B",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 1,
                },
                {
                    id: "3",
                    text: "Option 3",
                    label: "Label C",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 3,
                },
                {
                    id: "4",
                    text: "Option 4",
                    label: "Label D",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 4,
                },
            ],
            optionColumns: "",
            componentTypeId: "match_the_following",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Complete the gaps using the appropriate words from the box.",
            question_background: "",
        },
    },
    {
        title: "Hotspot Drag & Drop",
        contentType: "hotspot_dnd",
        icon: "/assets/hotspot_dnd.png",
        data: {
            name: "Hotspot Drag & Drop question title here",
            type: "hotspot_dnd",
            question: "",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [
                {
                    x: 0.27666666666666667,
                    y: 0.1285714285714285,
                    src: "",
                    zone: "A",
                },
                {
                    x: 0.6016666666666666,
                    y: 0.11142857142857145,
                    src: "",
                    zone: "B",
                },
                {
                    x: 0.45333333333333314,
                    y: 0.4457142857142858,
                    src: "",
                    zone: "C",
                },
                {
                    x: 0.27999999999999997,
                    y: 0.7542857142857141,
                    src: "",
                    zone: "D",
                },
                {
                    x: 0.6266666666666666,
                    y: 0.7314285714285714,
                    src: "",
                    zone: "E",
                },
            ],
            hotspots: [
                {
                    x: 0,
                    y: 0.1831428571428571,
                    src: "",
                    name: "One",
                    correct: "A",
                },
                {
                    x: 0,
                    y: 0.5948571428571428,
                    src: "",
                    name: "Two",
                    correct: "C",
                },
                {
                    x: 0.12133333333333332,
                    y: 0.43800000000000006,
                    src: "",
                    name: "Three",
                    correct: "B",
                },
                {
                    x: 0.14333333333333334,
                    y: 0.18114285714285716,
                    src: "",
                    name: "Four",
                    correct: "D",
                },
                {
                    x: 0,
                    y: 0.3414285714285714,
                    src: "",
                    name: "Five",
                    correct: "E",
                },
            ],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "hotspot_dnd",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
    {
        title: "Hotspot Drag & Drop Media",
        contentType: "hotspot_dnd_image",
        icon: "/assets/hotspot_dnd_media.png",
        data: {
            name: "Hotspot Drag & Drop Media question title here",
            type: "hotspot_dnd_image",
            question: "",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [
                {
                    x: 0.6461111111111111,
                    y: 0.4037142857142857,
                    src: "",
                    zone: "A",
                },
                {
                    x: 0.6366666666666667,
                    y: 0.7714285714285715,
                    src: "",
                    zone: "B",
                },
                {
                    x: 0.6383333333333333,
                    y: 0.07514285714285718,
                    src: "",
                    zone: "C",
                },
                {
                    x: 0.4133333333333334,
                    y: 0.4608571428571429,
                    src: "",
                    zone: "D",
                },
            ],
            hotspots: [
                {
                    x: 0,
                    y: 0.1973469387755217,
                    src: "",
                    name: "Option 1",
                    correct: "A",
                },
                {
                    x: 0.16526666666666662,
                    y: 0.6212734693878729,
                    src: "",
                    name: "Option 2",
                    correct: "C",
                },
                {
                    x: 0,
                    y: 0.7804244897959461,
                    src: "",
                    name: "Option 3",
                    correct: "B",
                },
                {
                    x: 0,
                    y: 0.4989142857143705,
                    src: "",
                    name: "Option 4",
                    correct: "D",
                },
            ],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "hotspot_dnd_image",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
    {
        title: "Long Answer",
        contentType: "long_answer",
        icon: "/assets/long_answer.png",
        data: {
            name: "Long Answer question title here",
            type: "long_answer",
            question:
                "Lorem ipsum dolor sit, amet consectetur adipisicing elit. Vitae ut nam, suscipit sunt soluta laudantium quos facilis quibusdam enim saepe.",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "long_answer",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
];

const questionDefaultValue = {
    question: "",
    question_type: "",
    difficulty_level: "easy",
};

const QuuestionCreate = () => {
    const params = useParams();
    const router = useNavigate();

    const [openSheet, setOpenSheet] = useState(false);
    const [questionTypes, setQuestionTypes] = useState([]);

    const [question, setQuestion] = useState(questionDefaultValue);
    const [quesData, setQuesData] = useState(null);
    const [contentData, setContentData] = useState(null);

    const handleClear = () => {
        setQuestion(questionDefaultValue);
        setQuesData(null);
        setContentData(null);
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setQuestion({ ...question, [name]: value });

        if (name == "question_type") {
            let content = contentTypes?.find((dt) => dt?.contentType == value);
            setContentData(content?.data);
        }
    };

    useEffect(() => {
        if (quesData !== null && params?.question_id !== undefined) {
            setQuestion({
                question: quesData?.question,
                question_type: quesData?.question_type,
                difficulty_level: quesData?.difficulty_level,
                option_id: quesData?.options?.id,
            });
            setContentData(quesData?.options);
        }
    }, [quesData]);

    useEffect(() => {
        if (params?.question_id !== undefined) {
            getQuestion(params?.question_id);
        }
    }, [params]);

    useEffect(() => {
        getQuestionTypes();
    }, []);

    const getQuestionTypes = async () => {
        await tanstackApi
            .get("question-type/list")
            .then((res) => {
                setQuestionTypes(res?.data?.data.filter((type) => type?.displayName.trim()));
            })
            .catch((err) => {
                setQuestionTypes([]);
            });
    };

    const getQuestion = async (ques_id) => {
        await tanstackApi
            .post("questions/list", {})
            .then((res) => {
                setQuesData(res?.data?.data?.find((dt) => dt?.id == parseInt(ques_id)));
            })
            .catch((err) => {
                setQuesData(null);
            });
    };

    const onSlideEdit = () => {
        setOpenSheet(true);
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!question?.question) {
            toast.warning("Question title", {
                description: "Please enter question title",
            });
            return false;
        } else if (!question?.question_type) {
            toast.warning("Question type", {
                description: "Please enter question type",
            });
            return false;
        } else if (!question?.difficulty_level) {
            toast.warning("Difficult level", {
                description: "Please enter difficult level",
            });
            return false;
        }

        if (params?.question_id !== undefined) {
            const payload = {
                question_id: Number(params?.question_id),
                option_id: Number(question?.option_id),
                question_type: question?.question_type,
                question: question?.question,
                options: contentData,
                difficulty_level: question?.difficulty_level,
            };
            await tanstackApi
                .put("questions/update", { ...payload })
                .then((res) => {
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    router(`/dashboard/question-bank`);
                })
                .catch((err) => {});
        } else {
            const payload = {
                question_type: question?.question_type,
                question: question?.question,
                options: contentData,
                difficulty_level: question?.difficulty_level,
            };
            await tanstackApi
                .post("questions/create", { ...payload })
                .then((res) => {
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                    router(`/dashboard/question-bank`);
                })
                .catch((err) => {});
        }
    };

    return (
        <div className="">
            <QuestionSettings
                open={openSheet}
                setOpen={setOpenSheet}
                contentData={contentData}
                setContentData={setContentData}
            />
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/question-bank">Question Bank</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Create Question</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <div className="tw-flex tw-items-center tw-gap-3">
                    <Button
                        className="tw-px-2 tw-py-1"
                        onClick={() => router(`/dashboard/question-bank`)}
                        variant="outline"
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button onClick={onDataSubmit} variant="">
                        <i className="fa-solid fa-floppy-disk"></i> Save Question
                    </Button>
                </div>
                {/* </Link> */}
            </div>
            <Card className="tw-mt-6 tw-font-lexend">
                <CardContent className="tw-mt-4 tw-space-y-5">
                    <div className="tw-grid tw-grid-cols-1 tw-gap-0">
                        <div className="tw-grid tw-grid-cols-[2fr_1fr_300px] tw-gap-5">
                            <div className="tw-space-y-1">
                                <Label htmlFor="question">Question Title</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={question?.question}
                                    name="question"
                                    id="question"
                                    placeholder="Enter question title here"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="username">Type</Label>
                                <Select
                                    onValueChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "question_type" } })
                                    }
                                    value={question?.question_type}
                                    name="question_type"
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose question type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {questionTypes?.map((data, idx) => (
                                            <SelectItem
                                                className="tw-font-lexend tw-font-medium"
                                                key={idx}
                                                value={data?.type}
                                            >
                                                {data?.displayName}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="tw-space-y-3">
                                <h1 className="tw-text-lg tw-font-semibold">Difficulty level</h1>
                                <div className="tw-flex-col-2 tw-mt-3 tw-flex tw-gap-10">
                                    <RadioGroup
                                        value={question?.difficulty_level}
                                        onValueChange={(e) =>
                                            onChangeHandle({ target: { value: e, name: "difficulty_level" } })
                                        }
                                        className="tw-flex"
                                    >
                                        <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                                            {difficultyLeverl?.map((data, idx) => (
                                                <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                                    <RadioGroupItem
                                                        value={data}
                                                        id={data}
                                                        checked={question?.difficulty_level == data}
                                                    />
                                                    <Label
                                                        htmlFor={data}
                                                        className="tw-cursor-pointer tw-font-normal tw-capitalize"
                                                    >
                                                        {data?.toLowerCase()}
                                                    </Label>
                                                </div>
                                            ))}
                                        </div>
                                    </RadioGroup>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-5">
                        <div className="tw-h-[570px] tw-w-[80%] tw-rounded-xl tw-border-[1px]">
                            {contentData?.componentTypeId == "singlechoice" && (
                                <Singlechoice content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "singlechoice_media" && (
                                <SinglechoiceMedia content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "fillin_the_blank" && (
                                <FillinTheBlanks content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "match_the_following" && (
                                <MatchTheFollowing content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "sequence_arrange" && (
                                <SequenceArrange content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "dnd_image_box" && (
                                <DragDropMedia content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "string_dropdown" && (
                                <StringDropdown content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "hotspot_dnd" && (
                                <HotspotDragDrop content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "hotspot_dnd_image" && (
                                <HotspotDragDropMedia content={contentData} setContentData={setContentData} />
                            )}
                            {contentData?.componentTypeId == "long_answer" && (
                                <LongAnswer content={contentData} setContentData={setContentData} />
                            )}
                        </div>
                        <div className="tw-flex tw-items-center tw-gap-3">
                            <Button variant="outline" className="tw-rounded-xl" onClick={handleClear}>
                                <i className="fa-solid fa-xmark"></i> Clear
                            </Button>
                            <Button onClick={onSlideEdit} variant="outline" className="tw-rounded-xl">
                                <i className="fa-solid fa-edit"></i> Edit Question
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default QuuestionCreate;
