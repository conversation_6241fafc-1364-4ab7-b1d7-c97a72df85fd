import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { Check } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

const Checkbox = React.forwardRef(({ className, ...props }, ref) => (
    <CheckboxPrimitive.Root
        ref={ref}
        className={cn(
            "tw-focus-visible:tw-outline-none tw-focus-visible:tw-ring-2 tw-focus-visible:tw-ring-ring tw-focus-visible:tw-ring-offset-2 tw-disabled:tw-cursor-not-allowed tw-disabled:tw-opacity-50 tw-peer tw-h-4 tw-w-4 tw-shrink-0 tw-rounded-sm tw-border tw-border-primary tw-ring-offset-background data-[state=checked]:tw-border-green-500 data-[state=checked]:tw-bg-green-500 data-[state=checked]:tw-text-white",
            className,
        )}
        {...props}
    >
        <CheckboxPrimitive.Indicator className={cn("tw-flex tw-items-center tw-justify-center tw-text-current")}>
            <Check className="tw-h-4 tw-w-4" />
        </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
));
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
