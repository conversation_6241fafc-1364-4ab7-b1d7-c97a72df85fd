import Pagination from "@/components/table/pagination";
import { But<PERSON> } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import QuestionTypeForm from "./QuestionTypeForm";

const ITEMS_PER_PAGE = 7;

const filterStateDefault = {
    displayName: "",
};

const QuestionTypes = () => {
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const [tableData, setTableData] = useState([]);
    const [currentPage, setCurrentPage] = useState(Number(searchParams.get("page") || 1));
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [open, setOpen] = useState(false);
    const [editData, setEditData] = useState(null);
    const [filterState, setFilterState] = useState(filterStateDefault);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState((prev) => ({ ...prev, [name]: value }));
    };

    const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

    useEffect(() => {
        getQuestionTypes();
    }, []);

    useEffect(() => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        setTableData(filteredData.slice(startIndex, endIndex));
    }, [currentPage, filteredData]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const getQuestionTypes = async () => {
        await tanstackApi
            .get("question-type/list")
            .then((res) => {
                const fetchedData = res?.data?.data || [];
                setDataList(fetchedData);
                setFilteredData(fetchedData);
            })
            .catch((err) => {
                setDataList([]);
                setFilteredData([]);
            });
    };

    const onAddNewType = () => {
        setEditData(null);
        setOpen(true);
    };

    const onEditType = (data) => {
        setEditData(data);
        setOpen(true);
    };

    const handleSearch = () => {
        const data = dataList.filter((item) => {
            const matchesName = filterState.displayName
                ? item.displayName.toLowerCase().includes(filterState.displayName.toLowerCase())
                : true;
            return matchesName;
        });
        setFilteredData(data);
        handlePageChange(1);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState(filterStateDefault);
        handlePageChange(1);
    };

    return (
        <>
            <QuestionTypeForm open={open} setOpen={setOpen} editData={editData} getQuestionTypes={getQuestionTypes} />
            <div>
                <div className="tw-flex tw-justify-between">
                    <h4 className="tw-text-xl tw-font-semibold">
                        <i className="fa-solid fa-asterisk"></i> Question Types
                    </h4>
                    <Button className="tw-px-2 tw-py-1" onClick={onAddNewType}>
                        <i className="fa-solid fa-plus"></i> New Question Type
                    </Button>
                </div>
                <br />
                <div className="page_filters tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label htmlFor="displayNameSearch">Display Name</label>
                        <input
                            id="displayNameSearch"
                            style={{ minWidth: "400px" }}
                            type="text"
                            name="displayName"
                            placeholder="Search by display Name ..."
                            value={filterState.displayName}
                            onChange={onFilterChange}
                        />
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={handleSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                {tableData && tableData.length > 0 ? (
                    <div className="custom_table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Icon</th>
                                    <th>Display Name</th>
                                    <th>Component Type</th>
                                    <th>Description</th>
                                    <th>Creation On</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {tableData.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>
                                            <img
                                                className="tw-h-[60px] tw-w-[60px] tw-object-cover"
                                                src={row?.display_icon || "/assets/course-placeholder.png"}
                                                alt=""
                                            />
                                        </td>
                                        <td>{row?.displayName}</td>
                                        <td>
                                            {row?.type} - {row?.component_type}
                                        </td>
                                        <td>{row?.description}</td>
                                        <td>{moment(row?.createdAt).format("LL")}</td>
                                        <td>
                                            <button className="selected_btn" onClick={() => onEditType(row)}>
                                                <i className="fa-solid fa-edit"></i> Edit
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                ) : (
                    <div className="tw-mt-10 tw-text-center tw-text-slate-500">No question types found.</div>
                )}
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">
                            {ITEMS_PER_PAGE}
                        </p>
                    </div>
                    <div>
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            handlePageChange={handlePageChange}
                            itemsPerPage={ITEMS_PER_PAGE}
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export default QuestionTypes;
