import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { colorsList } from "@/data/colors";
import { cn } from "@/lib/utils";
import { useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import { motion, useDragControls, useMotionValue, useMotionValueEvent } from "framer-motion";
import { Move } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

export function Reveal({ interactions }) {
    const { tabList } = useEditInteraction();
    const ref = useRef(null);
    const constraintsRef = useRef(null);

    return (
        <>
            <div className="tw-relative tw-pt-7">
                <div className="tw-flex tw-h-full tw-w-full tw-flex-wrap tw-gap-[1rem]" ref={ref}>
                    <motion.div
                        ref={constraintsRef}
                        className="tw-h-full tw-w-full tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-border tw-bg-white tw-bg-contain tw-bg-center tw-bg-no-repeat tw-text-[40px] tw-text-black"
                        style={{
                            backgroundImage: `url(${interactions?.structure?.question_thumbnail})`,
                        }}
                    >
                        {tabList.map((item, idx) => {
                            return <RevealItem constraintsRef={constraintsRef} item={{ ...item, idx }} key={idx} />;
                        })}
                    </motion.div>
                </div>
            </div>
        </>
    );
}

function RevealItem({ constraintsRef, item }) {
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const dragControls = useDragControls();
    const { handleTabChange, setContent } = useEditInteraction();
    const [titleEditable, setTitleEditable] = useState(false);
    const [descriptionEditable, setDescriptionEditable] = useState(false);
    const titleRef = useRef(null);
    const descriptionRef = useRef(null);

    const handleTitleEditable = useCallback(() => {
        setTitleEditable((prev) => !prev);
    }, []);

    const handleDescriptionEditable = useCallback(() => {
        setDescriptionEditable((prev) => !prev);
    }, []);

    const handleTitleBlur = useCallback(() => {
        handleTabChange("label", titleRef?.current?.innerText, item.idx);
    }, [handleTabChange, item.idx]);

    const handleDescriptionBlur = useCallback(() => {
        handleTabChange("description", descriptionRef?.current?.innerText, item.idx);
    }, [handleTabChange, item.idx]);

    useEffect(() => {
        if (constraintsRef?.current && item) {
            const container = constraintsRef.current;
            if (container.clientWidth === 0 || container.clientHeight === 0) return;

            const newX = ((Number(item.x) || 0) * container.clientWidth) / 100;
            const newY = ((Number(item.y) || 0) * container.clientHeight) / 100;

            setPosition({ x: newX, y: newY });
        }
    }, [constraintsRef, item?.x, item?.y]);

    const handleDragEnd = useCallback(
        (event, info) => {
            if (constraintsRef?.current) {
                const container = constraintsRef.current;
                const containerRect = container.getBoundingClientRect();

                // Calculate position relative to the container
                const newX = ((info.point.x - containerRect.left) / containerRect.width) * 100;
                const newY = ((info.point.y - containerRect.top) / containerRect.height) * 100;

                setContent((prev) => ({
                    ...prev,
                    structure: {
                        ...prev.structure,
                        database: prev.structure.database.map((data, idx) => {
                            if (idx === item.idx) return { ...data, x: newX, y: newY };
                            return data;
                        }),
                    },
                }));

                // Update local position to match the calculated percentage
                setPosition({
                    x: (newX / 100) * containerRect.width,
                    y: (newY / 100) * containerRect.height,
                });
            }
        },
        [constraintsRef, item.idx, setContent],
    );

    return (
        <motion.div
            className="tw-absolute"
            drag
            dragListener={false}
            dragControls={dragControls}
            dragConstraints={constraintsRef}
            dragMomentum={false}
            animate={{ x: position.x, y: position.y }}
            onDragEnd={handleDragEnd}
        >
            <div
                className="tw-flex tw-cursor-grab tw-items-center tw-justify-center"
                onPointerDown={(e) => {
                    dragControls.start(e);
                }}
            >
                <Move className="tw-size-3" />
            </div>
            <Popover>
                <PopoverTrigger asChild onPointerDown={(e) => e.stopPropagation()}>
                    <motion.div
                        className="tw-flex tw-size-6 tw-items-center tw-justify-center tw-rounded-full tw-text-sm"
                        style={{
                            backgroundColor: colorsList[item.idx],
                        }}
                    >
                        <span className="tw-select-none">{item.idx + 1}</span>
                    </motion.div>
                </PopoverTrigger>
                <PopoverContent className="tw-font-lexend">
                    <h2
                        ref={titleRef}
                        suppressContentEditableWarning
                        contentEditable={titleEditable}
                        onDoubleClick={handleTitleEditable}
                        onBlur={handleTitleBlur}
                        className="tw-text-[20px] 2xl:tw-text-[30px]"
                    >
                        {item.label}
                    </h2>
                    <p
                        ref={descriptionRef}
                        suppressContentEditableWarning
                        contentEditable={descriptionEditable}
                        onDoubleClick={handleDescriptionEditable}
                        onBlur={handleDescriptionBlur}
                        className="tw-text-base"
                    >
                        {item?.description}
                    </p>
                </PopoverContent>
            </Popover>
        </motion.div>
    );
}

export function RevealPreview() {
    const list = [
        "tw-left-[12%] tw-top-[6%]",
        "tw-left-[24%] tw-bottom-[12%]",
        "tw-right-[24%] tw-bottom-[40%]",
        "tw-right-[30%] tw-bottom-[80%]",
    ];

    return (
        <>
            <div className="tw-relative tw-flex tw-size-full tw-items-center tw-justify-center">
                <div className="tw-relative tw-flex tw-h-[80%] tw-w-full tw-items-center tw-justify-center tw-gap-[1rem] tw-rounded-md tw-bg-slate-300">
                    {list.map((className, i) => {
                        return (
                            <div
                                key={i}
                                className={cn("tw-absolute tw-size-2 tw-rounded-full", className)}
                                style={{
                                    backgroundColor: colorsList[i],
                                }}
                            >
                                <div className="tw-relative">
                                    <div className="tw-absolute tw-left-[10px] tw-aspect-video tw-w-4 tw-rounded-[2px] tw-bg-white"></div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </>
    );
}
