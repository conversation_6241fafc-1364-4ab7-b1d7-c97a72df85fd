import { Input } from "@/components/ui/input";
import { useRef } from "react";

export default function Input1({ placeholder, type, id, onChange }) {
    const ref = useRef(null);
    return (
        <div className="">
            <div className="tw-flex tw-rounded-lg tw-shadow-sm tw-shadow-black/5">
                <Input
                    id={id}
                    ref={ref}
                    className="tw--me-px tw-flex-1 tw-rounded-e-none tw-shadow-none focus-visible:tw-z-10"
                    placeholder={placeholder}
                    type={type}
                />
                <button
                    onClick={() => onChange(ref.current?.value)}
                    className="tw-inline-flex tw-items-center tw-rounded-e-lg tw-border tw-border-input tw-bg-background tw-px-3 tw-text-sm tw-font-medium tw-text-foreground tw-ring-offset-background tw-transition-shadow hover:tw-bg-accent hover:tw-text-foreground focus:tw-z-10 focus-visible:tw-border-ring focus-visible:tw-outline-none focus-visible:tw-ring-2 focus-visible:tw-ring-ring/30 focus-visible:tw-ring-offset-2 disabled:tw-pointer-events-none disabled:tw-cursor-not-allowed disabled:tw-opacity-50"
                >
                    Send
                </button>
            </div>
        </div>
    );
}
