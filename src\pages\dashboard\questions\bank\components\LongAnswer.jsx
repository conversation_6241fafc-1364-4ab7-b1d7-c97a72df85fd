import { useInView } from "framer-motion";
import { useRef } from "react";

const LongAnswer = ({ onSlideEdit, onRemoveSlide, index, content, template }) => {
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { y: "-50%", scaleY: 0.5 },
        inView: { y: 0, scaleY: 1 },
    };

    return (
        <div
            className="tw-grid tw-min-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            // style={{ backgroundImage: `url(${template?.background||content?.question_background})` }}
        >
            <div className="tw-px-10 tw-py-7">
                <div ref={ref} className="tw-relative tw-z-10 tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem]">
                    <div
                        // variants={variants}
                        // initial="initial"
                        // animate={inView ? "inView" : "initial"}
                        // transition={{ duration: 0.5, delay: 0.5 }}
                        className="tw-relative tw-space-y-4"
                    >
                        <label
                            htmlFor="textarea"
                            className="tw-rounded-md tw-bg-white tw-px-3 tw-text-left tw-font-mono tw-text-2xl tw-text-[#FE5C96]"
                        >
                            {content?.question}
                        </label>
                        <textarea
                            id="textarea"
                            rows={6}
                            className="tw-w-full tw-resize-none tw-rounded-md tw-border-2 tw-border-[#FE5C96] tw-bg-transparent tw-p-2 tw-text-[24px] tw-text-black tw-outline-none"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LongAnswer;
