import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";
import * as React from "react";

const SelectNative = React.forwardRef(({ className, children, ...props }, ref) => {
    return (
        <div className="tw-relative">
            <select
                className={cn(
                    "tw-peer tw-inline-flex tw-w-full tw-cursor-pointer tw-appearance-none tw-items-center tw-rounded-lg tw-border tw-border-input tw-bg-background tw-text-sm tw-text-foreground tw-shadow-sm tw-shadow-black/5 tw-transition-shadow focus-visible:tw-border-ring focus-visible:tw-outline-none focus-visible:tw-ring-[3px] focus-visible:tw-ring-ring/20 disabled:tw-pointer-events-none disabled:tw-cursor-not-allowed disabled:tw-opacity-50 has-[option[disabled]:checked]:tw-text-muted-foreground",
                    props.multiple
                        ? "tw-py-1 [&>*]:tw-px-3 [&>*]:tw-py-1 [&_option:checked]:tw-bg-accent"
                        : "tw-h-9 tw-pe-8 tw-ps-3",
                    className,
                )}
                ref={ref}
                {...props}
            >
                {children}
            </select>
            {!props.multiple && (
                <span className="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-end-0 tw-flex tw-h-full tw-w-9 tw-items-center tw-justify-center tw-text-muted-foreground/80 peer-disabled:tw-opacity-50">
                    <ChevronDown size={16} strokeWidth={2} aria-hidden="true" />
                </span>
            )}
        </div>
    );
});
SelectNative.displayName = "SelectNative";

export { SelectNative };
