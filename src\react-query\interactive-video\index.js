import { tanstackApi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetInteractiveVideo = ({ limit, offset }) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["interactive-video", { limit, offset, userId }],
        queryFn: async () => (await tanstackApi.get("/cron-queue/list", { params: { limit, offset } })).data,
    });
};

export const useCreateInteractiveVideo = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("/cron-queue/add", data)).data,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["interactive-video"] });
        },
    });
};

export const useParticularInteractiveVideo = ({ id }) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["interactive-video", { id, userId }],
        queryFn: async () => (await tanstackApi.get(`/cron-queue/get/${id}`)).data,
        enabled: !!id,
    });
};

export const useDeleteInteractiveVideo = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async ({ id }) => (await tanstackApi.delete(`/cron-queue/delete/${id}`)).data,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["interactive-video"] });
        },
    });
};
