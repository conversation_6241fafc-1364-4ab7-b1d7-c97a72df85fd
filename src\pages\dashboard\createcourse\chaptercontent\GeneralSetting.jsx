import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

const icons = [
    { icon: "/assets/book.png" },
    { icon: "/assets/problem-solving.png" },
    { icon: "/assets/formula.png" },
    { icon: "/assets/search.png" },
    { icon: "/assets/medal.png" },
    { icon: "/assets/robo-advisor.png" },
    { icon: "/assets/planning.png" },
    { icon: "/assets/books.png" },
    { icon: "/assets/embed.png" },
];

const GeneralSetting = ({ setContentData, contentData }) => {
    const onHandleChange = (value, name) => {
        if (name == "is_completion_required") {
            setContentData({ ...contentData, [name]: !contentData?.is_completion_required });
        } else {
            setContentData({ ...contentData, [name]: value });
        }
    };

    return (
        <div className="tw-mt-3 tw-space-y-4">
            <div className="tw-mt-4 tw-flex tw-justify-center">
                <div className="tw-flex tw-items-center tw-space-x-2">
                    <Switch
                        id="airplane-mode"
                        checked={contentData?.is_completion_required}
                        onCheckedChange={(e) => onHandleChange(e, "is_completion_required")}
                    />
                    <Label htmlFor="airplane-mode">Completion Required</Label>
                </div>
            </div>
            <div className="tw-space-y-1">
                <Label htmlFor="name">Content Title</Label>
                <Input
                    onChange={(e) => onHandleChange(e.target.value, "lecture_title")}
                    value={contentData?.lecture_title}
                    name="lecture_title"
                    id="name"
                    placeholder="Enter content title here"
                />
            </div>
            <div className="tw-space-y-1">
                <Label htmlFor="name">Choose Slide Icon</Label>
                <div className="tw-grid tw-w-full tw-grid-cols-6">
                    {icons?.map((content, idx) => (
                        <div
                            key={`${content.id}_${idx}`}
                            onClick={() => onHandleChange(content?.icon, "icon")}
                            className={`${content?.icon == contentData?.icon && "icon_selected"} tw-flex tw-aspect-square tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-xl tw-py-5 hover:tw-bg-slate-100`}
                        >
                            <img className="tw-w-[50px]" src={content?.icon} alt="" />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default GeneralSetting;
