import { tanstack<PERSON>pi } from "@/react-query/api";
import { timelineLog } from "@/react-query/common/timeline";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { useMutation } from "@tanstack/react-query";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

export const useSignup = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("signup", data.data)).data,
        onSuccess: (data, signUpData) => {
            const userData = data.data;
            const token = data.token;
            const signupType = signUpData.type;
            if (signupType === "learner") {
                localStorage.setItem("id", userData.id);
                localStorage.setItem("email", userData.email);
                localStorage.setItem("first_name", userData.first_name);
                localStorage.setItem("last_name", userData.last_name);
                localStorage.setItem("status", userData.status);
                localStorage.setItem("parent_user_id", userData.parent_user_id);
                localStorage.setItem("login_token", token);
                localStorage.setItem("auth_signup_token", token);
                localStorage.setItem("QL_email", userData.email);
                localStorage.setItem("QL_password", signUpData.data.password);
                timelineLog({
                    user_id: userData?.id,
                    event: "signup",
                    log: `${userData.first_name} ${userData.last_name} Sign up successfully `,
                });
                navigate("/learner-profile");
            } else if (signupType === "organisation") {
                localStorage.setItem("auth_signup_token", token);
                navigate("/signup-steps");
                localStorage.removeItem("domain_validity");
            }
        },
        onError: (err) => {
            dispatch(AlertSnackInfo({ message: err.response.data.message, result: err.response.data.success }));
        },
    });
};
