"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Check, X } from "lucide-react";
import { useEffect, useState } from "react";

export default function MultiChoiceQuestion({ question, onAnswerSubmit, showCorrectAnswer, savedAnswer }) {
    const [selectedOptions, setSelectedOptions] = useState([]);
    const [submitted, setSubmitted] = useState(false);

    useEffect(() => {
        if (savedAnswer) {
            setSelectedOptions(savedAnswer);
            setSubmitted(true);
        }
    }, [savedAnswer]);

    const toggleOption = (optionIndex) => {
        if (submitted) return;

        setSelectedOptions((prev) => {
            if (prev.includes(optionIndex)) {
                return prev.filter((index) => index !== optionIndex);
            } else {
                return [...prev, optionIndex];
            }
        });
    };

    const handleSubmit = () => {
        setSubmitted(true);
        const isCorrect =
            selectedOptions.length === question.correctAnswers.length &&
            selectedOptions.every((option) => question.correctAnswers.includes(option));
        onAnswerSubmit(selectedOptions, isCorrect);
    };

    const isOptionCorrect = (index) => question.correctAnswers.includes(index);

    return (
        <div className="tw-space-y-4">
            <p className="tw-mb-2 tw-text-sm tw-text-orange-300">Select all correct answers</p>

            <div className="tw-space-y-3">
                {question.options.map((option, index) => {
                    const isSelected = selectedOptions.includes(index);
                    const isCorrect = isOptionCorrect(index);
                    const showCorrect = showCorrectAnswer && isCorrect;
                    const showIncorrect = submitted && isSelected && !isCorrect;

                    return (
                        <div
                            key={index}
                            className={`tw-flex tw-items-center tw-gap-3 tw-rounded-md tw-p-3 tw-transition-colors ${
                                isSelected && submitted
                                    ? isCorrect
                                        ? "tw-border-2 tw-border-green-500 tw-bg-green-700/50"
                                        : "tw-border-2 tw-border-red-500 tw-bg-red-700/50"
                                    : showCorrect
                                      ? "tw-border-2 tw-border-green-500 tw-bg-green-700/50"
                                      : "tw-border-2 tw-border-orange-800 tw-bg-orange-900/30"
                            }`}
                        >
                            <Checkbox
                                id={`option-${index}`}
                                checked={isSelected}
                                onCheckedChange={() => toggleOption(index)}
                                disabled={submitted}
                                className="data-[state=checked]:tw-bg-orange-500 data-[state=checked]:tw-text-white"
                            />
                            <label htmlFor={`option-${index}`} className="tw-flex-1 tw-cursor-pointer">
                                {option}
                            </label>
                            {submitted &&
                                isSelected &&
                                (isCorrect ? (
                                    <Check className="tw-text-green-400" />
                                ) : (
                                    <X className="tw-text-red-400" />
                                ))}
                            {showCorrect && !isSelected && <Check className="tw-text-green-400" />}
                        </div>
                    );
                })}
            </div>

            {!submitted && (
                <Button onClick={handleSubmit} className="tw-mt-4 tw-bg-teal-700 tw-text-white hover:tw-bg-teal-600">
                    Submit Answer
                </Button>
            )}
        </div>
    );
}
