import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const FeedbackSettings = ({ setContentData, contentData }) => {
    const onHandleChange = (value, name) => {
        setContentData({
            ...contentData,
            feedbacks: {
                [name]: value,
            },
        });
    };

    return (
        <div>
            <Label>Customized Feedbacks :-</Label>
            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">
                Please define feedbacks here for all three cases mentioned below.
            </p>
            <div className="tw-mt-10 tw-space-y-5">
                <div className="tw-space-y-3">
                    <Label htmlFor="name" className="tw-flex tw-items-center tw-gap-2">
                        <i className="fa-solid fa-check"></i> Correct Answer
                    </Label>
                    <Input
                        onChange={(e) => onHandleChange(e.target.value, "correctAns")}
                        value={contentData?.feedbacks?.correctAns}
                        placeholder="Create feedback for correct answer here"
                    />
                </div>
                <div className="tw-space-y-3">
                    <Label htmlFor="name" className="tw-flex tw-items-center tw-gap-2">
                        <i className="fa-solid fa-filter"></i> Partially Correct Answer
                    </Label>
                    <Input
                        onChange={(e) => onHandleChange(e.target.value, "partiallyCorrectAns")}
                        value={contentData?.feedbacks?.partiallyCorrectAns}
                        placeholder="Create feedback for partially correct answer here"
                    />
                </div>

                <div className="tw-space-y-3">
                    <Label htmlFor="name" className="tw-flex tw-items-center tw-gap-2">
                        <i className="fa-solid fa-xmark"></i> Wrong Answer
                    </Label>
                    <Input
                        onChange={(e) => onHandleChange(e.target.value, "incorrectAns")}
                        value={contentData?.feedbacks?.incorrectAns}
                        placeholder="Create feedback for wrong answer here"
                    />
                </div>
            </div>
        </div>
    );
};

export default FeedbackSettings;
