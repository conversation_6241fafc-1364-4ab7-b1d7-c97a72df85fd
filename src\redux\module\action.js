import {
    ADD_MODULE,
    ADD_SUB_MODULE,
    DELETE_MODULE,
    DELETE_SUB_MODULE,
    FETCH_MODULES_LIST,
    GET_MODULES_REQ,
    GET_MODULE_CATEGORIES,
    GET_SUB_MODULE_LIST,
    SUB_MODULE_REQ,
    UPDATE_MODULE,
    UPDATE_SUB_MODULE,
} from "@/redux-types";

export const fetchModulesList = () => {
    return {
        type: GET_MODULES_REQ,
    };
};
export const getAllModules = (modulesList) => {
    return {
        type: FETCH_MODULES_LIST,
        payload: modulesList,
    };
};
export const getModulesCategories = (categories) => {
    return {
        type: GET_MODULE_CATEGORIES,
        payload: categories,
    };
};
export const AddModule = (module, handleClose) => {
    return {
        type: ADD_MODULE,
        payload: module,
        handleClose: handleClose,
    };
};
export const UpdateModule = (module) => {
    return {
        type: UPDATE_MODULE,
        payload: module,
    };
};
export const DeleteModule = (moduleID) => {
    return {
        type: DELETE_MODULE,
        payload: moduleID,
    };
};

// SUB- MODULE ACTIONS

export const fetchSubModulesReq = (subModuleId) => {
    return {
        type: SUB_MODULE_REQ,
        payload: subModuleId,
    };
};
export const getAllSubModules = (subModulesList) => {
    return {
        type: GET_SUB_MODULE_LIST,
        payload: subModulesList,
    };
};
export const AddSubModule = (module) => {
    return {
        type: ADD_SUB_MODULE,
        payload: module,
    };
};
export const UpdateSubModule = (module) => {
    return {
        type: UPDATE_SUB_MODULE,
        payload: module,
    };
};
export const DeleteSubModule = (moduleID) => {
    return {
        type: DELETE_SUB_MODULE,
        payload: moduleID,
    };
};
