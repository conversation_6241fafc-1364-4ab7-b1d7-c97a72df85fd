import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import BasicDetails from "./BasicDetails";
import CourseSettings from "./CourseSettings";
import TemplateSettings from "./TemplateSettings";

const CreateCourseWrapper = () => {
    const params = useParams();
    const router = useNavigate();
    const [CourseData, setCourseData] = useState(null);

    useEffect(() => {
        if (params?.course_id !== undefined) {
            getCourses({ course_id: params?.course_id });
        }
    }, [params]);

    const getCourses = async (payload) => {
        await tanstackApi
            .post("course/view-course", { ...payload })
            .then((res) => {
                setCourseData(res?.data?.data);
            })
            .catch((err) => {
                setCourseData(null);
            });
    };

    return (
        <div className="page_layout">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-square-pen"></i> {CourseData?.course_title ? "Update" : "Create"} Course
                </h4>
                <Button className="tw-px-2 tw-py-1" onClick={() => router("/dashboard/my-courses")}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
            </div>
            <Tabs defaultValue="basic_details" className="">
                {params?.init !== "yes" && (
                    <TabsList className="tw-grid tw-w-[450px] tw-grid-cols-3">
                        <TabsTrigger value="basic_details" className="tw-gap-2">
                            <i className="fa-solid fa-circle-info"></i> Basic Details
                        </TabsTrigger>
                        <TabsTrigger value="setting" className="tw-gap-2">
                            <i className="fa-solid fa-gear"></i> Settings
                        </TabsTrigger>
                        <TabsTrigger value="template" className="tw-gap-2">
                            <i className="fa-solid fa-brush"></i> Templates
                        </TabsTrigger>
                    </TabsList>
                )}
                <TabsContent value="basic_details">
                    <BasicDetails CourseData={CourseData} getCourses={getCourses} />
                </TabsContent>
                <TabsContent value="setting">
                    <CourseSettings CourseData={CourseData} getCourses={getCourses} />
                </TabsContent>
                <TabsContent value="template">
                    <TemplateSettings CourseData={CourseData} getCourses={getCourses} />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default CreateCourseWrapper;
