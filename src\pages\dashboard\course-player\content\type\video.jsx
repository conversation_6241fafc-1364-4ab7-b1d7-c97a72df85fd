import { animationVariants } from "@/pages/dashboard/course-player/content/type/animation";
import { MediaPlayer, MediaProvider } from "@vidstack/react";
import { PlyrLayout, plyrLayoutIcons } from "@vidstack/react/player/layouts/plyr";
import "@vidstack/react/player/styles/base.css";
import "@vidstack/react/player/styles/plyr/theme.css";
import { motion } from "framer-motion";

function ContentVideo({ content, template }) {
    const animation = animationVariants[content?.animation_type ?? "Grow"];
    return (
        <motion.div
            className="tw-h-full"
            initial={animation.initial}
            animate={animation.animate}
            transition={{
                duration: 1,
                ease: "easeInOut",
            }}
        >
            <MediaPlayer
                title={content?.lecture_title}
                className="!tw-aspect-video !tw-h-[390px] tw-border-[3px] tw-border-black tw-bg-black [--plyr-border-radius:_8px] 2xl:!tw-h-[520px]"
                src={content?.content_url}
                playsInline
            >
                <MediaProvider />
                <PlyrLayout icons={plyrLayoutIcons} />
            </MediaPlayer>
        </motion.div>
    );
}

export default ContentVideo;
