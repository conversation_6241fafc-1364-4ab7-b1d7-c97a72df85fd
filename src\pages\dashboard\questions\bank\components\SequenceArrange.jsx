import { Card } from "@/pages/dashboard/course-player/quiz-content/types/sequence-arrange/card";
import { ItemTypes } from "@/pages/dashboard/course-player/quiz-content/types/sequence-arrange/type";
import { motion, useInView } from "framer-motion";
import update from "immutability-helper";
import { memo, useCallback, useRef, useState } from "react";
import { DndProvider, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";

const SequenceArrange = ({ onSlideEdit, onRemoveSlide, index, content, template }) => {
    const [items, setItems] = useState(content?.answerKey ?? []);

    return (
        <div
            className="tw-grid tw-min-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"

            // style={{ backgroundImage: `url(${template?.background||content?.question_background})` }}
        >
            <div className="tw-px-10 tw-py-7">
                <div className="tw-relative tw-flex tw-w-full tw-items-center tw-justify-center tw-overflow-hidden">
                    <DndProvider backend={HTML5Backend}>
                        <Container items={content?.matchOptions} setItems={setItems} />
                    </DndProvider>
                </div>
            </div>
        </div>
    );
};

export default SequenceArrange;

const Container = memo(function Container({ items, setItems }) {
    const [cards, setCards] = useState(items);
    const findCard = useCallback(
        (id) => {
            const card = cards.filter((c) => `${c.id}` === id)[0];
            return {
                card,
                index: cards.indexOf(card),
            };
        },
        [cards],
    );
    const moveCard = useCallback(
        (id, atIndex) => {
            const { card, index } = findCard(id);
            const updated = update(cards, {
                $splice: [
                    [index, 1],
                    [atIndex, 0, card],
                ],
            });
            setCards(updated);
            setItems(updated);
        },
        [findCard, cards, setCards],
    );
    const [, drop] = useDrop(() => ({ accept: ItemTypes.CARD }));

    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { x: "-50%", scaleX: 0.5 },
        inView: { x: 0, scaleX: 1 },
    };
    const list = ["#FECC5C", "#FD5C5C", "#E08D67", "#FECC5C", "#FD5C5C", "#E08D67", "#FECC5C"];
    return (
        <div ref={ref} className="tw-w-full">
            <div ref={drop} className="tw-grid tw-w-full tw-grid-cols-1 tw-gap-[1rem]">
                {cards.map((card, index) => {
                    return (
                        <motion.div
                            variants={variants}
                            initial="initial"
                            whileInView={inView ? "inView" : "initial"}
                            transition={{
                                duration: 0.5,
                                delay: index * 0.2,
                            }}
                            key={card.id}
                            className="tw-w-full tw-rounded-xl tw-p-1"
                            style={{
                                backgroundColor: "#ececec",
                            }}
                        >
                            <Card key={card.id} id={card.id} moveCard={moveCard} findCard={findCard}>
                                <img
                                    src={card?.imageText || "/assets/thumbnail-beta.png"}
                                    className="tw-aspect-[1/1] tw-h-full tw-rounded-[0.5rem] tw-bg-white tw-object-cover"
                                ></img>
                                <p className="tw-font-mono tw-text-slate-500">{card?.text}</p>
                            </Card>
                        </motion.div>
                    );
                })}
            </div>
        </div>
    );
});
