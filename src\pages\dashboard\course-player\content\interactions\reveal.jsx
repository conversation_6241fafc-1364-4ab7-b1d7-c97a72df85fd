import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { colorsList } from "@/data/colors";
import { motion, useMotionValue } from "framer-motion";
import { useEffect, useRef } from "react";

export function Reveal({ template, interactions }) {
    const data = interactions?.structure?.database || interactions?.structure?.sections;
    const ref = useRef(null);
    const constraintsRef = useRef(null);

    return (
        <div className="tw-relative tw-size-[90%] tw-pt-7">
            <div className="tw-flex tw-h-full tw-w-full tw-flex-wrap tw-gap-[1rem]" ref={ref}>
                <motion.div
                    ref={constraintsRef}
                    className="tw-h-full tw-w-full tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-bg-white tw-bg-contain tw-bg-center tw-bg-no-repeat tw-text-[40px] tw-text-black"
                    style={{
                        backgroundImage: `url(${interactions?.structure?.question_thumbnail})`,
                    }}
                >
                    {data.map((item, idx) => {
                        return <RevealItem constraintsRef={constraintsRef} item={{ ...item, idx }} key={idx} />;
                    })}
                </motion.div>
            </div>
        </div>
    );
}

function RevealItem({ constraintsRef, item }) {
    const x = useMotionValue(0);
    const y = useMotionValue(0);

    useEffect(() => {
        if (constraintsRef?.current) {
            x.set(((Number(item?.x) || 0) * constraintsRef.current.clientWidth) / 100);
            y.set(((Number(item?.y) || 0) * constraintsRef.current.clientHeight) / 100);
        }
    }, [constraintsRef, item?.x, item?.y]);

    return (
        <Popover>
            <PopoverTrigger asChild>
                <motion.div
                    className="tw-absolute tw-flex tw-size-6 tw-items-center tw-justify-center tw-rounded-full tw-text-sm"
                    dragConstraints={constraintsRef}
                    style={{
                        backgroundColor: colorsList[item.idx],
                        x,
                        y,
                    }}
                    key={item.label}
                >
                    {item.idx + 1}
                </motion.div>
            </PopoverTrigger>
            <PopoverContent>
                <h2 className="tw-text-[20px] 2xl:tw-text-[30px]">{item.label}</h2>
                <p className="tw-text-base">{item?.description}</p>
            </PopoverContent>
        </Popover>
    );
}
