import { UpdateBookmarkingData } from "@/redux/course/action";
import { createContext, useContext, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

/**
 * @typedef {("show-correct-answer" | "show-only-right-wrong")} ShowAnswerType
 */

/**
 * @typedef {Object} PlayerContextType
 * @property {boolean} courseStarted - Indicates if the course has been started.
 * @property {(started: boolean) => void} setCourseStarted - Function to update the course started state.
 * @property {number} currentIndex - The current index in the course content array.
 * @property {(index: number) => void} setCurrentIndex - Function to update the current index.
 * @property {boolean} minimize - Indicates if the player is minimized.
 * @property {(minimized: boolean) => void} setMinimize - Function to update the minimize state.
 * @property {boolean} fullscreen - Indicates if the player is in fullscreen mode.
 * @property {(fullscreen: boolean) => void} setFullscreen - Function to update the fullscreen state.
 * @property {boolean} contentFullScreen - Indicates if the content area is in fullscreen mode.
 * @property {(contentFullScreen: boolean) => void} setContentFullScreen - Function to update the content fullscreen state.
 * @property {number} contentLength - The total number of content items in the course.
 * @property {(contentLength: number) => void} setContentLength - Function to update the content length.
 * @property {Object} bookmarking - The bookmarking data for the course.
 * @property {(bookmarking: Object) => void} setBookmarking - Function to update the bookmarking data.
 * @property {Object} courseDetails - The details of the current course.
 * @property {(courseDetails: Object) => void} setCourseDetails - Function to update the course details.
 * @property {Array} contentArray - An array of all course content items.
 * @property {(contentArray: Array) => void} setContentArray - Function to update the content array.
 * @property {() => void} handleNext - Function to navigate to the next content item.
 * @property {() => void} handlePrev - Function to navigate to the previous content item.
 * @property {ShowAnswerType} showAnswerType - The type of answer to show for quizzes.
 * @property {(CONTENT: any, isComplete: boolean) => void} updateBookmarking - Updates the bookmarking data.
 */

/** @type {import('react').Context<PlayerContextType|undefined>} */
const PlayerContext = createContext(undefined);

/**
 * Provider component that manages state for the course player.
 * @param {Object} props
 * @param {import('react').ReactNode} props.children - Child components that will have access to player context.
 * @returns {JSX.Element} Provider component wrapping children.
 */
export const PlayerProvider = ({ children }) => {
    const navigate = useNavigate();
    const params = useParams();
    const dispatch = useDispatch();
    const [courseStarted, setCourseStarted] = useState(false);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [contentLength, setContentLength] = useState(0);
    const [minimize, setMinimize] = useState(false);
    const [fullscreen, setFullscreen] = useState(false);
    const [contentFullScreen, setContentFullScreen] = useState(false);
    const [bookmarking, setBookmarking] = useState({});
    const [courseDetails, setCourseDetails] = useState({});
    const [contentArray, setContentArray] = useState([]);
    const Bookmarking = useSelector((state) => state?.CourseReducers?.courseBookmarking);

    /**
     * @type {[ShowAnswerType, React.Dispatch<React.SetStateAction<ShowAnswerType>>]}
     */
    const [showAnswerType, setShowAnswerType] = useState("show-correct-answer");

    useEffect(() => {
        if (contentArray[currentIndex]?.content?.content_type === "QUIZ") {
            setShowAnswerType(contentArray[currentIndex]?.content?.quiz?.showAnswerType);
        }
    }, [contentArray]);

    /**
     * Updates the bookmarking data based on the current content and completion status.
     *
     * @param {any} CONTENT - The current content being viewed.
     * @param {boolean} isComplete - Indicates whether the content is completed (default: false).
     */
    const updateBookmarking = (CONTENT, isComplete = false) => {
        const chapter = CONTENT?.chapter;
        const content = CONTENT?.content;

        const modified = Bookmarking?.course_bookmarking_details?.map((cptr) => {
            const totalContents = cptr.contentArray.length;
            const consumedContents = cptr.contentArray?.filter((dt) => dt?.is_content_consumed === true).length;
            const chapterPercentage = (consumedContents / totalContents) * 100;
            return {
                chapter_id: cptr?.chapter_id,
                is_chapter_seen: cptr?.chapter_id == chapter?.id ? true : cptr.is_chapter_seen,
                is_chapter_complete: chapterPercentage == 100 ? true : false,
                chapter_percentage: chapterPercentage,
                contentArray: cptr.contentArray.map((cnt) => {
                    let isContentConsumed = cnt?.content_id == content?.id ? true : cnt.is_content_consumed;
                    if (["ZIP", "QUIZ", "INTERACTION"].includes(content?.content_type)) {
                        isContentConsumed = cnt?.content_id == content?.id ? isComplete : cnt.is_content_consumed;
                    }
                    return {
                        content_id: cnt.content_id,
                        is_content_consumed: isContentConsumed,
                        is_content_seen: cnt?.content_id == content?.id ? true : cnt.is_content_seen,
                        video_last_timestamp: cnt.video_last_timestamp,
                    };
                }),
            };
        });

        const totalChapters = courseDetails?.courseChapters?.length || 0;
        const completedChapters = modified?.filter((cptr) => cptr?.is_chapter_complete)?.length || 0;
        const courseCompletionPercent = (completedChapters / totalChapters) * 100;

        const bookmark = {
            learning_path_id: params?.path_id ? parseInt(params?.path_id) : null,
            user_id: Bookmarking?.user_id,
            course_id: Bookmarking?.course_id,
            course_status: Bookmarking?.course_status,
            course_seen: Bookmarking?.course_seen,
            course_start_date: Bookmarking?.course_start_date,
            course_completion_percent: courseCompletionPercent,
            course_end_date: "",
            status: true,
            last_activity: JSON.stringify({
                name: CONTENT?.content?.lecture_title,
                sequence: currentIndex,
            }),
            course_bookmarking_details: modified,
        };

        dispatch(UpdateBookmarkingData(bookmark));
    };

    const handleNext = (status = "") => {
        if (!courseStarted) return toast.error("Please Start the course");
        const currentContent = contentArray?.[currentIndex]?.content;
        const isAssessmentType = ["QUIZ", "HOMEWORK", "ASSIGNMENT"].includes(currentContent?.content_type);

        if (isAssessmentType && currentContent?.is_completion_required && status !== "completed") {
            return toast.info(`You have to completed this ${currentContent?.content_type}`);
        }

        if (currentIndex < contentLength - 1) {
            setCurrentIndex(currentIndex + 1);
        } else {
            navigate(`/generate-certificate/${courseDetails?.id}/${bookmarking?.id}`);
        }
    };

    const handlePrev = () => {
        if (currentIndex > 0) setCurrentIndex(currentIndex - 1);
    };

    const value = {
        courseStarted,
        setCourseStarted,
        currentIndex,
        setCurrentIndex,
        minimize,
        setMinimize,
        fullscreen,
        setFullscreen,
        contentFullScreen,
        setContentFullScreen,
        contentLength,
        setContentLength,
        bookmarking,
        setBookmarking,
        courseDetails,
        setCourseDetails,
        handleNext,
        handlePrev,
        contentArray,
        setContentArray,
        showAnswerType,
        updateBookmarking,
    };

    return <PlayerContext.Provider value={value}>{children}</PlayerContext.Provider>;
};

/**
 * Custom hook to access the player context.
 * @returns {PlayerContextType} Object containing player state and setter functions.
 * @throws {Error} If used outside of PlayerProvider component.
 */
export const usePlayer = () => {
    const context = useContext(PlayerContext);
    if (context === undefined) throw new Error("usePlayer must be used within a PlayerProvider");
    return context;
};
