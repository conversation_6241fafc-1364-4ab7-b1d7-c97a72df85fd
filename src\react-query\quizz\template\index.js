import { tanstack<PERSON>pi } from "@/react-query/api";
import { timelineLog } from "@/react-query/common/timeline";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetTemplate = ({ limit = 10, offset = 0 }) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["template", { limit, offset, userId }],
        queryFn: async () => (await tanstackApi.get(`/template/list`, { params: { limit, offset } })).data,
    });
};

export const useGetParticularTemplate = (id) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["template", { id, userId }],
        queryFn: async () => (await tanstackApi.get(`/template/get/${id}`)).data,
        enabled: !!id,
    });
};

export const useCreateTemplate = () => {
    const query = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post(`/template/create`, data)).data,
        onSuccess: (_, data) => {
            timelineLog({
                user_id: localStorage.getItem("userId"),
                event: "template",
                log: `${data.template_title} has been created successfully.`,
            });
            query.invalidateQueries({ queryKey: ["template"] });
        },
    });
};

export const useUpdateTemplate = () => {
    const query = useQueryClient();
    return useMutation({
        mutationFn: async ({ id, ...data }) => (await tanstackApi.put(`/template/update/${id}`, data)).data,
        onSuccess: (_, data) => {
            timelineLog({
                user_id: localStorage.getItem("userId"),
                event: "template",
                log: `${data.template_title} has been updated successfully.`,
            });
            query.invalidateQueries({ queryKey: ["template"] });
        },
    });
};

export const useDeleteTemplate = () => {
    const query = useQueryClient();
    return useMutation({
        mutationFn: async ({ id }) => (await tanstackApi.delete(`/template/delete/${id}`)).data,
        onSuccess: (_, data) => {
            timelineLog({
                user_id: localStorage.getItem("userId"),
                event: "template",
                log: `${data.title} has been deleted successfully.`,
            });
            query.invalidateQueries({ queryKey: ["template"] });
        },
    });
};
