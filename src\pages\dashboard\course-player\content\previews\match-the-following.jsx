import { useEffect, useState } from "react";

export default function MatchTheFollowing({ data, index, onComponentAnswer, correctAns }) {
    const [compValue, setCompValue] = useState(data?.answerKey || []);
    const [draggedItemIndex, setDraggedItemIndex] = useState(null);

    useEffect(() => {
        if (data?.matchOptions?.length > 0) {
            setCompValue(
                data?.matchOptions?.map((dt, i) => {
                    return {
                        ...dt,
                        correctIndex: Number(dt?.correctIndex),
                    };
                }),
            );
        }
    }, [data]);

    const handleDragStart = (index) => {
        setDraggedItemIndex(index);
    };

    const handleDragOver = (index) => {
        const draggedOverItemIndex = index;
        if (draggedItemIndex === draggedOverItemIndex) return;

        const updatedItems = [...compValue];
        const draggedItem = updatedItems[draggedItemIndex];
        updatedItems.splice(draggedItemIndex, 1);
        updatedItems.splice(draggedOverItemIndex, 0, draggedItem);

        setDraggedItemIndex(draggedOverItemIndex);
        setCompValue(updatedItems);
    };

    const handleDragEnd = () => {
        setDraggedItemIndex(null);
    };

    useEffect(() => {
        let ans = compValue?.filter((dt, idx) => dt?.correctIndex == idx + 1);

        let ppq = data?.points / compValue?.length;
        let points = ans?.length * ppq;

        onComponentAnswer(index, ans, points);
    }, [compValue]);

    return (
        <div className="comp_control">
            <div className="match_the_following">
                <label htmlFor="">
                    {data?.name || "Component title here"} <b>{data?.preference === "required" && "*"}</b>
                </label>
                <div className="options_box">
                    <div className="left_group">
                        {data.matchOptions.map((op, idx) => (
                            <div className="input_group_cntrl" key={op.label}>
                                <label>{op.label}</label>
                            </div>
                        ))}
                    </div>
                    <div className="middle_group">
                        {compValue.map((idx) => (
                            <div className="" key={idx}>
                                <i className="fa-solid fa-arrow-right"></i>
                            </div>
                        ))}
                    </div>
                    <div className="right_group">
                        {compValue.map((op, idx) => (
                            <div
                                className=""
                                key={idx}
                                draggable
                                onDragStart={() => handleDragStart(idx)}
                                onDragOver={() => handleDragOver(idx)}
                                onDragEnd={handleDragEnd}
                            >
                                <label>{op.label2}</label>
                                <i className="fa-solid fa-grip-vertical"></i>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            {data?.specialInstruction && (
                <p className="special_instruction">
                    <i className="fa-solid fa-circle-info"></i>
                    <small>{data?.specialInstruction}</small>
                </p>
            )}
        </div>
    );
}
