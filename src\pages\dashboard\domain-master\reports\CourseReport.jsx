import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { utils, writeFileXLSX } from "xlsx";

const courseStatusList = [
    { key: "completed", label: "Published" },
    { key: "pending", label: "Pending" },
    { key: "deleted", label: "Deleted" },
    { key: "hold", label: "Hold" },
];

const defaultFilterValue = { search: "", expiration_date: "", course_status: "" };

const CourseReport = () => {
    const params = useParams();
    const [dataList, setDataList] = useState([]);

    // Only store the input filter values and the "applied" filter.
    const [filterState, setFilterState] = useState(defaultFilterValue);
    const [appliedFilter, setAppliedFilter] = useState(defaultFilterValue);
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(11);

    // Fetch data on mount.
    useEffect(() => {
        getReports();
    }, []);

    const getReports = async () => {
        try {
            const res = await tanstackApi.get("reports/get-course-reports", {
                params: { domain_id: params?.domain_id },
            });
            setDataList(res?.data?.data);
        } catch (err) {
            setDataList([]);
        }
    };

    // Compute filtered data using the applied filter (only updated on Search click)
    const filteredData = useMemo(() => {
        return dataList.filter((item) => {
            const matchesSearch = appliedFilter.search
                ? item.course_title?.toLowerCase().includes(appliedFilter.search.toLowerCase())
                : true;
            const matchesExpiration = appliedFilter.expiration_date
                ? moment(item.course?.lms_course_settings?.[0]?.expiration_date).format("DD/MMM/YYYY") ===
                  moment(appliedFilter.expiration_date).format("DD/MMM/YYYY")
                : true;
            const matchesStatus = appliedFilter.course_status
                ? item.course_status === appliedFilter.course_status
                : true;
            return matchesSearch && matchesExpiration && matchesStatus;
        });
    }, [dataList, appliedFilter]);

    // Compute paginated table data
    const tableData = useMemo(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        return filteredData.slice(startIndex, startIndex + recordsPerPage);
    }, [filteredData, currentPage, recordsPerPage]);

    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    const getVisiblePages = () => {
        const pagesToShow = 3;
        const halfRange = Math.floor(pagesToShow / 2);
        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);
        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }
        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }
        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    // Apply filter on Search button click
    const onSearch = () => {
        setAppliedFilter({ ...filterState });
        setCurrentPage(1);
    };

    // Clear filter and reset search inputs
    const onClear = () => {
        setFilterState(defaultFilterValue);
        setAppliedFilter(defaultFilterValue);
        setCurrentPage(1);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    function excelConverter() {
        const finalData = filteredData.map((row) => ({
            "Course Name": row.course_title,
            Access: row.access,
            "License Course": row.licensedCourse ? "Yes" : "No",
            "Retail Course": row.retailCourse ? "Yes" : "No",
            Licenses: `${row.totalIssuedLicenses} / ${row.availableIssuedLicenses}`,
            "Trainer Name": row.trainers
                ?.map((dt) => `${dt["lms_user.first_name"]} ${dt["lms_user.last_name"]}`)
                .toString(),
            Enrollment: row.enrollments,
            Completions: row.completed,
            "Start Date": moment(row.startDate).format("DD/MMM/YYYY"),
            "Expiration Date": row.course?.lms_course_settings?.[0]?.expiration_date
                ? moment(row.course.lms_course_settings[0].expiration_date).format("DD/MMM/YYYY")
                : "---",
            Status: row.status,
            "Inprogress Users": row.progress,
            "Course Status": row.course_status,
        }));
        const wb = utils.book_new();
        utils.book_append_sheet(wb, utils.json_to_sheet(finalData));
        writeFileXLSX(wb, "Course Reports.xlsx");
    }

    return (
        <>
            <div>
                <div className="tw-mt-3 tw-flex tw-items-center tw-justify-between">
                    <div className="page_filters tw-flex tw-flex-wrap">
                        <div className="input_group">
                            <label className="tw-text-sm">Search</label>
                            <input
                                className="tw-text-sm"
                                type="text"
                                placeholder="Search by names here..."
                                name="search"
                                value={filterState.search}
                                onChange={onFilterChange}
                            />
                        </div>
                        <div className="input_group">
                            <label className="tw-text-sm">Expiration Date</label>
                            <input
                                className="tw-text-sm"
                                type="date"
                                name="expiration_date"
                                value={filterState.expiration_date}
                                onChange={onFilterChange}
                            />
                        </div>
                        <div className="input_group">
                            <label className="tw-text-sm">Course Status</label>
                            <select
                                className="tw-text-sm"
                                name="course_status"
                                value={filterState.course_status}
                                onChange={onFilterChange}
                            >
                                <option value=""> - Choose Status - </option>
                                {courseStatusList.map((status, idx) => (
                                    <option key={idx} value={status.key}>
                                        {status.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="filter_controls">
                            <button className="search_btn" onClick={onSearch}>
                                <i className="fa-solid fa-magnifying-glass"></i> Search
                            </button>
                            <button className="clear_btn" onClick={onClear}>
                                <i className="fa-solid fa-xmark"></i> Clear
                            </button>
                        </div>
                    </div>
                    <Button className="tw-px-2 tw-py-1" onClick={excelConverter}>
                        <i className="fa-solid fa-download"></i> Export Data
                    </Button>
                </div>

                <div className="custom_table tw-mt-4">
                    <table>
                        <thead>
                            <tr>
                                <th>Course Name</th>
                                <th>Access</th>
                                <th>License Course</th>
                                <th>Retail Course</th>
                                <th>Licenses</th>
                                <th>Trainers</th>
                                <th>Enrollments</th>
                                <th>Completions</th>
                                <th>Start Date</th>
                                <th>Expiration Date</th>
                                <th>Status</th>
                                <th>Inprogress Users</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData.length > 0 ? (
                                tableData.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>{row.course_title}</td>
                                        <td>
                                            <Badge className="tw-capitalize" variant="outline">
                                                {row.access.toLowerCase()}
                                            </Badge>
                                        </td>
                                        <td>{row.licensedCourse ? "Yes" : "No"}</td>
                                        <td>{row.retailCourse ? "Yes" : "No"}</td>
                                        <td>
                                            {row.totalIssuedLicenses} / {row.availableIssuedLicenses}
                                        </td>
                                        <td>
                                            {row.trainers
                                                ?.map(
                                                    (dt) => `${dt["lms_user.first_name"]} ${dt["lms_user.last_name"]}`,
                                                )
                                                .toString()}
                                        </td>
                                        <td>{row.enrollments}</td>
                                        <td>{row.completed}</td>
                                        <td>{row.startDate ? moment(row.startDate).format("LL") : "-"}</td>
                                        <td>
                                            {row.course?.lms_course_settings?.[0]?.expiration_date
                                                ? moment(row.course.lms_course_settings[0].expiration_date).format("LL")
                                                : "-"}
                                        </td>
                                        <td>
                                            <Badge className="tw-capitalize" variant="outline">
                                                {row.course_status === "completed" ? "Published" : row.course_status}
                                            </Badge>
                                        </td>
                                        <td>{row.progress}</td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan={9}>
                                        <div className="tw-flex tw-h-40 tw-items-center tw-justify-center">
                                            <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                                No reports found.
                                            </h2>
                                        </div>
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>

                {tableData.length > 0 && (
                    <div className="tw-mt-4 tw-flex tw-items-center tw-justify-between">
                        <div className="tw-flex tw-items-center tw-gap-3">
                            <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                            <Select
                                onValueChange={(e) => {
                                    setRecordsPerPage(e);
                                    setCurrentPage(1);
                                }}
                                value={recordsPerPage}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value={7}>7</SelectItem>
                                    <SelectItem value={10}>10</SelectItem>
                                    <SelectItem value={20}>20</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Pagination>
                                <PaginationContent>
                                    <PaginationItem>
                                        <PaginationPrevious
                                            href="#"
                                            onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                            disabled={currentPage === 1}
                                        />
                                    </PaginationItem>

                                    {currentPage > 3 && (
                                        <>
                                            <PaginationItem>
                                                <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                    1
                                                </PaginationLink>
                                            </PaginationItem>
                                            <PaginationItem>
                                                <PaginationEllipsis />
                                            </PaginationItem>
                                        </>
                                    )}

                                    {getVisiblePages().map((page) => (
                                        <PaginationItem key={page}>
                                            <PaginationLink
                                                href="#"
                                                isActive={page === currentPage}
                                                onClick={() => handlePageChange(page)}
                                            >
                                                {page}
                                            </PaginationLink>
                                        </PaginationItem>
                                    ))}

                                    {currentPage < totalPages - 2 && (
                                        <>
                                            <PaginationItem>
                                                <PaginationEllipsis />
                                            </PaginationItem>
                                            <PaginationItem>
                                                <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                    {totalPages}
                                                </PaginationLink>
                                            </PaginationItem>
                                        </>
                                    )}

                                    <PaginationItem>
                                        <PaginationNext
                                            href="#"
                                            onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                            disabled={currentPage === totalPages}
                                        />
                                    </PaginationItem>
                                </PaginationContent>
                            </Pagination>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
};

export default CourseReport;
