import { Accordion } from "@/pages/dashboard/course-player/content/interactions/expandable";
import { Process, Steps } from "@/pages/dashboard/course-player/content/interactions/process";
import { Reveal } from "@/pages/dashboard/course-player/content/interactions/reveal";
import { Timeline } from "@/pages/dashboard/course-player/content/interactions/tab";

export default function ContentInteractions({ template, content }) {
    const interactions = content?.interaction;
    const components = {
        Accordion: <Accordion interactions={interactions} template={template} />,
        Process: <Process interactions={interactions} template={template} />,
        Steps: <Steps interactions={interactions} template={template} />,
        Timeline: <Timeline interactions={interactions} template={template} />,
        Reveal: <Reveal interactions={interactions} template={template} />,
    };

    return (
        <div className="tw-flex tw-h-full tw-w-full tw-flex-col tw-items-center tw-justify-center">
            {components[interactions?.type]}
        </div>
    );
}
