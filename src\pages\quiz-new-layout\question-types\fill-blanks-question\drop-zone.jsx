import { cn } from "@/lib/utils";
import DragItem from "@/pages/quiz-new-layout/question-types/fill-blanks-question/drag-item";
import { useDroppable } from "@/pages/quiz-new-layout/question-types/fill-blanks-question/use-droppable";
import React from "react";

const DropZone = ({ id, onItemDropped, onItemRemoved, filledItem }) => {
    const { isOver, dropRef } = useDroppable(id, (item) => {
        if (item.type === "word") {
            onItemDropped(item.id, item.word);
        }
    });

    return (
        <div
            ref={dropRef}
            className={cn(
                `tw-mx-2 tw-inline-block tw-h-8 tw-w-28 tw-border-b-2 tw-text-center tw-align-bottom`,
                isOver ? "tw-border-orange-500" : "tw-border-orange-400",
                filledItem ? "tw-bg-orange-100" : "tw-bg-transparent",
            )}
        >
            {filledItem && filledItem.itemId ? (
                <DragItem
                    id={filledItem.itemId}
                    word={filledItem.word}
                    isPlaced={false}
                    inBlank={true}
                    onRemove={onItemRemoved}
                />
            ) : null}
        </div>
    );
};

export default DropZone;
