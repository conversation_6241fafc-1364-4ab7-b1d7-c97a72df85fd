import { cn } from "@/lib/utils";
import { useDraggable } from "@/pages/quiz-new-layout/question-types/fill-blanks-question/use-draggable";
import { XIcon } from "lucide-react";
import React from "react";

const DragItem = ({ word, id, isPlaced, inBlank = false, onRemove }) => {
    const { isDragging, dragHandleProps } = useDraggable(id, { type: "word", word }, false);

    if (isPlaced && !inBlank) return null;

    return (
        <div
            {...dragHandleProps}
            className={cn(
                "tw-relative tw-flex tw-cursor-grab tw-select-none tw-items-center tw-justify-center tw-rounded tw-border-2 tw-border-orange-700 tw-bg-orange-600 tw-px-2 tw-py-1 tw-text-center tw-text-sm tw-font-medium tw-text-white tw-transition-colors hover:tw-bg-orange-500",
                isDragging ? "tw-opacity-50" : "tw-opacity-100",
            )}
        >
            {word}
            {inBlank && onRemove && (
                <button
                    onClick={onRemove}
                    className="tw-absolute -tw-right-2 -tw-top-2 tw-flex tw-h-5 tw-w-5 tw-items-center tw-justify-center tw-rounded-full tw-bg-red-500 tw-text-xs tw-text-white hover:tw-bg-red-600"
                >
                    <XIcon className="tw-size-3" />
                </button>
            )}
        </div>
    );
};

export default DragItem;
