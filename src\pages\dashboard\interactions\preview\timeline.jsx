import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { colorsList } from "@/data/colors";
import { cn, isUrl } from "@/lib/utils";
import { useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import { useFileUpload } from "@/react-query/common";
import { motion } from "framer-motion";
import { Upload, X } from "lucide-react";
import { useCallback, useRef, useState } from "react";

export function Timeline({ template }) {
    const { tabList } = useEditInteraction();
    const ref = useRef(null);
    const [index, setIndex] = useState(0);
    const handlePrevSlide = () => {
        if (index === 0) return;
        setIndex((prev) => prev - 1);
    };

    const handleNextSlide = () => {
        if (tabList.length - 1 === index) return;
        setIndex((prev) => prev + 1);
    };

    return (
        <>
            <div className="tw-relative tw-pt-7">
                <div className="tw-flex tw-h-full tw-w-full tw-flex-wrap tw-gap-[1rem]" ref={ref}>
                    <motion.div className="tw-h-full tw-w-full tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-bg-white tw-text-[40px] tw-text-black">
                        <Tabs
                            value={tabList[index]?.label}
                            onValueChange={(val) => setIndex(tabList.findIndex((dt) => dt?.label == val))}
                            className="w-[600px] tw-h-full"
                        >
                            <TabsList className="tw-absolute tw-left-0 tw-right-0 tw-top-2 tw-gap-4 tw-bg-transparent">
                                {tabList.map((step, index) => {
                                    return (
                                        <TabsTrigger
                                            key={index}
                                            value={step.label}
                                            className="tw-rounded-2xl tw-border-4 tw-border-[#3B3A3E] tw-bg-[#FFE19A] tw-px-6 tw-font-lazyDog !tw-text-[22px] tw-leading-none tw-text-[#3B3A3E] data-[state=active]:tw-bg-[#F8CA5C] data-[state=active]:tw-text-[#3B3A3E] 2xl:!tw-text-[36px]"
                                        >
                                            {index + 1}. {step.label}
                                        </TabsTrigger>
                                    );
                                })}
                            </TabsList>
                            <div className="tw-h-full tw-min-h-96 tw-w-full tw-border-4 tw-border-black tw-px-4 tw-pb-4 tw-pt-8">
                                {tabList.map((step, index) => (
                                    <TabItem key={step.label + index} index={index} item={step} />
                                ))}
                            </div>
                        </Tabs>
                    </motion.div>
                </div>
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    onClick={handlePrevSlide}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px]",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}

function TabItem({ item, index }) {
    const { handleTabChange } = useEditInteraction();
    const [titleEditable, setTitleEditable] = useState(false);
    const [descriptionEditable, setDescriptionEditable] = useState(false);
    const titleRef = useRef(null);
    const descriptionRef = useRef(null);
    const upload = useFileUpload();

    const handleImageChange = useCallback(async (e) => {
        let imageUrl = "";
        try {
            if (e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                imageUrl = response?.fileUrl;
            }
        } catch (error) {
            imageUrl = "";
        } finally {
            handleTabChange("image_src", imageUrl, index);
        }
    }, []);

    const removeImage = useCallback(() => {
        handleTabChange("image_src", "", index);
    }, []);

    const handleTitleEditable = useCallback(() => {
        setTitleEditable((prev) => !prev);
    }, []);

    const handleDescriptionEditable = useCallback(() => {
        setDescriptionEditable((prev) => !prev);
    }, []);

    const handleTitleBlur = useCallback(() => {
        handleTabChange("label", titleRef?.current?.innerText, index);
    }, []);

    const handleDescriptionBlur = useCallback(() => {
        handleTabChange("description", descriptionRef?.current?.innerText, index);
    }, []);

    return (
        <TabsContent
            value={item.label}
            className="tw-relative tw-grid tw-size-full tw-h-full tw-grid-cols-2 tw-grid-rows-1 tw-gap-4"
        >
            <div className="tw-absolute tw-right-0 tw-top-0 tw-z-20 tw-flex">
                {isUrl(item.image_src) ? (
                    <Button
                        variant="outline"
                        className="aspect-square max-sm:p-0 tw-text-gray-700"
                        onClick={removeImage}
                    >
                        <X className="opacity-60" size={16} strokeWidth={2} aria-hidden="true" />
                        <Label className="max-sm:sr-only">Remove</Label>
                    </Button>
                ) : (
                    <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10 tw-text-gray-700">
                        <Upload className="opacity-60" size={16} strokeWidth={2} aria-hidden="true" />
                        <input
                            onChange={handleImageChange}
                            type="file"
                            style={{ display: "none" }}
                            id="bg_image"
                            accept="image/*"
                        />
                        <Label htmlFor="bg_image" className="max-sm:sr-only">
                            {upload.isPending ? "Uploading..." : "Upload Image"}
                        </Label>
                    </Button>
                )}
            </div>
            <div className="tw-font-lazyDog">
                <h2
                    ref={titleRef}
                    contentEditable={titleEditable}
                    onDoubleClick={handleTitleEditable}
                    onBlur={handleTitleBlur}
                    className="tw-text-[30px] 2xl:tw-text-[45px]"
                >
                    {item.label}
                </h2>
                <div
                    ref={descriptionRef}
                    contentEditable={descriptionEditable}
                    onDoubleClick={handleDescriptionEditable}
                    onBlur={handleDescriptionBlur}
                    className="tw-text-base"
                    dangerouslySetInnerHTML={{ __html: item.description }}
                />
            </div>
            <div className="tw-relative tw-size-full">
                {isUrl(item.image_src) && (
                    <img
                        className="tw-absolute tw-inset-0 tw-size-full tw-rounded-xl tw-object-cover"
                        src={item.image_src}
                        alt={item.label}
                    />
                )}
                <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/40"></div>
            </div>
        </TabsContent>
    );
}

export function TimelinePreview() {
    const list = [0, 1, 2, 3];

    return (
        <>
            <div className="tw-relative tw-pt-7">
                <div className="tw-flex tw-h-full tw-w-full tw-flex-wrap tw-gap-[1rem]">
                    <motion.div className="tw-h-full tw-w-full tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-bg-white tw-text-[40px] tw-text-black">
                        <div className="tw-flex tw-w-full tw-translate-y-1 tw-items-center tw-justify-center tw-gap-2">
                            {list.map((step) => {
                                return (
                                    <div
                                        style={{
                                            backgroundColor: colorsList[step],
                                        }}
                                        key={step}
                                        className="tw-rounded-2xl tw-border-4 tw-px-2 tw-font-lazyDog tw-leading-none"
                                    />
                                );
                            })}
                        </div>

                        <div className="tw-grid tw-size-full tw-h-full tw-min-h-20 tw-grid-cols-2 tw-grid-rows-1 tw-gap-4 tw-rounded-md tw-border tw-bg-slate-300"></div>
                    </motion.div>
                </div>
            </div>
        </>
    );
}
