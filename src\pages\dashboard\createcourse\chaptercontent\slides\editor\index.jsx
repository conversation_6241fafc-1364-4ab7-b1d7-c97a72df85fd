import BubbleMenu from "@/pages/dashboard/createcourse/chaptercontent/slides/editor/bubble-menu";
import MenuBar from "@/pages/dashboard/createcourse/chaptercontent/slides/editor/menu-bar";
import { EditorContent } from "@tiptap/react";

export default function TipTapEditor({ editor }) {
    return (
        <div className="tw-relative tw-w-full tw-max-w-full tw-overflow-y-auto">
            <MenuBar editor={editor} />
            <EditorContent editor={editor} />
            <BubbleMenu editor={editor} />
        </div>
    );
}
