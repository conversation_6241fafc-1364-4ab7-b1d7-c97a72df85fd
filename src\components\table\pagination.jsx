import {
    PaginationContent,
    Pa<PERSON>ationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
    Pagination as ReactPagination,
} from "@/components/ui/pagination";
import { usePagination } from "@/hooks/use-pagination";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function Pagination({ currentPage, totalPages, handlePageChange }) {
    const navigate = useNavigate();
    const { pages } = usePagination({
        currentPage,
        totalPages,
        paginationItemsToDisplay: 5,
    });

    useEffect(() => {
        if (currentPage) {
            const searchParams = new URLSearchParams(window.location.search);
            searchParams.set("page", currentPage);
            navigate(`?${searchParams.toString()}`);
        }
    }, [currentPage]);

    const handleChange = (page) => {
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.set("page", currentPage);
        navigate(`?${searchParams.toString()}`);
        if (handlePageChange) handlePageChange(page);
    };

    return (
        <ReactPagination className="tw-mx-0 tw-w-[auto]">
            <PaginationContent>
                <PaginationItem>
                    <PaginationPrevious
                        onClick={() => handleChange(Math.max(currentPage - 1, 1))}
                        disabled={currentPage === 1}
                    />
                </PaginationItem>

                {pages.map((page, index) => {
                    const key = `${page}_${index}`;
                    if (page === -1) {
                        return (
                            <PaginationItem key={key}>
                                <PaginationEllipsis />
                            </PaginationItem>
                        );
                    }
                    return (
                        <PaginationItem key={key}>
                            <PaginationLink isActive={page === currentPage} onClick={() => handleChange(page)}>
                                {page}
                            </PaginationLink>
                        </PaginationItem>
                    );
                })}

                <PaginationItem>
                    <PaginationNext
                        onClick={() => {
                            if (currentPage !== totalPages) handleChange(Math.min(currentPage + 1, totalPages));
                        }}
                        disabled={currentPage === totalPages}
                    />
                </PaginationItem>
            </PaginationContent>
        </ReactPagination>
    );
}
