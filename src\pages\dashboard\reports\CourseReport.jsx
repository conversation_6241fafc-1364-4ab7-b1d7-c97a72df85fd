import {
    <PERSON><PERSON><PERSON>rumb,
    Bread<PERSON><PERSON>bI<PERSON>,
    Bread<PERSON>rumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import Pagination from "@/components/table/pagination";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { utils, writeFileXLSX } from "xlsx";

const ITEMS_PER_PAGE = 7;

const courseStatusList = [
    {
        key: "completed",
        label: "Published",
    },
    {
        key: "pending",
        label: "Pending",
    },
    {
        key: "deleted",
        label: "Deleted",
    },
    {
        key: "hold",
        label: "Hold",
    },
];

const defaultValue = {
    search: "",
    expiration_date: "",
    course_status: "",
};

const CourseReport = () => {
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(ITEMS_PER_PAGE);
    const [filterState, setFilterState] = useState(defaultValue);
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        if (filteredData?.length > 0) {
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;
            let options = filteredData.slice(startIndex, endIndex);
            setTableData(options);
        }
    }, [currentPage, filteredData]);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.course_title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const expiration = filterState?.expiration_date
                ? moment(item.course?.lms_course_settings?.[0]?.expiration_date).format("DD/MMM/YYYY") ===
                  moment(filterState?.expiration_date).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            const matchesEvent = filterState?.course_status ? item?.course_status === filterState?.course_status : true; // Allow all items if no subCategory filter

            return matchesSearch && expiration && matchesEvent; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState(defaultValue);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getReports();
    }, []);

    const getReports = async () => {
        await tanstackApi
            .get("reports/get-course-reports")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    function excelConverter() {
        var finalData = filteredData?.map((row, index) => {
            return {
                "Course Name": row?.course_title,
                Access: row?.access,
                "License Course": row?.licensedCourse ? "Yes" : "No",
                "Retail Course": row?.retailCourse ? "Yes" : "No",
                Licenses: `${row?.totalIssuedLicenses} / ${row?.availableIssuedLicenses}`,
                "Trainer Name": row?.trainers
                    ?.map((dt) => `${dt["lms_user.first_name"]} ${dt["lms_user.last_name"]}`)
                    .toString(),
                Enrollment: row?.enrollments,
                Completions: row?.completed,
                "Start Date": moment(row?.startDate).format("DD/MMM/YYYY"),
                "Expiration Date":
                    moment(row.course?.lms_course_settings?.[0]?.expiration_date).format("DD/MMM/YYYY") ||
                    moment(row?.startDate).add(1, "year").format("LL"),
                Status: row?.status,
                "Inprogress Users": row?.progress,
                "Course Status": row?.course_status,
            };
        });
        const wb = utils.book_new();
        utils.book_append_sheet(wb, utils.json_to_sheet(finalData));
        writeFileXLSX(wb, "Course Reports.xlsx");
    }

    return (
        <>
            <div>
                <div className="tw-flex tw-justify-between">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink href="#">Reports</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Course Reports</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <Button className="tw-px-2 tw-py-1" onClick={excelConverter}>
                        <i className="fa-solid fa-download"></i> Export Data
                    </Button>
                </div>
                {/* <br /> */}
                <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label htmlFor="">Search</label>
                        <input
                            type="text"
                            placeholder="Search by names here..."
                            onChange={onFilterChange}
                            value={filterState?.search}
                            name="search"
                        />
                    </div>
                    <div className="input_group">
                        <label htmlFor="">Expiration date</label>
                        <input
                            type="date"
                            onChange={onFilterChange}
                            value={filterState?.expiration_date}
                            name="expiration_date"
                        />
                    </div>
                    <div className="input_group">
                        <label htmlFor="">Course Status</label>
                        <select onChange={onFilterChange} value={filterState?.course_status} name="course_status">
                            <option value=""> - Choose Status - </option>
                            {courseStatusList?.map((status, idx) => (
                                <option key={idx} value={status?.key}>
                                    {status?.label}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>Course Name</th>
                                <th>Access</th>
                                <th>License Course</th>
                                <th>Retail Course</th>
                                <th>Licenses</th>
                                <th>Trainers</th>
                                <th>Enrollments</th>
                                <th>Completions</th>
                                <th>Start Date</th>
                                <th>Expiration Date</th>
                                <th>Status</th>
                                <th>Inprogress Users</th>
                            </tr>
                        </thead>
                        <tbody>
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>{row?.course_title}</td>
                                    <td>
                                        <Badge className="tw-capitalize" variant={"outline"}>
                                            {row?.access.toLowerCase()}
                                        </Badge>
                                    </td>
                                    <td>{row?.licensedCourse ? "Yes" : "No"}</td>
                                    <td>{row?.retailCourse ? "Yes" : "No"}</td>
                                    <td>
                                        {row?.totalIssuedLicenses} / {row?.availableIssuedLicenses}
                                    </td>
                                    <td>
                                        {row?.trainers
                                            ?.map((dt) => `${dt["lms_user.first_name"]} ${dt["lms_user.last_name"]}`)
                                            .toString()}
                                    </td>
                                    <td>{row?.enrollments}</td>
                                    <td>{row?.completed}</td>
                                    <td>{row?.startDate ? moment(row?.startDate).format("LL") : "-"}</td>
                                    <td>
                                        {row.course?.lms_course_settings?.[0]?.expiration_date
                                            ? moment(row.course?.lms_course_settings?.[0]?.expiration_date).format("LL")
                                            : moment(row?.startDate).add(1, "year").format("LL")}
                                    </td>
                                    <td>
                                        <Badge className="tw-capitalize" variant={"outline"}>
                                            {row?.course_status == "completed" ? "Published" : row?.course_status}
                                        </Badge>
                                    </td>
                                    <td>{row?.progress}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            handlePageChange={handlePageChange}
                            itemsPerPage={ITEMS_PER_PAGE}
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export default CourseReport;
