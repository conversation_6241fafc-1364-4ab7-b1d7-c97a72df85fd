import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import FileUploadSetting from "./FileUploadSetting";
import LayoutSetting from "./LayoutSetting";
import MediaSetting from "./MediaSetting";
import SelectFileSetting from "./SelectFileSetting";

let content_mapping = {
    HTML: "Layouts",
    ASSIGNMENT: "Select File",
    HOMEWORK: "Select File",
    QUIZ: "Select File",
    ZIP: "Upload",
    EMBED: "Upload",
    MP4: "Upload",
    MP3: "Upload",
    INTERACTIONS: "Select File",
};

const options_mapping = {
    HTML: [
        { label: "Layouts", icon: "fa-solid fa-table-columns" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    ASSIGNMENT: [
        { label: "Select File", icon: "fa-regular fa-folder-open" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    HOMEWORK: [
        { label: "Select File", icon: "fa-regular fa-folder-open" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    QUIZ: [
        { label: "Select File", icon: "fa-regular fa-folder-open" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    ZIP: [
        { label: "Upload", icon: "fa-solid fa-cloud-arrow-up" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    EMBED: [
        { label: "Upload", icon: "fa-solid fa-cloud-arrow-up" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    MP4: [
        { label: "Upload", icon: "fa-solid fa-cloud-arrow-up" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    MP3: [
        { label: "Upload", icon: "fa-solid fa-cloud-arrow-up" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
    INTERACTIONS: [
        { label: "Select File", icon: "fa-regular fa-folder-open" },
        { label: "Media", icon: "fa-solid fa-images" },
    ],
};

const SlideSettings = ({
    open,
    setOpen,
    setContentArray,
    contentArray,
    selectedContent,
    INDEX,
    setSelectedContent,
}) => {
    const [defaultSlide, setDefaultSlide] = useState(options_mapping[selectedContent?.content_type]?.[0]?.label);

    useEffect(() => {
        setDefaultSlide(options_mapping[selectedContent?.content_type]?.[0]?.label);
    }, [selectedContent?.content_type]);

    const onSaveChanges = (e) => {
        e.preventDefault();
        let options = [...contentArray];
        options[INDEX] = selectedContent;
        setContentArray(options);
        setOpen(false);
    };

    return (
        <div className="tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-grid-rows-[1fr_3rem] tw-gap-4">
            <Tabs
                value={defaultSlide}
                className="custom_scrollbar tw-overflow-y-auto tw-pr-1"
                onValueChange={setDefaultSlide}
            >
                <TabsList className="tw-h-fit tw-w-full tw-flex-wrap tw-items-start !tw-justify-start">
                    {(
                        options_mapping[selectedContent?.content_type] || [
                            { label: "Layouts", icon: "fa-solid fa-table-columns" },
                            { label: "Media", icon: "fa-solid fa-images" },
                        ]
                    )?.map((data, idx) => (
                        <TabsTrigger className="tw-gap-2" value={data.label} key={idx}>
                            <i className={data.icon}></i> {data.label}
                        </TabsTrigger>
                    ))}
                </TabsList>
                <TabsContent value="Upload">
                    <FileUploadSetting setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
                <TabsContent value="Select File">
                    <SelectFileSetting setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
                <TabsContent value="Media">
                    <MediaSetting setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
                <TabsContent value="Layouts">
                    <LayoutSetting setContentData={setSelectedContent} contentData={selectedContent} />
                </TabsContent>
            </Tabs>
            <div className="tw-flex tw-gap-2">
                <Button variant="outline">
                    <i className="fa-solid fa-xmark"></i> Cancel
                </Button>
                <Button type="submit" onClick={onSaveChanges}>
                    <i className="fa-solid fa-save"></i> Save changes
                </Button>
            </div>
        </div>
    );
};

export default SlideSettings;
