import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { PermissionProvider, usePermission } from "@/pages/dashboard/roles/permission/provider";
import { useGetPermissionListing, useUpdatePermission } from "@/react-query/permission";
import { ChevronUp, Edit, Save, Undo2, X } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { toast } from "sonner";

export default function RolePermissionPage() {
    return (
        <PermissionProvider>
            <Container />
        </PermissionProvider>
    );
}

function Container() {
    const params = useParams();
    const permissionListing = useGetPermissionListing(params.role_code);
    const { listing, setListing, edit, setEdit } = usePermission();
    const updatePermission = useUpdatePermission();

    useEffect(() => {
        if (permissionListing.status == "success") setListing(permissionListing.data.data);
    }, [permissionListing.status]);

    useEffect(() => {
        if (updatePermission.status === "success" || updatePermission.status === "error") {
            const message =
                updatePermission.status === "success"
                    ? { title: "Updated successfully", description: updatePermission.data.message }
                    : { title: "Something went wrong", description: updatePermission.error.response.data.message };
            toast[updatePermission.status](message.title, { description: message.description });
        }
    }, [updatePermission.status]);

    if (permissionListing.isLoading) return <div>Loading...</div>;

    return (
        <div>
            <div className="tw-mb-5 tw-flex tw-items-center tw-justify-between tw-gap-2">
                <h1 className="tw-font-lexend tw-text-lg tw-font-semibold">Permission</h1>
                <div className="tw-flex tw-gap-4">
                    <Button variant="secondary" asChild>
                        <Link to={`/dashboard/roles`}>
                            <Undo2 /> Back
                        </Link>
                    </Button>
                    {edit && (
                        <Button
                            onClick={() => {
                                updatePermission.mutate({
                                    role_code: params.role_code,
                                    permissionsList: listing,
                                });
                            }}
                            variant="success-outline"
                        >
                            <Save />
                            Save
                        </Button>
                    )}

                    <Button onClick={() => setEdit(!edit)} variant={edit ? "destructive-outline" : "outline"}>
                        {edit ? (
                            <>
                                <X />
                                Cancel
                            </>
                        ) : (
                            <>
                                <Edit />
                                Edit
                            </>
                        )}
                    </Button>
                </div>
            </div>
            <div className="tw-grid tw-grid-cols-5 tw-gap-x-4 tw-divide-y">
                <div className="tw-col-span-5 tw-grid tw-grid-cols-subgrid tw-bg-slate-100 tw-p-3 tw-font-lexend">
                    <div className="">Module Name</div>
                    <div className="tw-text-center">CREATE</div>
                    <div className="tw-text-center">READ</div>
                    <div className="tw-text-center">UPDATE</div>
                    <div className="tw-text-center">DELETE</div>
                </div>
                {listing.map((dt) => (
                    <ModuleItem item={dt} key={dt.id} />
                ))}
            </div>
        </div>
    );
}

const permissionTypes = ["CREATE", "READ", "UPDATE", "DELETE"];

function ModuleItem({ item }) {
    const [open, setOpen] = useState(false);
    const { onPermissionChange } = usePermission();
    return (
        <>
            <div className="tw-col-span-5 tw-grid tw-grid-cols-subgrid tw-p-3 tw-font-lexend hover:tw-bg-slate-100">
                <div className="font-medium tw-text-purple-700" onClick={() => setOpen(!open)}>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <p>{item.module_code.split("_").join(" ").split("-").join(" ")}</p>
                        {item.sub_modules.length > 0 && (
                            <ChevronUp className={cn("tw-transform tw-transition-all", open ? "" : "tw-rotate-180")} />
                        )}
                    </div>
                </div>
                {permissionTypes.map((pt) => {
                    return (
                        <div className="tw-text-center" key={pt}>
                            {item.possible_permissions.includes(pt) ? (
                                <PermissionCheckbox
                                    type={pt}
                                    permissions={item.permissions}
                                    onChange={(value) => onPermissionChange({ permission: pt, value, id: item.id })}
                                />
                            ) : null}
                        </div>
                    );
                })}
            </div>
            {item.sub_modules.length > 0 &&
                open &&
                item.sub_modules.map((sub) => <SubModuleItem key={sub.id} item={sub} />)}
        </>
    );
}

function SubModuleItem({ item }) {
    const { onSubPermissionChange } = usePermission();
    return (
        <div className="tw-col-span-5 tw-grid tw-grid-cols-subgrid tw-p-3 tw-font-lexend hover:tw-bg-slate-100">
            <div className="font-medium tw-text-teal-700">
                {item.sub_module_code.split("_").join(" ").split("-").join(" ")}
            </div>
            {permissionTypes.map((pt) => {
                return (
                    <div className="tw-text-center" key={pt}>
                        {item.possible_permissions.includes(pt) ? (
                            <PermissionCheckbox
                                type={pt}
                                permissions={item.permissions}
                                onChange={(value) =>
                                    onSubPermissionChange({
                                        permission: pt,
                                        value,
                                        id: item.id,
                                        parent: item.module_id,
                                    })
                                }
                            />
                        ) : null}
                    </div>
                );
            })}
        </div>
    );
}

function PermissionCheckbox({ permissions, type, onChange }) {
    const { edit } = usePermission();
    return (
        <div className="tw-flex tw-size-full tw-items-center tw-justify-center tw-gap-2">
            <Checkbox
                onCheckedChange={onChange}
                checked={permissions.includes(type)}
                className="disabled:tw-cursor-not-allowed disabled:tw-opacity-50"
                disabled={!edit}
            />
        </div>
    );
}
