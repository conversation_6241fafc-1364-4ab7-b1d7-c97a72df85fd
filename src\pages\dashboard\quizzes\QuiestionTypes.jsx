import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

export const quizContentTypes = [
    {
        title: "Singlechoice",
        contentType: "singlechoice",
        icon: "/assets/singlechoice.png",
        data: {
            name: "Singlechoice question title here",
            type: "singlechoice",
            question: "",
            points: 100,
            options: [
                {
                    src: "",
                    label: "Option A",
                    isCorrect: false,
                },
                {
                    src: "",
                    label: "Option B",
                    isCorrect: false,
                },
                {
                    src: "",
                    label: "Option C",
                    isCorrect: true,
                },
                {
                    src: "",
                    label: "Option D",
                    isCorrect: false,
                },
            ],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have chosen the option",
                incorrectAns: "Chosen the wrong option",
                partiallyCorrectAns: "That's not exactly the correct option",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "singlechoice",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            styles: {
                answer: {
                    color: "#000000",
                    fontSize: "40px",
                    fontFamily: "Lazy Dog",
                },
                default: {
                    borderColor: "#000000",
                    borderStyle: "solid",
                    borderWidth: "1px",
                },
                question: {
                    color: "#000000",
                    fontSize: "40px",
                    fontFamily: "Lazy Dog",
                },
                selected: {
                    color: "#ffffff",
                    borderColor: "#3e86f9",
                    borderStyle: "solid",
                    borderWidth: "1px",
                    backgroundColor: "#3e86f9",
                },
            },
            specialInstruction: "Choose the correct option out of below mentioned options",
            question_background: "",
        },
    },
    {
        title: "Singlechoice Media",
        contentType: "singlechoice_media",
        icon: "/assets/singlechoice_media.png",
        data: {
            name: "Singlechoice with images your question title here",
            type: "singlechoice_media",
            question: "",
            points: 100,
            options: [
                {
                    src: "/assets/thumbnail.png",
                    label: "image1",
                    isCorrect: true,
                },
                {
                    src: "/assets/thumbnail.png",
                    label: "image2",
                    isCorrect: false,
                },
                {
                    src: "/assets/thumbnail.png",
                    label: "image3",
                    isCorrect: false,
                },
            ],
            dropzone: [],
            dropdown_options: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have chosen the option",
                incorrectAns: "Chosen the wrong option",
                partiallyCorrectAns: "That's not exactly the correct option",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "singlechoice_media",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Choose the correct option out of below mentioned options",
            question_background: "",
        },
    },
    {
        title: "Fill in the blanks",
        contentType: "fillin_the_blank",
        icon: "/assets/fillin_the_blanks.png",
        data: {
            name: "Fill in the blanks question title here",
            type: "fillin_the_blank",
            question:
                "Lorem ipsum dolor sit amet [field] consectetur [field] adipiscing elit [field]. Aenean linkage, si habet duo vel plures rigid links quae moventur respectu ad rigid link, vocatur [field] point mechanismi ut moveat vel vim generet, vocatur [field].",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [
                {
                    label: "Option A",
                    correctIndex: 1,
                },
                {
                    label: "Option B",
                    correctIndex: 2,
                },
                {
                    label: "Option C",
                    correctIndex: 3,
                },
                {
                    label: "Option D",
                    correctIndex: 4,
                },
            ],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "fillin_the_blank",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Drage the below mentioned options into blank fields",
            question_background: "",
        },
    },
    {
        title: "Drag & Drop Image",
        contentType: "dnd_image_box",
        icon: "/assets/drag_drop_media.png",
        data: {
            name: "Drag & Drop Image question title here",
            type: "dnd_image_box",
            question: "Drag the image on correct placeholder make sure the label is correct for image.",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [
                {
                    id: "Zone One",
                    zone: "Zone One",
                },
                {
                    id: "Zone Two",
                    zone: "Zone Two",
                },
                {
                    id: "Zone Three",
                    zone: "Zone Three",
                },
            ],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "dnd_image_box",
            draggable_options: [
                {
                    id: "image1",
                    src: "",
                    zone: "Zone One",
                },
                {
                    id: "image2",
                    src: "",
                    zone: "Zone Two",
                },
                {
                    id: "image3",
                    src: "",
                    zone: "Zone Three",
                },
                {
                    id: "image4",
                    src: "",
                    zone: "",
                },
            ],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Drag the image on correct placeholder make sure the label is correct for image.",
            question_background: "",
        },
    },
    {
        title: "Sequence Arrange",
        contentType: "sequence_arrange",
        icon: "/assets/sequence_arrange.png",
        data: {
            name: "Sequence arrange question title here",
            type: "sequence_arrange",
            question:
                "Throughout history, people have been trying to reach the peak of Mt. Everest. Put these historical events in the correct order.",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 2,
                    label: "",
                    imageLabel: "",
                },
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 1,
                    label: "",
                    imageLabel: "",
                },
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 4,
                    label: "",
                    imageLabel: "",
                },
                {
                    text: "Sequence option string details",
                    imageText: "",
                    correctIndex: 3,
                    label: "",
                    imageLabel: "",
                },
            ],
            optionColumns: "",
            componentTypeId: "sequence_arrange",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Arrange the options in the correct sequence mentioned below.",
            question_background: "",
        },
    },
    {
        title: "String dropdown",
        contentType: "string_dropdown",
        icon: "/assets/dropdown.png",
        data: {
            name: "String dropdown question title here",
            type: "string_dropdown",
            question:
                "Lorem ipsum dolor sit amet {0} area montis saepe censetur Khumbu {1}, quod praesertim periculosum est ob {2} motum glaciei.",
            points: 100,
            options: [],
            dropdown_options: [
                {
                    id: "label_0",
                    correct: "Option B",
                    options: ["Option A", "Option B", "Option C"],
                },
                {
                    id: "label_1",
                    correct: "Option B",
                    options: ["Option A", "Option B", "Option C"],
                },
                {
                    id: "label_2",
                    correct: "Option B",
                    options: ["Option A", "Option B", "Option C"],
                },
            ],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "string_dropdown",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
    {
        title: "Match the following",
        contentType: "match_the_following",
        icon: "/assets/match_the_following.png",
        data: {
            name: "Match the following question title here",
            type: "match_the_following",
            question: "Complete the gaps using the appropriate words from the box",
            points: 100,
            options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [
                {
                    id: "1",
                    text: "Option 1",
                    label: "Label A",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 2,
                },
                {
                    id: "2",
                    text: "Option 2",
                    label: "Label B",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 1,
                },
                {
                    id: "3",
                    text: "Option 3",
                    label: "Label C",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 3,
                },
                {
                    id: "4",
                    text: "Option 4",
                    label: "Label D",
                    imageText: "",
                    imageLabel: "",
                    correctIndex: 4,
                },
            ],
            optionColumns: "",
            componentTypeId: "match_the_following",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "Complete the gaps using the appropriate words from the box.",
            question_background: "",
        },
    },
    {
        title: "Hotspot Drag & Drop",
        contentType: "hotspot_dnd",
        icon: "/assets/hotspot_dnd.png",
        data: {
            name: "Hotspot Drag & Drop question title here",
            type: "hotspot_dnd",
            question: "",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [
                {
                    x: 0.27666666666666667,
                    y: 0.1285714285714285,
                    src: "",
                    zone: "A",
                },
                {
                    x: 0.6016666666666666,
                    y: 0.11142857142857145,
                    src: "",
                    zone: "B",
                },
                {
                    x: 0.45333333333333314,
                    y: 0.4457142857142858,
                    src: "",
                    zone: "C",
                },
                {
                    x: 0.27999999999999997,
                    y: 0.7542857142857141,
                    src: "",
                    zone: "D",
                },
                {
                    x: 0.6266666666666666,
                    y: 0.7314285714285714,
                    src: "",
                    zone: "E",
                },
            ],
            hotspots: [
                {
                    x: 0,
                    y: 0.1831428571428571,
                    src: "",
                    name: "One",
                    correct: "A",
                },
                {
                    x: 0,
                    y: 0.5948571428571428,
                    src: "",
                    name: "Two",
                    correct: "C",
                },
                {
                    x: 0.12133333333333332,
                    y: 0.43800000000000006,
                    src: "",
                    name: "Three",
                    correct: "B",
                },
                {
                    x: 0.14333333333333334,
                    y: 0.18114285714285716,
                    src: "",
                    name: "Four",
                    correct: "D",
                },
                {
                    x: 0,
                    y: 0.3414285714285714,
                    src: "",
                    name: "Five",
                    correct: "E",
                },
            ],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "hotspot_dnd",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
    {
        title: "Hotspot Drag & Drop Media",
        contentType: "hotspot_dnd_image",
        icon: "/assets/hotspot_dnd_media.png",
        data: {
            name: "Hotspot Drag & Drop Media question title here",
            type: "hotspot_dnd_image",
            question: "",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [
                {
                    x: 0.6461111111111111,
                    y: 0.4037142857142857,
                    src: "",
                    zone: "A",
                },
                {
                    x: 0.6366666666666667,
                    y: 0.7714285714285715,
                    src: "",
                    zone: "B",
                },
                {
                    x: 0.6383333333333333,
                    y: 0.07514285714285718,
                    src: "",
                    zone: "C",
                },
                {
                    x: 0.4133333333333334,
                    y: 0.4608571428571429,
                    src: "",
                    zone: "D",
                },
            ],
            hotspots: [
                {
                    x: 0,
                    y: 0.1973469387755217,
                    src: "",
                    name: "Option 1",
                    correct: "A",
                },
                {
                    x: 0.16526666666666662,
                    y: 0.6212734693878729,
                    src: "",
                    name: "Option 2",
                    correct: "C",
                },
                {
                    x: 0,
                    y: 0.7804244897959461,
                    src: "",
                    name: "Option 3",
                    correct: "B",
                },
                {
                    x: 0,
                    y: 0.4989142857143705,
                    src: "",
                    name: "Option 4",
                    correct: "D",
                },
            ],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "hotspot_dnd_image",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
    {
        title: "Long Answer",
        contentType: "long_answer",
        icon: "/assets/long_answer.png",
        data: {
            name: "Long Answer question title here",
            type: "long_answer",
            question:
                "Lorem ipsum dolor sit, amet consectetur adipisicing elit. Vitae ut nam, suscipit sunt soluta laudantium quos facilis quibusdam enim saepe.",
            points: 100,
            options: [],
            dropdown_options: [],
            dropzone: [],
            hotspotDropzone: [],
            hotspots: [],
            feedbacks: {
                correctAns: "You have dragged all the options perfectly",
                incorrectAns: "Dragged the wrong options at all places",
                partiallyCorrectAns: "That's not exactly the correct options",
            },
            styles: {
                answer: {
                    color: "#000000",
                    fontSize: "40px",
                    fontFamily: "Lazy Dog",
                    borderColor: "#3e86f9",
                    borderStyle: "solid",
                    borderWidth: "2px",
                    backgroundColor: "white",
                },
                question: {
                    color: "#000000",
                    fontSize: "40px",
                    fontFamily: "Lazy Dog",
                },
            },
            fillOptions: [],
            matchOptions: [],
            optionColumns: "",
            componentTypeId: "long_answer",
            draggable_options: [],
            question_thumbnail: "/assets/thumbnail.png",
            specialInstruction: "",
            question_background: "",
        },
    },
];

const QuiestionTypes = ({ open, setOpen, setComponentsArray, componentsArray, onSlideEdit }) => {
    const onSelectContent = (content) => {
        let option = {
            ...content?.data,
        };
        setComponentsArray([...componentsArray, option]);
        setOpen(false);
        onSlideEdit(option, componentsArray?.length);
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-2xl">
                    <div className="tw-flex tw-justify-center">
                        <div className="tw-grid tw-w-full tw-grid-cols-4">
                            {quizContentTypes?.map((content, idx) => {
                                const inProgress = content?.inDevelopement;
                                if (inProgress && import.meta.env.DEV) return;
                                return (
                                    <div
                                        key={idx}
                                        onClick={() => onSelectContent(content)}
                                        className="tw-flex tw-cursor-pointer tw-flex-col tw-items-center tw-gap-2 tw-rounded-xl tw-py-5 hover:tw-bg-slate-100"
                                    >
                                        <img className="tw-w-[55px]" src={content?.icon} alt="" />
                                        <Label className="tw-text-center">{content?.title}</Label>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default QuiestionTypes;
