import { useGetUserCourse } from "@/react-query/courses";
import { useNavigate } from "react-router-dom";

const UserCoursesDash = () => {
    const navigate = useNavigate();
    const courses = useGetUserCourse();

    const onRedirectToCourse = (course) => {
        if (localStorage.getItem("level") === "levelTwo") {
            window.location.href = `/course-details/view/${course?.id}/${0}/${0}`;
        } else {
            if (course?.is_scorm) {
                window.location.href = `/dashboard/course-details/play-scorm/${course?.id}`;
            } else {
                navigate(`/course-details/view/${course?.id}/${0}/${0}`);
            }
        }
    };

    return (
        <div className="user_courses">
            <div className="courses_header">
                <div>
                    <h1>Enrolled Course</h1>
                </div>
                <div>
                    <button onClick={() => navigate("/dashboard/course")}>View All</button>
                </div>
            </div>
            <div className="courses_wrapper">
                {courses.data?.data?.slice(0, 4)?.map((course, idx) => (
                    <div key={idx} className="dash_course_card" onClick={() => onRedirectToCourse(course)}>
                        <div className="course_banner">
                            <img
                                src={
                                    course?.course?.course_banner_url
                                        ? course?.course?.course_banner_url
                                        : "/assets/placeholder.jpg"
                                }
                                alt=""
                            />
                        </div>
                        <div className="dash_course_details">
                            <h1>{course?.course_title}</h1>
                            <small>{course?.course?.lms_course_category?.category_name}</small>
                            <small>
                                Expired in : <b>{course?.course?.lms_course_settings[0]?.expiration_days} Days</b>
                            </small>
                            <p className="tw-line-clamp-3 tw-text-sm">{course?.course?.course_description}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default UserCoursesDash;
