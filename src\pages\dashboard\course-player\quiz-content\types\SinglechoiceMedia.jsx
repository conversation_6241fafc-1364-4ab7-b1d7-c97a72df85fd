import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useState } from "react";
import ContentSlideLayout from "../layouts/ContentSlideLayout";

const SinglechoiceMedia = ({
    handleNextSlide,
    handlePrevSlide,
    className,
    data,
    sequence,
    onComponentAnswer,
    template,
}) => {
    const [selected, setSelected] = useState(data?.answerKey ?? null);

    const handleClick = (option) => {
        setSelected(option);
        let ans = data?.options?.find((dt) => dt?.label == option.label);
        let points = Boolean(ans?.isCorrect) == true ? data?.points : 0;
        onComponentAnswer(sequence, option, points);
    };
    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data:
                    data?.name ??
                    "Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac aliquet odio mattis.",
                specailInstruction:
                    data?.specailInstruction ??
                    "Drag the image on correct placeholder make sure the label is correct for image.",
            }}
        >
            <div
                style={{
                    gridTemplateColumns: `repeat(${data?.options?.length}, 1fr)`,
                }}
                className="tw-grid tw-h-[50%] tw-w-full tw-grid-cols-3 tw-grid-rows-1 tw-gap-6"
            >
                {data?.options.map((img, idx) => {
                    const isChosen = selected?.label === img?.label;
                    return (
                        <motion.div
                            initial={{ y: 100, opacity: 0.5, scale: 0.5 }}
                            whileInView={{ y: 0, opacity: 1, scale: 1 }}
                            transition={{
                                duration: 0.35,
                                delay: 0.15 * idx,
                                ease: "linear",
                            }}
                            onClick={() => handleClick(img)}
                            style={{
                                borderWidth: data?.styles?.[isChosen ? "selected" : "default"]?.borderWidth,
                                borderColor: data?.styles?.[isChosen ? "selected" : "default"]?.borderColor,
                                borderStyle: data?.styles?.[isChosen ? "selected" : "default"]?.borderStyle,
                            }}
                            className={cn("tw-relative tw-h-full tw-w-full tw-overflow-hidden tw-rounded-lg")}
                            key={idx}
                        >
                            <div
                                className={cn(
                                    "tw-absolute tw-left-0 tw-top-0 tw-flex tw-size-10 tw-items-center tw-justify-center tw-rounded-br-lg",
                                    isChosen ? "tw-z-10 tw-block" : "tw-hidden",
                                )}
                                style={{
                                    borderColor: data?.styles?.[isChosen ? "selected" : "default"]?.borderColor,
                                    backgroundColor: data?.styles?.[isChosen ? "selected" : "default"]?.backgroundColor,
                                    color: data?.styles?.[isChosen ? "selected" : "default"]?.color,
                                }}
                            >
                                <i className="fa-solid fa-check tw-text-[28px]"></i>
                            </div>
                            <img
                                src={img?.src}
                                alt=""
                                className="tw-absolute tw-z-0 tw-h-full tw-w-full tw-rounded-lg tw-object-contain"
                            />
                        </motion.div>
                    );
                })}
            </div>
        </ContentSlideLayout>
    );
};

export default SinglechoiceMedia;
