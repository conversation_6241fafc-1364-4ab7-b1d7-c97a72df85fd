import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import ContentAudio from "@/pages/dashboard/course-player/content/type/audio";
import ContentEmbed from "@/pages/dashboard/course-player/content/type/embed";
import ContentHomework from "@/pages/dashboard/course-player/content/type/homework";
import ContentHTML from "@/pages/dashboard/course-player/content/type/html";
import ContentInteractions from "@/pages/dashboard/course-player/content/type/interactions";
import ContentQuizz from "@/pages/dashboard/course-player/content/type/quizz";
import ContentSCORM from "@/pages/dashboard/course-player/content/type/scorm";
import ContentVideo from "@/pages/dashboard/course-player/content/type/video";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import { Fullscreen } from "lucide-react";
import { useRef } from "react";
import { toast } from "sonner";

const ContentBody = ({ content, courseId, Bookmarking, template: templateData }) => {
    const template = content?.content_type == "INTERACTIONS" ? content?.interaction?.lms_template : templateData;
    const ref = useRef(null);
    const { fullscreen, contentFullScreen, setContentFullScreen } = usePlayer();

    const componentMapping = {
        HTML: <ContentHTML template={template} content={content} />,
        MP3: <ContentAudio template={template} content={content} />,
        MP4: <ContentVideo template={template} content={content} />,
        QUIZ: <ContentQuizz template={template} content={content} courseId={courseId} />,
        HOMEWORK: <ContentHomework template={template} content={content} />,
        ASSIGNMENT: <ContentHomework template={template} content={content} />,
        ZIP: <ContentSCORM template={template} content={content} Bookmarking={Bookmarking} />,
        INTERACTIONS: <ContentInteractions template={template} content={content} />,
        EMBED: <ContentEmbed template={template} content={content} />,
    };

    const Content = componentMapping[content?.content_type];

    const toggleFullScreen = () => {
        const elem = ref.current;
        if (elem && !fullscreen) {
            if (!document.fullscreenElement) {
                elem.requestFullscreen().catch((err) => {
                    toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
                });
                setContentFullScreen(!contentFullScreen);
            } else {
                document.exitFullscreen();
                setContentFullScreen(!contentFullScreen);
            }
        }
    };

    return (
        <div
            ref={ref}
            className="tw-relative tw-grid tw-h-full tw-w-full tw-gap-y-4 tw-overflow-hidden tw-rounded-xl tw-bg-cover tw-bg-no-repeat tw-py-10"
            style={{ backgroundImage: `url(${content?.background ?? template?.background})` }}
        >
            <div
                className={cn(
                    "tw-mx-auto tw-grid tw-h-full tw-max-h-full tw-w-[95%] tw-max-w-[95%] tw-gap-4 tw-overflow-hidden",
                    content?.content_type == "INTERACTIONS" ? "tw-grid-rows-1" : "tw-grid-rows-[60px_1fr]",
                )}
            >
                {content?.content_type !== "INTERACTIONS" && (
                    <div className="tw-text-center">
                        <p
                            className="!tw-mb-0"
                            style={{
                                fontFamily: template?.font_family?.subtitle?.font_family,
                                fontSize: template?.font_family?.subtitle?.font_size ?? "32px",
                                lineHeight: "1",
                                fontWeight: template?.font_family?.subtitle?.font_weight ?? "700",
                                color: template?.font_family?.subtitle?.color ?? "#333333",
                            }}
                        >
                            {content?.lecture_title}
                        </p>
                    </div>
                )}
                {Content}
            </div>
            {content?.content_type !== "ZIP" && (
                <Button
                    onClick={toggleFullScreen}
                    variant="secondary"
                    size="icon"
                    className="tw-absolute tw-inset-0 tw-left-0 tw-top-0 tw-z-50"
                >
                    <Fullscreen />
                </Button>
            )}
        </div>
    );
};

export default ContentBody;
