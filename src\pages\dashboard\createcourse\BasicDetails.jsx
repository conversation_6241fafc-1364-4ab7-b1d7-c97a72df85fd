import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

const formSchema = z.object({
    course_title: z.string({ required_error: "Course title required" }).trim().min(1, "Course title cannot be empty"),
    category_id: z.coerce.number().default(71),
    course_description: z
        .string({ required_error: "Course description required" })
        .trim()
        .min(1, "Course description cannot be empty"),
    sub_category_id: z.coerce.number().default(40),
    course_banner_url: z.string().default("https://lms-node20.vercel.app/assets/thumbnail.png"),
    keywords: z.string().optional(),
    audience_type: z.enum(["BEGINNER", "INTERMEDIATE", "EXPERTS", "ALL"]).default("ALL"),
});

const defaultValue = {
    course_title: "",
    category_id: 71,
    course_description: "-",
    sub_category_id: 40,
    course_banner_url: "https://lms-node20.vercel.app/assets/thumbnail.png",
    keywords: "-",
    audience_type: "ALL",
};

const BasicDetails = ({ CourseData, getCourses }) => {
    const params = useParams();
    const navigate = useNavigate();

    const AudienceTypes = ["BEGINNER", "INTERMEDIATE", "EXPERTS", "ALL"];

    const [courseDetail, setCourseDetail] = useState(defaultValue);

    const clearHandler = () => {
        if (params?.course_id) {
            setCourseDetail({
                course_title: CourseData?.course_title,
                category_id: CourseData?.category_id || 71,
                course_description: CourseData?.course_description || "No Description Yet!",
                sub_category_id: CourseData?.sub_category_id || 40,
                course_banner_url:
                    CourseData?.course_banner_url ?? "https://lms-node20.vercel.app/assets/thumbnail.png",
                keywords: CourseData?.keywords || "keyword",
                audience_type: CourseData?.audience_type || "ALL",
            });
        } else {
            setCourseDetail(defaultValue);
        }
    };

    const [categoryList, setCategoryList] = useState([]);
    const [subCategoryList, setSubCategoryList] = useState([]);
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        const allowedChars = /^[a-zA-Z0-9 ]*$/;
        if (name == "course_title") {
            if (!allowedChars.test(value)) {
                e.preventDefault();
            } else {
                setCourseDetail({ ...courseDetail, [name]: value });
            }
        } else {
            setCourseDetail({ ...courseDetail, [name]: value });
        }

        if (name == "category_id") {
            getSubCategory(value);
        }
    };

    useEffect(() => {
        getCategory();
    }, []);

    useEffect(() => {
        if (courseDetail?.category_id) {
            getSubCategory(courseDetail?.category_id);
        }
    }, [courseDetail]);

    useEffect(() => {
        if (CourseData !== null) {
            setCourseDetail({
                course_title: CourseData?.course_title ?? "",
                category_id: CourseData?.category_id ?? 71,
                course_description: CourseData?.course_description ?? "No Description Yet!",
                sub_category_id: CourseData?.sub_category_id ?? 40,
                course_banner_url:
                    CourseData?.course_banner_url ?? "https://lms-node20.vercel.app/assets/thumbnail.png",
                keywords: CourseData?.keywords ?? "keyword",
                audience_type: CourseData?.audience_type ?? "ALL",
            });
        } else {
            setCourseDetail(defaultValue);
        }
    }, [CourseData]);

    const RemoveImage = () => {
        setCourseDetail({ ...courseDetail, course_banner_url: "" });
    };

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-IMAGES");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setCourseDetail({ ...courseDetail, course_banner_url: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setCourseDetail({ ...courseDetail, course_banner_url: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setCourseDetail({ ...courseDetail, course_banner_url: "" });
            });
    };

    const getCategory = async (payload) => {
        await tanstackApi
            .get("course/categories/get-course-category")
            .then((res) => {
                setCategoryList(res?.data?.data);
            })
            .catch((err) => {
                setCategoryList([]);
            });
    };

    const getSubCategory = async (payload) => {
        await tanstackApi
            .get(`course/categories/get-sub-categories/${payload}`)
            .then((res) => {
                setSubCategoryList(res?.data?.data);
            })
            .catch((err) => {
                setSubCategoryList([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        const validate = formSchema.safeParse(courseDetail);
        if (!validate.success) {
            toast.warning("Invalid Data", {
                description: validate.error.errors[0].message,
            });
            return false;
        }

        const payload = {
            course_id: params?.course_id,
            step: "details",
            data: {
                course_title: courseDetail.course_title,
                category_id: courseDetail.category_id,
                course_description: courseDetail.course_description,
                sub_category_id: courseDetail.sub_category_id,
                course_banner_url: courseDetail.course_banner_url,
                keywords: courseDetail.keywords || "no kewords",
                audience_type: "ALL",
            },
        };

        await tanstackApi
            .post("course/creation/update-course-details", { ...payload })
            .then((res) => {
                if (params?.init == "yes") {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "course",
                        log: `${courseDetail.course_title} created successfully.`,
                    });
                    toast.success("Course has been created Successfully", {
                        description: res?.data?.message,
                    });
                    navigate(`/dashboard/view-course/${params?.course_id}`);
                } else {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "course",
                        log: `${courseDetail.course_title} details updated successfully.`,
                    });
                    getCourses({ course_id: params?.course_id });
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                }
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Course basic details</CardTitle>
                <CardDescription>
                    Add course title, banner , decription etc here. Click save when you&apos;re done.
                </CardDescription>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[1.5fr_2fr] tw-gap-0">
                    <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                        <div className="tw-aspect-[2/1] tw-w-[90%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-md tw-object-cover"
                                src={courseDetail?.course_banner_url || "/assets/thumbnail.png"}
                            />
                        </div>
                        <div className="tw-flex tw-gap-2">
                            {!courseDetail?.course_banner_url && (
                                <Label
                                    htmlFor="course_banner_url"
                                    className={cn(buttonVariants({ variant: "outline" }), "aspect-square max-sm:p-0")}
                                >
                                    <Upload
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <input
                                        onChange={onImageChange}
                                        type="file"
                                        style={{ display: "none" }}
                                        id="course_banner_url"
                                        accept="image/*"
                                    />
                                    <div className="max-sm:sr-only">
                                        {load ? "Uploading" : "Upload course banner *"} {load ? `${uploaded}%` : null}
                                    </div>
                                </Label>
                            )}
                            {courseDetail?.course_banner_url && (
                                <Button variant="outline" className="aspect-square max-sm:p-0" onClick={RemoveImage}>
                                    <X
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <Label className="max-sm:sr-only">Remove</Label>
                                </Button>
                            )}
                        </div>
                    </div>
                    <div className="tw-space-y-3">
                        <div className="tw-space-y-1">
                            <Label htmlFor="name">Course Title *</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={courseDetail?.course_title}
                                name="course_title"
                                id="name"
                                placeholder="Enter course title here"
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="name">
                                Keywords <small>(Optional)</small>
                            </Label>
                            <Input
                                onChange={onChangeHandle}
                                value={courseDetail?.keywords}
                                name="keywords"
                                id="name"
                                placeholder="eg: keyword 1, keyword 2 ..."
                            />
                        </div>
                        <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                            <div className="tw-w-full tw-space-y-1">
                                <Label htmlFor="category_id">Category</Label>
                                <Select
                                    onValueChange={(e) => onChangeHandle({ target: { value: e, name: "category_id" } })}
                                    value={courseDetail?.category_id}
                                    name="category_id"
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose Category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categoryList?.map((data, idx) => (
                                            <SelectItem key={idx} value={data?.id}>
                                                {data?.category_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="username">Sub Category</Label>
                                <Select
                                    onValueChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "sub_category_id" } })
                                    }
                                    value={courseDetail?.sub_category_id}
                                    name="sub_category_id"
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Choose Sub Category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {subCategoryList?.map((data, idx) => (
                                            <SelectItem key={idx} value={data?.id}>
                                                {data?.sub_category_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="name">Description *</Label>
                            <Textarea
                                onChange={onChangeHandle}
                                value={courseDetail?.course_description}
                                name="course_description"
                                className="tw-text-sm"
                                placeholder="Define course description here."
                            />
                        </div>
                        <div className="tw-space-y-3">
                            <h1 className="tw-text-xl tw-font-semibold">Who is your Audience</h1>
                            <div className="tw-flex-col-2 tw-mt-3 tw-flex tw-gap-10">
                                <RadioGroup
                                    value={courseDetail?.audience_type}
                                    onValueChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "audience_type" } })
                                    }
                                    className="tw-flex"
                                >
                                    <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                                        {AudienceTypes?.map((data, idx) => (
                                            <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                                <RadioGroupItem
                                                    value={data}
                                                    id={data}
                                                    checked={courseDetail?.audience_type == data}
                                                />
                                                <Label
                                                    htmlFor={data}
                                                    className="tw-cursor-pointer tw-font-normal tw-capitalize"
                                                >
                                                    {data?.toLowerCase()}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" onClick={clearHandler} variant="outline">
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> Save
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default BasicDetails;
