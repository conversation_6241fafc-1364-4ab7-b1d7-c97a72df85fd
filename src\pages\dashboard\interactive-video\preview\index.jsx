import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useGetMedia } from "@/react-query/common";
import { useParticularInteractiveVideo } from "@/react-query/interactive-video";
import { Check } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

export default function InteractiveVideoPreview() {
    const params = useParams();
    const particulardata = useParticularInteractiveVideo({ id: params.id });
    const mediaData = useGetMedia({ id: particulardata.data?.data.primary_media_id });

    useEffect(() => {
        if (particulardata.isSuccess) mediaData.refetch();
    }, [particulardata]);

    if (!mediaData.isSuccess) return <div>Loading...</div>;
    return <VideoWithQuestions videoData={mediaData.data} />;
}

const VideoWithQuestions = ({ videoData }) => {
    const navigate = useNavigate();
    const [currentTime, setCurrentTime] = useState(0);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [activeQuestion, setActiveQuestion] = useState(null);
    const videoRef = useRef(null);
    const [selected, setSelected] = useState(videoData.data.questions.map(() => ""));

    const handleTimeUpdate = (e) => {
        const currentVideoTime = Math.floor(e.target.currentTime); // Get current video time
        setCurrentTime(currentVideoTime);
    };

    const continueVideo = () => {
        videoRef.current?.play();
        setActiveQuestion(null);
    };

    useEffect(() => {
        // Check if there is a question for the current time
        const question = videoData.data.questions.find((q) => q.timestamp === currentTime);
        const index = videoData.data.questions.findIndex((q) => q.timestamp === currentTime);
        if (question) {
            videoRef.current?.pause();
            setActiveQuestion(question);
            setCurrentIndex(index);
        } else {
            setActiveQuestion(null);
        }
    }, [currentTime, videoData]);

    return (
        <div>
            <div className="tw-flex tw-items-center tw-justify-end">
                <Button onClick={() => navigate(-1)}>Back</Button>
            </div>
            <div className="tw-relative tw-w-[80%]">
                <video
                    ref={videoRef}
                    src={videoData.data.url}
                    controls
                    onTimeUpdate={handleTimeUpdate}
                    className="tw-aspect-video tw-w-full tw-rounded-md tw-bg-black/60"
                >
                    Your browser does not support the video tag.
                </video>
                {activeQuestion && (
                    <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-white/80 tw-p-5">
                        <div className="tw-flex tw-items-center tw-justify-center tw-font-lexend">
                            <div className="tw-w-full tw-shrink-0">
                                <h3 className="tw-text-lg tw-font-bold lg:tw-text-3xl">{activeQuestion.question}</h3>
                                <ul className="tw-mt-3 tw-grid tw-grid-cols-2 tw-gap-3 lg:tw-grid-cols-1">
                                    {Object.entries(activeQuestion.options[0]?.option_json || {}).map(
                                        ([key, value]) => {
                                            if (key.startsWith("option_")) {
                                                return (
                                                    <li
                                                        className="tw-flex tw-items-center tw-gap-2 tw-rounded-md tw-border tw-bg-white tw-p-3"
                                                        key={key}
                                                        onClick={() => {
                                                            const answer = key;
                                                            const oldData = [...selected];
                                                            oldData[currentIndex] = answer;
                                                            setSelected(oldData);
                                                        }}
                                                    >
                                                        <div
                                                            className={cn(
                                                                "tw-flex tw-size-6 tw-items-center tw-justify-center tw-p-1",
                                                                selected[currentIndex] == key
                                                                    ? "tw-rounded-full tw-bg-blue-500 tw-text-white tw-opacity-100"
                                                                    : "tw-opacity-0",
                                                            )}
                                                        >
                                                            <Check className={cn("tw-size-4")} />
                                                        </div>
                                                        {value}
                                                    </li>
                                                );
                                            }
                                            return null;
                                        },
                                    )}
                                </ul>
                                <div className="tw-mt-3 tw-flex tw-justify-end">
                                    <Button onClick={continueVideo}>Continue</Button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
