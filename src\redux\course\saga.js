import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import {
    fetchBookmarkingReq,
    getAllCourrsesList,
    getBookmarking,
    getCourseDetails,
    GetUpdatedBookmarking,
    presequireList,
    setCourse,
} from "@/redux/course/action";
import { addTimelineLog } from "@/redux/reports/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* getAllCoursesList() {
    const data = yield axios
        .get(`${CONSTANTS.getAPI()}course/get-courses`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data.reverse();
            return response;
        })
        .catch((err) => {
            var errMsg = [];
            return errMsg;
        });

    if (data) {
        yield put(getAllCourrsesList(data));
    } else {
        yield put(getAllCourrsesList([]));
    }
}

function* fetchCoursePresequite() {
    const data = yield axios
        .get(`${CONSTANTS.getAPI()}course/list-course-prerequisites`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(presequireList(data));
    }
}

function* fetchCOURSEREQ(initData) {
    const categoryDataPost = yield axios
        .post(`${CONSTANTS.getAPI()}course/creation/init`, initData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (categoryDataPost?.success) {
        yield put(setCourse(categoryDataPost.data));
    }
}

function* deleteCourseSaga(courseId) {
    const deleteRes = yield axios
        .delete(`${CONSTANTS.getAPI()}course/delete-course`, courseId.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (deleteRes) {
        yield put(fetchCOURSEREQ());
    }
}

function* updateCourseDetails(data) {
    const categoryDataPost = yield axios
        .post(`${CONSTANTS.getAPI()}course/creation/update-course-details`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;

            return response;
        })
        .catch((err) => {
            const errMsg = err;

            return errMsg;
        });
    if (categoryDataPost?.success) {
        if (data?.payload?.step == "details") {
            yield put(
                addTimelineLog({
                    user_id: localStorage.getItem("userId"),
                    event: "course",
                    log: `${data?.payload?.data?.course_title} created successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
                }),
            );
        }
        yield put(setCourse(categoryDataPost.data));
        yield put(AlertSnackInfo({ message: categoryDataPost.message, result: categoryDataPost.success }));
    } else {
        yield put(
            AlertSnackInfo({
                message: categoryDataPost?.response.data.message,
                result: categoryDataPost?.response.data.success,
            }),
        );
    }
}
function* updateCourseStatus(data) {
    const updateStatus = yield axios
        .post(`${CONSTANTS.getAPI()}course/creation/update-course-status`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err;
            alert(errMsg);
            return errMsg;
        });
    if (updateStatus?.success) {
        yield put(setCourse(updateStatus.data));
        yield put(AlertSnackInfo({ message: updateStatus.message, result: updateStatus.success }));
        window.location.reload();
    } else {
        yield put(AlertSnackInfo({ message: updateStatus.message, result: updateStatus.success }));
        yield put(fetchCOURSEREQ());
    }
}

function* saveChapterDetails(data) {
    const categoryDataPost = yield axios
        .post(`${CONSTANTS.getAPI()}course/creation/save-chapter`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (categoryDataPost?.success) {
        yield put(AlertSnackInfo({ message: categoryDataPost.message, result: categoryDataPost.success }));
    } else {
        yield put(AlertSnackInfo({ message: categoryDataPost.message, result: categoryDataPost.success }));
    }
}

function* updateChapterDetails(data) {
    const categoryDataPost = yield axios
        .put(`${CONSTANTS.getAPI()}course/creation/update-chapter`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err?.response?.data?.message ? err?.response?.data?.message : err.message;
            return errMsg;
        });
    if (categoryDataPost?.success) {
        yield put(AlertSnackInfo({ message: categoryDataPost.message, result: categoryDataPost.success }));
    } else {
        yield put(AlertSnackInfo({ message: categoryDataPost, result: false }));
    }
}

function* fetchCOURSE_Details(course_id) {
    const courseDetail = yield axios
        .post(`${CONSTANTS.getAPI()}course/view-course`, course_id.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = {};
            return errMsg;
        });
    if (courseDetail?.success) {
        yield put(getCourseDetails(courseDetail.data));
    }
}

function* fetchCourseBookmarking(course_id) {
    const data = yield axios
        .get(`${CONSTANTS.getAPI()}course/bookmark/get-course-bookmarking/${course_id?.payload?.course_id}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            if (res.data.success) {
                return res.data.data;
            } else {
                return {};
            }
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(getBookmarking(data));
    }
}

function* updateBookmarking(data) {
    const bookmarking = yield axios
        .post(`${CONSTANTS.getAPI()}course/bookmark/update-course-bookmarking`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (bookmarking?.success) {
        yield put(GetUpdatedBookmarking(bookmarking?.data));
        let payload = {
            course_id: bookmarking?.data?.course_id,
        };
        yield put(fetchBookmarkingReq(payload));
    }
}

export function* CourseWatcher() {
    yield takeEvery(actions.FETCH_COURSE_LIST_REQ, fetchCOURSEREQ);
    yield takeEvery(actions.DELETE_COURSE, deleteCourseSaga);
    yield takeEvery(actions.UPDATE_COURSE, updateCourseDetails);
    yield takeEvery(actions.UPDTAE_COURSE_STATUS, updateCourseStatus);
    yield takeEvery(actions.SAVE_CHAPTERS, saveChapterDetails);
    yield takeEvery(actions.UPDATE_CHAPTERS, updateChapterDetails);
    yield takeEvery(actions.FETCH_ALL_COURSES_LIST, getAllCoursesList);
    yield takeEvery(actions.COURSE_DETAILS_REQ, fetchCOURSE_Details);
    yield takeEvery(actions.COURSE_PREREQUISITE_REQ, fetchCoursePresequite);
    yield takeEvery(actions.COURSE_BOOKMARKING_REQ, fetchCourseBookmarking);
    yield takeEvery(actions.UPDATE_BOOKMARKING_DATA, updateBookmarking);
}

export default function* CourseSaga() {
    yield all([fork(CourseWatcher)]);
}
