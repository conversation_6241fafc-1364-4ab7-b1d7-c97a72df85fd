import { ColorPicker } from "@/components/ui/color-picker";
import { Input } from "@/components/ui/input";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";

export default function ColorInput({ onInput, onHandleChange, ...props }) {
    const [color, setColor] = useState(props.value);
    const debouncedColorItem = useDebounce(color, 500);

    console.log(debouncedColorItem);

    useEffect(() => {
        if (onHandleChange) onHandleChange(debouncedColorItem);
    }, [debouncedColorItem]);

    return (
        <div>
            <ColorPicker value={color} onChange={(color) => setColor(color)} />
        </div>
    );
}
