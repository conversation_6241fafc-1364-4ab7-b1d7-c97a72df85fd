import Pagination from "@/components/table/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

function calculateTotalPoints(questionsArray, key) {
    // Check if input is an array
    if (!Array.isArray(questionsArray)) {
        throw new Error("Input must be an array of question objects");
    }

    // Use reduce to accumulate total points
    const totalPoints = questionsArray.reduce((sum, question) => {
        // Validate if the current object has 'points' and it's a number
        if (question && typeof question[key] === "number") {
            return sum + question[key];
        } else {
            console.warn(`Skipping invalid question at index ${questionsArray.indexOf(question)}`);
            return sum; // Skip invalid objects
        }
    }, 0);

    return totalPoints;
}

const ITEMS_PER_PAGE = 7;

const SumbittedQuizzes = (allSubmissions) => {
    const [dataList, setDatalist] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(ITEMS_PER_PAGE);

    const [filterState, setFilterState] = useState({
        search: "",
        status: "",
        submission_date: "",
    });

    useEffect(() => {
        getSubmissionsData();
    }, []);

    const getSubmissionsData = async (formData) => {
        await tanstackApi
            .post("quiz/list-submissions", { user_id: localStorage.getItem("userId") })
            .then((res) => {
                setDatalist(res?.data?.data);
            })
            .catch((err) => {
                setDatalist([]);
            });
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? item?.quiz?.title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const status = filterState?.status ? item?.result == filterState?.status : true; // Allow all items if no subCategory filter

            const submission = filterState?.submission_date
                ? moment(item.createdAt).format("DD/MMM/YYYY") ===
                  moment(filterState?.submission_date).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            return matchesname && status && submission; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            status: "",
            submission_date: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    return (
        <>
            <div className="page_filters">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Quiz Name
                    </label>
                    <input
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState?.search}
                        name="search"
                        type="text"
                        placeholder="Search by name ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Status
                    </label>
                    <select className="tw-text-sm" onChange={onFilterChange} value={filterState?.status} name="status">
                        <option value="">- All -</option>
                        <option value="passed">Passed</option>
                        <option value="failed">Failed</option>
                        <option value="pending">Pending</option>
                    </select>
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Submission Date
                    </label>
                    <input
                        className="tw-text-sm"
                        type="date"
                        onChange={onFilterChange}
                        value={filterState?.submission_date}
                        name="submission_date"
                    />
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>Thumbnail</th>
                            <th>Quiz Title</th>
                            <th>Trainer</th>
                            <th>Marks</th>
                            <th>Result</th>
                            <th>Submission Date</th>
                            <th>Result</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td style={{ width: "130px" }}>
                                    <div className="table_image_alpha">
                                        <img
                                            src={row?.quiz?.thumbnail_img || "/assets/course-placeholder.png"}
                                            alt=""
                                        />
                                    </div>
                                </td>
                                <td>{row?.quiz?.title}</td>
                                <td>{row?.quiz?.trainer_name}</td>
                                <td>
                                    {" "}
                                    {calculateTotalPoints(
                                        row?.components?.length > 0 ? row?.components : [],
                                        "earnedPoints",
                                    )}
                                    {" / "}
                                    {calculateTotalPoints(row?.components?.length > 0 ? row?.components : [], "points")}
                                </td>
                                <td>{row?.result}</td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td>
                                    <Link to={`/dashboard/submissions-learner-quiz-view/Quizzes/${row?.id}`}>
                                        <button className="selected_btn">
                                            <i className="fa-solid fa-square-poll-vertical"></i> Check Result
                                        </button>
                                    </Link>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination currentPage={currentPage} totalPages={totalPages} handlePageChange={handlePageChange} />
                </div>
            </div>
        </>
    );
};

export default SumbittedQuizzes;
