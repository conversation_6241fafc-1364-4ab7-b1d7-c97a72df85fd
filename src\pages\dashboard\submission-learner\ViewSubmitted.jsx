import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";

const ViewSubmitted = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const params = useParams();

    const [database, setDatabase] = useState(null);

    const [submissionDetails, setSubmissionDetails] = useState({
        status: "inprogress",
        marks: "",
        remarks: "",
    });

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setSubmissionDetails({ ...submissionDetails, [name]: value });
    };

    useEffect(() => {
        if (params?.id !== undefined) {
            getSubmissionsData(params?.id);
        }
    }, [params]);

    useEffect(() => {
        setSubmissionDetails({
            status: database?.evaluation_status,
            marks: database?.marks,
            remarks: database?.remarks,
        });
    }, [database]);

    const getSubmissionsData = async (workID) => {
        await tanstackApi
            .post("homework/submissions/get-my-submissions", {})
            .then((res) => {
                setDatabase(res?.data?.data?.find((dt) => dt?.id == Number(workID)));
            })
            .catch((err) => {
                setDatabase(null);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!submissionDetails?.marks) {
            toast.error("Please enter marks");
            return false;
        }

        if (!submissionDetails?.remarks) {
            toast.error("Please add some remarks");
            return false;
        }

        let payload = {
            course_homework_submission_id: params?.id,
            status: submissionDetails?.status,
            marks: submissionDetails?.marks,
            remarks: submissionDetails?.remarks,
        };

        await tanstackApi
            .post("homework/submission/review-homework-trainer", { ...payload })
            .then((res) => {
                toast.success("Successfully Remarked");
                navigate("/dashboard/submissions");
            })
            .catch((err) => {
                toast.error("Something went wrong!");
            });
    };

    const onDownloadHomework = () => {
        const url = database?.lms_course_homework?.attachment_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `${database?.lms_course_homework?.attachment_url}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    const onDownloadSubmission = () => {
        const url = database?.submission_attachment;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `${database?.submission_attachment}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    return (
        <div className="">
            <div className="pageHeader">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/submissions-learner">All Submission</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Check Remarks of {params?.type}</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>

                <Link to={"/dashboard/submissions-learner"}>
                    <Button className="">
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                </Link>
            </div>

            <br />
            <div className="content_body_homework">
                <div className="homework_title">
                    <h1>
                        <i className="fa-solid fa-laptop-file"></i> {database?.lms_course_homework?.homework_title}
                    </h1>
                </div>
                <div className="homework_description">
                    <p>{database?.lms_course_homework?.description}</p>
                </div>
                <div className="homework_details">
                    <div>
                        <p>
                            <i className="fa-solid fa-dice-d6"></i> Points
                        </p>
                        <h4>{database?.lms_course_homework?.homework_points}</h4>
                    </div>
                    <div>
                        <p>
                            {" "}
                            <i className="fa-solid fa-clock"></i> Submission
                        </p>
                        <h4>{moment(database?.lms_course_homework?.submission_date).format("LLL")}</h4>
                    </div>
                    <div>
                        <p>
                            <i className="fa-solid fa-paperclip"></i> Attachments
                        </p>
                        <h4>
                            {!database?.lms_course_homework?.attachment_url ? (
                                <button onClick={onDownloadHomework}>
                                    <i className="fa-solid fa-download"></i> Download
                                </button>
                            ) : (
                                <button className="tw-opacity-[.2]">File not found!</button>
                            )}
                        </h4>
                    </div>
                </div>
            </div>
            <div className="subission_details">
                <div className="tw-text-right">
                    Submitted on <strong>{moment(database?.created_at).format("LLL")}</strong>
                </div>
                <h5>#. Submission by user :-</h5>
                <p>{database?.submission}</p>
                <br />
                <h5>#. Submission Attachment:-</h5>
                {database?.submission_attachment ? (
                    <button onClick={onDownloadSubmission}>
                        <i className="fa-solid fa-download"></i> Download Attached File
                    </button>
                ) : (
                    <button className="tw-opacity-[.2]">File not found!</button>
                )}

                <br />
                <br />
                <h5>
                    #. Evaluation Status: <b>{database?.evaluation_status}</b>
                </h5>
            </div>
            <div className="tw-mx-12 tw-mt-5 tw-space-y-4">
                <div className="tw-flex tw-items-center tw-gap-2">
                    <h1 className="tw-text-lg tw-font-semibold">Points: </h1>
                    <p>
                        <i className="fa-solid fa-cube"></i> {database?.marks} /{" "}
                        {database?.lms_course_homework?.homework_points}
                    </p>
                </div>
                <div className="tw-flex tw-flex-col tw-gap-1">
                    <h1 className="tw-text-lg tw-font-semibold">Remarks: </h1>
                    <p>{database?.remarks}</p>
                </div>
            </div>
        </div>
    );
};

export default ViewSubmitted;
