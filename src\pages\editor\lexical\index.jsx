import ToolbarPlugin from "@/pages/editor/lexical/toolbar";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { $isTextNode, ParagraphNode, TextNode } from "lexical";
import { useState } from "react";

const theme = {
    code: "editor-code",
    heading: {
        h1: "editor-heading-h1",
        h2: "editor-heading-h2",
        h3: "editor-heading-h3",
        h4: "editor-heading-h4",
        h5: "editor-heading-h5",
    },
    image: "editor-image",
    link: "editor-link",
    list: {
        listitem: "editor-listitem",
        nested: {
            listitem: "editor-nested-listitem",
        },
        ol: "editor-list-ol",
        ul: "editor-list-ul",
    },
    ltr: "ltr",
    paragraph: "editor-paragraph",
    placeholder: "editor-placeholder",
    quote: "editor-quote",
    rtl: "rtl",
    text: {
        bold: "editor-text-bold",
        code: "editor-text-code",
        hashtag: "editor-text-hashtag",
        italic: "editor-text-italic",
        overflowed: "editor-text-overflowed",
        strikethrough: "editor-text-strikethrough",
        underline: "editor-text-underline",
        underlineStrikethrough: "editor-text-underlineStrikethrough",
    },
};

const removeStylesExportDOM = (editor, target) => {
    const output = target.exportDOM(editor);
    if (output && output.element instanceof HTMLElement) {
        for (const el of [output.element, ...output.element.querySelectorAll('[style],[class],[dir="ltr"]')]) {
            el.removeAttribute("class");
            el.removeAttribute("style");
            if (el.getAttribute("dir") === "ltr") {
                el.removeAttribute("dir");
            }
        }
    }
    return output;
};

const exportMap = new Map([
    [ParagraphNode, removeStylesExportDOM],
    [TextNode, removeStylesExportDOM],
]);

const MIN_ALLOWED_FONT_SIZE = 8;
const MAX_ALLOWED_FONT_SIZE = 72;

const parseAllowedFontSize = (input) => {
    const match = input.match(/^(\d+(?:\.\d+)?)px$/);
    if (match) {
        const n = Number(match[1]);
        if (n >= MIN_ALLOWED_FONT_SIZE && n <= MAX_ALLOWED_FONT_SIZE) {
            return input;
        }
    }
    return "";
};

function parseAllowedColor(input) {
    return /^rgb\(\d+, \d+, \d+\)$/.test(input) ? input : "";
}

const getExtraStyles = (element) => {
    let extraStyles = "";
    const fontSize = parseAllowedFontSize(element.style.fontSize);
    const backgroundColor = parseAllowedColor(element.style.backgroundColor);
    const color = parseAllowedColor(element.style.color);
    if (fontSize !== "" && fontSize !== "15px") {
        extraStyles += `font-size: ${fontSize};`;
    }
    if (backgroundColor !== "" && backgroundColor !== "rgb(255, 255, 255)") {
        extraStyles += `background-color: ${backgroundColor};`;
    }
    if (color !== "" && color !== "rgb(0, 0, 0)") {
        extraStyles += `color: ${color};`;
    }
    return extraStyles;
};

const constructImportMap = () => {
    const importMap = {};
    for (const [tag, fn] of Object.entries(TextNode.importDOM() || {})) {
        importMap[tag] = (importNode) => {
            const importer = fn(importNode);
            if (!importer) return null;
            return {
                ...importer,
                conversion: (element) => {
                    const output = importer.conversion(element);
                    if (
                        output === null ||
                        output.forChild === undefined ||
                        output.after !== undefined ||
                        output.node !== null
                    ) {
                        return output;
                    }
                    const extraStyles = getExtraStyles(element);
                    if (extraStyles) {
                        const { forChild } = output;
                        return {
                            ...output,
                            forChild: (child, parent) => {
                                const textNode = forChild(child, parent);
                                if ($isTextNode(textNode)) {
                                    textNode.setStyle(textNode.getStyle() + extraStyles);
                                }
                                return textNode;
                            },
                        };
                    }
                    return output;
                },
            };
        };
    }

    return importMap;
};

const initialConfig = {
    namespace: "MyEditor",
    theme,
    html: {
        export: exportMap,
        import: constructImportMap(),
    },
    nodes: [ParagraphNode, TextNode],
    onError: (error) => console.error(error),
};

export default function LexicalEditor() {
    const [editorState, setEditorState] = useState();
    return (
        <div>
            <LexicalComposer initialConfig={initialConfig}>
                <ToolbarPlugin />
                <div className="tw-relative">
                    <RichTextPlugin
                        contentEditable={<ContentEditable />}
                        placeholder={<div className="tw-absolute tw-left-0 tw-top-0">Enter some text...</div>}
                        ErrorBoundary={LexicalErrorBoundary}
                    />
                </div>
                <HistoryPlugin />
                <AutoFocusPlugin />
            </LexicalComposer>
        </div>
    );
}
