{"name": "lms-node-20", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview --port 3000", "prod": "vite build && vite preview --port 3000", "format": "prettier . --write"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@jacobsdigitalfactory/react-image-hotspots": "^1.7.2", "@lexical/react": "^0.20.0", "@lexical/utils": "^0.20.0", "@mui/icons-material": "^6.1.6", "@mui/material": "^6.1.6", "@mui/x-data-grid": "^7.22.1", "@mui/x-date-pickers": "^7.22.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@reduxjs/toolkit": "^2.3.0", "@tailwindcss/typography": "^0.5.15", "@tanstack/query-sync-storage-persister": "^5.64.1", "@tanstack/react-query": "^5.60.2", "@tanstack/react-query-devtools": "^5.62.8", "@tanstack/react-query-persist-client": "^5.64.1", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-bullet-list": "^2.10.2", "@tiptap/extension-collaboration": "^2.9.1", "@tiptap/extension-color": "^2.9.1", "@tiptap/extension-document": "^2.9.1", "@tiptap/extension-dropcursor": "^2.9.1", "@tiptap/extension-floating-menu": "^2.9.1", "@tiptap/extension-font-family": "^2.9.1", "@tiptap/extension-heading": "^2.9.1", "@tiptap/extension-horizontal-rule": "^2.9.1", "@tiptap/extension-image": "^2.9.1", "@tiptap/extension-list-item": "^2.10.2", "@tiptap/extension-list-keymap": "^2.10.2", "@tiptap/extension-paragraph": "^2.9.1", "@tiptap/extension-placeholder": "^2.10.2", "@tiptap/extension-text": "^2.9.1", "@tiptap/extension-text-align": "^2.9.1", "@tiptap/extension-text-style": "^2.9.1", "@tiptap/extension-typography": "^2.9.1", "@tiptap/pm": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@uidotdev/usehooks": "^2.4.1", "@vidstack/react": "^1.12.12", "antd": "^5.21.6", "antd-local-icon": "^0.1.3", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "framer-motion": "^11.11.11", "html-react-parser": "^5.1.18", "immutability-helper": "^3.1.1", "immutable": "^5.0.0", "input-otp": "^1.4.1", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "lexical": "^0.20.0", "lucide-react": "^0.454.0", "moment": "^2.30.1", "next-themes": "^0.4.3", "qs": "^6.13.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-icons": "^5.3.0", "react-image-hotspots": "^2.2.5", "react-intl": "^6.8.7", "react-leaflet": "5.0.0", "react-redux": "^9.1.2", "react-responsive-modal": "^6.4.2", "react-router": "^7.1.1", "react-router-dom": "^6.28.0", "react-scrollbars-custom": "^4.1.1", "react-sortablejs": "^6.1.4", "react-to-print": "^3.0.2", "react-use": "^17.6.0", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0", "redux-thunk": "^3.1.0", "scorm-again": "^2.6.2", "sonner": "^1.7.0", "sortablejs": "^1.15.6", "styled-components": "^6.1.13", "styled-theme": "^0.3.3", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "uuid": "^11.0.3", "xlsx": "^0.18.5", "y-prosemirror": "^1.2.12", "y-protocols": "^1.0.6", "yjs": "^13.6.20", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.13.0", "@tanstack/eslint-plugin-query": "^5.60.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "sass": "^1.80.6", "scss": "^0.2.4", "tailwindcss": "^3.4.14", "vite": "^5.4.10"}}