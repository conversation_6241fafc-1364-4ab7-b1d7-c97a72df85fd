import { Button } from "@/components/ui/button";
import { DataTable } from "@/pages/dashboard/roles/permission/data-table";
import { FetchBadgesList } from "@/redux/gamification/action";
import { format } from "date-fns";
import { Pencil, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import BadgesForm from "./form";

export default function BadgesPage() {
    const dispatch = useDispatch();
    const badgesList = useSelector((state) => state.GamificationReducer)?.badgesList;

    useEffect(() => {
        dispatch(FetchBadgesList());
    }, []);

    const [open, setOpen] = useState(false);
    const [selected, setSelected] = useState(null);

    const handleUpdate = (id) => {
        const data = badgesList?.find((dt) => dt.id == id);
        setSelected(data);
        setOpen(true);
    };

    const columns = [
        {
            accessorKey: "id",
        },
        {
            accessorKey: "icon_url",
            header: "Icon",
            cell: ({ row }) => {
                return (
                    <>
                        <img src={row.getValue("icon_url")} style={{ height: "45px" }} alt="" />
                    </>
                );
            },
        },
        {
            accessorKey: "name",
            header: "Name",
        },
        {
            accessorKey: "level",
            header: "Level",
        },
        {
            accessorKey: "points_required",
            header: "Points Required",
        },
        {
            accessorKey: "points_upper_limit",
            header: "Points upper limit",
        },
        {
            accessorKey: "description",
            header: "Description",
        },
        {
            accessorKey: "createdAt",
            header: "Create On",
            cell: ({ row }) => {
                return <div className="text-right font-medium">{format(row.getValue("createdAt"), "PP")}</div>;
            },
        },
        {
            accessorKey: "updatedAt",
            header: "Actions",
            cell: ({ row }) => {
                return (
                    <div
                        className="text-right font-medium"
                        onClick={() => {
                            handleUpdate(row.getValue("id"));
                        }}
                    >
                        <Pencil className="tw-text-blue-400" />
                    </div>
                );
            },
        },
    ];
    return (
        <div>
            <div className="tw-mb-3 tw-flex tw-items-center tw-justify-between">
                <div className="tw-font-lexend tw-text-2xl tw-font-medium">Badges</div>
                <Button
                    onClick={() => {
                        setSelected(null);
                        setOpen(true);
                    }}
                    variant="outline"
                >
                    <Plus />
                    Create Badges
                </Button>
            </div>
            <DataTable columns={columns} data={badgesList ? badgesList : []} />
            <BadgesForm open={open} setOpen={setOpen} editData={selected} />
        </div>
    );
}
