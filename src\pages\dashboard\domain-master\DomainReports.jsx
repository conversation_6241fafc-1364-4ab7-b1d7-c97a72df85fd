import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>read<PERSON>rumb<PERSON><PERSON>,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import AttendanceReport from "./reports/Attendance";
import CertificateReport from "./reports/CertificateReport";
import CourseReport from "./reports/CourseReport";
import LearnerReport from "./reports/LearnerReport";
import TimelineLogs from "./reports/TimelineLogs";

const DomainReports = () => {
    const params = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const router = useNavigate();
    const [userGroupData, setUserGroupData] = useState(null);

    useEffect(() => {
        if (params?.group_id !== undefined) {
            getUserGroups(params?.group_id);
        }
    }, [params]);

    const getUserGroups = async (group_id) => {
        await tanstackApi
            .post(`user-groups/list`)
            .then((res) => {
                setUserGroupData(res?.data?.data?.find((dt) => dt?.id == Number(group_id)));
            })
            .catch((err) => {
                setUserGroupData(null);
            });
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/domain-users">Domains</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>All Reports</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <Button className="tw-px-2 tw-py-1" onClick={() => router(`/dashboard/domain-users`)}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
                {/* </Link> */}
            </div>
            <Tabs
                defaultValue={searchParams.get("tab") ?? "attendance_report"}
                onValueChange={(value) => setSearchParams({ tab: value })}
                className="tw-mt-5 tw-w-full"
            >
                <TabsList className="tw-grid tw-w-[850px] tw-grid-cols-5">
                    <TabsTrigger value="attendance_report" className="tw-gap-2">
                        <i className="fa-solid fa-circle-info"></i> Attendance Report
                    </TabsTrigger>
                    <TabsTrigger value="timeline_logs" className="tw-gap-2">
                        <i className="fa-solid fa-book"></i> Timeline Logs
                    </TabsTrigger>
                    <TabsTrigger value="course_report" className="tw-gap-2">
                        <i className="fa-solid fa-box-open"></i> Course Report
                    </TabsTrigger>
                    <TabsTrigger value="learner_report" className="tw-gap-2">
                        <i className="fa-solid fa-box-open"></i> Learner Report
                    </TabsTrigger>
                    <TabsTrigger value="certificate_report" className="tw-gap-2">
                        <i className="fa-solid fa-box-open"></i> Certificate Report
                    </TabsTrigger>
                </TabsList>
                <TabsContent value="attendance_report">
                    <AttendanceReport />
                </TabsContent>
                <TabsContent value="timeline_logs">
                    <TimelineLogs />
                </TabsContent>
                <TabsContent value="course_report">
                    <CourseReport />
                </TabsContent>
                <TabsContent value="learner_report">
                    <LearnerReport />
                </TabsContent>
                <TabsContent value="certificate_report">
                    <CertificateReport />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default DomainReports;
