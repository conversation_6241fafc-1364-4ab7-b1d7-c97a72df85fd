import React, { createContext, useContext, useState } from "react";

/**
 * PermissionContextType defines the structure of the permission context.
 * @property {boolean} edit - Indicates if the edit mode is active.
 * @property {function} setEdit - Function to update the edit mode state.
 * @property {Array} listing - The list of permissions.
 * @property {function} setListing - Function to update the permissions listing.
 * @property {function} onPermissionChange - Function to handle changes to permissions.
 * @property {function} onSubPermissionChange - Function to handle changes to sub-permissions.
 */
const PermissionContext = createContext();

/**
 * Provider component for permission context.
 * @param {Object} props - The component props.
 * @param {React.ReactNode} props.children - The children components.
 * @returns {JSX.Element} The provider component.
 */
export const PermissionProvider = ({ children }) => {
    const [edit, setEdit] = useState(false);
    const [listing, setListing] = useState([]);

    /**
     * Handles permission changes.
     * @param {Object} param0 - The permission change details.
     * @param {string} param0.permission - The permission to change.
     * @param {boolean} param0.value - The new value for the permission.
     * @param {string} param0.id - The ID of the permission.
     */
    const onPermissionChange = ({ permission, value, id }) => {
        const oldValues = [...listing];
        const newValues = oldValues.find((item) => item.id === id);
        const canUpdatePermission = newValues.possible_permissions.includes(permission);
        if (!canUpdatePermission) return;

        const updatedPermissions = new Set(newValues.permissions);
        if (value) {
            updatedPermissions.add(permission);
        } else {
            updatedPermissions.delete(permission);
        }

        setListing(
            oldValues.map((item) => (item.id === id ? { ...item, permissions: Array.from(updatedPermissions) } : item)),
        );
    };

    /**
     * Handles sub-permission changes.
     * @param {Object} param0 - The sub-permission change details.
     * @param {string} param0.permission - The sub-permission to change.
     * @param {boolean} param0.value - The new value for the sub-permission.
     * @param {string} param0.id - The ID of the sub-permission.
     * @param {string} param0.parent - The ID of the parent module.
     */
    const onSubPermissionChange = ({ permission, value, id, parent }) => {
        const oldValues = [...listing];
        const parentModule = oldValues.find((item) => item.id === parent);
        const subModule = parentModule.sub_modules.find((item) => item.id === id);
        const canUpdatePermission = subModule.possible_permissions.includes(permission);
        if (!canUpdatePermission) return;

        const updatedPermissions = new Set(subModule.permissions);
        if (value) {
            updatedPermissions.add(permission);
        } else {
            updatedPermissions.delete(permission);
        }

        setListing((prevListing) =>
            prevListing.map((item) =>
                item.id === parent
                    ? {
                          ...item,
                          sub_modules: item.sub_modules.map((sub) =>
                              sub.id === id ? { ...sub, permissions: Array.from(updatedPermissions) } : sub,
                          ),
                      }
                    : item,
            ),
        );
    };

    const value = {
        edit,
        setEdit,
        listing,
        setListing,
        onPermissionChange,
        onSubPermissionChange,
    };

    return <PermissionContext.Provider value={value}>{children}</PermissionContext.Provider>;
};

/**
 * Custom hook to use the PermissionContext.
 * @returns {Object} The context value.
 */
export const usePermission = () => {
    const context = useContext(PermissionContext);
    if (context === undefined) throw new Error("usePermission must be used within a PermissionProvider");
    return context;
};
