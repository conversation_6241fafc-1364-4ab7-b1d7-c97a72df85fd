import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useState } from "react";

const SinglechoiceMedia = ({ onSlideEdit, onRemoveSlide, index, content, template }) => {
    const [selected, setSelected] = useState(content?.answerKey ?? content?.options[0]);

    const handleClick = (option) => {
        setSelected(option);
    };

    return (
        <div
            className="tw-grid tw-min-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            // style={{ backgroundImage: `url(${template?.background||content?.question_background})` }}
        >
            <div className="tw-px-10 tw-py-7">
                <div
                    className={`tw-grid tw-h-full tw-w-full tw-grid-cols-${content?.options?.length} tw-grid-rows-1 tw-gap-4`}
                >
                    {content?.options.map((img, idx) => {
                        const isChosen = selected?.label === img?.label;
                        return (
                            <motion.div
                                initial={{ y: 100, opacity: 0.5, scale: 0.5 }}
                                whileInView={{ y: 0, opacity: 1, scale: 1 }}
                                transition={{
                                    duration: 0.35,
                                    delay: 0.15 * idx,
                                    ease: "linear",
                                }}
                                onClick={() => handleClick(img)}
                                className={cn(
                                    "tw-relative tw-aspect-square tw-h-full tw-w-full tw-rounded-lg tw-border-[2px] tw-border-dashed",
                                    isChosen && "tw-border-[6px] tw-border-[#FE5C96] tw-bg-[#FE5C96] tw-text-white",
                                )}
                                key={idx}
                            >
                                <div
                                    className={cn(
                                        "tw-absolute tw-left-0 tw-top-0 tw-flex tw-size-10 tw-items-center tw-justify-center tw-rounded-br-lg tw-bg-[#FE5C96]",
                                        isChosen ? "tw-z-10 tw-block" : "tw-hidden",
                                    )}
                                >
                                    <i className="fa-solid fa-check tw-text-[28px]"></i>
                                </div>
                                <img
                                    src={img?.src}
                                    alt=""
                                    className="tw-absolute tw-z-0 tw-h-full tw-w-full tw-rounded-lg tw-object-cover"
                                />
                            </motion.div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default SinglechoiceMedia;
