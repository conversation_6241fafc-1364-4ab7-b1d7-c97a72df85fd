import { fetchUserPointsReq } from "@/redux/dashboard/dash/action";
import { FetchBadgesList } from "@/redux/gamification/action";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

function getUserBadge(userPoints, badges) {
    const sortedBadges = badges.sort((a, b) => a.points_required - b.points_required);
    let earnedBadge = null;
    for (const badge of sortedBadges) {
        if (userPoints >= badge.points_required) {
            earnedBadge = badge;
        } else {
            break;
        }
    }

    return earnedBadge ? earnedBadge : null;
}

function getAchievedBadges(userPoints, badges) {
    const sortedBadges = badges.sort((a, b) => a.points_required - b.points_required);
    const achievedBadges = sortedBadges.filter((badge) => userPoints >= badge.points_required);
    return achievedBadges;
}

const LearnerProfileDash = ({ setBadgeDeatils }) => {
    const dispatch = useDispatch();

    const userPoints = useSelector((state) => state.DashReducer.userPoints);
    const badgesList = useSelector((state) => state.GamificationReducer.badgesList);

    const [badge, setBadge] = useState(null);
    const [achievedBadges, setAchievedBadges] = useState([]);
    const [levelProgress, setLevelProgress] = useState(0);

    useEffect(() => {
        dispatch(fetchUserPointsReq());
        dispatch(FetchBadgesList());
    }, []);

    useEffect(() => {
        if (userPoints?.points) {
            setBadge(getUserBadge(userPoints?.points, badgesList));
            setBadgeDeatils(getUserBadge(userPoints?.points, badgesList));
            setAchievedBadges(getAchievedBadges(userPoints?.points, badgesList));
        }
    }, [userPoints, badgesList]);

    useEffect(() => {
        if (badge !== null) {
            setLevelProgress(((badge?.level * 100) / 6 || 0)?.toFixed(0));
        }
    }, [badge]);

    return (
        <div className="profile_dash">
            <div className="details">
                <div className="profile_icon">
                    <img
                        src={
                            localStorage.getItem("user_image") !== "null"
                                ? localStorage.getItem("user_image")
                                : "/assets/learner.png"
                        }
                        alt=""
                    />
                    <div>{/* <img src={`/assets/badge-${badge?.level}.png`} alt="" /> */}</div>
                </div>
                <h5>
                    {localStorage.getItem("lms_fName")} {localStorage.getItem("lms_lName")}
                </h5>
                <div className="badge_level">
                    <img src={`/assets/level-${badge?.level}.png`} alt="" />
                    <div className="progress">
                        <div className="progress-done" style={{ "--wid": `${levelProgress}%` }}></div>
                    </div>
                </div>
            </div>

            {/* {userPoints?.user_points?.length > 0 && (
                <div className="notifications">
                    <h2>Notifications</h2>
                    <div className="noti_wrapper">
                        {userPoints?.user_points?.reverse()?.map((noti, idx) => (
                            <div key={idx}>
                                <h4>
                                    {noti?.event}
                                    <small>{moment(noti?.createdAt).endOf("minute").fromNow()}</small>
                                </h4>
                                <p>{noti?.points} points credited successfully</p>
                            </div>
                        ))}
                    </div>
                </div>
            )} */}

            {/* <div className="performance">
        <h2>Skill Performance</h2>
        <div className="perform_wrapper">
          <div className="perform_card">
            <div className="left">
              <h3>89%</h3>
              <p>5-Day Attendance Streak </p>
            </div>
            <div className="right">
              <img src="/assets/perform-1.png" alt="" />
            </div>
          </div>
          <div className="perform_card">
            <div className="left">
              <h3>90%</h3>
              <p>Score Average </p>
            </div>
            <div className="right">
              <img src="/assets/perform-2.png" alt="" />
            </div>
          </div>
        </div>
      </div> */}
        </div>
    );
};

export default LearnerProfileDash;
