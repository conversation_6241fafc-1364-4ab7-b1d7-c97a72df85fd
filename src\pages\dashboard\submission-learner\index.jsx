import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import SumbittedAssignments from "./SumbittedAssignments";
import SumbittedHomeworks from "./SumbittedHomeworks";
import SumbittedQuizzes from "./SumbittedQuizzes";

import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const tabsData = [{ name: "Assignments" }, { name: "Homeworks" }, { name: "Quizzes" }];

const SubmissionsLearner = () => {
    const [allSubmissions, setAllSubmissions] = useState([]);
    const [activeTab, setActiveTab] = useState("Assignments");

    useEffect(() => {
        getSubmissionsData();
    }, []);

    const getSubmissionsData = async (formData) => {
        await tanstackApi
            .post("homework/submissions/get-my-submissions", formData)
            .then((res) => {
                setAllSubmissions(res?.data?.data);
            })
            .catch((err) => {
                setAllSubmissions([]);
            });
    };

    return (
        <div className="">
            <div>
                <div className="pageHeader">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>All Submission</BreadcrumbPage>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>{activeTab}</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
                <div className="page_tabs">
                    {tabsData?.map((tab, idx) => (
                        <p
                            key={idx}
                            onClick={() => setActiveTab(tab?.name)}
                            className={activeTab == tab?.name ? "active" : ""}
                        >
                            {tab?.name}
                        </p>
                    ))}
                </div>
                {activeTab == "Assignments" && (
                    <SumbittedAssignments allSubmissions={allSubmissions} activeTab={activeTab} />
                )}
                {activeTab == "Homeworks" && (
                    <SumbittedHomeworks allSubmissions={allSubmissions} activeTab={activeTab} />
                )}
                {activeTab == "Quizzes" && <SumbittedQuizzes allSubmissions={allSubmissions} activeTab={activeTab} />}
            </div>
        </div>
    );
};

export default SubmissionsLearner;
