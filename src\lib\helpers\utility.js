import { Map } from "immutable";

export function clearToken() {
    localStorage.removeItem("id_token");
}

export function getToken() {
    try {
        const idToken = localStorage.getItem("id_token");
        return new Map({ idToken });
    } catch (err) {
        clearToken();
        return new Map();
    }
}

export function arrayEqual(array1, array2) {
    return array1.sort().toString() === array2.sort().toString();
}

export function timeDifference(givenTime) {
    givenTime = new Date(givenTime);
    const milliseconds = new Date().getTime() - givenTime.getTime();
    const numberEnding = (number) => {
        return number > 1 ? "s" : "";
    };
    const number = (num) => (num > 9 ? `${num}` : `0${num}`);
    const getTime = () => {
        let temp = Math.floor(milliseconds / 1000);
        const years = Math.floor(temp / 31536000);
        if (years) {
            const month = number(givenTime.getUTCMonth() + 1);
            const day = number(givenTime.getUTCDate());
            const year = givenTime.getUTCFullYear() % 100;
            return `${day}-${month}-${year}`;
        }
        const days = Math.floor((temp %= 31536000) / 86400);
        if (days) {
            if (days < 28) {
                return `${days} day${numberEnding(days)}`;
            } else {
                const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                const month = months[givenTime.getUTCMonth()];
                const day = number(givenTime.getUTCDate());
                return `${day} ${month}`;
            }
        }
        const hours = Math.floor((temp %= 86400) / 3600);
        if (hours) {
            return `${hours} hour${numberEnding(hours)} ago`;
        }
        const minutes = Math.floor((temp %= 3600) / 60);
        if (minutes) {
            return `${minutes} minute${numberEnding(minutes)} ago`;
        }
        return "a few seconds ago";
    };
    return getTime();
}

export function stringToInt(value, defValue = 0) {
    if (!value) {
        return 0;
    } else if (!isNaN(value)) {
        return parseInt(value, 10);
    }
    return defValue;
}

export function stringToPosetiveInt(value, defValue = 0) {
    const val = stringToInt(value, defValue);
    return val > -1 ? val : defValue;
}

export function PermissionGranted(module_name, operation) {
    const permission = JSON.parse(localStorage.getItem("permissions"));

    if (operation) {
        const finalResult = permission
            ?.filter((perm) => perm?.module_code == module_name)?.[0]
            ?.permissions?.includes(operation);
        return finalResult;
    } else if (!operation) {
        return permission?.filter((perm) => perm.module_code == module_name).length > 0;
    }
}

export function PermissionGrantedSubModule(module_name, submodule_name, submodule_operation) {
    const permission = JSON.parse(localStorage.getItem("permissions"));
    if (submodule_operation) {
        const finalResult = permission?.filter((perm) => perm?.module_code == module_name);
        if (finalResult && finalResult.length > 0) {
            const sub_modules = finalResult[0].sub_modules;
            if (sub_modules && sub_modules.length > 0) {
                const finalResult1 = sub_modules
                    .filter((io) => io.sub_module_code === submodule_name)?.[0]
                    ?.permissions?.includes(submodule_operation);
                return finalResult1;
            }
        }
        return false;
    } else if (!submodule_operation) {
        const finalResult = permission?.filter((perm) => perm?.module_code == module_name);
        if (finalResult && finalResult.length > 0) {
            const sub_modules = finalResult[0].sub_modules;
            if (sub_modules && sub_modules.length > 0) {
                const finalResult1 = sub_modules.filter((io) => io.sub_module_code === submodule_name).length > 0;
                return finalResult1;
            }
        }
        return false;
    }
}

/**
 * Performs a deep comparison between two values
 * @param a First value to compare
 * @param b Second value to compare
 * @returns Boolean indicating whether the values are deeply equal
 */
export function deepEqual(a, b) {
    // Check for strict equality first
    if (a === b) return true;

    // Check if both are null or undefined
    if (a == null || b == null) return a === b;

    // Check if both are of the same type
    if (typeof a !== typeof b) return false;

    // Handle arrays
    if (Array.isArray(a) && Array.isArray(b)) {
        if (a.length !== b.length) return false;

        for (let i = 0; i < a.length; i++) {
            if (!deepEqual(a[i], b[i])) return false;
        }

        return true;
    }

    // Handle objects
    if (typeof a === "object" && typeof b === "object") {
        const keysA = Object.keys(a);
        const keysB = Object.keys(b);

        if (keysA.length !== keysB.length) return false;

        for (const key of keysA) {
            if (!keysB.includes(key)) return false;
            if (!deepEqual(a[key], b[key])) return false;
        }

        return true;
    }

    // Handle primitive types
    return a === b;
}

/**
 * Utility class for advanced deep comparison and manipulation
 */
export class ComparisonUtil {
    /**
     * Deep compare two values with optional configuration
     * @param a First value to compare
     * @param b Second value to compare
     * @param options Optional comparison configuration
     * @returns Boolean indicating deep equality
     */
    static compare(a, b, options = {}) {
        const { ignoreKeys = [], strict = true } = options;

        // Handle null/undefined cases
        if (a == null || b == null) return strict ? a === b : a == b;

        // Handle arrays
        if (Array.isArray(a) && Array.isArray(b)) {
            if (a.length !== b.length) return false;

            return a.every((item, index) => this.compare(item, b[index], options));
        }

        // Handle objects
        if (typeof a === "object" && typeof b === "object") {
            const keysA = Object.keys(a).filter((key) => !ignoreKeys.includes(key));
            const keysB = Object.keys(b).filter((key) => !ignoreKeys.includes(key));

            if (keysA.length !== keysB.length) return false;

            return keysA.every((key) => this.compare(a[key], b[key], options));
        }

        // Primitive comparison
        return strict ? a === b : a == b;
    }

    /**
     * Create a deep copy of an object or array
     * @param value Value to clone
     * @returns Deep copied value
     */
    static clone(value) {
        if (value == null) return value;

        if (Array.isArray(value)) {
            return value.map((item) => this.clone(item));
        }

        if (typeof value === "object") {
            const clonedObj = {};
            for (const key in value) {
                if (Object.prototype.hasOwnProperty.call(value, key)) {
                    clonedObj[key] = this.clone(value[key]);
                }
            }
            return clonedObj;
        }

        return value;
    }
}
