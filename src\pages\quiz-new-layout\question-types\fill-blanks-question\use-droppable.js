import { useState, useRef, useEffect } from "react";

export const useDroppable = (id, onDrop) => {
    const [isOver, setIsOver] = useState(false);
    const dropRef = useRef(null);

    useEffect(() => {
        const currentElement = dropRef.current;
        if (!currentElement) return;

        const handleDragOver = (e) => {
            e.preventDefault();
            setIsOver(true);
            e.dataTransfer.dropEffect = "move";
        };

        const handleDragLeave = () => {
            setIsOver(false);
        };

        const handleDrop = (e) => {
            e.preventDefault();
            setIsOver(false);

            try {
                const itemData = JSON.parse(e.dataTransfer.getData("application/json"));
                onDrop(itemData);
            } catch (error) {
                console.error("Error parsing dropped data", error);
            }
        };

        currentElement.addEventListener("dragover", handleDragOver);
        currentElement.addEventListener("dragleave", handleDragLeave);
        currentElement.addEventListener("drop", handleDrop);

        return () => {
            currentElement.removeEventListener("dragover", handleDragOver);
            currentElement.removeEventListener("dragleave", handleDragLeave);
            currentElement.removeEventListener("drop", handleDrop);
        };
    }, [id, onDrop]);

    return { isOver, dropRef };
};
