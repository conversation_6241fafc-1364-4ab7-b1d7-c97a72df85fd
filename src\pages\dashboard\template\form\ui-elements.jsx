import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { generateRandomString, isUrl } from "@/lib/utils";
import { useFileUpload } from "@/react-query/common";
import { Trash, Upload, X } from "lucide-react";
import { useEffect, useState } from "react";

const defaultValues = {
    src: "",
    position: { x: 0, y: 0 },
};

export default function UIElementsForm({ setTemplate, template }) {
    const data = Object.keys(template.ui_elements ?? {});
    const [activeTab, setActiveTab] = useState(data[0]);

    useEffect(() => {
        if (!template.ui_elements) {
            setTemplate((prev) => ({
                ...prev,
                ui_elements: { [`element_${generateRandomString()}`]: defaultValues },
            }));
        }
    }, [template?.ui_elements]);

    const addElements = () => {
        setTemplate((prev) => ({
            ...prev,
            ui_elements: { ...prev.ui_elements, [`element_${generateRandomString()}`]: defaultValues },
        }));
    };

    const removeElements = (key) => {
        const newElements = { ...template.ui_elements };
        delete newElements[key];
        setTemplate((prev) => ({
            ...prev,
            ui_elements: newElements,
        }));
        setActiveTab(Object.keys(newElements)[0]);
    };

    const removeImage = (key) => {
        setTemplate((prev) => ({
            ...prev,
            ui_elements: { ...prev.ui_elements, [key]: defaultValues },
        }));
    };

    const upload = useFileUpload();
    const onImageChange = async (e, key) => {
        try {
            if (e.target.files && e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                setTemplate((prev) => ({
                    ...prev,
                    ui_elements: { ...prev.ui_elements, [key]: { src: response.data.url, position: { x: 0, y: 0 } } },
                }));
            }
        } catch (error) {
            removeImage(key);
        }
    };

    return (
        <div className="tw-w-full">
            <Button onClick={addElements}>Add</Button>

            {/* <div className="tw-grid tw-w-full tw-grid-cols-3 tw-gap-3">
                {data.map((key) => {
                    return (
                        <>
                            <Button key={key} value={key} variant="secondary" className="tw-w-fit tw-capitalize">
                                {key.replaceAll("_", " ")}
                            </Button>
                            <div className="tw-space-y-1">
                                <div className="tw-flex tw-w-full tw-flex-col tw-items-start tw-gap-2">
                                    {isUrl(template?.ui_elements?.[key]?.src) && (
                                        <div className="tw-aspect-video tw-h-36 tw-object-contain">
                                            <img
                                                src={template.ui_elements[key]?.src}
                                                alt=""
                                                className="tw-size-full tw-rounded-md"
                                            />
                                        </div>
                                    )}
                                    <div className="tw-flex tw-items-center tw-gap-2">
                                        {isUrl(template?.ui_elements?.[key]?.src) && (
                                            <Button
                                                variant="destructive"
                                                className="aspect-square max-sm:p-0"
                                                onClick={() => {
                                                    removeImage(key);
                                                }}
                                            >
                                                <X
                                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                                    size={16}
                                                    strokeWidth={2}
                                                    aria-hidden="true"
                                                />
                                                <Label className="max-sm:sr-only">Remove</Label>
                                            </Button>
                                        )}
                                        <Label
                                            htmlFor="bg_image"
                                            className={cn(
                                                buttonVariants({ variant: "outline" }),
                                                "aspect-square max-sm:p-0 tw-h-10",
                                            )}
                                        >
                                            <Upload
                                                className="opacity-60 sm:-ms-1 sm:me-2"
                                                size={16}
                                                strokeWidth={2}
                                                aria-hidden="true"
                                            />
                                            <input
                                                onChange={onImageChange}
                                                type="file"
                                                style={{ display: "none" }}
                                                id="bg_image"
                                                accept="image/*"
                                            />
                                            <Label htmlFor="bg_image" className="max-sm:sr-only">
                                                {upload.isPending ? "Uploading..." : "Upload Background Image"}
                                            </Label>
                                        </Label>
                                    </div>
                                </div>
                            </div>
                        </>
                    );
                })}
            </div> */}
            <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="tw-mt-2 tw-grid tw-w-full tw-grid-cols-3 tw-gap-3"
            >
                <div className="tw-col-span-1">
                    <TabsList className="tw-h-auto tw-flex-col tw-flex-wrap">
                        {data.map((key) => {
                            return (
                                <TabsTrigger key={key} value={key} className="tw-capitalize">
                                    {key.replaceAll("_", " ")}
                                </TabsTrigger>
                            );
                        })}
                    </TabsList>
                </div>
                <div className="tw-col-span-2">
                    {data.map((key) => {
                        return (
                            <TabsContent key={key} value={key} className="pt-0 mt-0 tw-space-y-3">
                                <div className="tw-space-y-1">
                                    <div className="tw-flex tw-w-full tw-flex-col tw-items-start tw-gap-2">
                                        <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
                                            <Label htmlFor="bg_image" className="tw-capitalize">
                                                {key.replaceAll("_", " ")} Image
                                            </Label>
                                            {data.length > 1 && (
                                                <Button
                                                    onClick={() => removeElements(key)}
                                                    className="!tw-size-6"
                                                    variant="destructive"
                                                    size="icon"
                                                >
                                                    <Trash className="!tw-size-3" />
                                                </Button>
                                            )}
                                        </div>
                                        {isUrl(template?.ui_elements?.[key]?.src) && (
                                            <div className="tw-aspect-video tw-h-36 tw-object-contain">
                                                <img
                                                    src={template.ui_elements[key]?.src}
                                                    alt=""
                                                    className="tw-size-full tw-rounded-md"
                                                />
                                            </div>
                                        )}
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {isUrl(template?.ui_elements?.[key]?.src) && (
                                                <Button
                                                    variant="destructive"
                                                    className="aspect-square max-sm:p-0"
                                                    onClick={() => {
                                                        removeImage(key);
                                                    }}
                                                >
                                                    <X
                                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                                        size={16}
                                                        strokeWidth={2}
                                                        aria-hidden="true"
                                                    />
                                                    <Label className="max-sm:sr-only">Remove</Label>
                                                </Button>
                                            )}
                                            <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10">
                                                <Upload
                                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                                    size={16}
                                                    strokeWidth={2}
                                                    aria-hidden="true"
                                                />
                                                <input
                                                    onChange={(e) => onImageChange(e, key)}
                                                    type="file"
                                                    style={{ display: "none" }}
                                                    id="bg_image"
                                                    accept="image/*"
                                                />
                                                <Label htmlFor="bg_image" className="max-sm:sr-only">
                                                    {upload.isPending ? "Uploading..." : "Upload Background Image"}
                                                </Label>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                        );
                    })}
                </div>
            </Tabs>
        </div>
    );
}
