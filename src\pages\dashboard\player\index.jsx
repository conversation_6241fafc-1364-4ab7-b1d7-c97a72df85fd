import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

export default function PlayerPage() {
    // const [searchParams] = useSearchParams();
    // const templateType = searchParams.get("temp") ?? "1";
    // const showComponent = {
    //     1: <TemplateOne />,
    //     2: <TemplateTwo />,
    // };

    // return showComponent[templateType];

    return (
        <div>
            <Tabs defaultValue="account" className="w-[400px]">
                <TabsList>
                    <TabsTrigger value="account">Account</TabsTrigger>
                    <TabsTrigger value="password">Password</TabsTrigger>
                </TabsList>
                <TabsContent value="account">Make changes to your account here.</TabsContent>
                <TabsContent value="password">Change your password here.</TabsContent>
            </Tabs>
        </div>
    );
}
