import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const QuestionImport = ({ open, setOpen, editData, getPoolData, questionList, questionTypes }) => {
    const params = useParams();
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [filterState, setFilterState] = useState({
        search: "",
        type: "",
        difficulty: "",
    });

    useEffect(() => {
        setFilteredData(questionList);
    }, [questionList]);

    const onSearch = () => {
        const data = questionList?.filter((item) => {
            const matchesname = filterState?.search
                ? item?.question?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const type = filterState?.type ? item?.question_type == filterState?.type : true; // Allow all items if no subCategory filter
            const difficulty = filterState?.difficulty ? item?.difficulty_level == filterState?.difficulty : true; // Allow all items if no subCategory filter

            return matchesname && type && difficulty; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilterState({
            search: "",
            type: "",
            difficulty: "",
        });
        setFilteredData(questionList);
    };

    useEffect(() => {}, [editData]);

    const onQuestionSelection = (questionID) => {
        if (selectedItems?.includes(questionID)) {
            setSelectedItems(selectedItems?.filter((dt) => dt !== questionID));
        } else {
            setSelectedItems([...selectedItems, questionID]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();
        if (selectedItems?.length == 0) {
            toast.warning("Select Questions", {
                description: "Please select questions from list",
            });
            return false;
        }

        const payload = {
            pool_id: params?.pool_id,
            question_ids: selectedItems,
        };

        await tanstackApi
            .post("questions/import-questions", { ...payload })
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("userId"),
                    event: "question pool",
                    log: `${selectedItems?.length} question imported from question bank in ${editData?.name} successfully.`,
                });
                getPoolData(params?.pool_id);
                setOpen(false);
                setOpenAlert(false);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Import Questions, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected questions in your question pool details.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Yes Import!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-6xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">Import questions in {editData?.name}</h1>
                        </DialogTitle>
                        <DialogDescription>
                            Choose the questions from below listing of Questions bank. And click on save button.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="tw-grid tw-grid-cols-[1.6fr_1fr_1fr_100px_100px] tw-gap-2">
                        <Input
                            placeholder="Search question name"
                            onChange={(e) => setFilterState({ ...filterState, search: e.target.value })}
                            value={filterState?.search}
                        />
                        <Select
                            onValueChange={(e) => setFilterState({ ...filterState, type: e })}
                            value={filterState?.type}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Question Type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    {questionTypes?.map((type, index) => (
                                        <SelectItem
                                            className="tw-font-lexend tw-font-medium"
                                            value={type?.type}
                                            key={index}
                                        >
                                            {type?.displayName}
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                        <Select
                            onValueChange={(e) => setFilterState({ ...filterState, difficulty: e })}
                            value={filterState?.difficulty}
                        >
                            <SelectTrigger className="tw-capitalize">
                                <SelectValue placeholder="Difficulty Level" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    {["easy", "hard", "medium"]?.map((type, index) => (
                                        <SelectItem value={type} key={index} className="tw-capitalize">
                                            {type}
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                        <Button variant="outline" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </Button>
                        <Button variant="outline" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>
                    </div>

                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Question</th>
                                    <th>Type</th>
                                    <th>Diffculty</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredData?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>{row.question}</td>
                                        <td>{row?.question_type}</td>
                                        <td>
                                            <Badge variant="outline" className={"tw-capitalize"}>
                                                {row?.difficulty_level}
                                            </Badge>
                                        </td>
                                        <td>
                                            {editData?.questions?.map((dt) => dt?.id)?.includes(row?.id) ? (
                                                <Button
                                                    variant="outline"
                                                    className="!tw-cursor-not-allowed tw-bg-emerald-50 tw-text-gray-500"
                                                >
                                                    Added{" "}
                                                    <i className="fa-regular fa-circle-check tw-text-md tw-text-emerald-500"></i>
                                                </Button>
                                            ) : (
                                                <>
                                                    {selectedItems?.includes(row?.id) ? (
                                                        <Button onClick={() => onQuestionSelection(row?.id)}>
                                                            Selected
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            onClick={() => onQuestionSelection(row?.id)}
                                                            variant="outline"
                                                        >
                                                            Choose
                                                        </Button>
                                                    )}
                                                </>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                <i className="fa-solid fa-xmark"></i> Close
                            </Button>
                        </DialogClose>
                        <Button onClick={() => setOpenAlert(true)}>
                            <i className="fa-solid fa-download"></i> Import & Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default QuestionImport;
