import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ColorPicker } from "@/components/ui/color-picker";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";
import { BubbleMenu as TipTapBubbleMenu } from "@tiptap/react";
import { AlignCenter, AlignLeft, AlignRight, BoldIcon, ItalicIcon, StrikethroughIcon } from "lucide-react";

const textAlignment = {
    left: AlignLeft,
    center: AlignCenter,
    right: AlignRight,
};

export default function BubbleMenu({ editor }) {
    return (
        <TipTapBubbleMenu editor={editor} tippyOptions={{ duration: 100 }}>
            <Card className="tw-flex tw-p-0">
                <ToggleGroup
                    onValueChange={(value) => {
                        editor
                            .chain()
                            .focus()
                            .setTextAlign(value === "" ? "left" : value)
                            .run();
                    }}
                    value="left"
                    type="single"
                >
                    {Object.entries(textAlignment).map(([key, Icon]) => {
                        return (
                            <ToggleGroupItem key={key} value={key} asChild>
                                <Button
                                    className={cn(
                                        "!tw-size-8 !tw-text-black [&_svg]:tw-size-3",
                                        editor.isActive({ textAlign: key }) ? "!tw-bg-gray-100" : "",
                                    )}
                                >
                                    <Icon />
                                </Button>
                            </ToggleGroupItem>
                        );
                    })}
                </ToggleGroup>
                <ColorPicker
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    value={editor.getAttributes("textStyle").color}
                    onChange={(color) => {
                        editor.chain().focus().setColor(color).run();
                    }}
                />
                <Button
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    variant={editor.isActive("bold") ? "secondary" : "ghost"}
                    size="icon"
                    className="!tw-size-8 [&_svg]:tw-size-3"
                >
                    <BoldIcon />
                </Button>
                <Button
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    variant={editor.isActive("italic") ? "secondary" : "ghost"}
                    size="icon"
                    className="!tw-size-8 [&_svg]:tw-size-3"
                >
                    <ItalicIcon />
                </Button>
                <Button
                    onClick={() => editor.chain().focus().toggleStrike().run()}
                    variant={editor.isActive("strike") ? "secondary" : "ghost"}
                    size="icon"
                    className="!tw-size-8 [&_svg]:tw-size-3"
                >
                    <StrikethroughIcon />
                </Button>
            </Card>
        </TipTapBubbleMenu>
    );
}
