import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import PricingAddEdit from "./PricingAddEdit";

const tabsData = [{ name: "Course Pricing" }, { name: "Bundle Pricing" }];

const NormalPricingDomain = () => {
    const [activeTab, setActiveTab] = useState("Course Pricing");
    const [open, setOpen] = useState(false);
    const [courseList, setCourseList] = useState([]);
    const [editData, setEditData] = useState(null);
    const [serverData, setServerData] = useState([]);
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(9);
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);
        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);
        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }
        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }
        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };
    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const [filterState, setFilterState] = useState({
        search: "",
        validity_type: "",
        subscription_type: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? (activeTab == "Course Pricing" ? item?.course?.course_title : item?.bundle?.name)
                      ?.toLowerCase()
                      ?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const subscription = filterState?.subscription_type
                ? item?.subscription_type == filterState?.subscription_type
                : true; // Allow all items if no subCategory filter
            const validity = filterState?.validity_type ? item?.validity_type == filterState?.validity_type : true; // Allow all items if no subCategory filter

            return matchesSearch && subscription && validity; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            validity_type: "",
            subscription_type: "",
        });
    };

    useEffect(() => {
        getPriceList();
        getCourses();
    }, []);

    useEffect(() => {
        if (activeTab == "Course Pricing") {
            setDataList(serverData?.filter((dt) => dt?.course_id !== null));
        } else {
            setDataList(serverData?.filter((dt) => dt?.bundle_id !== null));
        }
    }, [serverData, activeTab]);

    useEffect(() => {
        setFilteredData(dataList);
    }, [dataList]);

    const getPriceList = async () => {
        await tanstackApi
            .get("course-bundle-prices/list", {
                params: {
                    domain_id: localStorage.getItem("userId"),
                },
            })
            .then((res) => {
                setServerData(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setServerData([]);
            });
    };

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const onAddNewType = () => {
        setEditData(null);
        setOpen(true);
    };

    const onEditData = (data) => {
        setEditData(data);
        setOpen(true);
    };

    return (
        <>
            <PricingAddEdit
                open={open}
                setOpen={setOpen}
                editData={editData}
                activeTab={activeTab}
                getPriceList={getPriceList}
            />
            <div>
                <div className="tw-flex tw-justify-between">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Pricing</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <Button className="tw-px-2 tw-py-1" onClick={onAddNewType}>
                        <i className="fa-solid fa-plus"></i> New Pricing
                    </Button>
                </div>
                <div className="page_tabs !tw-mt-0">
                    {tabsData?.map((tab, idx) => (
                        <p
                            key={idx}
                            onClick={() => setActiveTab(tab?.name)}
                            className={activeTab == tab?.name ? "active" : ""}
                        >
                            {tab?.name}
                        </p>
                    ))}
                </div>
                <div className="page_filters tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Search
                        </label>
                        <input
                            value={filterState?.search}
                            name="search"
                            onChange={onFilterChange}
                            className="tw-text-sm"
                            type="text"
                            placeholder="Search by name ..."
                        />
                    </div>
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Validity Type
                        </label>
                        <select
                            className="tw-text-sm"
                            value={filterState?.validity_type}
                            name="validity_type"
                            onChange={onFilterChange}
                        >
                            <option value=""> - All - </option>
                            <option value="lifetime">Lifetime</option>
                            <option value="limited">Limited</option>
                        </select>
                    </div>
                    <div className="input_group">
                        <label className="tw-text-sm" htmlFor="">
                            Subscription Type
                        </label>
                        <select
                            className="tw-text-sm"
                            value={filterState?.subscription_type}
                            name="subscription_type"
                            onChange={onFilterChange}
                        >
                            <option value=""> - All - </option>
                            <option value={"monthly"}>Monthly</option>
                            <option value={"annually"}>Annually</option>
                            <option value={"one-time"}>One-time</option>
                            <option value={"trial"}>Trial</option>
                        </select>
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table">
                    <table>
                        <thead>
                            <tr>
                                <th>{activeTab == "Course Pricing" ? "Course Name" : "Bundle Name"}</th>
                                <th>Price</th>
                                <th>Currency</th>
                                <th>Subscription Type</th>
                                <th>Validity Type</th>
                                <th>Valid Days</th>
                                <th>Creation On</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody className="tw-font-lexend">
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>
                                        {activeTab == "Course Pricing" ? row?.course?.course_title : row?.bundle?.name}
                                    </td>
                                    <td>
                                        {!row?.is_free ? `${row?.currency?.currency_symbol} ${row?.price}` : "Free"}
                                    </td>
                                    <td>{row?.currency?.currency_code || "-"}</td>
                                    <td>
                                        <Badge variant={"outline"}>{row?.subscription_type}</Badge>
                                    </td>
                                    <td>
                                        <Badge variant={"outline"}>{row?.validity_type}</Badge>
                                    </td>
                                    <td>{row?.valid_days} days</td>
                                    <td>{moment(row?.createdAt).format("LL")}</td>
                                    <td>
                                        <button className="selected_btn" onClick={() => onEditData(row)}>
                                            <i className="fa-solid fa-edit"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination className="">
                            <PaginationContent>
                                {/* Previous Button */}
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {/* Page Numbers with Ellipses */}
                                {currentPage > 3 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                1
                                            </PaginationLink>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    </>
                                )}

                                {getVisiblePages().map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href="#"
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {currentPage < totalPages - 2 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                {totalPages}
                                            </PaginationLink>
                                        </PaginationItem>
                                    </>
                                )}

                                {/* Next Button */}
                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </>
    );
};

export default NormalPricingDomain;
