import { tanstack<PERSON>pi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetUserGroups = (data) => {
    return useQuery({
        queryKey: ["users-groups", data],
        queryFn: async () => (await tanstackApi.post("user-groups/list", data)).data,
    });
};

export const useGetAssignedUserGroups = (data) => {
    return useQuery({
        queryKey: ["users-assigned-groups", data],
        queryFn: async () => (await tanstackApi.post("learner-permission/get-assigned-groups", data)).data,
    });
};

export const useAddUserToGroups = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("learner-permission/add-user-to-groups", data)).data,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["users-groups"] });
            queryClient.invalidateQueries({ queryKey: ["users-assigned-groups"] });
        },
    });
};

export const useGetUserGroupsTrainers = () => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["trainer-groups", { userId }],
        queryFn: async () => (await tanstackApi.post("user-groups/get-my-groups", {})).data,
    });
};
