import { FETCH_PERMISSION_REQ, GET_PERMISSION_LIST } from "@/redux-types";

const initialState = {
    permissionList: [],
    isLoading: true,
    error: null,
};

const PermissionReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_PERMISSION_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_PERMISSION_LIST:
            return {
                ...state,
                permissionList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};

export default PermissionReducer;
