import { motion, useInView } from "framer-motion";
import { useRef, useState } from "react";

const StringDropdown = ({ onSlideEdit, onRemoveSlide, index, content, template }) => {
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { scale: 0.5 },
        inView: { scale: 1 },
    };
    const [tempSelectedValues, setTempSelectedValues] = useState(Array(content?.dropdown_options?.length).fill(""));

    const handleTempChange = (index, event) => {
        const newTempValues = [...tempSelectedValues];
        newTempValues[index] = event.target.value;
        setTempSelectedValues(newTempValues);
    };

    const renderTemplate = () => {
        const parts = content?.question?.split(/{(\d+)}/g);
        return parts?.map((part, index) => {
            if (index % 2 === 1) {
                const dropdownIndex = parseInt(part, 10);
                const dt = content?.dropdown_options[dropdownIndex];
                const randomString = Math.random().toString(36).substring(2, 15);
                return (
                    <motion.label
                        key={index}
                        variants={variants}
                        initial="initial"
                        whileInView={inView ? "inView" : "initial"}
                        transition={{
                            duration: 0.5,
                        }}
                        htmlFor={`option-${randomString}`}
                        className="tw-inline-flex tw-rounded-md tw-border tw-border-gray-500 tw-bg-white tw-p-1"
                    >
                        <select
                            key={index}
                            value={tempSelectedValues[dropdownIndex]}
                            onChange={(event) => handleTempChange(dropdownIndex, event)}
                            id={`option-${randomString}`}
                            className="tw-w-full tw-p-0 tw-text-[33px] tw-text-[var(--primary-color)] tw-outline-none"
                        >
                            <option value="">- Select -</option>
                            {dt?.options?.map((option, idx) => (
                                <option key={idx} value={option}>
                                    {option}
                                </option>
                            ))}
                        </select>
                    </motion.label>
                );
            } else {
                return part;
            }
        });
    };

    return (
        <div
            className="tw-grid tw-min-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            // style={{ backgroundImage: `url(${template?.background||content?.question_background})` }}
        >
            <div className="tw-px-10 tw-py-7">
                <div className="tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden">
                    <div className="tw-flex tw-h-1/2 tw-w-full tw-flex-wrap tw-gap-[1rem]" ref={ref}>
                        <motion.div className="tw-inline-flex tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[37px] tw-leading-[60px] tw-text-black">
                            <p className="tw-font-mono">{content !== null && renderTemplate()}</p>
                        </motion.div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StringDropdown;
