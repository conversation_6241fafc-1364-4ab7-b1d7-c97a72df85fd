import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { addSubCategory, updateSubCategory } from "@/redux/category/action";
import { useEffect, useId, useState } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";

export default function SubCategoryForm({ open, setOpen, selected }) {
    const formId = useId();
    const params = useParams();
    const dispatch = useDispatch();
    const [data, setData] = useState({
        sub_category_name: "",
        is_active: "true",
    });
    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setData({ ...data, [name]: value });
    };

    useEffect(() => {
        if (selected !== null) {
            setData({
                sub_category_name: selected.sub_category_name,
                is_active: Boolean(selected.is_active),
                id: selected.id,
            });
        } else {
            setData({
                sub_category_name: "",
                is_active: "true",
            });
        }
    }, [selected]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (selected !== null) {
            const payload = {
                id: selected?.id,
                category_id: parseInt(params?.id),
                sub_category_name: data?.sub_category_name,
                is_active: data?.is_active === "true" ? true : false,
            };
            dispatch(updateSubCategory(payload));
            setOpen(false);
        } else {
            const payloadAdd = {
                category_id: parseInt(params?.id),
                sub_category_name: data?.sub_category_name,
                is_active: data?.is_active === "true" ? true : false,
            };
            dispatch(addSubCategory(payloadAdd));
            setOpen(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="tw-z-[1000]">
                <DialogHeader>
                    <DialogTitle>{selected ? "Edit Sub Category" : "Create Sub Category"}</DialogTitle>
                </DialogHeader>
                <div>
                    <form id={formId} onSubmit={handleSubmit} className="tw-space-y-3">
                        <Input type="hidden" name="id" id="id" value={data?.id} />

                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="display_name">Sub Category Name</Label>
                            <Input
                                type="text"
                                name="sub_category_name"
                                value={data?.sub_category_name}
                                onChange={onChangeHandle}
                                id="display_name"
                                placeholder="Sub Category Name"
                            />
                        </div>
                        <div className="tw-grid tw-w-full tw-items-center tw-gap-1.5">
                            <Label htmlFor="is_active">Sub Category Status</Label>
                            <SelectNative
                                name="is_active"
                                id="is_active"
                                value={data?.is_active}
                                onChange={onChangeHandle}
                                placeholder=""
                            >
                                <option value="true">Active</option>
                                <option value="false">In Active</option>
                            </SelectNative>
                        </div>
                    </form>
                </div>
                <DialogFooter>
                    <Button variant="secondary" onClick={() => setOpen(false)}>
                        Cancel
                    </Button>
                    <Button variant="success" form={formId} type="submit">
                        Save
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
