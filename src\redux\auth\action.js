import {
    CLEAR_USER_DATA,
    DOMAIN_HOLDER_DATA,
    DOMAIN_NAME_CHECK,
    DOMAIN_NAME_SAVE,
    LOGIN_DATA,
    LOGIN_REQUEST,
    PERMISSION_DATA,
    USER_PERSONAL_DETAILS_HOLDER,
    USER_PERSONAL_DETAILS_SAVE,
    USER_SIGN_UP,
} from "@/redux-types";

export const onUserLogin = (loginDetails) => {
    return {
        type: LOGIN_REQUEST,
        payload: loginDetails,
    };
};

export const checkDomainAvaiable = (domainName) => {
    return {
        type: DOMAIN_NAME_CHECK,
        payload: domainName,
    };
};
export const onUserSignUp = (signUpData) => {
    return {
        type: USER_SIGN_UP,
        payload: signUpData,
    };
};

export const onUserData = (payload) => {
    return {
        type: LOGIN_DATA,
        payload: payload,
    };
};

export const clearUserData = () => {
    return {
        type: CLEAR_USER_DATA,
        payload: null,
    };
};

export const onDomainData = (payload) => {
    return {
        type: DOMAIN_HOLDER_DATA,
        payload: payload,
    };
};
export const onUserPersonalData = (payload) => {
    return {
        type: USER_PERSONAL_DETAILS_HOLDER,
        payload: payload,
    };
};

export const onDomainSave = (domain) => {
    return {
        type: DOMAIN_NAME_SAVE,
        payload: domain,
    };
};

export const onUserPersonalDataSave = (data) => {
    return {
        type: USER_PERSONAL_DETAILS_SAVE,
        payload: data,
    };
};

export const onPermissionDataSave = (data) => {
    return {
        type: PERMISSION_DATA,
        payload: data,
    };
};
