import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { iconsList } from "@/pages/dashboard/createcourse/chaptercontent/slides/icon-list";
import { CircleFadingPlus } from "lucide-react";
import { EditIcon, Trash2, X } from "lucide-react";
import { useRef, useState } from "react";

export default function SlideHeader({
    index,
    content,
    contentArray,
    onSlideEdit,
    onSetDelete,
    onRemoveSlide,
    setOpenIconDialog,
    setContentArray,
}) {
    const [editable, setEditable] = useState(false);
    const headingRef = useRef(null);
    const DeleteIcon = content?.id ? Trash2 : X;
    const contentEditable = ["interaction", "homework", "quiz", "content_url", "lecture_data"].some(
        (key) => content?.[key],
    );
    const completionRequired = ["interaction", "homework"].some((key) => content?.[key]);
    return (
        <div className="tw-flex tw-items-center tw-justify-between tw-p-3">
            <div className="tw-flex tw-items-center tw-gap-2">
                <img
                    onClick={() => {
                        setOpenIconDialog(true);
                        onSlideEdit(content, index);
                    }}
                    className="tw-w-[40px] tw-cursor-pointer"
                    src={content?.icon || iconsList[0].icon}
                    alt=""
                />
                <h1
                    title="Double Click to Edit"
                    ref={headingRef}
                    onBlur={() => {
                        setContentArray((prev) => {
                            prev[index]["lecture_title"] = headingRef?.current?.innerText;
                            return [...prev];
                        });
                        setEditable(false);
                    }}
                    onDoubleClick={() => setEditable(true)}
                    contentEditable={editable}
                    className="tw-min-h-8 tw-min-w-20 tw-rounded-md tw-border tw-px-2 tw-font-mono tw-text-xl tw-font-semibold tw-text-gray-400"
                >
                    {content?.lecture_title}
                </h1>
            </div>
            <div className="tw-flex tw-items-center tw-justify-end tw-gap-2">
                {completionRequired && (
                    <div className="tw-flex tw-items-center tw-space-x-2">
                        <Switch
                            id={`is_completion_required_${index}`}
                            checked={contentArray[index]?.is_completion_required}
                            onCheckedChange={(e) => {
                                setContentArray((prev) => {
                                    prev[index].is_completion_required = e;
                                    return [...prev];
                                });
                            }}
                        />
                        <Label htmlFor={`is_completion_required_${index}`}>
                            Completion <br /> Required
                        </Label>
                    </div>
                )}
                {contentEditable && (
                    <div
                        title="Edit"
                        onClick={() => onSlideEdit(content, index)}
                        className="tw-flex tw-w-[130px] tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-full tw-border-[1px] tw-border-solid tw-border-gray-400 tw-bg-gray-100 tw-p-1 tw-transition-all hover:tw-gap-1 hover:tw-bg-gray-200"
                    >
                        <h1 className="tw-text-sm">Edit Content</h1> <EditIcon size={16} />
                    </div>
                )}
                <div
                    title={content?.id ? "Delete" : "Remove"}
                    onClick={() => {
                        if (content?.id) {
                            onSetDelete(content, index);
                        } else {
                            onRemoveSlide(index);
                        }
                    }}
                    className="tw-text-md tw-flex tw-h-[30px] tw-w-[30px] tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-full tw-border-[1px] tw-bg-gray-100 tw-font-bold tw-shadow-sm hover:tw-border-[1px] hover:tw-bg-slate-200"
                >
                    <DeleteIcon className="tw-text-slate-600" size={18} />
                </div>
            </div>
        </div>
    );
}

export function AddContentFunction({ onSlideEdit, content, index }) {
    return (
        <div className="tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center tw-gap-5 tw-rounded-md tw-p-3">
            <Button
                variant="reset"
                onClick={() => onSlideEdit(content, index)}
                className="tw-m-1 tw-flex !tw-h-auto tw-w-[200px] tw-cursor-pointer tw-items-center tw-justify-center tw-gap-3 tw-rounded-2xl tw-border-[1px] tw-border-dashed tw-border-gray-400 !tw-p-4 tw-transition-all hover:tw-bg-white"
            >
                <p className="tw-leading-0 tw-text-xl tw-font-semibold tw-text-gray-400">Add Content</p>
                <CircleFadingPlus className="!tw-size-6 tw-text-gray-400" />
            </Button>
        </div>
    );
}
