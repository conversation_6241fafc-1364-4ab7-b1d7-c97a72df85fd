const BreadCrumb = ({ breadCrumbs }) => {
    return (
        <div className="simple_brdcrmb_lms d-flex width_100 justify_content_right margin_bottom_xs margin_top_xs">
            {breadCrumbs.map((breadCrumb, i, arr) => (
                <div key={i} className={`d_flex`}>
                    {i !== 0 && <span>/</span>}
                    <a
                        href={breadCrumb?.redirection ? breadCrumb?.redirection : "javascript:void(0)"}
                        className={`${i === arr.length - 1 ? "active_brdcrmb" : ""}`}
                    >
                        {breadCrumb.header}
                    </a>
                </div>
            ))}
        </div>
    );
};

export default BreadCrumb;
