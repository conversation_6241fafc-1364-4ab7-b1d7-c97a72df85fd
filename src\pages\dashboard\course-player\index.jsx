import { ProgressCustom } from "@/components/progress";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogOverlay,
    DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import ContentWrapper from "@/pages/dashboard/course-player/ContentWrapper";
import { PlayerProvider, usePlayer } from "@/pages/dashboard/course-player/player-context";
import { useViewCourses } from "@/react-query/courses";
import { fetchBookmarkingReq, UpdateBookmarkingData } from "@/redux/course/action";
import { AddUserPoints } from "@/redux/dashboard/dash/action";
import { IconButton, Tooltip } from "@mui/material";
import { Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import "react-responsive-modal/styles.css";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

function sortTheSequence(data) {
    const transformData = data.map((chapter) => ({
        ...chapter,
        lms_course_chapter_contents: chapter.lms_course_chapter_contents.sort(
            (a, b) => Number(a?.content_order) - Number(b?.content_order),
        ),
    }));
    return transformData;
}

const CoursePlayerPage = () => {
    return (
        <PlayerProvider>
            <Container />
        </PlayerProvider>
    );
};

export default CoursePlayerPage;

function Container() {
    const [open, setOpen] = useState(false);
    const onOpenModal = () => setOpen(true);
    const {
        currentIndex,
        setCurrentIndex,
        courseStarted,
        setCourseStarted,
        minimize,
        setMinimize,
        fullscreen,
        setFullscreen,
        setContentLength,
        setBookmarking,
        setCourseDetails,
        contentArray,
        setContentArray,
        handleNext,
        handlePrev,
    } = usePlayer();
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const dispatch = useDispatch();
    const Bookmarking = useSelector((state) => state?.CourseReducers?.courseBookmarking);
    const elementRef = useRef(null);
    const [CONTENT, setCONTENT] = useState(null);
    const [chaptersArray, setChaptersArray] = useState([]);
    const [courseDetails, setLocalCourseDetails] = useState({});
    const [courseAccess, setCourseAccess] = useState(false);
    const courseData = useViewCourses(params.course_id);

    useEffect(() => {
        if (courseData.isSuccess) {
            if (!courseData.data.success) setCourseAccess(true);
            setCourseDetails(courseData.data.data);
            setLocalCourseDetails(courseData.data.data);
        }
        if (courseData.isError) setCourseAccess(true);
    }, [courseData.isSuccess, courseData.isError]);

    useEffect(() => {
        if (courseDetails?.id) {
            const data = { course_id: courseDetails?.id };
            dispatch(fetchBookmarkingReq(data));
        }
    }, [courseDetails]);

    useEffect(() => {
        if (Object.keys(courseDetails)?.length > 0) {
            setCourseDetails(courseDetails);
            const allLectures = sortTheSequence(courseDetails?.courseChapters).reduce((acc, chapter, chapterIndex) => {
                chapter.lms_course_chapter_contents.forEach((content, contentIndex) => {
                    acc.push({
                        content,
                        chapter,
                        contentIndex,
                        chapterIndex,
                    });
                });
                return acc;
            }, []);
            setContentArray(allLectures);
            setChaptersArray(sortTheSequence(courseDetails?.courseChapters));
        }
    }, [courseDetails]);

    useEffect(() => {
        if (contentArray?.length > 0 && courseStarted) {
            setCONTENT(contentArray[currentIndex] || null);
            setContentLength(contentArray.length);
        }
    }, [currentIndex, contentArray, courseStarted]);

    useEffect(() => {
        if (CONTENT !== null) onBookmark(CONTENT);
    }, [CONTENT]);

    useEffect(() => {
        if (Bookmarking?.course_bookmarking_details !== undefined && CONTENT !== null) {
            const bookmark = Bookmarking?.course_bookmarking_details?.find(
                (dt) => dt?.chapter_id == CONTENT?.chapter?.id,
            );
            const chapter = courseDetails?.courseChapters?.find((dt) => dt?.id == CONTENT?.chapter?.id);

            if (bookmark?.is_chapter_complete) {
                dispatch(
                    AddUserPoints({
                        user_id: localStorage.getItem("userId"),
                        points: chapter?.chapter_points,
                        event: "Start Chapter",
                        entity_id: `${CONTENT?.chapter?.id}`,
                    }),
                );
            }
        }
    }, [Bookmarking, CONTENT]);

    const onCourseStart = () => setCourseStarted(true);

    const onCourseBack = () => setCourseStarted(false);

    const onContentSelect = (sequence) => {
        if (courseDetails?.courseSettings?.navigation_sidenav == true) {
            if (!courseStarted) {
                toast.warning("Please Start the course");
                return false;
            }
            setCurrentIndex(sequence - 1);
        }
    };

    const onBookmark = (CONTENT) => {
        const chapter = CONTENT?.chapter;
        const content = CONTENT?.content;

        const modified = Bookmarking?.course_bookmarking_details?.map((cptr) => {
            const totalContents = cptr.contentArray.length;
            const consumedContents = cptr.contentArray?.filter((dt) => dt?.is_content_consumed === true).length;
            const chapterPercentage = (consumedContents / totalContents) * 100;
            return {
                chapter_id: cptr?.chapter_id,
                is_chapter_seen: cptr?.chapter_id == chapter?.id ? true : cptr.is_chapter_seen,
                is_chapter_complete: false,
                chapter_percentage: chapterPercentage,
                contentArray: cptr.contentArray.map((cnt) => {
                    let isContentConsumed = cnt?.content_id == content?.id ? true : cnt.is_content_consumed;
                    if (["ZIP", "QUIZ", "INTERACTION"].includes(content?.content_type)) {
                        isContentConsumed = cnt?.content_id == content?.id ? false : cnt.is_content_consumed;
                    }
                    return {
                        content_id: cnt.content_id,
                        is_content_consumed: isContentConsumed,
                        is_content_seen: cnt?.content_id == content?.id ? true : cnt.is_content_seen,
                        video_last_timestamp: cnt.video_last_timestamp,
                    };
                }),
            };
        });

        const totalChapters = courseDetails?.courseChapters?.length || 0;
        const completedChapters = modified?.filter((cptr) => cptr?.is_chapter_complete)?.length || 0;
        const courseCompletionPercent = (completedChapters / totalChapters) * 100;

        const bookmark = {
            learning_path_id: params?.path_id ? parseInt(params?.path_id) : null,
            user_id: Bookmarking?.user_id,
            course_id: Bookmarking?.course_id,
            course_status: Bookmarking?.course_status,
            course_seen: Bookmarking?.course_seen,
            course_start_date: Bookmarking?.course_start_date,
            course_completion_percent: courseCompletionPercent,
            course_end_date: "",
            status: true,
            last_activity: JSON.stringify({
                name: CONTENT?.content?.lecture_title,
                sequence: currentIndex,
            }),
            course_bookmarking_details: modified,
        };

        dispatch(UpdateBookmarkingData(bookmark));
    };

    useEffect(() => {
        if (location?.pathname?.includes("course-details/view")) {
            if (
                params?.chapter_id == undefined &&
                params?.content_id == undefined &&
                Object?.keys(courseDetails)?.length > 0
            ) {
                var chapterID = courseDetails?.courseChapters[0]?.id;
                var contentID = courseDetails?.courseChapters[0]?.lms_course_chapter_contents[0]?.id;
                navigate(`/dashboard/course-details/view/${params?.course_id}/${chapterID}/${contentID}`);
            }
        }
    }, [params, courseDetails]);

    const toggleFullScreen = () => {
        const elem = elementRef.current;
        if (elem) {
            if (!document.fullscreenElement) {
                elem.requestFullscreen().catch((err) => {
                    toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
                });
                setFullscreen(!fullscreen);
            } else {
                document.exitFullscreen();
                setFullscreen(!fullscreen);
            }
        }
    };

    useEffect(() => {
        if (Object.keys(Bookmarking)?.length > 0 && Bookmarking !== undefined) {
            setBookmarking(Bookmarking);
            if (Bookmarking?.last_activity) {
                if (localStorage.getItem("course_resume_startover") !== "true") onOpenModal();
            }
        }
    }, [Bookmarking]);

    const backToCourse = () => {
        localStorage.setItem("course_resume_startover", "false");
        navigate(-1);
    };

    const chapterTitle = courseDetails?.courseChapters?.find(
        (dt) => dt?.id == CONTENT?.content?.chapter_id,
    )?.chapter_title;
    const lectureTitle = CONTENT?.content?.lecture_title;

    const onSubmitCourse = () => {
        if (Bookmarking?.course_status == "completed") {
            navigate(`/generate-certificate/${courseDetails?.id}/${Bookmarking?.id}`);
        } else {
            toast.warning(`Please complete course 100%`, {
                description: `You need to complete the course first, then you can submit & get certificate`,
            });
        }
    };

    return courseData.isLoading ? (
        <div className="tw-flex tw-min-h-dvh tw-items-center tw-justify-center tw-bg-white">
            <Loader2 className="tw-size-10 tw-animate-spin tw-text-blue-600" />
        </div>
    ) : (
        <>
            <div className="tw-grid tw-h-dvh tw-grid-cols-12 tw-grid-rows-12 tw-gap-4 tw-overflow-hidden tw-bg-gray-200 tw-p-3 2xl:tw-p-5">
                <div className="tw-col-span-1 tw-flex tw-items-center tw-justify-center tw-rounded-xl tw-bg-white">
                    <img
                        className="tw-h-6 2xl:tw-h-12"
                        src={JSON.parse(localStorage.getItem("org_data"))?.about?.company_logo}
                        alt=""
                    />
                </div>
                <div className="tw-col-span-11 tw-flex tw-w-full tw-items-center tw-justify-between tw-rounded-xl tw-bg-white tw-px-5">
                    <div className="tw-max-w-[30rem]">
                        <h5 className="tw-line-clamp-1 !tw-font-lexend tw-text-lg tw-font-medium tw-leading-none">
                            {courseDetails?.course_title}
                        </h5>
                    </div>
                    <div className="tw-flex tw-items-center tw-gap-4">
                        <div className="plyr_breadcrumbs">
                            <div className="tw-flex tw-items-center tw-gap-4 !tw-font-lexend tw-font-medium">
                                {chapterTitle ? (
                                    <span title={chapterTitle} className="tw-line-clamp-1 tw-w-fit tw-max-w-60">
                                        {chapterTitle}
                                    </span>
                                ) : (
                                    ""
                                )}

                                {lectureTitle ? (
                                    <span title={lectureTitle} className="tw-line-clamp-1 tw-w-fit tw-max-w-60">
                                        / {lectureTitle}
                                    </span>
                                ) : (
                                    ""
                                )}
                            </div>
                        </div>
                        <div className="plyr_action">
                            <Tooltip title="Exit from course">
                                <IconButton size="small" onClick={backToCourse}>
                                    <i className="fa-solid fa-xmark"></i>
                                </IconButton>
                            </Tooltip>
                        </div>
                    </div>
                </div>
                <div
                    className={cn(
                        "tw-col-span-2 tw-row-span-11 tw-h-full tw-overflow-hidden tw-rounded-xl tw-bg-white",
                        minimize ? "tw-hidden" : "tw-block",
                    )}
                >
                    <div className="tw-flex tw-size-full tw-flex-col tw-gap-3 tw-overflow-y-auto tw-p-6">
                        {chaptersArray?.map((chapter, chapterIndex) => (
                            <div key={chapterIndex} className="tw-flex tw-flex-col tw-gap-4">
                                <h6 className="tw-flex tw-items-center tw-gap-2 !tw-font-lexend tw-text-base tw-font-bold tw-leading-5">
                                    <i className="fa-solid fa-cube"></i> {chapter?.chapter_title}
                                </h6>
                                <div className="tw-space-y-2">
                                    {chapter?.lms_course_chapter_contents?.map((content, contentIndex) => (
                                        <div className="tw-relative tw-pl-6" key={contentIndex}>
                                            <div className="tw-absolute tw-left-0">
                                                {CONTENT?.content?.id == content?.id && (
                                                    <i
                                                        style={{
                                                            animation: "scale-icon 1s ease-in-out infinite",
                                                        }}
                                                        className="fa-solid fa-circle-dot tw-text-[#dbd8d8]"
                                                    ></i>
                                                )}
                                            </div>
                                            <p
                                                className={cn(
                                                    `tw-line-clamp-1 tw-font-medium`,
                                                    CONTENT?.content?.id == content?.id && "!tw-text-black",
                                                    Bookmarking?.course_bookmarking_details?.[chapterIndex]
                                                        ?.contentArray?.[contentIndex]?.is_content_consumed &&
                                                        "!tw-text-green-600",
                                                    courseDetails?.courseSettings?.navigation_sidenav
                                                        ? "tw-cursor-pointer"
                                                        : "tw-cursor-not-allowed",
                                                )}
                                                onClick={() => onContentSelect(content?.sequence)}
                                            >
                                                <i className={content?.icon}></i> {content?.lecture_title}
                                            </p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
                <div
                    ref={elementRef}
                    className={cn(
                        "tw-grid tw-grid-rows-subgrid",
                        minimize ? "tw-col-span-12" : "tw-col-span-10",
                        fullscreen ? "tw-row-span-12 tw-bg-gray-200 tw-p-4" : "tw-row-span-11",
                    )}
                >
                    <div
                        className={cn(
                            "tw-h-full tw-overflow-hidden tw-rounded-xl tw-border-2 tw-border-dashed tw-border-gray-800 tw-bg-white",
                            minimize ? "tw-col-span-12" : "tw-col-span-10",
                            fullscreen ? "tw-row-span-11" : "tw-row-span-10",
                        )}
                    >
                        <ContentWrapper
                            courseStarted={courseStarted}
                            CONTENT={CONTENT}
                            courseDetails={courseDetails}
                            Bookmarking={Bookmarking}
                        />
                    </div>
                    <div
                        className={cn(
                            "tw-row-span-1 tw-flex tw-items-center tw-justify-between tw-rounded-xl",
                            minimize ? "tw-col-span-12" : "tw-col-span-10",
                        )}
                    >
                        <div className="tw-flex tw-items-center tw-gap-3">
                            <button className="icon_btns" onClick={() => setMinimize(!minimize)}>
                                {minimize ? (
                                    <i className="fa-solid fa-bars-staggered"></i>
                                ) : (
                                    <i className="fa-solid fa-bars"></i>
                                )}
                            </button>
                            <button className="icon_btns" onClick={toggleFullScreen}>
                                {fullscreen ? (
                                    <i className="fa-solid fa-compress"></i>
                                ) : (
                                    <i className="fa-solid fa-expand"></i>
                                )}
                            </button>
                        </div>
                        <div className="tw-flex tw-items-center tw-justify-center tw-gap-3">
                            <div className="tw-flex tw-size-12 tw-items-center tw-justify-center tw-rounded-full tw-bg-white">
                                <ProgressCustom progress={Bookmarking?.course_completion_percent || 0} />
                            </div>
                            <div className="tw-flex tw-h-full tw-min-h-full tw-items-center tw-gap-5 tw-rounded-2xl tw-bg-white tw-px-4 tw-py-3 tw-text-[#535354b4]">
                                <b className="!tw-font-lexend tw-text-[#535354b4]">
                                    Course {Bookmarking?.course_status}
                                </b>{" "}
                                {" | "}
                                <p className="!tw-font-lexend tw-text-sm tw-text-[#00AFF0]">
                                    {
                                        Bookmarking?.course_bookmarking_details?.filter(
                                            (dt) => dt?.is_chapter_complete == false,
                                        )?.length
                                    }{" "}
                                    Chapters left
                                </p>
                            </div>
                        </div>

                        <div className="tw-flex tw-items-center tw-justify-center tw-gap-3">
                            <>
                                {courseStarted && (
                                    <button className="plyr_btns" onClick={onCourseBack}>
                                        <i className="fa-solid fa-left-long"></i> <p>Back</p>
                                    </button>
                                )}
                            </>
                            <>
                                {currentIndex >= 1 && (
                                    <button className="plyr_btns" onClick={handlePrev}>
                                        <i className="fa-solid fa-chevron-left"></i> <p>Prev</p>
                                    </button>
                                )}
                            </>

                            {currentIndex == contentArray?.length - 1 ? (
                                <>
                                    {/* {currentIndex >= 0 && courseStarted ? (
                                        <button className="plyr_btns" onClick={onSubmitCourse}>
                                            <i className="fa-solid fa-check"></i> <p>Submit</p>
                                        </button>
                                    ) : null} */}
                                </>
                            ) : (
                                <>
                                    {currentIndex >= 0 && courseStarted ? (
                                        <button className="plyr_btns" onClick={handleNext}>
                                            <p>Next</p> <i className="fa-solid fa-chevron-right"></i>
                                        </button>
                                    ) : null}
                                </>
                            )}

                            {courseStarted == false && (
                                <button className="plyr_btns" onClick={onCourseStart}>
                                    <i className="fa-solid fa-play"></i> <p>Start</p>
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            <Dialog
                open={courseAccess}
                onOpenChange={(e) => {
                    if (e == false) return navigate(`/dashboard/course?page=1`);
                }}
            >
                <DialogOverlay className="tw-z-[1001] tw-bg-white" />
                <DialogContent className="tw-z-[1002]">
                    <DialogHeader>
                        <DialogTitle className="!tw-text-2xl tw-font-medium">Access Restricted</DialogTitle>
                        <DialogDescription>
                            You do not have the required permissions to view this course. Please reach out to your
                            administrator for further assistance.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button type="submit" asChild>
                            <Link to="/dashboard">Go to Dashboard</Link>
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
}
