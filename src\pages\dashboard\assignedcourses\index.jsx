import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import CreateCourse from "./CreateCourse";

const courseStatusList = [
    { key: "completed", label: "Published" },
    { key: "pending", label: "Pending" },
    { key: "deleted", label: "Deleted" },
    { key: "hold", label: "Hold" },
];

const AssignedCourses = () => {
    const navigate = useNavigate();
    const [allCourses, setAllCourses] = useState([]);
    const [filteredCourses, setFilteredCourses] = useState([]);
    const [categoryList, setCategoryList] = useState([]);
    const [courseStatus, setCourseStatus] = useState(null);
    const [filterState, setFilterState] = useState({
        search: "",
        category_id: "",
        course_status: "",
    });

    const [open, setOpen] = useState(false);
    const [openAlert, setOpenAlert] = useState(false);

    useEffect(() => {
        getCourses();
        getCategory();
    }, []);

    const getCourses = async () => {
        try {
            const res = await tanstackApi.get("course/list-assigned-courses");
            setAllCourses(res?.data?.data);
            setFilteredCourses(res?.data?.data); // Initialize filteredCourses with all courses
        } catch (err) {
            setAllCourses([]);
            setFilteredCourses([]);
        }
    };

    const getCategory = async () => {
        try {
            const res = await tanstackApi.get("course/categories/get-course-category");
            setCategoryList(res?.data?.data);
        } catch (err) {
            setCategoryList([]);
        }
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSetCourseStatus = async () => {
        let payload = courseStatus;
        try {
            await tanstackApi.post("course/creation/update-course-status", { ...payload });
            toast.success("Status changed", {
                description: `Course status has been changed successfully`,
            });
            getCourses();
        } catch (err) {
            toast.error("Something went wrong", {
                description: `Course status not changed. Sorry!`,
            });
        }
    };

    const onCourseStatusChange = (e, courseID) => {
        let payload = {
            course_id: courseID,
            status: e?.target.value,
        };
        setCourseStatus(payload);
        setOpenAlert(true);
    };

    const handleSearch = () => {
        const results = allCourses.filter((course) => {
            const matchesSearch = course.course_title.toLowerCase().includes(filterState.search.toLowerCase());
            const matchesCategory = filterState.category_id ? course.category_id === filterState.category_id : true;
            const matchesStatus = filterState.course_status ? course.course_status === filterState.course_status : true;
            return matchesSearch && matchesCategory && matchesStatus;
        });
        setFilteredCourses(results);
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will change your course&apos;s status and will reflect in all course listings.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onSetCourseStatus}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <CreateCourse open={open} setOpen={setOpen} getCourses={getCourses} />
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-book"></i> Assigned Courses
                </h4>
            </div>
            <br />
            <div className="page_filters tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Course Title
                    </label>
                    <input
                        className="tw-text-sm"
                        type="text"
                        placeholder="Search by course title ..."
                        name="search"
                        value={filterState.search}
                        onChange={onChangeHandle}
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Category
                    </label>
                    <select
                        className="tw-text-sm"
                        name="category_id"
                        value={filterState.category_id}
                        onChange={onChangeHandle}
                    >
                        <option value=""> - Choose Category - </option>
                        {categoryList?.map((data, idx) => (
                            <option key={idx} value={data?.id}>
                                {data?.category_name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Course Status
                    </label>
                    <select
                        className="tw-text-sm"
                        name="course_status"
                        value={filterState.course_status}
                        onChange={onChangeHandle}
                    >
                        <option value=""> - Choose Status - </option>
                        {courseStatusList?.map((status, idx) => (
                            <option key={idx} value={status?.key}>
                                {status?.label}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={handleSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button
                        className="clear_btn"
                        onClick={() => {
                            setFilterState({ search: "", category_id: "", course_status: "" });
                            setFilteredCourses(allCourses); // Reset to show all courses
                        }}
                    >
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table tw-mt-2 tw-h-full">
                <table>
                    <thead>
                        <tr>
                            <th>Course</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Category</th>
                            <th>Access</th>
                            <th>Course Status</th>
                            <th>Creation Date</th>
                            <th>Expiration Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredCourses.length === 0 ? (
                            <tr>
                                <td colSpan="9" className="tw-h-40 tw-py-4 tw-text-center">
                                    No courses found matching your criteria.
                                </td>
                            </tr>
                        ) : (
                            filteredCourses.map((row, idx) => (
                                <tr key={idx}>
                                    <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                        <div className="table_image_alpha">
                                            <img
                                                src={row?.course_banner_url || "/assets/course-placeholder.png"}
                                                alt=""
                                            />
                                        </div>
                                    </td>
                                    <td onClick={() => navigate(`/dashboard/view-course/${row?.id}`)}>
                                        {row?.course_title}
                                    </td>
                                    <td>{row?.is_scorm ? "SCORM" : "Combined"}</td>
                                    <td>{row?.lms_course_category?.category_name}</td>
                                    <td>{row?.is_public ? "Public" : "Private"}</td>
                                    <td>
                                        <select
                                            name="course_status"
                                            value={row?.course_status}
                                            className="table_input"
                                            onChange={(e) => onCourseStatusChange(e, row?.id)}
                                        >
                                            {courseStatusList?.map((status, idx) => (
                                                <option key={idx} value={status?.key}>
                                                    {status?.label}
                                                </option>
                                            ))}
                                        </select>
                                    </td>
                                    <td>{moment(row?.createdAt).format("LL")}</td>
                                    <td>{moment(row?.lms_course_settings?.[0]?.expiration_date).format("LL")}</td>
                                    <td>
                                        <div className="tw-flex tw-gap-2">
                                            {!row?.is_public ? (
                                                <Link to={`/dashboard/edit-course/${row?.id}`}>
                                                    <button className="selected_btn_alpha">
                                                        <i className="fa-solid fa-pen-to-square"></i> Edit
                                                    </button>
                                                </Link>
                                            ) : null}
                                            <Link to={`/dashboard/view-course/${row?.id}`}>
                                                <button className="selected_btn_alpha">
                                                    <i className="fa-solid fa-eye"></i> View
                                                </button>
                                            </Link>
                                        </div>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
        </>
    );
};

export default AssignedCourses;
