import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alogClose,
    <PERSON>alogContent,
    DialogDescription,
    <PERSON>alogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const TagsForm = ({ open, setOpen, editData, getTagsList }) => {
    const [tag, setTag] = useState({
        display_name: "",
        type: "",
    });

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setTag({ ...tag, [name]: value });
    };

    useEffect(() => {
        if (editData !== null) {
            setTag({
                display_name: editData?.display_name,
                type: editData?.type,
            });
        } else {
            setTag({
                display_name: "",
                type: "",
            });
        }
    }, [editData]);

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!tag?.display_name) {
            toast?.warning("Display Icon", {
                description: "Tag display name is required",
            });
            return false;
        }

        if (editData == null) {
            const payload = {
                display_name: tag?.display_name,
                type: tag?.display_name?.toLowerCase()?.replaceAll(" ", "_"),
            };

            await tanstackApi
                .post("tag-master/add-tag", { ...payload })
                .then((res) => {
                    toast.success("Saved Successfully", {
                        description: res?.data?.message,
                    });
                    getTagsList();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                id: editData?.id,
                display_name: tag?.display_name,
                type: tag?.display_name?.toLowerCase()?.replaceAll(" ", "_"),
            };

            await tanstackApi
                .put("tag-master/update-tag", { ...payload })
                .then((res) => {
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    getTagsList();
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">{editData !== null ? "Update" : "Create a new"} Tag!</h1>
                        </DialogTitle>
                        <DialogDescription>Fill up below details according to your Tag convenience.</DialogDescription>
                    </DialogHeader>
                    <div className="tw-grid tw-grid-cols-1 tw-gap-5">
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="display_name">Display Name</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={tag?.display_name}
                                        name="display_name"
                                        id="display_name"
                                        placeholder="Enter tag display name here"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary">
                                <i className="fa-solid fa-xmark"></i> Close
                            </Button>
                        </DialogClose>
                        <Button type="submit" onClick={onDataSubmit}>
                            <i className="fa-solid fa-floppy-disk"></i> Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default TagsForm;
