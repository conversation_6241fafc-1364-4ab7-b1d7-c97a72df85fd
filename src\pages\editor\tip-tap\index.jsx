import MenuBar from "@/pages/editor/tip-tap/menu";
import { Color } from "@tiptap/extension-color";
import Document from "@tiptap/extension-document";
import Dropcursor from "@tiptap/extension-dropcursor";
import FontFamily from "@tiptap/extension-font-family";
import Image from "@tiptap/extension-image";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import Text from "@tiptap/extension-text";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Typography from "@tiptap/extension-typography";
import { EditorContent, FloatingMenu, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { AlignCenter, AlignJustify, AlignLeft, AlignRight } from "lucide-react";

const extensions = [
    StarterKit,
    Color,
    Document,
    Paragraph,
    Text,
    TextStyle,
    FontFamily,
    Dropcursor,
    Placeholder.configure({
        placeholder: "Write something …",
    }),
    Image,
    TextAlign.configure({
        types: ["heading", "paragraph"],
    }),
    Typography,
];
const content = "";

const textAlignment = {
    left: AlignLeft,
    center: AlignCenter,
    right: AlignRight,
    justify: AlignJustify,
};

export default function TipTapEditor() {
    const editor = useEditor({
        extensions,
        content,
        editorProps: {
            attributes: {
                spellcheck: "false",
            },
        },
    });

    return (
        <>
            <div className="tw-prose tw-w-full tw-max-w-full prose-p:tw-mb-1 prose-p:tw-mt-2 prose-hr:tw-my-4 prose-hr:tw-border-gray-500">
                <MenuBar editor={editor} />
                <EditorContent editor={editor} />
                {editor && (
                    <FloatingMenu editor={editor} tippyOptions={{ duration: 100 }}>
                        {/* <button
                            onClick={() => editor.chain().focus().setTextAlign("left").run()}
                            className={editor.isActive({ textAlign: "left" }) ? "is-active" : ""}
                        >
                            Left
                        </button>
                        <button
                            onClick={() => editor.chain().focus().setTextAlign("center").run()}
                            className={editor.isActive({ textAlign: "center" }) ? "is-active" : ""}
                        >
                            Center
                        </button>
                        <button
                            onClick={() => editor.chain().focus().setTextAlign("right").run()}
                            className={editor.isActive({ textAlign: "right" }) ? "is-active" : ""}
                        >
                            Right
                        </button>
                        <button
                            onClick={() => editor.chain().focus().setTextAlign("justify").run()}
                            className={editor.isActive({ textAlign: "justify" }) ? "is-active" : ""}
                        >
                            Justify
                        </button> */}
                        {/* <div className="floating-menu">
                        <button
                            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                            className={editor.isActive("heading", { level: 1 }) ? "is-active" : ""}
                        >
                            H1
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                            className={editor.isActive("heading", { level: 2 }) ? "is-active" : ""}
                        >
                            H2
                        </button>
                        <button
                            onClick={() => editor.chain().focus().toggleBulletList().run()}
                            className={editor.isActive("bulletList") ? "is-active" : ""}
                        >
                            Bullet list
                        </button>
                    </div> */}
                    </FloatingMenu>
                )}
                {/* <BubbleMenu editor={editor}>
                    <Card className="tw-p-0">
                        <ToggleGroup
                            onValueChange={(value) => {
                                editor
                                    .chain()
                                    .focus()
                                    .setTextAlign(value == "" ? "left" : value)
                                    .run();
                            }}
                            value="left"
                            type="single"
                        >
                            {Object.entries(textAlignment).map(([key, Icon]) => {
                                return (
                                    <ToggleGroupItem key={key} value={key} asChild>
                                        <Button
                                            className={cn(
                                                "!tw-text-black",
                                                editor.isActive({ textAlign: key }) ? "tw-bg-gray-500" : "",
                                            )}
                                        >
                                            <Icon />
                                        </Button>
                                    </ToggleGroupItem>
                                );
                            })}
                        </ToggleGroup>
                    </Card>
                </BubbleMenu> */}
            </div>
            {/* <div className="tw-mx-auto tw-w-full !tw-max-w-[80%] tw-bg-secondary tw-p-5">
                <p className="tw-font-bold">Preview - JSON</p>
                <EditorJSONPreview editor={editor} />
            </div> */}
        </>
    );
}
