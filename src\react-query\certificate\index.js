import { tanstackApi } from "@/react-query/api";
import { useQuery } from "@tanstack/react-query";

export function useGetCertificate(certificateType) {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["certificate", { certificateType, userId }],
        queryFn: async () => {
            return (await tanstackApi.get(`/certificate/get-certificates/${certificateType}`)).data;
        },
        enabled: !!certificateType,
    });
}

export function useGetUserCertificate() {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["certificate", { userId }],
        queryFn: async () => {
            return (await tanstackApi.post(`/reports/get-certificate-reports-user`, {})).data;
        },
        enabled: !!userId,
    });
}
