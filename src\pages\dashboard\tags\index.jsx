import { Button } from "@/components/ui/button";
import { useGetTags } from "@/react-query/tags";
import moment from "moment";
import { useMemo, useState } from "react";
import TagsForm from "./TagsForm";

const TagMaster = () => {
    const { data: tags, isLoading, refetch } = useGetTags();
    const [searchTerm, setSearchTerm] = useState("");
    const [appliedFilter, setAppliedFilter] = useState("");

    const allTags = useMemo(() => {
        return tags?.data || [];
    }, [tags]);
    const filteredTags = useMemo(() => {
        if (!appliedFilter) return allTags;
        return allTags.filter((tag) => tag.display_name?.toLowerCase().includes(appliedFilter.toLowerCase()));
    }, [allTags, appliedFilter]);

    const [open, setOpen] = useState(false);
    const [editData, setEditData] = useState(null);

    const onAddNewType = () => {
        setEditData(null);
        setOpen(true);
    };

    const onEditType = (data) => {
        setEditData(data);
        setOpen(true);
    };

    const handleSearch = () => {
        setAppliedFilter(searchTerm);
    };

    const handleClear = () => {
        setSearchTerm("");
        setAppliedFilter("");
    };

    return (
        <>
            <TagsForm open={open} setOpen={setOpen} editData={editData} getTagsList={refetch} />
            <div>
                <div className="tw-flex tw-justify-between">
                    <h4 className="tw-text-xl tw-font-semibold">
                        <i className="fa-solid fa-tags"></i> Tags
                    </h4>
                    <Button className="tw-px-2 tw-py-1" onClick={onAddNewType}>
                        <i className="fa-solid fa-plus"></i> New Tag
                    </Button>
                </div>
                <div className="page_filters tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label htmlFor="tagSearch">Tag Name</label>
                        <input
                            id="tagSearch"
                            style={{ minWidth: "400px" }}
                            type="text"
                            placeholder="Search by display Name ..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={handleSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={handleClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table tw-mt-4">
                    {isLoading ? (
                        <div className="tw-flex tw-h-40 tw-items-center tw-justify-center tw-rounded-md tw-border tw-py-4 tw-text-center tw-text-slate-500">
                            Loading...
                        </div>
                    ) : filteredTags && filteredTags.length > 0 ? (
                        <table>
                            <thead>
                                <tr>
                                    <th>Display Name</th>
                                    <th>Tag (integration key)</th>
                                    <th>Creation On</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredTags.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>{row?.display_name}</td>
                                        <td>{row?.type}</td>
                                        <td>{moment(row?.createdAt).format("LL")}</td>
                                        <td>
                                            <button className="selected_btn" onClick={() => onEditType(row)}>
                                                <i className="fa-solid fa-edit"></i> Edit
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    ) : (
                        <div className="tw-flex tw-h-40 tw-items-center tw-justify-center tw-rounded-md tw-border tw-py-4 tw-text-center tw-text-slate-500">
                            No tags found.
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default TagMaster;
