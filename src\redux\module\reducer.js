import {
    ADD_MODULE,
    ADD_SUB_MODULE,
    FETCH_MODULES_LIST,
    GET_MODULES_REQ,
    GET_SUB_MODULE_LIST,
    SUB_MODULE_REQ,
    UPDATE_MODULE,
    UPDATE_SUB_MODULE,
} from "@/redux-types";

const initialState = {
    modulesList: [],
    subModulesList: [],
    isLoading: true,
    error: null,
    moduleAddData: {},
    subModuleData: {},
};

const ModulesReducer = (state = initialState, action) => {
    switch (action.type) {
        case GET_MODULES_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case FETCH_MODULES_LIST:
            return {
                ...state,
                modulesList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case ADD_MODULE:
            return {
                ...state,
                moduleAddData: action.payload,
                handleClose: action.handleClose,
                isLoading: false,
                error: action.payload,
            };
        case UPDATE_MODULE:
            return {
                ...state,
                moduleAddData: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case ADD_SUB_MODULE:
            return {
                ...state,
                subModuleData: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case UPDATE_SUB_MODULE:
            return {
                ...state,
                subModuleData: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case SUB_MODULE_REQ:
            return {
                ...state,
                isLoading: true,
                subModuleId: action.payload,
            };
        case GET_SUB_MODULE_LIST:
            return {
                ...state,
                subModulesList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};

export default ModulesReducer;
