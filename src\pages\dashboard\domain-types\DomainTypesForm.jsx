import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Bread<PERSON>rumb<PERSON><PERSON>,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import AddBundlesToDomainType from "./AddBundlesToDomainType";
import AddCoursesToDomainType from "./AddCoursesToDomainType";

const DomainTypesForm = () => {
    const params = useParams();
    const router = useNavigate();
    const [userGroupData, setUserGroupData] = useState(null);

    useEffect(() => {
        if (params?.group_id !== undefined) {
            getUserGroups(params?.group_id);
        }
    }, [params]);

    const getUserGroups = async (group_id) => {
        await tanstackApi
            .post(`user-groups/list`)
            .then((res) => {
                setUserGroupData(res?.data?.data?.find((dt) => dt?.id == Number(group_id)));
            })
            .catch((err) => {
                setUserGroupData(null);
            });
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/domain-types">Domain Types</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Assign Courses & Course Bundles</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                {/* <Link to={""}> */}
                <Button className="tw-px-2 tw-py-1" onClick={() => router(`/dashboard/domain-types`)}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
                {/* </Link> */}
            </div>
            <Tabs defaultValue="Add Courses" className="">
                <TabsList className="tw-grid tw-w-[350px] tw-grid-cols-2">
                    <TabsTrigger value="Add Courses" className="tw-gap-2">
                        <i className="fa-solid fa-book"></i> Assign Courses
                    </TabsTrigger>
                    <TabsTrigger value="Add Bundles" className="tw-gap-2">
                        <i className="fa-solid fa-box-open"></i> Assign Bundles
                    </TabsTrigger>
                </TabsList>
                <TabsContent value="Add Courses">
                    <AddCoursesToDomainType userGroupData={userGroupData} getUserGroups={getUserGroups} />
                </TabsContent>
                <TabsContent value="Add Bundles">
                    <AddBundlesToDomainType userGroupData={userGroupData} getUserGroups={getUserGroups} />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default DomainTypesForm;
