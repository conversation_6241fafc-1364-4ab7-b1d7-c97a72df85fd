"use client";

import { useState, useEffect } from "react";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "@/components/ui/button";
import { Check, X, GripVertical } from "lucide-react";

function SortableItem({ id, text, disabled }) {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id, disabled });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            className={`tw-mb-2 tw-flex tw-items-center tw-gap-2 tw-rounded-md tw-border-2 tw-border-orange-800 tw-bg-orange-900/30 tw-p-3 ${
                disabled ? "tw-opacity-80" : "tw-cursor-grab"
            }`}
            {...attributes}
            {...listeners}
        >
            <GripVertical className="tw-text-orange-400" />
            <span className="tw-flex-1">{text}</span>
        </div>
    );
}

export default function ArrangeTextQuestion({ question, onAnswerSubmit, showCorrectAnswer, savedAnswer }) {
    const [items, setItems] = useState([]);
    const [submitted, setSubmitted] = useState(false);
    const [isCorrect, setIsCorrect] = useState(null);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        }),
    );

    useEffect(() => {
        // Initialize with shuffled items
        const initialItems = question.textItems.map((text, index) => ({
            id: `item-${index}`,
            text,
            originalIndex: index,
        }));

        // If we have a saved answer, use that order
        if (savedAnswer) {
            const orderedItems = savedAnswer.map((index) => ({
                id: `item-${index}`,
                text: question.textItems[index],
                originalIndex: index,
            }));
            setItems(orderedItems);
            setSubmitted(true);

            // Check if the saved answer is correct
            const isCorrect = JSON.stringify(savedAnswer) === JSON.stringify(question.correctOrder);
            setIsCorrect(isCorrect);
        } else {
            // Otherwise shuffle the items
            const shuffledItems = [...initialItems].sort(() => Math.random() - 0.5);
            setItems(shuffledItems);
        }
    }, [question, savedAnswer]);

    function handleDragEnd(event) {
        const { active, over } = event;

        if (active.id !== over.id) {
            setItems((items) => {
                const oldIndex = items.findIndex((item) => item.id === active.id);
                const newIndex = items.findIndex((item) => item.id === over.id);
                return arrayMove(items, oldIndex, newIndex);
            });
        }
    }

    const handleSubmit = () => {
        setSubmitted(true);

        // Extract the original indices in their current order
        const currentOrder = items.map((item) => Number.parseInt(item.id.split("-")[1]));

        // Check if the current order matches the correct order
        const correct = JSON.stringify(currentOrder) === JSON.stringify(question.correctOrder);
        setIsCorrect(correct);

        onAnswerSubmit(currentOrder, correct);
    };

    const renderCorrectOrder = () => {
        if (!showCorrectAnswer) return null;

        const correctItems = question.correctOrder.map((index) => ({
            id: `correct-${index}`,
            text: question.textItems[index],
        }));

        return (
            <div className="tw-mt-6 tw-rounded-md tw-border-2 tw-border-green-500 tw-bg-slate-700/50 tw-p-4">
                <h3 className="tw-mb-2 tw-text-xl tw-font-semibold tw-text-white">Correct Order</h3>
                <div className="tw-space-y-2">
                    {correctItems.map((item) => (
                        <div
                            key={item.id}
                            className="tw-rounded-md tw-border-2 tw-border-green-500 tw-bg-green-700/30 tw-p-3"
                        >
                            {item.text}
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    return (
        <div className="tw-space-y-4">
            <p className="tw-mb-2 tw-text-sm tw-text-orange-300">Arrange the items in the correct order</p>

            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                <SortableContext items={items.map((item) => item.id)} strategy={verticalListSortingStrategy}>
                    {items.map((item) => (
                        <SortableItem key={item.id} id={item.id} text={item.text} disabled={submitted} />
                    ))}
                </SortableContext>
            </DndContext>

            {!submitted ? (
                <Button onClick={handleSubmit} className="tw-mt-4 tw-bg-teal-700 tw-text-white hover:tw-bg-teal-600">
                    Submit Answer
                </Button>
            ) : (
                <div className="tw-mt-4 tw-flex tw-items-center tw-gap-2 tw-rounded-md tw-bg-slate-700/50 tw-p-3">
                    <span>Your answer is:</span>
                    {isCorrect ? <Check className="tw-text-green-400" /> : <X className="tw-text-red-400" />}
                </div>
            )}

            {renderCorrectOrder()}
        </div>
    );
}
