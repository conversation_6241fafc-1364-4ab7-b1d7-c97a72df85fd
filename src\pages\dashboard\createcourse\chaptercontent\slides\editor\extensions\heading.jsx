import { Heading } from "@tiptap/extension-heading";

const HeadingExtends = Heading.extend({
    addAttributes() {
        return {
            class: {
                default: null,
                parseHTML: (element) => element.getAttribute("class"),
                renderHTML: (attributes) => {
                    if (!attributes.class) return {};
                    return { class: attributes.class };
                },
            },
            dataLevel: {
                default: null,
                parseHTML: (element) => element.getAttribute("data-level"),
                renderHTML: (attributes) => {
                    if (!attributes.dataLevel) return {};
                    return { "data-level": attributes.dataLevel };
                },
            },
        };
    },
    renderHTML({ node, HTMLAttributes }) {
        const level = this.options.levels.includes(node.attrs.level) ? node.attrs.level : this.options.levels[0];
        const levelClasses = {
            1: "tw-text-4xl font-bold",
            2: "tw-text-2xl font-semibold",
            3: "tw-text-xl font-medium",
            4: "tw-text-lg font-medium",
            5: "tw-text-base font-medium",
            6: "tw-text-sm font-medium",
        };
        return [
            `h${level}`,
            {
                ...HTMLAttributes,
                class: levelClasses[level] || "tw-text-sm",
                "data-level": level,
            },
            0,
        ];
    },
});

export default HeadingExtends;
