import { Badge } from "@/components/ui/badge";
import { useGetLanguageList } from "@/react-query/language";
import { useGetUserGroupsTrainers } from "@/react-query/users/group";
import { Clock, Languages, ShieldCheck, Users } from "lucide-react";
import moment from "moment";
import { useRef, useState } from "react";
import { Link } from "react-router-dom";

const MyGroups = () => {
    const trainerGroups = useGetUserGroupsTrainers();
    const languageList = useGetLanguageList();
    const [searchQuery, setSearchQuery] = useState("");
    const ref = useRef(null);

    const filteredGroups =
        trainerGroups.data?.data?.filter((group) => group.name.toLowerCase().includes(searchQuery.toLowerCase())) ?? [];

    return (
        <div>
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-people-group"></i> My Groups
                </h4>
            </div>
            <div className="page_filters tw-mt-5 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label htmlFor="">Group Name</label>
                    <input ref={ref} type="text" className="tw-min-w-[300px]" placeholder="Search by name ..." />
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={() => setSearchQuery(ref.current.value)}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button
                        className="clear_btn"
                        onClick={() => {
                            setSearchQuery("");
                            if (ref.current) ref.current.value = "";
                        }}
                    >
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            {filteredGroups.length == 0 ? (
                <div className="tw-flex tw-h-40 tw-w-full tw-items-center tw-justify-center tw-rounded-xl tw-border tw-p-1">
                    <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">No Groups Found</h2>
                </div>
            ) : (
                <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-5 tw-p-0">
                    {filteredGroups?.map((group, index) => (
                        <div key={index} className="tw-w-[500px] tw-rounded-xl tw-border-[1px] tw-p-1">
                            <div className="tw-p-2">
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                        {group?.name}
                                    </h2>
                                </div>
                                <p className="tw-mt-2 tw-line-clamp-3">{group?.description}</p>
                                <div className="tw-mt-1 tw-space-y-2">
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Users size={18} /> {group?.user_count}/{group?.user_restriction} Users
                                    </span>{" "}
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Languages size={18} />{" "}
                                        <Badge variant={"outline"}>
                                            {languageList.data?.data?.find((dt) => dt?.id == group?.language_id)?.name}
                                        </Badge>
                                    </span>{" "}
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Clock size={18} /> Created on {moment(group?.createdAt).format("LL")}
                                    </span>{" "}
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <ShieldCheck size={18} />{" "}
                                        {group?.lms_trainer_group_mappings?.length > 0 ? (
                                            group?.lms_trainer_group_mappings?.map((trainer, idx) => (
                                                <>
                                                    <p key={idx} className="tw-font-semibold tw-underline">
                                                        {trainer?.lms_user?.first_name} {trainer?.lms_user?.last_name},
                                                    </p>
                                                </>
                                            ))
                                        ) : (
                                            <p className="tw-text-sm tw-text-slate-400">No trainers assigned Yet!</p>
                                        )}
                                    </span>{" "}
                                </div>
                                <div className="tw-mt-3 tw-grid tw-grid-cols-1 tw-gap-2">
                                    <Link
                                        to={`/dashboard/user-teams/${group?.id}/${group?.name}`}
                                        className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                    >
                                        <i className="fa-solid fa-users-line tw-text-md"></i>
                                        View Teams
                                    </Link>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default MyGroups;
