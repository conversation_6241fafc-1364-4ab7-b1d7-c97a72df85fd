import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

function getDurationInMinutes(startTime, endTime) {
    // Split the start and end time strings into hours, minutes, and seconds
    const [startHours, startMinutes, startSeconds] = startTime.split(":").map(Number);
    const [endHours, endMinutes, endSeconds] = endTime.split(":").map(Number);

    // Convert start and end times to total minutes since midnight
    const startTotalMinutes = startHours * 60 + startMinutes + startSeconds / 60;
    const endTotalMinutes = endHours * 60 + endMinutes + endSeconds / 60;

    // Calculate the difference in minutes
    const duration = parseInt(endTotalMinutes - startTotalMinutes);

    return duration;
}

const BulkPricingAddEdit = ({ open, setOpen, editData, getPriceList, activeTab }) => {
    const navigate = useNavigate();
    const [openAlert, setOpenAlert] = useState(false);

    const [courseList, setCourseList] = useState([]);
    const [bundleList, setBundleList] = useState([]);
    const [currencyList, setCurrencyList] = useState([]);

    const [pricingDetails, setPricingDetails] = useState({
        course_id: null,
        bundle_id: null,
        lower_limit: null,
        upper_limit: null,
        unit_price: null,
        currency_id: null,
    });

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        if (name == "is_free") {
            setPricingDetails({ ...pricingDetails, [name]: !pricingDetails?.is_free });
        } else {
            setPricingDetails({ ...pricingDetails, [name]: value });
        }
    };

    useEffect(() => {
        if (editData !== null) {
            setPricingDetails({
                course_id: editData?.course_id,
                bundle_id: editData?.bundle_id,
                lower_limit: editData?.lower_limit,
                upper_limit: editData?.upper_limit,
                unit_price: editData?.unit_price,
                currency_id: editData?.currency_id,
            });
        } else {
            setPricingDetails({
                course_id: null,
                bundle_id: null,
                lower_limit: null,
                upper_limit: null,
                unit_price: null,
                currency_id: null,
            });
        }
    }, [editData]);

    const onClearForm = () => {
        setPricingDetails({
            course_id: null,
            bundle_id: null,
            lower_limit: "",
            upper_limit: "",
            unit_price: "",
            currency_id: null,
        });
    };

    useEffect(() => {
        getCurrency();
    }, []);

    const getCurrency = async (payload) => {
        await tanstackApi
            .get("currency/listing")
            .then((res) => {
                setCurrencyList(res?.data?.data);
            })
            .catch((err) => {
                setCurrencyList([]);
            });
    };

    useEffect(() => {
        if (activeTab == "Course Pricing") {
            getCourses();
        } else {
            getCourseBundles();
        }
    }, [activeTab]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getCourseBundles = async (payload) => {
        await tanstackApi
            .get("course-bundle/get-course-bundles")
            .then((res) => {
                setBundleList(res?.data?.data);
            })
            .catch((err) => {
                setBundleList([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!pricingDetails?.course_id && activeTab == "Course Pricing") {
            toast.warning("Course", {
                description: "Course selection is required",
            });
            return false;
        }

        if (!pricingDetails?.bundle_id && activeTab == "Bundle Pricing") {
            toast.warning("Bundle", {
                description: "Bundle selection is required",
            });
            return false;
        }
        if (!pricingDetails?.lower_limit) {
            toast.warning("Min Users", {
                description: "Define minimum users limit",
            });
            return false;
        }

        if (!pricingDetails?.upper_limit) {
            toast.warning("Max Users", {
                description: "Define maximum users limit",
            });
            return false;
        }

        if (!pricingDetails?.unit_price) {
            toast.warning("Price", {
                description: "Pricing is required",
            });
            return false;
        }
        if (!pricingDetails?.currency_id) {
            toast.warning("Currency", {
                description: "Currency selection is required",
            });
            return false;
        }

        if (editData !== null) {
            const payload = {
                course_id: pricingDetails?.course_id || undefined,
                bundle_id: pricingDetails?.bundle_id || undefined,
                lower_limit: pricingDetails?.lower_limit,
                upper_limit: pricingDetails?.upper_limit,
                unit_price: pricingDetails?.unit_price,
                currency_id: pricingDetails?.currency_id,
            };

            await tanstackApi
                .put(`course-bundle-bulk-price/update/${editData?.id}`, { ...payload })
                .then((res) => {
                    getPriceList();
                    toast.success("Bulk Pricing Updated", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                course_id: pricingDetails?.course_id,
                bundle_id: pricingDetails?.bundle_id,
                lower_limit: pricingDetails?.lower_limit,
                upper_limit: pricingDetails?.upper_limit,
                unit_price: pricingDetails?.unit_price,
                currency_id: pricingDetails?.currency_id,
            };

            await tanstackApi
                .post("course-bundle-bulk-price/create", { ...payload })
                .then((res) => {
                    getPriceList();
                    toast.success("Bulk Pricing Created", {
                        description: res?.data?.message,
                    });
                    setOpen(false);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full !tw-max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">
                                {editData ? "Update" : "Add"} Bulk {activeTab}
                            </h1>
                        </DialogTitle>
                        <DialogDescription>fill below details add bulk {activeTab}.</DialogDescription>
                    </DialogHeader>
                    <div className="tw-flex tw-flex-col tw-gap-3">
                        <div className="tw-grid tw-grid-cols-1 tw-gap-3">
                            {activeTab == "Course Pricing" ? (
                                <div className="tw-space-y-1">
                                    <Label htmlFor="name">Courses *</Label>
                                    <Select
                                        onValueChange={(e) =>
                                            onHandleChange({ target: { value: e, name: "course_id" } })
                                        }
                                        value={pricingDetails?.course_id}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select course" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectGroup>
                                                {courseList?.map((data, idx) => (
                                                    <SelectItem key={idx} value={data?.id}>
                                                        {data?.course_title}
                                                    </SelectItem>
                                                ))}
                                            </SelectGroup>
                                        </SelectContent>
                                    </Select>
                                </div>
                            ) : (
                                <div className="tw-space-y-1">
                                    <Label htmlFor="name">Bundles *</Label>
                                    <Select
                                        onValueChange={(e) =>
                                            onHandleChange({ target: { value: e, name: "bundle_id" } })
                                        }
                                        value={pricingDetails?.bundle_id}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select bundles" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectGroup>
                                                {bundleList?.map((data, idx) => (
                                                    <SelectItem key={idx} value={data?.id}>
                                                        {data?.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectGroup>
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </div>
                        <div className="tw-grid tw-grid-cols-4 tw-gap-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Min Users</Label>
                                <Input
                                    placeholder="Define lower limit"
                                    name="lower_limit"
                                    type="number"
                                    onChange={onHandleChange}
                                    value={pricingDetails?.lower_limit}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Max Users</Label>
                                <Input
                                    placeholder="Define upper limit"
                                    name="upper_limit"
                                    type="number"
                                    onChange={onHandleChange}
                                    value={pricingDetails?.upper_limit}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Unit Price</Label>
                                <Input
                                    placeholder="Define price"
                                    name="unit_price"
                                    type="number"
                                    onChange={onHandleChange}
                                    value={pricingDetails?.unit_price}
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Currency *</Label>
                                <Select
                                    onValueChange={(e) => onHandleChange({ target: { value: e, name: "currency_id" } })}
                                    value={pricingDetails?.currency_id}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select currency" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            {currencyList?.map((curr, idx) => (
                                                <SelectItem value={curr?.id} key={idx}>
                                                    {curr?.name}
                                                </SelectItem>
                                            ))}
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <Button type="button" variant="secondary" onClick={onClearForm}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button>
                        <Button onClick={onDataSubmit}>
                            <i className="fa-regular fa-floppy-disk"></i> {editData ? "Update" : "Create"} Pricing
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default BulkPricingAddEdit;
