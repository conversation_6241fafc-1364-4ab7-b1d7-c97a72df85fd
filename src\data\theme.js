const fontStyles = {
    fontFamily: "",
    fontSize: "",
    fontWeight: "",
    lineHeight: "",
    color: "",
};

const reviewType = {
    show_answer: "show_answer",
    show_feedback: "show_feedback",
    both: "both",
};

const theme = {
    questions: "Enter your question here",
    note: "",
    options: [
        {
            label: "Option 1",
            img: { src: "" },
            isCorrect: false,
        },
    ],
    reviewType: reviewType,
    styles: {
        fonts: {
            question: {
                ...fontStyles,
            }, // answer, note,
        },
        background: {
            color: "",
            img: "",
            opacity: "",
            position: "",
            cover: "",
        },
        popup: {
            success: {
                ...fontStyles,
                message: "",
                img: {
                    src: "",
                    postition: "",
                    opacity: "",
                    x: "",
                    y: "",
                },
            },
            failure: {
                ...fontStyles,
                message: "",
                img: {
                    src: "",
                    postition: "",
                    opacity: "",
                    x: "",
                    y: "",
                },
            },
            warning: {
                ...fontStyles,
                message: "",
            },
        },
    },
    ui_lements: {
        random_12dtg: {
            src: "oimagecode",
            width: "100%",
            height: "100%",
            position: { x: 0, y: 0 },
        },
    },
};
