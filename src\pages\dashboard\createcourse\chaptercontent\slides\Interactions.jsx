import SlideHeader, { AddContentFunction } from "@/pages/dashboard/createcourse/chaptercontent/slides/common";
import { useInView } from "framer-motion";
import { CircleFadingPlus, Layers2, PanelRightClose, Radar } from "lucide-react";
import { useEffect, useRef } from "react";

const Interactions = ({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    onSetDelete,
    setCurrentId,
    setContentArray,
    contentArray,
    setOpenIconDialog,
}) => {
    const containerRef = useRef(null);
    const inView = useInView(containerRef, { amount: 0.8 });
    useEffect(() => {
        if (inView) setCurrentId(`#slide_${index}`);
    }, [inView]);

    return (
        <div
            ref={containerRef}
            className="tw-grid tw-min-h-[35vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${template?.background || content?.background})` }}
        >
            <SlideHeader
                index={index}
                content={content}
                contentArray={contentArray}
                onSlideEdit={onSlideEdit}
                onSetDelete={onSetDelete}
                onRemoveSlide={onRemoveSlide}
                setOpenIconDialog={setOpenIconDialog}
                setContentArray={setContentArray}
            />
            {content?.interaction == null ? (
                <AddContentFunction onSlideEdit={onSlideEdit} content={content} index={index} />
            ) : (
                <div className="tw-px-10 tw-py-7">
                    <div>
                        <h1 className="tw-text-3xl tw-font-bold">{content?.interaction?.title}</h1>
                        <p className="my-2 tw-text-lg">{content?.interaction?.description}</p>
                    </div>
                    <div className="tw-mt-5 tw-flex tw-items-center tw-gap-10 tw-space-y-1">
                        <span className="tw-flex tw-items-center tw-gap-2 tw-text-lg tw-font-normal">
                            <Layers2 size={22} /> {content?.interaction?.category}
                        </span>
                        <span className="tw-flex tw-items-center tw-gap-2 tw-text-lg tw-font-normal">
                            <Radar size={22} /> {content?.interaction?.type}
                        </span>
                        <span className="tw-flex tw-items-center tw-gap-2 tw-text-lg tw-font-normal">
                            <PanelRightClose size={22} /> {content?.interaction?.structure?.database?.length} Slides
                        </span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Interactions;
