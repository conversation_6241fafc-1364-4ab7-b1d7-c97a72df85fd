import root_reducer from "@/redux/reducer";
import root_saga from "@/redux/saga";
import { configureStore } from "@reduxjs/toolkit";
import createSagaMiddleware from "redux-saga";
import { thunk } from "redux-thunk";

const sagaMiddleware = createSagaMiddleware();
const middlewares = [thunk, sagaMiddleware];

const store = configureStore({
    reducer: root_reducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(middlewares),
});

sagaMiddleware.run(root_saga);
export { store };
