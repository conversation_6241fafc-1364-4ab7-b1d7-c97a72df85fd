import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { generateDefaultForType } from "@/lib/utils";
import ColorInput from "@/pages/dashboard/template/form/color-input";
import { useState } from "react";
import { toast } from "sonner";

const structure = {
    singlechoice: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        default: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
        selected: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
    },
    singlechoice_media: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        default: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
        selected: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
    },
    string_dropdown: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        default: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
        selected: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
    },
    sequence_arrange: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
            {
                type: "border",
            },
        ],
    },
    match_the_following: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
            {
                type: "border",
            },
        ],
        selected: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
    },
    long_answer: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
    },
    hotspot_dragdrop: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
    },
    hotspot_dragdrop_media: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
    },
    fill_in_the_blank: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        answer: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
        ],
        default: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "color",
                name: "backgroundColor",
            },
            {
                type: "border",
            },
        ],
        selected: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "border",
            },
        ],
    },
    dragdrop_media: {
        question: [
            {
                type: "color",
                name: "color",
            },
            {
                type: "select",
                name: "fontFamily",
                options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
            },
            {
                type: "text",
                name: "fontSize",
            },
            {
                type: "border",
            },
        ],
        answer: [
            {
                type: "border",
            },
        ],
    },
};

const border = [
    {
        type: "text",
        name: "borderWidth",
    },
    {
        type: "color",
        name: "borderColor",
    },
    {
        type: "select",
        name: "borderStyle",
        options: ["solid", "dashed"],
    },
];

const isEmpty = (data) => [null, undefined, ""].includes(data);

const keysWithData = (data) =>
    Object.keys(data).filter((key) => {
        return Object.keys(data[key]).length > 0;
    });

export default function QuizStyle({ componentsArray, index, setComponentsArray, type, setStyleDialogOpen }) {
    const [data, setData] = useState({
        ...generateDefaultForType(type, structure),
        ...componentsArray[index].styles,
    });
    const [errors, setErrors] = useState({});

    const onHandleChange = ({ name, value }, parent) => {
        const oldData = { ...data };
        if (oldData[parent] === undefined) oldData[parent] = {};
        oldData[parent] = {
            ...oldData[parent],
            [name]: value,
        };
        setData(oldData);

        if (errors[parent] && errors[parent][name]) {
            const updatedErrors = { ...errors };
            updatedErrors[parent] = { ...updatedErrors[parent] };
            delete updatedErrors[parent][name];
            setErrors(updatedErrors);
        }
    };

    const validateForm = () => {
        let valid = true;
        const newErrors = {};

        Object.entries(structure[type]).forEach(([parent, fieldsArray]) => {
            newErrors[parent] = {};
            fieldsArray.forEach((field) => {
                const hexRegex = /^#(?:[0-9A-Fa-f]{3}){1,2}$/;
                const pxRegex = /^\d+px$/;
                if (field.type == "border") {
                    if (isEmpty(data[parent].borderColor)) {
                        valid = false;
                        newErrors[parent]["borderColor"] = "This field is required";
                        return;
                    } else if (!hexRegex.test(data[parent].borderColor)) {
                        valid = false;
                        newErrors[parent]["borderColor"] = "This field is invalid";
                        return;
                    }
                    if (isEmpty(data[parent].borderWidth)) {
                        valid = false;
                        newErrors[parent]["borderWidth"] = "This field is required";
                        return;
                    } else if (!pxRegex.test(data[parent].borderWidth)) {
                        valid = false;
                        newErrors[parent]["borderWidth"] = "This field is invalid";
                        return;
                    }

                    if (isEmpty(data[parent].borderStyle)) {
                        valid = false;
                        newErrors[parent]["borderStyle"] = "This field is required";
                        return;
                    }

                    return;
                }

                const value = data[parent] && data[parent][field.name];

                if (!value) {
                    valid = false;
                    newErrors[parent][field.name] = "This field is required";
                    return;
                }

                switch (field.name) {
                    case "color":
                    case "borderColor":
                    case "backgroundColor":
                        if (isEmpty(value)) {
                            valid = false;
                            newErrors[parent][field.name] = "This field is required";
                        } else if (!hexRegex.test(value)) {
                            valid = false;
                            newErrors[parent][field.name] = "This field is invalid";
                        }
                        break;
                    case "fontSize":
                    case "borderWidth":
                        if (isEmpty(value)) {
                            valid = false;
                            newErrors[parent][field.name] = "This field is required";
                        } else if (!pxRegex.test(value)) {
                            valid = false;
                            newErrors[parent][field.name] = "This field is invalid eg (24px)";
                        }
                        break;
                }
            });
        });

        setErrors(newErrors);
        return valid;
    };

    return (
        <div className="tw-flex tw-flex-col tw-justify-between tw-gap-1">
            <Tabs defaultValue="question">
                <TabsList className="tw-h-auto tw-flex-wrap">
                    {Object.keys(structure[type]).map((key) => {
                        return (
                            <TabsTrigger key={key} value={key} className="tw-capitalize">
                                {key}
                            </TabsTrigger>
                        );
                    })}
                </TabsList>
                {Object.entries(structure[type]).map(([parent, fieldsArray]) => {
                    return (
                        <TabsContent key={parent} value={parent} className="tw-space-y-3">
                            {fieldsArray.map(({ name, type, options }) => {
                                if (type === "border") {
                                    return (
                                        <div key={name} className="tw-grid tw-grid-cols-3 tw-gap-3">
                                            {border.map((brField) => {
                                                return (
                                                    <StyleInput
                                                        key={brField.name}
                                                        parent={parent}
                                                        data={data}
                                                        onHandleChange={onHandleChange}
                                                        name={brField.name}
                                                        type={brField.type}
                                                        options={brField.options}
                                                        error={errors[parent] ? errors[parent][brField.name] : ""}
                                                    />
                                                );
                                            })}
                                        </div>
                                    );
                                }
                                return (
                                    <StyleInput
                                        key={name}
                                        parent={parent}
                                        data={data}
                                        name={name}
                                        onHandleChange={onHandleChange}
                                        options={options}
                                        type={type}
                                        error={errors[parent] ? errors[parent][name] : ""}
                                    />
                                );
                            })}
                        </TabsContent>
                    );
                })}
            </Tabs>
            <div className="tw-flex tw-justify-end">
                <Button
                    onClick={() => {
                        if (validateForm()) {
                            const newData = [...componentsArray];
                            if (newData[index].styles === undefined) newData[index].styles = {};
                            newData[index].styles = data;
                            setComponentsArray(newData);
                            setStyleDialogOpen(false);
                        } else {
                            toast.error(
                                <p>
                                    Check <b className="tw-capitalize">{keysWithData(errors).join(", ")}</b> tab for
                                    errors
                                </p>,
                            );
                        }
                    }}
                >
                    Save
                </Button>
            </div>
        </div>
    );
}

function StyleInput({ name, onHandleChange, type, options, data, parent, error }) {
    return (
        <div className="tw-space-y-1">
            <Label className="tw-capitalize">{name}</Label>
            {type === "color" && (
                <ColorInput
                    onHandleChange={(value) => {
                        onHandleChange({ name, value }, parent);
                    }}
                    value={data[parent] ? data[parent][name] : ""}
                    name={name}
                />
            )}
            {type === "text" && (
                <Input
                    value={data[parent] ? data[parent][name] : ""}
                    name={name}
                    onChange={(e) => {
                        onHandleChange({ name, value: e.target.value }, parent);
                    }}
                    type={type}
                />
            )}
            {type === "select" && (
                <SelectNative
                    name={name}
                    defaultValue={data[parent] ? data[parent][name] : ""}
                    onChange={(e) => onHandleChange({ name, value: e.target.value }, parent)}
                >
                    <option value="">Select Options</option>
                    {options &&
                        options.map((v) => (
                            <option key={v} value={v}>
                                {v}
                            </option>
                        ))}
                </SelectNative>
            )}
            {error && <div className="tw-text-xs tw-text-red-500">{error}</div>}
        </div>
    );
}
