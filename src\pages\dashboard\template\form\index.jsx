import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { cn, isUrl } from "@/lib/utils";
import ElementsForm from "@/pages/dashboard/template/form/elements";
import FontFamilyForm from "@/pages/dashboard/template/form/font-family";
import UIElementsForm from "@/pages/dashboard/template/form/ui-elements";
import { useFileUpload } from "@/react-query/common";
import {
    useCreateTemplate,
    useGetParticularTemplate,
    useGetTemplate,
    useUpdateTemplate,
} from "@/react-query/quizz/template";
import { motion, useMotionValue, useMotionValueEvent } from "framer-motion";
import { Check, Upload, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { toast } from "sonner";

const type = ["course", "quiz", "interactions"];

export default function TemplateForm() {
    const params = useParams();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const titleRef = useRef(null);
    const subHeadingRef = useRef(null);
    const paragraphRef = useRef(null);
    const [headingEditable, setHeadingEditable] = useState(false);
    const [subHeadingEditable, setSubHeadingEditable] = useState(false);
    const [paragraphEditable, setParagraphEditable] = useState(false);
    const action = params.id ? "update" : "create";
    const [list, setList] = useState([]);
    const [activeTab, setActiveTab] = useState("template");
    const [selectedTemplate, setSelectedTemplate] = useState({
        template_type: searchParams.get("type") ?? "course",
        template_title: "",
        slide_animation: "tween",
        is_public: localStorage.getItem("level") == "levelOne" ? true : false,
        sound_effect: {},
        sound_effect_kit: {},
        ui_elements: {},
    });

    const template = useGetTemplate({ limit: 10, offset: 0 });
    const particularTemplate = useGetParticularTemplate(params.id);
    const create = useCreateTemplate();
    const update = useUpdateTemplate();
    const loginToken = localStorage.getItem("login_token");

    useEffect(() => {
        if (template.status === "success") setList(template.data.data.filter((t) => type.includes(t.template_type)));
    }, [template.status]);

    useEffect(() => {
        if (particularTemplate.status === "success" && action === "update") {
            setSelectedTemplate(particularTemplate.data.data);
        }
    }, [particularTemplate.isSuccess, action]);

    const handleSave = async (type = "create") => {
        try {
            const { createdAt, updatedAt, user_id, id, ...rest } = selectedTemplate;
            const payload = { ...rest, is_public: Boolean(rest.is_public) };
            payload.background = payload.background ?? "";
            payload.template_title = titleRef?.current?.innerText;
            payload.sound_effect = payload.sound_effect ?? {};
            payload.sound_effect_kit = payload.sound_effect_kit ?? {};
            payload.ui_elements = payload.ui_elements ?? {};
            payload.is_draft = type == "draft";
            payload.font_family = {
                ...payload.font_family,
                textContent: {
                    subtitle: subHeadingRef?.current?.innerText,
                    body: paragraphRef?.current?.innerText,
                },
            };
            if (params.id && action === "update") {
                payload.id = params.id;
                await update.mutateAsync(payload);
            } else {
                await create.mutateAsync(payload);
            }
            toast.success(action == "update" ? "Template Saved Successfully" : "Template Created Successfully");
            window.open(`https://lms-course-builder.vercel.app/dashboard/templates?token=${loginToken}`, "_blank");
        } catch (error) {
            toast.error("Something went wrong");
        }
    };

    const upload = useFileUpload();
    const onImageChange = async (e) => {
        try {
            if (e.target.files && e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                setSelectedTemplate((prev) => ({ ...prev, background: response?.fileUrl }));
            }
        } catch (error) {
            setSelectedTemplate((prev) => ({ ...prev, background: "" }));
        }
    };
    const constraintsRef = useRef(null);

    return (
        <div className="tw-grid tw-size-full tw-grid-cols-6 tw-gap-5">
            <div
                ref={constraintsRef}
                className="tw-relative tw-col-span-4 tw-size-full tw-rounded-lg tw-border tw-bg-slate-300 tw-bg-center"
                style={{
                    backgroundImage: `url(${selectedTemplate?.background})`,
                }}
            >
                {Object.entries(selectedTemplate?.ui_elements ?? {}).map(([key, options]) => {
                    if (!isUrl(options.src)) return null;
                    return (
                        <UiElements
                            key={key}
                            id={key}
                            template={selectedTemplate}
                            setTemplate={setSelectedTemplate}
                            constraintsRef={constraintsRef}
                            options={options}
                        />
                    );
                })}
                <div className="tw-relative tw-flex tw-size-full tw-flex-col tw-gap-5 tw-p-10">
                    <h1
                        style={{
                            color: selectedTemplate?.font_family?.title?.color,
                            fontFamily: selectedTemplate?.font_family?.title?.font_family,
                            fontSize: selectedTemplate?.font_family?.title?.font_size,
                            textAlign: selectedTemplate?.font_family?.title?.text_align,
                            fontWeight: selectedTemplate?.font_family?.title?.font_weight,
                            backgroundColor: selectedTemplate?.font_family?.title?.background_color,
                            textDecoration: selectedTemplate?.font_family?.title?.text_decoration,
                        }}
                        ref={titleRef}
                        contentEditable={headingEditable}
                        onBlur={() => setHeadingEditable(false)}
                        onClick={() => setHeadingEditable(true)}
                    >
                        {selectedTemplate?.template_title}
                    </h1>
                    <p
                        ref={subHeadingRef}
                        style={{
                            color: selectedTemplate?.font_family?.subtitle?.color,
                            fontFamily: selectedTemplate?.font_family?.subtitle?.font_family,
                            fontSize: selectedTemplate?.font_family?.subtitle?.font_size,
                            textAlign: selectedTemplate?.font_family?.subtitle?.text_align,
                            fontWeight: selectedTemplate?.font_family?.subtitle?.font_weight,
                            backgroundColor: selectedTemplate?.font_family?.subtitle?.background_color,
                            textDecoration: selectedTemplate?.font_family?.subtitle?.text_decoration,
                        }}
                        contentEditable={subHeadingEditable}
                        onBlur={() => setSubHeadingEditable(false)}
                        onClick={() => setSubHeadingEditable(true)}
                    >
                        {selectedTemplate.font_family?.textContent?.subtitle ??
                            "Lorem ipsum dolor sit amet consectetur adipisicing elit. Repellat, nemo?"}
                    </p>
                    <p
                        ref={paragraphRef}
                        style={{
                            color: selectedTemplate?.font_family?.body?.color,
                            fontFamily: selectedTemplate?.font_family?.body?.font_family,
                            fontSize: selectedTemplate?.font_family?.body?.font_size,
                            textAlign: selectedTemplate?.font_family?.body?.text_align,
                            fontWeight: selectedTemplate?.font_family?.body?.font_weight,
                            backgroundColor: selectedTemplate?.font_family?.body?.background_color,
                            textDecoration: selectedTemplate?.font_family?.body?.text_decoration,
                        }}
                        contentEditable={paragraphEditable}
                        onBlur={() => setParagraphEditable(false)}
                        onClick={() => setParagraphEditable(true)}
                    >
                        {selectedTemplate.font_family?.textContent?.body ??
                            `Lorem, ipsum dolor sit amet consectetur adipisicing elit. Exercitationem unde, accusantium, eos
                        nesciunt, reprehenderit itaque deleniti laudantium placeat molestias nostrum tenetur provident
                        nemo maiores perspiciatis accusamus! Unde, enim! Velit quasi laboriosam numquam odit hic officia
                        quidem itaque necessitatibus consectetur ipsa.`}
                    </p>
                </div>
                <button
                    style={{
                        backgroundColor: selectedTemplate?.elements?.info_button?.background_color,
                        borderColor: selectedTemplate?.elements?.info_button?.border_color,
                        color: selectedTemplate?.elements?.info_button?.color,
                        fontFamily: selectedTemplate?.elements?.info_button?.font_family,
                        borderWidth: selectedTemplate?.elements?.info_button?.border_width,
                        borderStyle: selectedTemplate?.elements?.info_button?.border_style,
                    }}
                    className="tw-absolute tw-right-2 tw-top-2 tw-aspect-square tw-w-[60px] tw-rounded-full"
                >
                    <i className={cn("fa-solid tw-text-[28px] tw-leading-[28px]", "fa-info")} />
                </button>
                <div className="tw-absolute tw-bottom-4 tw-right-4 tw-flex tw-gap-1">
                    <button
                        style={{
                            backgroundColor: selectedTemplate?.elements?.prev_button?.background_color,
                            borderColor: selectedTemplate?.elements?.prev_button?.border_color,
                            color: selectedTemplate?.elements?.prev_button?.color,
                            fontFamily: selectedTemplate?.elements?.prev_button?.font_family,
                            borderWidth: selectedTemplate?.elements?.prev_button?.border_width,
                        }}
                        className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full disabled:tw-opacity-70"
                    >
                        <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px]"></i>
                    </button>
                    <button
                        style={{
                            backgroundColor: selectedTemplate?.elements?.next_button?.background_color,
                            borderColor: selectedTemplate?.elements?.next_button?.border_color,
                            color: selectedTemplate?.elements?.next_button?.color,
                            fontFamily: selectedTemplate?.elements?.next_button?.font_family,
                            borderWidth: selectedTemplate?.elements?.next_button?.border_width,
                            borderStyle: selectedTemplate?.elements?.next_button?.border_style,
                        }}
                        className={cn(
                            "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-px-0 tw-text-[30px] tw-leading-[30px]",
                        )}
                    >
                        <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px]"></i>
                    </button>
                    <button
                        style={{
                            color: selectedTemplate?.elements?.start_button?.color,
                            backgroundColor: selectedTemplate?.elements?.start_button?.background_color,
                            fontFamily: selectedTemplate?.elements?.start_button?.font_family,
                            borderColor: selectedTemplate?.elements?.start_button?.border_color,
                            borderWidth: selectedTemplate?.elements?.start_button?.border_width,
                            borderStyle: selectedTemplate?.elements?.start_button?.border_style,
                        }}
                        className={cn(
                            "tw-h-[60px] tw-w-fit tw-flex-shrink-0 tw-rounded-full tw-px-4 tw-text-[30px] tw-leading-[30px]",
                        )}
                    >
                        Start
                    </button>
                </div>
            </div>
            <div className="tw-col-span-2 tw-size-full tw-rounded-lg">
                <div className="tw-mb-5 tw-flex tw-justify-end tw-gap-3">
                    <Button
                        variant="outline"
                        className="tw-px-2 tw-py-1"
                        onClick={() => navigate("/dashboard/templates?page=1")}
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button onClick={() => handleSave(params.id ? "update" : "create")}>Save</Button>
                </div>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList>
                        <TabsTrigger value="template">Template</TabsTrigger>
                        <TabsTrigger value="setting">Setting</TabsTrigger>
                        <TabsTrigger value="elements">Elements</TabsTrigger>
                        <TabsTrigger value="fontfamily">Font Family</TabsTrigger>
                        <TabsTrigger value="uielements">UI Elements</TabsTrigger>
                    </TabsList>
                    <TabsContent value="setting" className="tw-space-y-3">
                        <div className="tw-space-y-1">
                            <Label>Template Title</Label>
                            <Input
                                value={selectedTemplate?.template_title}
                                onChange={(e) =>
                                    setSelectedTemplate((prev) => ({ ...prev, template_title: e.target.value }))
                                }
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label>Template Type</Label>
                            <SelectNative
                                value={selectedTemplate?.template_type}
                                onChange={(e) => {
                                    setSelectedTemplate((prev) => ({ ...prev, template_type: e.target.value }));
                                }}
                            >
                                <option value="course">Course</option>
                                <option value="quiz">Quiz</option>
                                <option value="interaction">Interaction</option>
                            </SelectNative>
                        </div>
                        <div className="tw-space-y-1">
                            <Label>Slide Animation</Label>
                            <SelectNative
                                value={selectedTemplate?.slide_animation}
                                onChange={(e) =>
                                    setSelectedTemplate({ ...selectedTemplate, slide_animation: e.target.value })
                                }
                            >
                                <option value="tween">Tween</option>
                                <option value="carousel">Carousel</option>
                            </SelectNative>
                        </div>
                        <div className="tw-flex tw-w-full tw-flex-col tw-items-start tw-gap-2">
                            <Label htmlFor="bg_image">Background Image</Label>
                            {selectedTemplate.background && (
                                <div className="tw-aspect-video tw-h-36 tw-object-contain">
                                    <img
                                        src={selectedTemplate.background}
                                        alt=""
                                        className="tw-size-full tw-rounded-md"
                                    />
                                </div>
                            )}
                            <div className="tw-flex tw-items-center tw-gap-2">
                                {selectedTemplate.background && (
                                    <Button
                                        variant="destructive"
                                        className="aspect-square max-sm:p-0"
                                        onClick={() => {
                                            setSelectedTemplate((prev) => ({ ...prev, background: "" }));
                                        }}
                                    >
                                        <X
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <Label className="max-sm:sr-only">Remove</Label>
                                    </Button>
                                )}
                                <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10">
                                    <Upload
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <input
                                        onChange={onImageChange}
                                        type="file"
                                        style={{ display: "none" }}
                                        id="bg_image"
                                        accept="image/*"
                                    />
                                    <Label htmlFor="bg_image" className="max-sm:sr-only">
                                        {upload.isPending ? "Uploading..." : "Upload Background Image"}
                                    </Label>
                                </Button>
                            </div>
                        </div>
                    </TabsContent>
                    <TabsContent value="template" className="tw-grid tw-grid-cols-3 tw-gap-3">
                        {list.map((t) => (
                            <div
                                key={t.id}
                                className="tw-relative tw-flex tw-aspect-video tw-items-center tw-justify-center tw-gap-2 tw-space-y-1 tw-rounded-xl tw-border"
                                style={{ backgroundImage: `url(${t.background})` }}
                                onClick={() => {
                                    const { createdAt, updatedAt, ...rest } = t;
                                    setSelectedTemplate(rest);
                                }}
                            >
                                <p className="tw-w-[80%] tw-text-center">{t.template_title}</p>
                                {selectedTemplate?.id === t.id && (
                                    <div className="tw-absolute tw-inset-0 tw-z-50 tw-flex tw-size-full tw-items-center tw-justify-center tw-rounded-lg tw-bg-black/10">
                                        <div className="tw-aspect-square tw-rounded-full tw-bg-green-500 tw-p-2 tw-text-white">
                                            <Check />
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </TabsContent>
                    <TabsContent value="fontfamily">
                        <FontFamilyForm template={selectedTemplate} setTemplate={setSelectedTemplate} />
                    </TabsContent>
                    <TabsContent value="elements" className="tw-space-y-6">
                        <ElementsForm template={selectedTemplate} setTemplate={setSelectedTemplate} />
                    </TabsContent>
                    <TabsContent value="uielements">
                        <UIElementsForm template={selectedTemplate} setTemplate={setSelectedTemplate} />
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
}

function UiElements({ constraintsRef, options, id, setTemplate }) {
    const x = useMotionValue(((Number(options.position.x) ?? 0) * (constraintsRef?.current?.clientWidth ?? 1)) / 100);
    const y = useMotionValue(((Number(options.position.y) ?? 0) * (constraintsRef?.current?.clientHeight ?? 1)) / 100);

    useMotionValueEvent(x, "change", (value) => {
        setTemplate((prev) => {
            const oldData = { ...prev };
            if (!oldData.ui_elements[id]) oldData.ui_elements[id] = { src: "", position: { x: 0, y: 0 } };
            oldData.ui_elements[id].position.x = (value / constraintsRef?.current?.clientWidth) * 100; // Convert to percentage
            return oldData;
        });
    });

    useMotionValueEvent(y, "change", (value) => {
        setTemplate((prev) => {
            const oldData = { ...prev };
            if (!oldData.ui_elements[id]) oldData.ui_elements[id] = { src: "", position: { x: 0, y: 0 } };
            oldData.ui_elements[id].position.y = (value / constraintsRef?.current?.clientHeight) * 100; // Convert to percentage
            return oldData;
        });
    });

    return (
        <motion.img
            className="tw-absolute tw-z-50 tw-size-20"
            dragConstraints={constraintsRef}
            dragMomentum={false}
            src={options.src}
            style={{ x, y }}
            drag
            alt={id}
        />
    );
}
