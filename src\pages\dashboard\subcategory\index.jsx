import { Button } from "@/components/ui/button";
import { DataTable } from "@/pages/dashboard/roles/permission/data-table";
import { fetchCATEGORYREQ, fetchSUBCATEGORYREQ } from "@/redux/category/action";
import { format } from "date-fns";
import { Pencil, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import SubCategoryForm from "./form";

export default function SubCategory() {
    const params = useParams();
    const dispatch = useDispatch();
    const [head, setHead] = useState({});
    const CategoryData = useSelector((state) => state.CategoryReducers)?.categoryList;
    const AllsubCategory = useSelector((state) => state.CategoryReducers)?.subCategoryList;

    useEffect(() => {
        dispatch(fetchCATEGORYREQ());
    }, []);

    useEffect(() => {
        if (CategoryData && params.id) {
            var spr = CategoryData.filter((data) => data.id == params.id);
            setHead(spr[0]);
        }
    }, [CategoryData]);

    useEffect(() => {
        var data = {
            categoryId: parseInt(params.id),
        };
        dispatch(fetchSUBCATEGORYREQ(data));
    }, []);

    const handleUpdate = (id) => {
        const data = AllsubCategory?.find((dt) => dt.id == id);
        setSelected(data);
        setOpen(true);
    };

    const [open, setOpen] = useState(false);
    const [selected, setSelected] = useState(null);

    const columns = [
        {
            accessorKey: "id",
        },
        {
            accessorKey: "sub_category_code",
            header: "Sub Category Code",
        },
        {
            accessorKey: "sub_category_name",
            header: "Sub Category Name",
        },
        {
            accessorKey: "is_active",
            header: "Status",
            cell: ({ row }) => {
                const is_active = Boolean(row.getValue("is_active"));
                return <div className="text-right font-medium">{is_active ? "Active" : "In Active"}</div>;
            },
        },
        {
            accessorKey: "createdAt",
            header: "Create On",
            cell: ({ row }) => {
                return <div className="text-right font-medium">{format(row.getValue("createdAt"), "PP")}</div>;
            },
        },
        {
            accessorKey: "created_by",
            header: "Actions",
            cell: ({ row }) => {
                return (
                    <div
                        className="text-right font-medium"
                        onClick={() => {
                            handleUpdate(row.getValue("id"));
                        }}
                    >
                        <Pencil className="tw-text-blue-400" />
                    </div>
                );
            },
        },
    ];

    return (
        <div>
            <div className="tw-mb-3 tw-flex tw-items-center tw-justify-between">
                <div className="tw-font-lexend tw-text-xl tw-font-medium">{head?.category_name} - Sub Categories</div>
                <Button
                    onClick={() => {
                        setSelected(null);
                        setOpen(true);
                    }}
                    variant="outline"
                >
                    <Plus />
                    Create Sub Category
                </Button>
            </div>
            <DataTable columns={columns} data={AllsubCategory ? AllsubCategory : []} />
            <SubCategoryForm open={open} setOpen={setOpen} selected={selected} />
        </div>
    );
}
