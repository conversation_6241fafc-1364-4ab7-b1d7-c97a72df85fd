import { fetchUserPointsReq } from "@/redux/dashboard/dash/action";
import { FetchBadgesList } from "@/redux/gamification/action";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

function getAchievedBadges(userPoints, badges) {
    const sortedBadges = badges.sort((a, b) => a.points_required - b.points_required);
    const achievedBadges = sortedBadges.filter((badge) => userPoints >= badge.points_required);
    return achievedBadges;
}

const UserKPISDash = ({ DashData, badgeDeatils }) => {
    const history = useDispatch();
    const dispatch = useDispatch();
    const [achievedBadges, setAchievedBadges] = useState([]);
    const [nextBadge, setNextBadge] = useState(null);
    const [barProgress, setBarProgress] = useState(0);

    const userPoints = useSelector((state) => state.DashReducer.userPoints);
    const badgesList = useSelector((state) => state.GamificationReducer.badgesList);

    useEffect(() => {
        dispatch(fetchUserPointsReq());
        dispatch(FetchBadgesList());
    }, []);

    useEffect(() => {
        if (userPoints?.points) {
            setAchievedBadges(getAchievedBadges(userPoints?.points > 15000 ? 15000 : userPoints?.points, badgesList));
        }
    }, [userPoints, badgesList]);

    useEffect(() => {
        if (badgesList?.length > 0 && badgeDeatils !== null) {
            let nextLevel = badgeDeatils?.level + 1;
            let data = badgesList?.find((dt) => dt?.level == nextLevel);
            setNextBadge(data);
        }

        if (!badgeDeatils) {
            setNextBadge(badgesList?.find((dt) => dt?.level == 1));
        }
    }, [badgesList, badgeDeatils]);

    useEffect(() => {
        if (userPoints !== null) {
            setBarProgress(
                (((userPoints?.points > 15000 ? 15000 : userPoints?.points) / 15000) * 100 || 0)?.toFixed(0),
            );
        }
    }, [userPoints]);

    return (
        <div className="dash_head">
            <div className="dashboard_beta">
                <div className="dash_badge">
                    <div className="left">
                        <motion.img
                            initial={{
                                x: -15,
                                y: 10,
                                rotate: -15,
                            }}
                            animate={{
                                x: 15,
                                y: [10, 2, 10],
                                rotate: 15,
                            }}
                            transition={{
                                repeat: Infinity,
                                repeatType: "reverse",
                                duration: 1, // Slightly longer duration for a more fluid motion
                                ease: "easeInOut",
                            }}
                            src={`/assets/badge-${badgeDeatils?.level}.png`}
                            alt=""
                        />
                        <div>
                            <h4>{badgeDeatils?.name}</h4>
                            <p>
                                <i className="fa-solid fa-star"></i> Level {badgeDeatils?.level || 0}
                            </p>
                        </div>
                    </div>
                    <div className="right"></div>
                </div>
                <div className="dash_middle">
                    <div>
                        <div>
                            <p>Next Level</p>
                            <h4>{nextBadge?.name}</h4>
                        </div>
                        <div>
                            <p>To Reach Next</p>
                            <h4>
                                <i className="fa-solid fa-dice-d6"></i>{" "}
                                {nextBadge?.points_required - userPoints.points || 0}
                            </h4>
                        </div>
                    </div>
                </div>
                <div className="dash_stepper">
                    <div className="stepper_track">
                        <div className="stepper_thumb" style={{ "--wid": `${barProgress}%` }}></div>
                        {badgesList
                            ?.filter((dt) => dt?.level <= 6)
                            ?.map((bdg, idx) => (
                                <>
                                    {achievedBadges?.map((dt) => dt?.level)?.includes(bdg?.level) ? (
                                        <div key={idx} className={`milestone reached step_${bdg?.level}`}>
                                            <motion.img
                                                animate={{ rotate: [15, -15, 15] }} // Pendulum-like rotation
                                                transition={{
                                                    duration: 2,
                                                    repeat: Infinity,
                                                    repeatType: "loop",
                                                    ease: "easeInOut",
                                                }}
                                                src={`/assets/badge-${bdg?.level}.png`}
                                                alt=""
                                            />
                                            <h4>
                                                <i className="fa-solid fa-dice-d6"></i> {bdg?.points_required}
                                            </h4>
                                        </div>
                                    ) : (
                                        <div key={idx} className={`milestone step_${bdg?.level}`}>
                                            <img src={`/assets/badge-${bdg?.level}.png`} alt="" />
                                            <h4>
                                                <i className="fa-solid fa-dice-d6"></i> {bdg?.points_required}
                                            </h4>
                                        </div>
                                    )}
                                </>
                            ))}
                    </div>
                </div>
            </div>
            <div className="dash_cards">
                <div className="dash_card" onClick={() => history.push(`/dashboard/course`)}>
                    <div className="left">
                        <h3>{DashData?.enrolledCourses}</h3>
                        <p>Enrolled Courses</p>
                    </div>
                    <div className="right">
                        <img src="/assets/enrolled-course.png" alt="" />
                    </div>
                </div>
                <div className="dash_card" onClick={() => history.push(`/dashboard/learning-path`)}>
                    <div className="left">
                        <h3>{DashData?.learningPaths}</h3>
                        <p>Learning Paths</p>
                    </div>
                    <div className="right">
                        <img src="/assets/learning-path.png" alt="" />
                    </div>
                </div>
                <div className="dash_card" onClick={() => history.push(`/dashboard/course`)}>
                    <div className="left">
                        <h3>{DashData?.completedCourse}</h3>
                        <p>Completed Courses</p>
                    </div>
                    <div className="right">
                        <img src="/assets/completed-course.png" alt="" />
                    </div>
                </div>
                <div className="dash_card" onClick={() => history.push(`/dashboard/course`)}>
                    <div className="left">
                        <h3>{DashData?.inProgressCourse}</h3>
                        <p>In Progress</p>
                    </div>
                    <div className="right">
                        <img src="/assets/inprogress.png" alt="" />
                    </div>
                </div>
                <div className="dash_card">
                    <div className="left">
                        <h3>+{DashData?.totalCertificates}</h3>
                        <p>Certificates</p>
                    </div>
                    <div className="right">
                        <img src="/assets/certificate.png" alt="" />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UserKPISDash;
