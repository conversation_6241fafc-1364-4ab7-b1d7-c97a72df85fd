import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const AddAssignmentsToSubGroup = ({ groupTeamData, getGroupTeams }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [homeworkList, setHomeworkList] = useState([]);
    const [subGroupHomeworks, setSubGroupHomeworks] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedHomework, setSelectedHomework] = useState([]);

    useEffect(() => {
        getHomeworks();
        if (params?.subgroup_id !== undefined) {
            getSubGroupHomework({ subgroup_id: params?.subgroup_id });
        }
    }, [params]);

    const getHomeworks = async (payload) => {
        await tanstackApi
            .post("homework/list", { is_assignment: true })
            .then((res) => {
                setHomeworkList(res?.data?.data);
            })
            .catch((err) => {
                setHomeworkList([]);
            });
    };

    const getSubGroupHomework = async (payload) => {
        await tanstackApi
            .post("user-subgroup/list-subgroup-homework", { ...payload })
            .then((res) => {
                setSubGroupHomeworks(res?.data?.data?.filter((dt) => dt?.[`lms_course_homework.is_assignment`] == 1));
            })
            .catch((err) => {
                setSubGroupHomeworks([]);
            });
    };

    useEffect(() => {
        if (subGroupHomeworks !== null) {
            setSelectedHomework(subGroupHomeworks?.map((dt) => dt?.homework_id) || []);
        }
    }, [subGroupHomeworks]);

    const onCourseSelection = (item) => {
        if (selectedHomework?.includes(item)) {
            setSelectedHomework(selectedHomework?.filter((dt) => dt !== item));
        } else {
            setSelectedHomework([...selectedHomework, item]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        const addedData = selectedHomework?.filter(
            (dt) => !subGroupHomeworks?.map((dt) => dt?.homework_id)?.includes(dt),
        );
        const options = subGroupHomeworks?.map((dt) => dt?.homework_id) || [];
        const removeData = options?.filter((item) => !selectedHomework?.includes(item));

        if (params?.subgroup_id !== undefined) {
            if (addedData?.length > 0) {
                const payload = {
                    subgroup_id: params?.subgroup_id,
                    homework_id: addedData,
                };

                await tanstackApi
                    .post("user-subgroup/assign-homework", { ...payload })
                    .then((res) => {
                        punchTimelineLog({
                            user_id: localStorage.getItem("userId"),
                            event: "teams",
                            log: `${addedData?.length} assignments assign to ${groupTeamData?.name} successfully.`,
                        });

                        toast.success(`${addedData?.length} Assignment Added`, {
                            description: res?.data?.message,
                        });
                        getSubGroupHomework({ subgroup_id: params?.subgroup_id });
                        setOpenAlert(false);
                    })
                    .catch((err) => {
                        toast.error("Something went wrong", {
                            description: err?.response?.data?.message,
                        });
                    });
            }

            if (removeData?.length > 0) {
                const payloadRemove = {
                    subgroup_id: params?.subgroup_id,
                    homework_ids: removeData || [],
                };
                await tanstackApi
                    .post("user-subgroup/de-assign-homework", { ...payloadRemove })
                    .then((res) => {
                        punchTimelineLog({
                            user_id: localStorage.getItem("userId"),
                            event: "teams",
                            log: `${removeData?.length} assignments removed from ${groupTeamData?.name} successfully.`,
                        });
                        toast.success(`${removeData?.length} assignments Removed`, {
                            description: res?.data?.message,
                        });
                        getSubGroupHomework({ subgroup_id: params?.subgroup_id });
                        setOpenAlert(false);
                    })
                    .catch((err) => {
                        toast.error("Something went wrong", {
                            description: err?.response?.data?.message,
                        });
                    });
            }
        }
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Assign Assignment, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will add selected assignment in this team.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Select assignments for team</CardTitle>
                            <CardDescription>
                                Click on choose button to select any assignment for team. Click on Add Assignment when
                                you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{selectedHomework?.length} Assignment Selected</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Assignment Title</th>
                                    <th>Points</th>
                                    <th>Related Course</th>
                                    <th>Submission date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {homeworkList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>{row?.homework_title}</td>
                                        <td>{row?.homework_points}</td>
                                        <td>{row?.lms_course?.course_title}</td>
                                        <td>{moment(row?.submission_date).format("LL")}</td>
                                        <td>
                                            {selectedHomework?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                {selectedHomework?.length > 0 && (
                    <CardFooter>
                        <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                            {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                            <Button onClick={() => setOpenAlert(true)}>
                                <i className="fa-solid fa-floppy-disk"></i> Add Assignment
                            </Button>
                        </div>
                    </CardFooter>
                )}
            </Card>
        </>
    );
};

export default AddAssignmentsToSubGroup;
