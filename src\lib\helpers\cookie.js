/**
 * @module cookie
 * @description A module for managing cookies using js-cookie.
 */

import Cookies from "js-cookie";
const baseDomain = "vercel.app"; // Set to your actual domain or leave empty for current domain

/**
 * Initializes cookie attributes with a specified expiration time.
 * @param {number} day - The number of days until the cookie expires. Default is 1.
 * @returns {CookiesStatic} The initialized cookie attributes.
 */
const getCookieInit = (day = 1) => {
    const expires = new Date(Date.now() + day * 864e5);
    expires.setUTCHours(23, 59, 59, 999); // Set to end of the day
    return Cookies.withAttributes({ path: "/", domain: baseDomain, expires: expires });
};

/**
 * Sets a cookie with a specified name, value, and expiration time.
 * @param {string} name - The name of the cookie.
 * @param {string} value - The value of the cookie.
 * @param {number} [days=1] - The number of days until the cookie expires. Default is 1.
 * @returns {string|undefined} The value of the cookie that was set.
 */
export const setCookie = (name, value, days = 1) => {
    return getCookieInit(days).set(name, value);
};

/**
 * Retrieves the value of a cookie by its name.
 * @param {string} name - The name of the cookie to retrieve.
 * @returns {string|undefined} The value of the cookie, or undefined if not found.
 */
export const getCookie = (name) => {
    return getCookieInit().get(name);
};

/**
 * Deletes a cookie by its name.
 * @param {string} name - The name of the cookie to delete.
 * @returns {void}
 */
export const deleteCookie = (name) => {
    return getCookieInit().remove(name);
};
