import QuizContainer from "@/pages/dashboard/quizzes/components/container";

const LongAnswer = (props) => {
    const content = props.content;
    const data = props.componentsArray[props.index];
    return (
        <QuizContainer {...props} type="long_answer" empty={content?.question == ""}>
            <div className="tw-px-10 tw-py-7">
                <div className="tw-relative tw-z-10 tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem]">
                    <div className="tw-relative tw-space-y-4">
                        <label
                            htmlFor="textarea"
                            className="tw-rounded-md tw-px-3 tw-text-left tw-font-mono tw-text-2xl tw-text-[#FE5C96]"
                            style={{
                                color: data?.styles?.question?.color,
                                fontFamily: data?.styles?.question?.fontFamily,
                                fontSize: data?.styles?.question?.fontSize,
                                lineHeight: 1.25,
                            }}
                        >
                            {content?.question}
                        </label>
                        <textarea
                            id="textarea"
                            rows={6}
                            style={{
                                color: data?.styles?.answer?.color,
                                fontFamily: data?.styles?.answer?.fontFamily,
                                fontSize: data?.styles?.answer?.fontSize,
                                backgroundColor: data?.styles?.answer?.backgroundColor,
                                borderColor: data?.styles?.answer?.borderColor,
                                borderWidth: data?.styles?.answer?.borderWidth,
                                borderStyle: data?.styles?.answer?.borderStyle,
                            }}
                            className="tw-w-full tw-resize-none tw-rounded-md tw-bg-transparent tw-p-2 tw-text-[24px] tw-text-black"
                        />
                    </div>
                </div>
            </div>
        </QuizContainer>
    );
};

export default LongAnswer;
