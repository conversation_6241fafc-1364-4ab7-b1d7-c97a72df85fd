import { FETCH_NOTIFICATION_REQ, GET_NOTIFICATION_LIST, READ_NOTIFICATION } from "@/redux-types";

export const fetchNotificationsReq = () => {
    return {
        type: FETCH_NOTIFICATION_REQ,
    };
};

export const getNotificationList = (NotificationList) => {
    return {
        type: GET_NOTIFICATION_LIST,
        payload: NotificationList,
    };
};

export const readNotification = (notficationID) => {
    return {
        type: READ_NOTIFICATION,
        payload: notficationID,
    };
};
