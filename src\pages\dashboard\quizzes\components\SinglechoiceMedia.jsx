import { cn } from "@/lib/utils";
import QuizContainer from "@/pages/dashboard/quizzes/components/container";
import { motion } from "framer-motion";
import { useState } from "react";

const getColumns = (length) => {
    if (length <= 3) return length;
    return Math.ceil(Math.sqrt(length)); // Approximate square grid
};

const getRows = (length) => {
    if (length <= 3) return 1;
    const cols = getColumns(length);
    return Math.ceil(length / cols); // Ensure enough rows for all items
};

const SinglechoiceMedia = (props) => {
    const [selected, setSelected] = useState("");
    const content = props.content;
    const handleClick = (option) => {
        setSelected(option);
    };

    return (
        <QuizContainer {...props} empty={content?.options?.length === 0} type="singlechoice_media">
            <div className="tw-h-full tw-px-10 tw-py-7">
                <div
                    className={`tw-grid tw-h-full tw-w-full tw-gap-4`}
                    style={{
                        gridTemplateColumns: `repeat(${getColumns(content?.options?.length)}, 1fr)`,
                        gridTemplateRows: `repeat(${getRows(content?.options?.length)}, 1fr)`,
                    }}
                >
                    {content?.options.map((img, idx) => {
                        const isChosen = selected?.label === img?.label;
                        return (
                            <motion.div
                                initial={{ y: 100, opacity: 0.5, scale: 0.5 }}
                                whileInView={{ y: 0, opacity: 1, scale: 1 }}
                                transition={{
                                    duration: 0.35,
                                    delay: 0.15 * idx,
                                    ease: "linear",
                                }}
                                onClick={() => handleClick(img)}
                                className={cn("tw-relative tw-h-full tw-w-full tw-overflow-hidden tw-rounded-lg")}
                                style={{
                                    borderWidth: content?.styles?.[isChosen ? "selected" : "default"]?.borderWidth,
                                    borderColor: content?.styles?.[isChosen ? "selected" : "default"]?.borderColor,
                                    borderStyle: content?.styles?.[isChosen ? "selected" : "default"]?.borderStyle,
                                }}
                                key={idx}
                            >
                                <div
                                    className={cn(
                                        "tw-absolute tw-left-0 tw-top-0 tw-flex tw-size-10 tw-items-center tw-justify-center tw-rounded-br-lg",
                                        isChosen ? "tw-z-10 tw-block" : "tw-hidden",
                                    )}
                                    style={{
                                        borderColor: content?.styles?.[isChosen ? "selected" : "default"]?.borderColor,
                                        backgroundColor:
                                            content?.styles?.[isChosen ? "selected" : "default"]?.backgroundColor,
                                        color: content?.styles?.[isChosen ? "selected" : "default"]?.color,
                                    }}
                                >
                                    <i className="fa-solid fa-check tw-text-[28px]"></i>
                                </div>
                                <img
                                    src={img?.src}
                                    alt=""
                                    className="tw-absolute tw-z-0 tw-h-full tw-w-full tw-rounded-lg tw-object-cover"
                                />
                            </motion.div>
                        );
                    })}
                </div>
            </div>
        </QuizContainer>
    );
};

export default SinglechoiceMedia;
