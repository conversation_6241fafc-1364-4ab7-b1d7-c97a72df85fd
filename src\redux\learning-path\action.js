import {
    ADD_LEARNING_PATH,
    ASSIGN_COURSE_OR_GROUP_TO_LEARNING_PATH,
    ASSIGN_COURSE_TO_LEARNING_PATH,
    FETCH_LEARNING_PATH_REQ,
    GET_LEARNING_PATH,
    UPDATE_ASSIGNED_COURSE_OR_GROUP_TO_LEARNING_PATH,
    UPDATE_LEARNING_PATH,
    UPLOAD_LEARNING_PATH,
    VIEW_LEARNING_PATH_DATA,
    VIEW_LEARNING_PATH_REQ,
    VIEW_LEVEL_THREEE_LEARNING_PATH_REQ,
} from "@/redux-types";

export const fetchLearningPathReq = () => {
    return {
        type: FETCH_LEARNING_PATH_REQ,
    };
};

export const getAllLearningPaths = (usersList) => {
    return {
        type: GET_LEARNING_PATH,
        payload: usersList,
    };
};

export const uploadLearningPathImg = (learningPath) => {
    return {
        type: UPLOAD_LEARNING_PATH,
        payload: learningPath,
    };
};

export const addLearningPath = (data) => {
    return {
        type: ADD_LEARNING_PATH,
        payload: data,
    };
};
export const updateLearningPath = (data) => {
    return {
        type: UPDATE_LEARNING_PATH,
        payload: data,
    };
};
export const assignCourseToPath = (data) => {
    return {
        type: ASSIGN_COURSE_TO_LEARNING_PATH,
        payload: data,
    };
};
export const assignUserORgroupToPath = (data) => {
    return {
        type: ASSIGN_COURSE_OR_GROUP_TO_LEARNING_PATH,
        payload: data,
    };
};
export const updateAssignedUserORgroupToPath = (data) => {
    return {
        type: UPDATE_ASSIGNED_COURSE_OR_GROUP_TO_LEARNING_PATH,
        payload: data,
    };
};
export const viewLevel2LearningPathReq = (data) => {
    return {
        type: VIEW_LEARNING_PATH_REQ,
        payload: data,
    };
};
export const viewLevel3LearningPathReq = (data) => {
    return {
        type: VIEW_LEVEL_THREEE_LEARNING_PATH_REQ,
        payload: data,
    };
};
export const viewLearningPathData = (data) => {
    return {
        type: VIEW_LEARNING_PATH_DATA,
        payload: data,
    };
};
