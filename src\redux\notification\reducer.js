import { FETCH_NOTIFICATION_REQ, GET_NOTIFICATION_LIST } from "@/redux-types";

const initialState = {
    notificationList: [],
    isLoading: true,
    error: null,
};

const NotificationReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_NOTIFICATION_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_NOTIFICATION_LIST:
            return {
                ...state,
                notificationList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};

export default NotificationReducer;
