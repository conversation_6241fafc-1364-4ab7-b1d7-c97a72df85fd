// "use client";

// import { Button } from "@/components/ui/button";
// import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
// import { restrictToWindowEdges } from "@dnd-kit/modifiers";
// import { Check, X } from "lucide-react";
// import { useEffect, useState } from "react";

// export default function FillBlanksQuestion({ question, onAnswerSubmit, showCorrectAnswer, savedAnswer }) {
//     const [draggables, setDraggables] = useState([]);
//     const [droppables, setDroppables] = useState([]);
//     const [activeId, setActiveId] = useState(null);
//     const [submitted, setSubmitted] = useState(false);

//     const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor));

//     useEffect(() => {
//         // Create draggables from the blanks
//         const initialDraggables = question.blanks.map((blank, index) => ({
//             id: `draggable-${index}`,
//             content: blank,
//         }));

//         // Create droppables for each blank in the text
//         const textParts = question.text.split("___");
//         const initialDroppables = Array(textParts.length - 1)
//             .fill(null)
//             .map((_, index) => ({
//                 id: `droppable-${index}`,
//                 content: null,
//             }));

//         // If we have a saved answer, populate the droppables
//         if (savedAnswer) {
//             const updatedDroppables = initialDroppables.map((droppable, index) => ({
//                 ...droppable,
//                 content: savedAnswer[index],
//             }));

//             // Filter out the draggables that have been used
//             const updatedDraggables = initialDraggables.filter((draggable) => !savedAnswer.includes(draggable.content));

//             setDroppables(updatedDroppables);
//             setDraggables(updatedDraggables);
//             setSubmitted(true);
//         } else {
//             // Shuffle the draggables
//             const shuffledDraggables = [...initialDraggables].sort(() => Math.random() - 0.5);
//             setDraggables(shuffledDraggables);
//             setDroppables(initialDroppables);
//         }
//     }, [question, savedAnswer]);

//     function handleDragStart(event) {
//         setActiveId(event.active.id);
//     }

//     function handleDragEnd(event) {
//         const { active, over } = event;
//         setActiveId(null);

//         if (!over) return;

//         // If dropping on a droppable
//         if (over.id.startsWith("droppable")) {
//             const draggableIndex = draggables.findIndex((d) => d.id === active.id);
//             const droppableIndex = droppables.findIndex((d) => d.id === over.id);

//             if (draggableIndex !== -1 && droppableIndex !== -1) {
//                 // Update the droppable with the draggable's content
//                 const updatedDroppables = [...droppables];
//                 updatedDroppables[droppableIndex].content = draggables[draggableIndex].content;

//                 // Remove the draggable
//                 const updatedDraggables = draggables.filter((_, index) => index !== draggableIndex);

//                 setDroppables(updatedDroppables);
//                 setDraggables(updatedDraggables);
//             }
//         }
//     }

//     function handleDragCancel() {
//         setActiveId(null);
//     }

//     const handleSubmit = () => {
//         setSubmitted(true);

//         // Extract the answers from the droppables
//         const answers = droppables.map((droppable) => droppable.content || "");

//         // Check if all blanks are filled
//         const allFilled = answers.every((answer) => answer !== "");

//         if (allFilled) {
//             // Check if the answers match the correct answers
//             const correct = JSON.stringify(answers) === JSON.stringify(question.correctAnswers);
//             onAnswerSubmit(answers, correct);
//         } else {
//             onAnswerSubmit(answers, false);
//         }
//     };

//     const renderText = () => {
//         const textParts = question.text.split("___");

//         return (
//             <div className="tw-mb-6 tw-text-lg">
//                 {textParts.map((part, index) => (
//                     <span key={index}>
//                         {part}
//                         {index < textParts.length - 1 && (
//                             <span
//                                 className={`tw-mx-1 tw-inline-block tw-min-w-24 tw-border-b-2 tw-px-2 tw-py-1 tw-text-center ${
//                                     submitted
//                                         ? droppables[index]?.content === question.correctAnswers[index]
//                                             ? "tw-border-green-500 tw-bg-green-700/30"
//                                             : "tw-border-red-500 tw-bg-red-700/30"
//                                         : "tw-border-orange-400"
//                                 }`}
//                             >
//                                 {droppables[index]?.content || "______"}
//                             </span>
//                         )}
//                     </span>
//                 ))}
//             </div>
//         );
//     };

//     const renderDraggables = () => {
//         return (
//             <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-2">
//                 {draggables.map((draggable) => (
//                     <div
//                         key={draggable.id}
//                         id={draggable.id}
//                         className={`tw-cursor-grab tw-rounded-md tw-bg-orange-700 tw-px-3 tw-py-2 ${
//                             activeId === draggable.id ? "tw-opacity-50" : ""
//                         }`}
//                         draggable
//                         onDragStart={(e) => {
//                             e.dataTransfer.setData("text/plain", draggable.id);
//                             handleDragStart({ active: { id: draggable.id } });
//                         }}
//                     >
//                         {draggable.content}
//                     </div>
//                 ))}
//             </div>
//         );
//     };

//     const renderCorrectAnswers = () => {
//         if (!showCorrectAnswer) return null;

//         return (
//             <div className="tw-mt-6 tw-rounded-md tw-border-2 tw-border-green-500 tw-bg-slate-700/50 tw-p-4">
//                 <h3 className="tw-mb-2 tw-text-xl tw-font-semibold tw-text-white">Correct Answers</h3>
//                 <div className="tw-space-y-2">
//                     {question.correctAnswers.map((answer, index) => (
//                         <div key={index} className="tw-flex tw-items-center tw-gap-2">
//                             <span>Blank {index + 1}:</span>
//                             <span className="tw-rounded-md tw-border tw-border-green-500 tw-bg-green-700/30 tw-px-3 tw-py-1">
//                                 {answer}
//                             </span>
//                         </div>
//                     ))}
//                 </div>
//             </div>
//         );
//     };

//     return (
//         <div className="tw-space-y-4">
//             <p className="tw-mb-2 tw-text-sm tw-text-orange-300">Drag and drop the words to fill in the blanks</p>

//             {renderText()}

//             <DndContext
//                 sensors={sensors}
//                 collisionDetection={closestCenter}
//                 onDragStart={handleDragStart}
//                 onDragEnd={handleDragEnd}
//                 onDragCancel={handleDragCancel}
//                 modifiers={[restrictToWindowEdges]}
//             >
//                 {!submitted && renderDraggables()}
//             </DndContext>

//             {!submitted && droppables.some((d) => d.content !== null) && (
//                 <Button onClick={handleSubmit} className="tw-mt-4 tw-bg-teal-700 tw-text-white hover:tw-bg-teal-600">
//                     Submit Answer
//                 </Button>
//             )}

//             {submitted && (
//                 <div className="tw-mt-4 tw-flex tw-items-center tw-gap-2 tw-rounded-md tw-bg-slate-700/50 tw-p-3">
//                     <span>Your answer is:</span>
//                     {JSON.stringify(droppables.map((d) => d.content)) === JSON.stringify(question.correctAnswers) ? (
//                         <Check className="tw-text-green-400" />
//                     ) : (
//                         <X className="tw-text-red-400" />
//                     )}
//                 </div>
//             )}

//             {renderCorrectAnswers()}
//         </div>
//     );
// }

import DragItem from "@/pages/quiz-new-layout/question-types/fill-blanks-question/drag-item";
import DropZone from "@/pages/quiz-new-layout/question-types/fill-blanks-question/drop-zone";
import React, { useState } from "react";
import { toast } from "sonner";

const FillInBlanks = ({ question, onNext }) => {
    const [filledBlanks, setFilledBlanks] = useState({});
    const [placedItems, setPlacedItems] = useState({});

    const handleItemDropped = (blankId, itemId, word) => {
        Object.entries(filledBlanks).forEach(([existingBlankId, data]) => {
            if (data.itemId === itemId) {
                handleItemRemoved(existingBlankId);
            }
        });

        const previousItem = filledBlanks[blankId]?.itemId;
        if (previousItem) {
            setPlacedItems((prev) => ({ ...prev, [previousItem]: false }));
        }

        // Update the filled blank
        setFilledBlanks((prev) => ({
            ...prev,
            [blankId]: { word, itemId },
        }));

        // Mark the item as placed
        setPlacedItems((prev) => ({ ...prev, [itemId]: true }));

        toast.success("Word placed successfully!");
    };

    const handleItemRemoved = (blankId) => {
        const itemId = filledBlanks[blankId]?.itemId;

        if (itemId) {
            // Make the item available again
            setPlacedItems((prev) => ({ ...prev, [itemId]: false }));

            // Remove it from the blank
            setFilledBlanks((prev) => {
                const newState = { ...prev };
                delete newState[blankId];
                return newState;
            });

            toast.info("Word removed from blank");
        }
    };

    const handleReviewLater = () => {
        toast.info("Question saved for review later");
        onNext();
    };

    const renderTextWithBlanks = () => {
        const parts = question.text.split("___");

        return (
            <div className="tw-my-6 tw-font-lexend tw-text-lg tw-font-medium tw-leading-relaxed tw-text-white">
                {parts.map((part, index) => {
                    return (
                        <React.Fragment key={index}>
                            {part}
                            {index < question.blanks.length && (
                                <DropZone
                                    id={question.blanks[index]}
                                    onItemDropped={(itemId, word) =>
                                        handleItemDropped(question.blanks[index], itemId, word)
                                    }
                                    onItemRemoved={() => handleItemRemoved(question.blanks[index])}
                                    filledItem={filledBlanks[question.blanks[index]] || null}
                                />
                            )}
                        </React.Fragment>
                    );
                })}
            </div>
        );
    };

    const WordsContainer = () => {
        return (
            <div className="tw-mb-10 tw-mt-8 tw-flex tw-flex-wrap tw-justify-center tw-gap-3">
                {question.blanks.map((option, index) => (
                    <DragItem
                        key={`${question.id}-option-${index}`}
                        id={`${question.id}-option-${index}`}
                        word={option}
                        isPlaced={placedItems[`${question.id}-option-${index}`] || false}
                    />
                ))}
            </div>
        );
    };

    return (
        <div className="">
            {renderTextWithBlanks()}

            <WordsContainer />
        </div>
    );
};

export default FillInBlanks;
