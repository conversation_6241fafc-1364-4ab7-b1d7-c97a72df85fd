import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import useLocalStorage from "@/hooks/use-local-storage";
import { PermissionGranted } from "@/lib/helpers/utility";
import { DataTable } from "@/pages/dashboard/users/data-table";
import UserForm from "@/pages/dashboard/users/form";
import { useGetRoleListing } from "@/react-query/roles";
import { useGetUsersListing } from "@/react-query/users";
import ConfirmationNumberIcon from "@mui/icons-material/ConfirmationNumber";
import EditIcon from "@mui/icons-material/Edit";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import { format } from "date-fns";
import { ArrowUpDown } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import AssignCoursesTrainer from "./AssignCoursesTrainer";

export default function UsersPage() {
    const [level] = useLocalStorage("level", null);
    const userListing = useGetUsersListing(level);
    const listingRoles = useGetRoleListing();
    const [selected, setSelected] = useState(null);
    const [open, setOpen] = useState(false);
    const [openTrainer, setOpenTrainer] = useState(false);
    const [editData, setEditData] = useState(null);
    const [listData, setListData] = useState([]);

    const handleSelect = (id) => {
        const f = userListing.data.data.find((dt) => dt.id == id);
        setSelected(f);
        setOpen(true);
    };

    useEffect(() => {
        if (userListing.status == "success") {
            setListData(userListing.data.data);
        }
    }, [userListing]);

    const columns = [
        {
            accessorKey: "picture_url",
            header: "Profile",
            cell: ({ row }) => {
                return (
                    <Avatar>
                        <AvatarImage src={row.getValue("picture_url")} />
                        <AvatarFallback>
                            {row.getValue("first_name")[0]}
                            {row.getValue("last_name")[0]}
                        </AvatarFallback>
                    </Avatar>
                );
            },
        },
        {
            accessorKey: "user_code",
            header: "User Code",
        },
        {
            accessorKey: "first_name",
            header: ({ column }) => {
                return (
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        First Name
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                );
            },
        },
        {
            accessorKey: "last_name",
            header: "Last Name",
        },
        {
            accessorKey: "email",
            header: ({ column }) => {
                return (
                    <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
                        Email
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                );
            },
        },
        {
            accessorKey: "role",
            header: "Role",
        },
        {
            accessorKey: "is_trainer",
            header: "Trainer",
            cell: ({ row }) => {
                const is_trainer = Boolean(row.getValue("is_trainer"));
                return <div className="text-right font-medium">{is_trainer ? "Yes" : "-"}</div>;
            },
        },
        {
            accessorKey: "job_title",
            header: "Assign",
            cell: ({ row }) => {
                const is_trainer = Boolean(row.getValue("is_trainer"));
                return (
                    <div className="text-right font-medium">
                        {is_trainer ? (
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setEditData(row.getValue("id"));
                                    setOpenTrainer(true);
                                }}
                            >
                                <i className="fa-solid fa-book"></i> Courses
                            </Button>
                        ) : (
                            "-"
                        )}
                    </div>
                );
            },
        },
        {
            accessorKey: "status",
            header: "Status",
        },
        {
            accessorKey: "createdAt",
            header: "Create On",
            cell: ({ row }) => {
                return <div className="text-right font-medium">{format(row.getValue("createdAt"), "PP")}</div>;
            },
        },
        {
            accessorKey: "id",
            header: "Action",
            cell: (props) => {
                return (
                    <div className="tw-flex tw-gap-2">
                        {(PermissionGranted("USER", "EDIT") || PermissionGranted("USER", "UPDATE")) && (
                            <EditIcon
                                onClick={() => {
                                    handleSelect(props.row.getValue("id"));
                                }}
                                className="tw-text-blue-500"
                            />
                        )}
                        {props?.row.getValue("is_trainer") ? null : (
                            <>
                                <Link
                                    to={`/dashboard/users/control-panel/${props.row.getValue("user_code")}_${props.row.getValue("id")}`}
                                >
                                    <ManageAccountsIcon className="tw-text-blue-500" />
                                </Link>
                                <Link
                                    to={`/dashboard/users/enroll/${props.row.getValue("user_code")}_${props.row.getValue("id")}`}
                                >
                                    <ConfirmationNumberIcon className="tw-text-blue-500" />
                                </Link>
                            </>
                        )}
                    </div>
                );
            },
        },
    ];
    if (userListing.isLoading || listingRoles.isLoading) return <div>Loading...</div>;
    return (
        <div>
            <div>
                <div className="tw-font-lexend tw-text-3xl tw-font-medium">Users</div>
            </div>
            <DataTable
                columns={columns}
                roles={listingRoles.data.data}
                data={listData}
                setOpen={setOpen}
                setSelected={setSelected}
            />
            <UserForm open={open} setOpen={setOpen} selected={selected} roles={listingRoles.data.data} />
            <AssignCoursesTrainer open={openTrainer} setOpen={setOpenTrainer} editData={editData} />
        </div>
    );
}
