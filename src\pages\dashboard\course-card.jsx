import { BiSignal2, BiSignal3, BiSignal5 } from "react-icons/bi";

const DashboardCoursesCard = ({ course }) => {
    let value = null;

    const getMonths = (dt) => {
        value = Number(dt?.expiration_days) / 30;
        return Math.floor(value);
    };
    const getAudience = (type) => {
        if (type === "BEGINNER") {
            return <BiSignal2 id="signal-icon" />;
        } else if (type === "EXPERTS") {
            return <BiSignal5 id="signal-icon" />;
        } else if (type === "INTERMIDIATE") {
            return <BiSignal3 id="signal-icon" />;
        } else if (type === "ALL") {
            return <BiSignal3 id="signal-icon" />;
        }
    };
    return (
        <div className="dash-course-crd-mn">
            <div className="img">
                <img src={course?.course_banner_url} alt="" />
                <span>
                    {course?.audience_type} {getAudience(course?.audience_type)}
                </span>
            </div>
            <div className="dt-cntr">
                <div>
                    <h5>
                        {course?.course_title.length < 20 ? (
                            course?.course_title
                        ) : (
                            <>{course?.course_title.slice(0, 20)}...</>
                        )}
                    </h5>
                    <span>{course?.lms_course_category?.category_name}</span>
                </div>
                {/* <div className="center">
                    <div className="top">
                        <div><MdOutlineLibraryBooks id='cntrl-icon' /><span>98</span></div>
                        <div><FiUsers id='cntrl-icon' /><span>98</span></div>
                    </div>
                    <div className="bottom">
                        <div className="bar">
                            <span className="course-tooltip">50%</span>
                            <div className="status"></div>
                        </div>
                    </div>
                </div> */}
                <footer>
                    {course?.lms_course_settings?.[0]?.pricing_details?.is_free_course ? (
                        <span>This course is free!</span>
                    ) : (
                        <span>
                            Price : {course?.lms_course_settings?.[0]?.pricing_details?.price_type}{" "}
                            {course?.lms_course_settings?.[0]?.pricing_details?.course_price}
                        </span>
                    )}

                    <span>
                        {getMonths(course?.lms_course_settings?.[0]) > 0 ? (
                            <>{getMonths(course?.lms_course_settings?.[0])} Months</>
                        ) : (
                            ""
                        )}
                    </span>
                </footer>
            </div>
        </div>
    );
};

export default DashboardCoursesCard;
