import { But<PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON>alogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";

import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { tanstackApi } from "@/react-query/api";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { toast } from "sonner";

const ChangePassword = ({ open, setOpen, editChapter, CourseData, getCourses }) => {
    const params = useParams();

    const [step, setStep] = useState("email");

    const [email, setEmail] = useState("");
    const [userData, setUserData] = useState({});
    const [OTP, setOTP] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");

    const onGenerateOTP = async () => {
        if (!email) {
            toast.warning("Email", {
                description: `Please enter email.`,
            });
            return false;
        }

        const payload = {
            email: email,
        };
        await tanstackApi
            .post("signup/forgot-password", { ...payload })
            .then((res) => {
                toast.success("OTP Sent", {
                    description: res?.data?.message,
                });
                setStep("otp");
                setUserData(res?.data);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const onVerifyOTP = async () => {
        if (!OTP) {
            toast.warning("OTP", {
                description: `Please enter OTP.`,
            });
            return false;
        }

        const payload = {
            userId: Number(userData?.userId),
            userOtp: Number(OTP),
        };
        await tanstackApi
            .post("signup/verify-otp", { ...payload })
            .then((res) => {
                if (res?.data?.success) {
                    toast.success("Verified", {
                        description: res?.data?.message,
                    });
                    setStep("password");
                    localStorage.setItem("login_token", res?.data?.token);
                } else {
                    toast.error("Wrong OTP", {
                        description: "Entered otp is incorrect",
                    });
                }
            })
            .catch((err) => {
                toast.error("Wrong OTP", {
                    description: "Entered otp is incorrect",
                });
            });
    };

    const onUpdatePassword = async (e) => {
        e.preventDefault();
        if (!password) {
            toast.warning("Password", {
                description: `Please enter Password.`,
            });
            return false;
        }
        if (!confirmPassword) {
            toast.warning("Confirm Password", {
                description: `Please confirm your passsword.`,
            });
            return false;
        }
        if (password !== confirmPassword) {
            toast.warning("Passwords Not Matching", {
                description: `Entered password is not matching with confirm password.`,
            });
            return false;
        }

        const payload = {
            password: password,
        };
        await tanstackApi
            .post("signup/change-password", { ...payload })
            .then((res) => {
                toast.success("Updated Successfully", {
                    description: res?.data?.message,
                });
                setStep("email");
                setOpen(false);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-md">
                    <DialogHeader>
                        <DialogTitle className="tw-text-2xl">Change your password</DialogTitle>
                        <DialogDescription>
                            Fill up below details in order to update or change password.
                        </DialogDescription>
                    </DialogHeader>
                    {step == "email" && (
                        <div className="tw-flex tw-flex-col tw-gap-2">
                            <div className="tw-grid tw-grid-cols-1 tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="name">Registered Email</Label>
                                    <Input
                                        onChange={(e) => setEmail(e.target.value)}
                                        value={email}
                                        id="name"
                                        type="email"
                                        placeholder="Enter your registered email id here"
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                    {step == "otp" && (
                        <div className="tw-m-5 tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-3">
                            <InputOTP maxLength={4} onChange={(e) => setOTP(e)}>
                                <InputOTPGroup>
                                    <InputOTPSlot index={0} />
                                </InputOTPGroup>
                                <InputOTPGroup>
                                    <InputOTPSlot index={1} />
                                </InputOTPGroup>
                                <InputOTPGroup>
                                    <InputOTPSlot index={2} />
                                </InputOTPGroup>
                                <InputOTPGroup>
                                    <InputOTPSlot index={3} />
                                </InputOTPGroup>
                            </InputOTP>
                            <p className="tw-text-sm tw-text-slate-400">
                                Enter 4 digit OTP , Sent to your registered email : {email}
                            </p>
                        </div>
                    )}
                    {step == "password" && (
                        <div>
                            <div className="tw-grid tw-grid-cols-1 tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="password">New Password</Label>
                                    <Input
                                        onChange={(e) => setPassword(e.target.value)}
                                        value={password}
                                        id="password"
                                        type="text"
                                        placeholder="eg:121abcd#1234"
                                    />
                                </div>
                                <div className="tw-space-y-1">
                                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                                    <Input
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        value={confirmPassword}
                                        id="confirmPassword"
                                        type="text"
                                        placeholder="Confirm your passsword"
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild></DialogClose>
                        {step == "email" && (
                            <>
                                <Button type="button" variant="secondary" onClick={() => setEmail("")}>
                                    <i className="fa-solid fa-xmark"></i> Clear
                                </Button>
                                <Button type="submit" onClick={onGenerateOTP}>
                                    <i className="fa-solid fa-quote-left"></i> Generate OTP
                                </Button>
                            </>
                        )}
                        {step == "otp" && (
                            <>
                                <Button type="button" variant="secondary" onClick={() => setStep("email")}>
                                    <i className="fa-solid fa-left-long"></i> Back
                                </Button>
                                <Button type="submit" onClick={onVerifyOTP}>
                                    <i className="fa-solid fa-circle-check"></i> Verify OTP
                                </Button>
                            </>
                        )}
                        {step == "password" && (
                            <>
                                <Button type="button" variant="secondary" onClick={() => setStep("email")}>
                                    <i className="fa-solid fa-left-long"></i> Back
                                </Button>
                                <Button type="submit" onClick={onUpdatePassword}>
                                    <i className="fa-solid fa-circle-check"></i> Update Password
                                </Button>
                            </>
                        )}
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default ChangePassword;
