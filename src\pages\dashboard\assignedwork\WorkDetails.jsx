import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useParams } from "react-router-dom";

import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const WorkDetails = () => {
    const dispatch = useDispatch();
    const params = useParams();

    const [database, setDatabase] = useState(null);

    useEffect(() => {
        if (params?.id !== undefined) {
            getAssignedsData(params?.id);
        }
    }, [params]);

    const getAssignedsData = async (workID) => {
        await tanstackApi
            .post("homework/list-homeworks-trainer", { is_public: false })
            .then((res) => {
                setDatabase(res?.data?.data?.find((dt) => dt?.id == Number(workID)));
            })
            .catch((err) => {
                setDatabase(null);
            });
    };

    const onDownloadHomework = () => {
        const url = database?.attachment_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `${database?.attachment_url}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    return (
        <div className="">
            <div className="pageHeader">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>All Submission</BreadcrumbPage>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{params?.type} Details</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Link to={"/dashboard/assigned-data"}>
                    <Button className="back_btn">
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                </Link>
            </div>
            <br />
            <div className="content_body_homework">
                <div className="homework_title">
                    <h1>
                        <i className="fa-solid fa-laptop-file"></i> {database?.homework_title}
                    </h1>
                </div>
                <div className="homework_description">
                    <p>{database?.description}</p>
                </div>
                <div className="homework_details">
                    <div>
                        <p>
                            <i className="fa-solid fa-dice-d6"></i> Points
                        </p>
                        <h4>{database?.homework_points}</h4>
                    </div>
                    <div>
                        <p>
                            {" "}
                            <i className="fa-solid fa-clock"></i> Submission
                        </p>
                        <h4>{moment(database?.submission_date).format("LLL")}</h4>
                    </div>
                    <div>
                        <p>
                            <i className="fa-solid fa-paperclip"></i> Attachments
                        </p>
                        <h4>
                            <button onClick={onDownloadHomework}>
                                <i className="fa-solid fa-download"></i> Download
                            </button>
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WorkDetails;
