import { tanstack<PERSON>pi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetUsersEnrollListing = ({ userId }) => {
    return useQuery({
        queryKey: ["users-enroll", { userId }],
        queryFn: async () => {
            return (await tanstackApi.post(`learner-permission/get-assigned-courses`, { userId })).data;
        },
        enabled: Boolean(userId),
    });
};

export const useGetUsersEnrollApprove = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.post(`learner-permission/allow-enrollment`, data)).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries(["users-enroll"]);
        },
    });
};

export const useGetUserAssignCourse = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => {
            return (await tanstackApi.post(`learner-permission/add-courses-to-user`, data)).data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries(["users-enroll"]);
        },
    });
};
