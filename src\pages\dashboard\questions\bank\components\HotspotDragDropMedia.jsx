import { useEffect, useRef, useState } from "react";
import Draggable from "react-draggable";

const HotspotDragDropMedia = ({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    componentsArray,
    setComponentsArray,
}) => {
    const containerRef = useRef(null);
    const [circles, setCircles] = useState([]);
    const [dropzones, setDropzones] = useState([]);
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

    // Update container dimensions dynamically
    useEffect(() => {
        const updateContainerSize = () => {
            if (containerRef.current) {
                const rect = containerRef.current.getBoundingClientRect();
                setContainerSize({ width: rect.width, height: rect.height });
            }
        };

        updateContainerSize();
        window.addEventListener("resize", updateContainerSize);

        return () => {
            window.removeEventListener("resize", updateContainerSize);
        };
    }, []);

    const clampPosition = (x, y) => {
        return {
            x: Math.min(Math.max(x, 0), containerSize.width),
            y: Math.min(Math.max(y, 0), containerSize.height),
        };
    };

    // Convert relative positions to absolute positions
    const getAbsolutePosition = (relativeX, relativeY) => {
        if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
        return clampPosition(relativeX * containerSize.width, relativeY * containerSize.height);
    };

    // Convert absolute positions to relative positions
    const getRelativePosition = (data, absoluteX, absoluteY) => {
        if (!containerSize.width || !containerSize.height) return { x: 0, y: 0 };
        return {
            ...data,
            x: absoluteX / containerSize.width,
            y: absoluteY / containerSize.height,
        };
    };

    // Initialize hotspots and dropzones from `data`
    useEffect(() => {
        if (content?.hotspots?.length > 0) {
            setCircles(content.hotspots);
            setDropzones(content.hotspotDropzone);
        }
    }, [content, containerSize]);

    // Handle draggable stop for hotspots
    const handleStop = (index, pos) => {
        setCircles((prevCircles) =>
            prevCircles.map((circle, i) => (i === index ? { ...getRelativePosition(circle, pos.x, pos.y) } : circle)),
        );
    };

    // Handle draggable stop for dropzones
    const handleStopDropzone = (index, pos) => {
        setDropzones((prevZones) =>
            prevZones.map((zone, i) => (i === index ? { ...getRelativePosition(zone, pos.x, pos.y) } : zone)),
        );
    };

    // useEffect(() => {
    //     let options = [...componentsArray];
    //     options[index].hotspotDropzone = dropzones;
    //     options[index].hotspots = circles;
    //     setComponentsArray(options);
    // }, [circles, dropzones]);

    return (
        <div
            className="tw-grid tw-min-h-full tw-grid-rows-[1fr] tw-rounded-2xl tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            // style={{ backgroundImage: `url(${template?.background||content?.question_background})` }}
        >
            <div className="tw-flex tw-items-center tw-justify-center tw-px-10 tw-py-7">
                <div
                    className="tw-overflow-hidden tw-rounded-lg tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{
                        backgroundImage: `url(${content?.question_thumbnail})`,
                        position: "relative",
                        // aspectRatio: 12/7,
                        width: "600px",
                        height: "350px",
                    }}
                    id="dnd_img_container"
                    ref={containerRef}
                >
                    {/* Render Hotspots */}
                    {circles.map((position, index) => {
                        const { x, y } = getAbsolutePosition(position.x, position.y);
                        return (
                            <Draggable
                                key={`circle-${index}`}
                                bounds="parent"
                                position={{ x, y }}
                                onStop={(e, data) => handleStop(index, data)}
                            >
                                <div className="tw-absolute tw-z-10 tw-flex tw-h-[80px] tw-w-[80px] tw-cursor-grab tw-flex-col tw-items-center tw-justify-center tw-gap-1 tw-rounded-full tw-bg-gray-100">
                                    <img
                                        className="tw-h-[35px]"
                                        src={position.src || "/assets/image.png"}
                                        alt={position.name}
                                    />
                                    <p className="tw-text-xs tw-font-medium">{position.name}</p>
                                </div>
                            </Draggable>
                        );
                    })}

                    {/* Render Dropzones */}
                    {dropzones.map((position, index) => {
                        const { x, y } = getAbsolutePosition(position.x, position.y);
                        return (
                            <Draggable
                                key={`dropzone-${index}`}
                                bounds="parent"
                                position={{ x, y }}
                                onStop={(e, data) => handleStopDropzone(index, data)}
                            >
                                <div className="tw-absolute tw-z-0 tw-flex tw-h-[80px] tw-w-[80px] tw-cursor-grab tw-flex-col tw-items-center tw-justify-center tw-gap-1 tw-rounded-full tw-border-[1px] tw-border-dashed tw-border-slate-950 tw-bg-white">
                                    {/* <img src={position.src} alt={`Zone ${position.zone}`} /> */}
                                    <p className="tw-text-xs tw-font-medium">{position.zone}</p>
                                </div>
                            </Draggable>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default HotspotDragDropMedia;
