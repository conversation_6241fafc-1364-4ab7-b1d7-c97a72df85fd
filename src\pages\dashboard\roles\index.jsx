import { But<PERSON> } from "@/components/ui/button";
import { PermissionGranted } from "@/lib/helpers/utility";
import RoleForm from "@/pages/dashboard/roles/form";
import { DataTable } from "@/pages/dashboard/roles/permission/data-table";
import { useGetRoleListing } from "@/react-query/roles";
import { format } from "date-fns";
import { Edit, Eye, Plus } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

export default function RolesPage() {
    const listingRoles = useGetRoleListing();

    const [open, setOpen] = useState(false);
    const [selected, setSelected] = useState(null);

    const handleUpdate = (id) => {
        const data = listingRoles.data?.data.find((dt) => dt.id == id);
        setSelected(data);
        setOpen(true);
    };

    const columns = [
        {
            accessorKey: "id",
        },
        {
            accessorKey: "role_code",
        },
        {
            accessorKey: "display_name",
            header: "Role",
        },
        {
            accessorKey: "is_active",
            header: "Status",
            cell: ({ row }) => {
                const is_active = Boolean(row.getValue("is_active"));
                return <div className="text-right font-medium">{is_active ? "Active" : "In Active"}</div>;
            },
        },
        {
            accessorKey: "createdAt",
            header: "Create On",
            cell: ({ row }) => {
                return <div className="text-right font-medium">{format(row.getValue("createdAt"), "PP")}</div>;
            },
        },
        {
            accessorKey: "is_system_role",
            header: "Action",
            cell: ({ row }) => {
                return (
                    <div className="tw-flex tw-gap-2">
                        <Link to={`/dashboard/roles/permission/${row.getValue("role_code")}`}>
                            <Eye className="tw-text-blue-500" />
                        </Link>
                        {row.getValue("is_system_role") === 0 &&
                            (PermissionGranted("ROLES", "EDIT") || PermissionGranted("ROLES", "UPDATE")) && (
                                <Edit
                                    onClick={() => {
                                        handleUpdate(row.getValue("id"));
                                    }}
                                    className="tw-text-blue-500"
                                />
                            )}
                    </div>
                );
            },
        },
    ];

    if (listingRoles.isLoading) return <div>Loading...</div>;

    return (
        <div>
            <div className="tw-mb-3 tw-flex tw-items-center tw-justify-between">
                <div className="tw-font-lexend tw-text-3xl tw-font-medium">Roles</div>
                <Button
                    onClick={() => {
                        setSelected(null);
                        setOpen(true);
                    }}
                    variant="outline"
                >
                    <Plus />
                    Create Role
                </Button>
            </div>
            <DataTable columns={columns} data={listingRoles.data.data} />
            <RoleForm open={open} setOpen={setOpen} selected={selected} />
        </div>
    );
}
