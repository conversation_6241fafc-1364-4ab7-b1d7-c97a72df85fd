import { useEffect, useState } from "react";

const SingleChoice = ({ data, index, onComponentAnswer, Disable, correctAns }) => {
    const [compValue, setCompValue] = useState(data?.answerKey);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        let ans = data?.options?.filter((dt) => dt?.name == compValue)?.[0];
        let points = ans?.isCorrect == true ? data?.points : 0;
        onComponentAnswer(index, compValue, points);
    }, [compValue]);

    useEffect(() => {
        setIsMounted(true);
        return () => {
            setIsMounted(false);
        };
    }, []);

    return (
        <div className="comp_control">
            <div className="single_choice_input">
                <label htmlFor="">
                    {data?.name || "Component title here"} <b>{data?.preference == "required" && "*"}</b>
                </label>
                <div className="option_box" style={{ gridTemplateColumns: data?.optionColumns }}>
                    {data?.options?.map((option, idx) => (
                        <div key={idx} className={data?.answerKey == option?.name && "isSelected"}>
                            <input
                                type="radio"
                                name="single_choice"
                                id={option?.name}
                                value={option?.name}
                                checked={data?.answerKey == option?.name}
                                onChange={(e) => setCompValue(e.target.value)}
                                disabled={Disable}
                            />
                            <label htmlFor={option?.name} className={option?.isCorrect ? "" : ""}>
                                {option?.name}
                            </label>
                        </div>
                    ))}
                </div>
            </div>
            {data?.specialInstruction && (
                <p className="special_instruction">
                    <i className="fa-solid fa-circle-info"></i>
                    <small>{data?.specialInstruction}</small>
                </p>
            )}
        </div>
    );
};

export default SingleChoice;
