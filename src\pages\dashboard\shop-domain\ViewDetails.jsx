import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { TvMinimalPlay } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";

const ViewDetails = ({ open, setOpen, editData, onAddtoCart }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [course, setCourse] = useState(null);
    const [bundle, setBundle] = useState(null);
    const [bulkPriceList, setBulkPriceList] = useState([]);
    const [bulkData, setBulkData] = useState([]);
    const [dataType, setDataType] = useState("");

    const [bulkPriceID, setBulkPriceID] = useState(null);
    const [finalBulkSelect, setFinalBulkSelect] = useState(null);

    const onPriceChange = (value) => {
        if (value == "One Unit") {
            setBulkPriceID(value);
        } else {
            setBulkPriceID(value);
        }
    };

    useEffect(() => {
        if (editData?.bundle_id !== null) {
            // getBundle({ bundle_id: editData?.bundle_id });
            setBundle({ ...editData?.bundle, id: editData?.bundle_id });
            setDataType("bundle");
        }

        if (editData?.course_id !== null) {
            // getCourses({ course_id: editData?.course_id });
            setCourse(editData?.course);
            setDataType("course");
        }
    }, [editData]);

    useEffect(() => {
        if (bulkPriceID == "One Unit") {
            setFinalBulkSelect(null);
        } else {
            let data = bulkData?.find((dt) => dt?.id == Number(bulkPriceID));
            setFinalBulkSelect(data);
        }
    }, [bulkPriceID]);

    useEffect(() => {
        if (bulkPriceList?.length > 0) {
            if (dataType == "course") {
                let data = bulkPriceList?.filter((dt) => dt?.course_id == course?.id);
                setBulkData(data);
            } else {
                let data = bulkPriceList?.filter((dt) => dt?.bundle_id == bundle?.id);
                setBulkData(data);
            }
        }
    }, [bulkPriceList, dataType]);

    useEffect(() => {
        getBulkPriceList();
    }, [editData]);

    const getBulkPriceList = async () => {
        await tanstackApi
            .get("course-bundle-bulk-price/all", {
                params: {
                    currency_code: "USD",
                },
            })
            .then((res) => {
                setBulkPriceList(res?.data?.data);
            })
            .catch((err) => {
                setBulkPriceList([]);
            });
    };

    const AddCart = () => {
        if (bulkPriceID == "One Unit") {
            onAddtoCart(editData, true);
        } else {
            let final = finalBulkSelect;
            onAddtoCart(final, false);
        }
    };

    return (
        <>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-max-h-full tw-min-h-[500px] !tw-max-w-3xl">
                    {dataType == "course" && (
                        <div>
                            <div className="tw-grid tw-grid-cols-[270px_auto] tw-gap-3">
                                <div className="tw-h-[160px] tw-w-[270px] tw-overflow-hidden tw-rounded-lg tw-shadow-md">
                                    <img
                                        className="tw-h-full tw-w-full tw-object-cover"
                                        src={course?.course_banner_url}
                                        alt=""
                                    />
                                </div>
                                <div>
                                    <h1 className="tw-line-clamp-2 tw-font-lexend tw-text-lg">
                                        {course?.course_title}
                                    </h1>
                                    <div className="tw-mt-1 tw-flex tw-items-center tw-gap-1 tw-capitalize">
                                        <Badge variant={"outline"}>{editData?.subscription_type}</Badge>
                                        <Badge variant={"outline"}>{editData?.validity_type}</Badge>
                                    </div>
                                    <h1 className="tw-py-2 tw-text-2xl tw-font-bold">
                                        ${(finalBulkSelect?.converted_price || editData?.converted_price)?.toFixed(2)}
                                    </h1>
                                    <div className="tw-mt-1 tw-grid tw-grid-cols-2 tw-gap-3">
                                        <Select onValueChange={onPriceChange}>
                                            <SelectTrigger className="">
                                                <SelectValue placeholder="One Unit" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="One Unit">One Unit</SelectItem>
                                                    {bulkData?.map((data, index) => (
                                                        <SelectItem value={data?.id} key={index}>
                                                            {`${data?.lower_limit} - ${data?.upper_limit}`} users
                                                        </SelectItem>
                                                    ))}
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                        <Button onClick={AddCart}>
                                            <i className="fa-solid fa-cart-plus"></i> Add to cart
                                        </Button>
                                    </div>
                                </div>
                            </div>
                            <br />
                            <Tabs defaultValue="Content" className="">
                                <TabsList className="grid w-full grid-cols-2">
                                    <TabsTrigger value="Content" className="tw-flex tw-gap-2">
                                        <i className="fa-solid fa-shapes"></i> Content
                                    </TabsTrigger>
                                    <TabsTrigger value="Description" className="tw-flex tw-gap-2">
                                        <i className="fa-solid fa-circle-info"></i> Description
                                    </TabsTrigger>
                                </TabsList>
                                <TabsContent value="Content" className="tw-overflow-auto">
                                    <Accordion type="single" collapsible className="w-full">
                                        {course?.lms_course_chapters?.map((chapter, index) => (
                                            <AccordionItem key={index} value={`item-${index + 1}`}>
                                                <AccordionTrigger>
                                                    Chapter {index + 1} : {chapter?.chapter_title}
                                                </AccordionTrigger>
                                                <AccordionContent>
                                                    <div className="tw-flex tw-flex-col tw-gap-1">
                                                        {chapter?.lms_course_chapter_contents?.map((content, idx) => (
                                                            <p key={idx}>
                                                                {idx + 1}. {content?.lecture_title}
                                                            </p>
                                                        ))}
                                                    </div>
                                                </AccordionContent>
                                            </AccordionItem>
                                        ))}
                                    </Accordion>
                                </TabsContent>
                                <TabsContent value="Description">
                                    <p className="tw-text-md tw-text-gray-400">{course?.course_description}</p>
                                </TabsContent>
                            </Tabs>
                        </div>
                    )}
                    {dataType == "bundle" && (
                        <div>
                            <div className="tw-grid tw-grid-cols-[280px_auto] tw-gap-3">
                                <div className="tw-h-[170px] tw-w-[280px] tw-overflow-hidden tw-rounded-lg tw-shadow-md">
                                    <img
                                        className="tw-h-full tw-w-full tw-object-cover"
                                        src={bundle?.logo_image_url}
                                        alt=""
                                    />
                                </div>
                                <div>
                                    <h1 className="tw-line-clamp-1 tw-font-lexend tw-text-lg">{bundle?.name}</h1>
                                    <p className="tw-line-clamp-1 tw-font-lexend tw-text-sm tw-text-gray-500">
                                        {bundle?.description}
                                    </p>
                                    <div className="tw-mt-2 tw-flex tw-items-center tw-gap-1 tw-capitalize">
                                        <Badge variant={"outline"}>{editData?.subscription_type}</Badge>
                                        <Badge variant={"outline"}>{editData?.validity_type}</Badge>
                                    </div>
                                    <h1 className="tw-py-2 tw-text-2xl tw-font-bold">
                                        ${(finalBulkSelect?.converted_price || editData?.converted_price)?.toFixed(2)}
                                    </h1>
                                    <div className="tw-mt-1 tw-grid tw-grid-cols-2 tw-gap-3">
                                        <Select onValueChange={onPriceChange}>
                                            <SelectTrigger className="">
                                                <SelectValue placeholder="One Unit" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="One Unit">One Unit</SelectItem>
                                                    {bulkData?.map((data, index) => (
                                                        <SelectItem value={data?.id} key={index}>
                                                            {`${data?.lower_limit} - ${data?.upper_limit}`} users
                                                        </SelectItem>
                                                    ))}
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                        <Button onClick={AddCart}>
                                            <i className="fa-solid fa-cart-plus"></i> Add to cart
                                        </Button>
                                    </div>
                                </div>
                            </div>
                            <br />
                            <Tabs defaultValue="Content" className="">
                                <TabsList className="grid w-full grid-cols-2">
                                    <TabsTrigger value="Content" className="tw-flex tw-gap-2">
                                        <i className="fa-solid fa-shapes"></i> Content
                                    </TabsTrigger>
                                </TabsList>
                                <TabsContent value="Content" className="tw-flex tw-flex-col tw-gap-2 tw-overflow-auto">
                                    {bundle?.course_bundle_details?.map((data, index) => (
                                        <div
                                            key={index}
                                            className="p-1 tw-grid tw-grid-cols-[150px_auto] tw-gap-2 tw-rounded-lg tw-bg-slate-50"
                                        >
                                            <div className="tw-h-[75px] tw-w-[150px] tw-overflow-hidden tw-rounded-lg tw-border-[1px]">
                                                <img
                                                    className="tw-h-full tw-w-full tw-object-cover"
                                                    src={data?.course?.course_banner_url}
                                                    alt=""
                                                />
                                            </div>
                                            <div className="tw-flex tw-flex-col tw-gap-1">
                                                <h1 className="tw-text-md tw-line-clamp-1 tw-font-medium tw-text-gray-600">
                                                    {data?.course?.course_title}
                                                </h1>
                                                <p className="tw-line-clamp-2 tw-text-sm">
                                                    {data?.course?.course_description}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </TabsContent>
                            </Tabs>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </>
    );
};

export default ViewDetails;
