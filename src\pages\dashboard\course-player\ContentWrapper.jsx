import ContentBody from "@/pages/dashboard/course-player/content";
import IntroPage from "@/pages/dashboard/course-player/intro";
import ChapterIntro from "@/pages/dashboard/course-player/intro/chapter";
import QuizContent from "@/pages/dashboard/course-player/quiz-content";

const ContentWrapper = ({ courseStarted, CONTENT, courseDetails, Bookmarking }) => {
    return (
        <>
            {courseStarted ? (
                <>
                    {CONTENT?.type == "chapter" && (
                        <ChapterIntro details={CONTENT?.content} template={courseDetails?.lms_template} />
                    )}
                    {CONTENT?.content?.content_type !== "QUIZ" && (
                        <ContentBody
                            content={CONTENT?.content}
                            template={courseDetails?.lms_template}
                            courseId={courseDetails?.id}
                            Bookmarking={Bookmarking}
                        />
                    )}
                    {CONTENT?.content?.content_type == "QUIZ" && (
                        <>
                            <QuizContent
                                content={CONTENT?.content}
                                courseId={courseDetails?.id}
                                Bookmarking={Bookmarking}
                            />
                        </>
                    )}
                </>
            ) : (
                <IntroPage details={courseDetails} template={courseDetails?.lms_template} />
            )}
        </>
    );
};

export default ContentWrapper;
