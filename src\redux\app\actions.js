export function getView(width) {
    let newView = width > 1220 ? "DesktopView" : width > 767 ? "TabView" : "MobileView";
    return newView;
}

const appActions = {
    COLLPSE_CHANGE: "COLLPSE_CHANGE",
    COLLPSE_OPEN_DRAWER: "COLLPSE_OPEN_DRAWER",
    CHANGE_OPEN_KEYS: "CHANGE_OPEN_KEYS",
    TOGGLE_ALL: "TOGGLE_ALL",
    CHANGE_CURRENT: "CHANGE_CURRENT",
    CLEAR_MENU: "CLEAR_MENU",
    toggleCollapsed: () => ({
        type: appActions.COLLPSE_CHANGE,
    }),
    toggleAll: (width, height) => {
        const view = getView(width);
        const collapsed = view !== "DesktopView";
        return {
            type: appActions.TOGGLE_ALL,
            collapsed,
            view,
            height,
        };
    },
    toggleOpenDrawer: () => ({
        type: appActions.COLLPSE_OPEN_DRAWER,
    }),
    changeOpenKeys: (openKeys) => ({
        type: appActions.CHANGE_OPEN_KEYS,
        openKeys,
    }),
    changeCurrent: (current) => ({
        type: appActions.CHANGE_CURRENT,
        current,
    }),
    clearMenu: () => ({ type: appActions.CLEAR_MENU }),
};
export default appActions;
