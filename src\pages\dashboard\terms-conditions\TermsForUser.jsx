import { useEffect, useState } from "react";

const TermsForUser = () => {
    const [TermsData, setTermsData] = useState(null);
    useEffect(() => {
        var TerData = localStorage.getItem("terms___conditions");
        setTermsData(TerData);
    }, []);

    return (
        <div>
            <div className="TermsConditions">
                <h1 className="tw-text-center tw-text-3xl tw-font-bold">Terms & Conditions</h1>
                <div dangerouslySetInnerHTML={{ __html: TermsData }}></div>
            </div>
        </div>
    );
};

export default TermsForUser;
