import moment from "moment";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";

const WorkAssignments = ({ allAssignedWork, activeTab, UserGroups }) => {
    const [teams, setTeams] = useState([]);

    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(7);

    const [filterState, setFilterState] = useState({
        search: "",
        group_id: "",
        team_id: "",
        submission_date: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });

        if (name == "group_id") {
            getUserSubGroupData(value);
        }
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? item?.homework_title?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const team = filterState?.team_id
                ? item?.lms_homework_subgroups_mappings
                      ?.map((dt) => dt?.subgroup_id)
                      .includes(Number(filterState?.team_id))
                : true; // Allow all items if no subCategory filter

            const submission = filterState?.submission_date
                ? moment(item.submission_date).format("DD/MMM/YYYY") ===
                  moment(filterState?.submission_date).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            return matchesname && team && submission; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            group_id: "",
            team_id: "",
            submission_date: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        }
    }, [dataList]);

    useEffect(() => {
        if (allAssignedWork?.length > 0) {
            setDataList(allAssignedWork?.filter((dt) => dt?.is_assignment == true));
        }
    }, [allAssignedWork, activeTab]);

    const getUserSubGroupData = async (groupID) => {
        if (localStorage.getItem("is_trainer") == "true") {
            await tanstackApi
                .post("user-subgroup/list-subgroup-trainer")
                .then((res) => {
                    setTeams(res?.data?.data?.filter((dt) => dt?.lms_user_subgroup?.group_id == Number(groupID)));
                    // setTeams(res?.data?.data);
                })
                .catch((err) => {
                    setTeams([]);
                });
        } else {
            await tanstackApi
                .post("user-subgroup/get-my-subgroups")
                .then((res) => {
                    setTeams(res?.data?.data?.filter((dt) => dt?.group_id == Number(groupID)));
                })
                .catch((err) => {
                    setTeams([]);
                });
        }
    };

    return (
        <>
            <div className="page_filters">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Title
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState.search}
                        name="search"
                        className="tw-text-sm"
                        type="text"
                        placeholder="Search by title ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Teams
                    </label>
                    <select
                        className="tw-text-sm"
                        onChange={onFilterChange}
                        value={filterState.group_id}
                        name="group_id"
                    >
                        <option value=""> - Choose Teams - </option>
                        {UserGroups?.map((group, idx) => (
                            <option value={group?.id} key={idx}>
                                {group?.name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Submission Date
                    </label>
                    <input
                        className="tw-text-sm"
                        type="date"
                        onChange={onFilterChange}
                        value={filterState.submission_date}
                        name="submission_date"
                    />
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>Assignment Title</th>
                            <th>Teams</th>
                            <th>Submission Date</th>
                            <th>Assigned On</th>
                            <th>Submission & Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>{row?.homework_title}</td>
                                <td style={{ width: "500px" }}>
                                    <div className="chips_group">
                                        {row?.lms_homework_subgroups_mappings?.map((subgroup, idx) => (
                                            <p key={idx}>{subgroup?.lms_user_subgroup?.name}, </p>
                                        ))}
                                    </div>
                                </td>
                                <td>{moment(row?.submission_date).format("LLL")}</td>
                                <td>{moment(row?.created_at).format("LLL")}</td>
                                <td>
                                    <Link to={`/dashboard/assigned-data/Assignment/${row?.id}`}>
                                        <button className="selected_btn">
                                            <i className="fa-regular fa-folder-open"></i> View Details
                                        </button>
                                    </Link>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination className="">
                        <PaginationContent>
                            {/* Previous Button */}
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page Numbers with Ellipses */}
                            {currentPage > 3 && (
                                <>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                            1
                                        </PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                </>
                            )}

                            {getVisiblePages().map((page) => (
                                <PaginationItem key={page}>
                                    <PaginationLink
                                        href="#"
                                        isActive={page === currentPage}
                                        onClick={() => handlePageChange(page)}
                                    >
                                        {page}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {currentPage < totalPages - 2 && (
                                <>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                            {totalPages}
                                        </PaginationLink>
                                    </PaginationItem>
                                </>
                            )}

                            {/* Next Button */}
                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </>
    );
};

export default WorkAssignments;
