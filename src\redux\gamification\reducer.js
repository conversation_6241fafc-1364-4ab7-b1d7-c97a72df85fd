import {
    FETCH_BADGES_REQ,
    FETCH_HOMEWORK_REQ,
    <PERSON>ETCH_MY_GROUPS_REQ,
    FETCH_MY_SUB_GROUPS_REQ,
    FETCH_QUIZ_REQ,
    FETCH_USERS_HOMEWORK_REQ,
    GET_BADGES_LIST,
    GET_HOMEWORK_LIST,
    GET_MY_GROUPS_LIST,
    GET_MY_SUB_GROUPS_LIST,
    GET_QUIZ_LIST,
    GET_USERS_HOMEWORK_LIST,
} from "@/redux-types";

const initialState = {
    badgesList: [],
    quizzesList: [],
    homworkeList: [],
    userHomworkeList: [],
    myGrpupsList: [],
    mySubGrpupsList: [],
};

const GamificationReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_MY_SUB_GROUPS_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_MY_SUB_GROUPS_LIST:
            return {
                ...state,
                mySubGrpupsList: action.payload,
            };
        case FETCH_MY_GROUPS_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_MY_GROUPS_LIST:
            return {
                ...state,
                myGrpupsList: action.payload,
            };
        case FETCH_USERS_HOMEWORK_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_USERS_HOMEWORK_LIST:
            return {
                ...state,
                userHomworkeList: action.payload,
            };
        case FETCH_BADGES_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_BADGES_LIST:
            return {
                ...state,
                badgesList: action.payload,
            };
        case FETCH_QUIZ_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_QUIZ_LIST:
            return {
                ...state,
                quizzesList: action.payload,
            };
        case FETCH_HOMEWORK_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_HOMEWORK_LIST:
            return {
                ...state,
                homworkeList: action.payload,
            };
        default:
            return state;
    }
};
export default GamificationReducer;
