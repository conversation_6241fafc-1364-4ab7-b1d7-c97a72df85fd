.user_loggedin_logo {
    background: orange;
    width: 41px;
    height: 100%;
    aspect-ratio: 1/1;

    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0px 0px 7px 1.4px rgba(110, 110, 110, 1);
    position: relative;
}

.user_loggedin_logo img {
    border-radius: 50%;
}

.user_loggedin_logo p {
    border: 1px solid white;
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 13px;
    height: 13px;
    background-color: rgba(115, 173, 1, 1);
    border-radius: 50%;
}

.user_loggedin_logo span {
    font-size: 1.2rem;
    color: white;
    font-weight: 500;
    text-transform: uppercase;
}

.user_loggedin_logo:hover {
    cursor: pointer;
    box-shadow: 0px 0px 10px 1.4px rgba(70, 70, 70, 1);
    transition: 0.6s;
}

.ant-popover-inner {
    padding: 0 !important;
}
