@import url("https://fonts.googleapis.com/css2?family=Josefin+Sans:wght@100;300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Love+Ya+Like+A+Sister&display=swap");
@import url("https://fonts.cdnfonts.com/css/lazy-dog");
@import url("https://fonts.googleapis.com/css2?family=Marhey:wght@300..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap");

:root {
    --color-primary: #7c73e6;
    --color-primary-light: #7d73e6a9;
    --color-light-gray: #d8d8d8;
    --color-dark-gray: #767676;
    --color-middle-gray: #76767677;
    --card-ui-border-radius: 15px;
    --dash-primary-color: #43b8b1;
    --input-primary-color: rgb(0, 102, 204);
    --input-primary-color-50: rgba(0, 102, 204, 0.5);
    --input-primary-color-70: rgba(0, 102, 204, 0.7);
    --input-primary-color-85: rgba(0, 102, 204, 0.85);
    --input-label-color: #41403e;
    --input-value-color: #10172a;
    --course-tab-bg: rgb(246, 250, 255);
}
.main-error-text {
    color: rgba(249, 53, 53, 0.845) !important;
    font-size: 0.7rem !important;
    font-weight: 500 !important;
    text-transform: capitalize;
    letter-spacing: 1px !important;
    /* margin: 0 !important; */
    margin-top: 0.4rem !important;
    margin-left: 0.2rem !important;
}
.label-with-error {
    display: flex !important;
    align-items: center !important;
}

.search {
    width: 500px;
    position: relative;
    display: flex;
}

.searchTerm {
    width: 100%;
    border: 1px solid var(--input-primary-color);
    border-right: none;
    padding: 5px;
    height: 37px;
    border-radius: 5px 0 0 5px;
    outline: none;
    color: #9dbfaf;
    transition: 200ms all ease;
}

.searchTerm:focus {
    color: #000;
    border: 2px solid var(--input-primary-color);
}

.searchButton {
    width: 40px;
    height: 37px;
    border: 1px solid var(--input-primary-color);
    background: var(--input-primary-color);
    text-align: center;
    color: #fff;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
    font-size: 20px;
}

.sco_status {
    display: flex;
    padding: 10px;
}
input[type="date"]:after {
    content: attr(placeholder);
}

.table {
    display: block;
    /* height: 30vh; */
    overflow: auto;
    border-spacing: 0;
    border-bottom: 1px solid rgba(220, 219, 219, 0.9);
}

.pagination {
    display: flex;
    /* width: 100%; */
    justify-content: right;
    padding: 20px;
}

.table::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

/* Track */
.table::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
.table::-webkit-scrollbar-thumb {
    background: #ebf8f4;
    border-radius: 5px;
}

/* Handle on hover */
.table::-webkit-scrollbar-thumb:hover {
    background: #c3e9de;
}

tbody {
    cursor: pointer;
    /* border-top: 0 !important; */
}

table {
    border-collapse: collapse;
    width: 100%;
    font-family: "Public Sans", sans-serif;
}

thead th {
    /* border-bottom: 2px solid var(--color_light); */
    font-size: 14px;
    color: rgb(33, 43, 54);
    text-align: left;
    font-weight: 600;
    white-space: nowrap;
    background: #f4f6f8;
    padding: 10px;
}

td {
    border-bottom: 1px dashed #ececce;
    line-height: 1.57143;
    font-size: 0.95rem;
    text-align: left;
    white-space: wrap;
    padding: 10px;
}

tbody tr {
    cursor: pointer;
}

tbody tr.selected td {
    background: #ebf8f4;
}

tbody tr:hover:not(.selected) td,
tbody tr.hover:not(.selected) td {
    background: #eeeeee9c;
}
