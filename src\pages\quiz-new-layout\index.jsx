import QuizInterface from "@/pages/quiz-new-layout/quiz-interface";

export const questions = [
    // Single Choice MCQ
    {
        id: 1,
        type: "single-choice",
        question: "Who is the Prime Minister of India?",
        options: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
        correctAnswer: 0,
    },

    // Multi Choice MCQ
    {
        id: 2,
        type: "multi-choice",
        question: "Which of the following are planets in our solar system?",
        options: ["Earth", "Moon", "Mars", "Sun", "Jupiter"],
        correctAnswers: [0, 2, 4],
    },

    // Arrange Text
    {
        id: 3,
        type: "arrange-text",
        question: "Arrange the following events in chronological order:",
        textItems: [
            "World War I begins",
            "The Declaration of Independence is signed",
            "The Berlin Wall falls",
            "The first human lands on the moon",
        ],
        correctOrder: [1, 0, 3, 2],
    },

    // MCQ with Images as Options
    {
        id: 4,
        type: "image-choice",
        question: "Which of these is the planet Mars?",
        options: [
            {
                imageUrl: "/assets/placeholder.jpg",
                label: "Planet A",
            },
            {
                imageUrl: "/assets/placeholder.jpg",
                label: "Planet B",
            },
            {
                imageUrl: "/assets/placeholder.jpg",
                label: "Planet C",
            },
            {
                imageUrl: "/assets/placeholder.jpg",
                label: "Planet D",
            },
        ],
        correctAnswer: 2,
    },

    // Fill in the Blanks with Drag and Drop
    {
        id: 5,
        type: "fill-blanks",
        question: "Complete the sentence by dragging the correct words:",
        text: "The ___ is the largest ___ in our solar system, and it's ___ times larger than Earth.",
        blanks: ["Sun", "star", "109", "planet", "Moon", "1000"],
        correctAnswers: ["Sun", "star", "109"],
    },

    // Drag and Drop with Images and Labels
    {
        id: 6,
        type: "image-label",
        question: "Label the parts of the solar system:",
        imageUrl: "/assets/placeholder.jpg",
        labels: [
            { id: "label-1", text: "Sun" },
            { id: "label-2", text: "Earth" },
            { id: "label-3", text: "Mars" },
            { id: "label-4", text: "Jupiter" },
        ],
        dropZones: [
            { id: "dropzone-1", x: 10, y: 40, width: 15, height: 15, correctLabelId: "label-1" },
            { id: "dropzone-2", x: 40, y: 30, width: 10, height: 10, correctLabelId: "label-2" },
            { id: "dropzone-3", x: 55, y: 45, width: 10, height: 10, correctLabelId: "label-3" },
            { id: "dropzone-4", x: 75, y: 60, width: 15, height: 15, correctLabelId: "label-4" },
        ],
    },

    // Dropdown Options in Paragraph
    {
        id: 7,
        type: "dropdown-paragraph",
        question: "Complete the paragraph about the solar system:",
        paragraphSegments: [
            "The solar system consists of the ",
            " and everything that orbits around it, including ",
            " planets, dwarf planets, and countless ",
            ".",
        ],
        dropdownOptions: [
            ["Moon", "Sun", "Earth", "Milky Way"],
            ["eight", "nine", "seven", "ten"],
            ["asteroids", "stars", "galaxies", "black holes"],
        ],
        correctAnswers: [1, 0, 0],
    },
];

export default function QuizInterfacePage() {
    return (
        <main className="min-h-screen bg-slate-900 bg-[url('/stars-bg.png')] flex items-center justify-center p-4">
            <QuizInterface questions={questions} />
        </main>
    );
}
