"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

import SingleChoiceQuestion from "@/pages/quiz-new-layout/question-types/single-choice-question";
import MultiChoiceQuestion from "@/pages/quiz-new-layout/question-types/multi-choice-question";
import ArrangeTextQuestion from "@/pages/quiz-new-layout/question-types/arrange-text-question";
import ImageChoiceQuestion from "@/pages/quiz-new-layout/question-types/image-choice-question";
import FillBlanksQuestion from "@/pages/quiz-new-layout/question-types/fill-blanks-question";
import ImageLabelQuestion from "@/pages/quiz-new-layout/question-types/image-label-question";
import DropdownParagraphQuestion from "@/pages/quiz-new-layout/question-types/dropdown-paragraph-question";
import Timer from "@/pages/quiz-new-layout/timer";
import QuestionsList from "@/pages/quiz-new-layout/question-list";

export default function QuizInterface({ questions }) {
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [answeredQuestions, setAnsweredQuestions] = useState({});
    const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
    const [timeRemaining, setTimeRemaining] = useState(60); // 60 seconds per question
    const [isCorrect, setIsCorrect] = useState(null);

    const currentQuestion = questions[currentQuestionIndex];

    useEffect(() => {
        setShowCorrectAnswer(false);
        setIsCorrect(null);
    }, [currentQuestionIndex]);

    const handleAnswerSubmit = (answer, correct) => {
        setAnsweredQuestions((prev) => ({
            ...prev,
            [currentQuestion.id]: answer,
        }));
        setIsCorrect(correct);
    };

    const handleNext = () => {
        if (currentQuestionIndex < questions.length - 1) {
            setCurrentQuestionIndex(currentQuestionIndex + 1);
        }
    };

    const handlePrev = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex(currentQuestionIndex - 1);
        }
    };

    const handleReviewLater = () => {
        // Mark for review functionality would go here
        handleNext();
    };

    const handleShowAnswer = () => {
        setShowCorrectAnswer(true);
    };

    const renderQuestionComponent = () => {
        switch (currentQuestion.type) {
            case "single-choice":
                return (
                    <SingleChoiceQuestion
                        question={currentQuestion}
                        onAnswerSubmit={handleAnswerSubmit}
                        showCorrectAnswer={showCorrectAnswer}
                        savedAnswer={answeredQuestions[currentQuestion.id]}
                    />
                );
            case "multi-choice":
                return (
                    <MultiChoiceQuestion
                        question={currentQuestion}
                        onAnswerSubmit={handleAnswerSubmit}
                        showCorrectAnswer={showCorrectAnswer}
                        savedAnswer={answeredQuestions[currentQuestion.id]}
                    />
                );
            case "arrange-text":
                return (
                    <ArrangeTextQuestion
                        question={currentQuestion}
                        onAnswerSubmit={handleAnswerSubmit}
                        showCorrectAnswer={showCorrectAnswer}
                        savedAnswer={answeredQuestions[currentQuestion.id]}
                    />
                );
            case "image-choice":
                return (
                    <ImageChoiceQuestion
                        question={currentQuestion}
                        onAnswerSubmit={handleAnswerSubmit}
                        showCorrectAnswer={showCorrectAnswer}
                        savedAnswer={answeredQuestions[currentQuestion.id]}
                    />
                );
            case "fill-blanks":
                return (
                    <FillBlanksQuestion
                        question={currentQuestion}
                        onAnswerSubmit={handleAnswerSubmit}
                        showCorrectAnswer={showCorrectAnswer}
                        savedAnswer={answeredQuestions[currentQuestion.id]}
                    />
                );
            case "image-label":
                return (
                    <ImageLabelQuestion
                        question={currentQuestion}
                        onAnswerSubmit={handleAnswerSubmit}
                        showCorrectAnswer={showCorrectAnswer}
                        savedAnswer={answeredQuestions[currentQuestion.id]}
                    />
                );
            case "dropdown-paragraph":
                return (
                    <DropdownParagraphQuestion
                        question={currentQuestion}
                        onAnswerSubmit={handleAnswerSubmit}
                        showCorrectAnswer={showCorrectAnswer}
                        savedAnswer={answeredQuestions[currentQuestion.id]}
                    />
                );
            default:
                return <div>Unknown question type</div>;
        }
    };

    return (
        <div className="tw-flex tw-w-full tw-max-w-6xl tw-flex-col tw-gap-6 md:tw-flex-row">
            <div className="md:tw-w-2/3">
                <Card className="tw-border-none tw-bg-slate-800/80 tw-p-6 tw-text-white tw-shadow-lg">
                    <h2 className="tw-mb-6 tw-text-2xl tw-font-bold tw-text-orange-500">{currentQuestion.question}</h2>

                    {renderQuestionComponent()}

                    <div className="tw-mt-8 tw-flex tw-justify-between">
                        <Button
                            onClick={handlePrev}
                            variant="outline"
                            className="tw-border-none tw-bg-orange-700 tw-text-white hover:tw-bg-orange-600"
                            disabled={currentQuestionIndex === 0}
                        >
                            Prev
                        </Button>

                        <Button
                            onClick={handleReviewLater}
                            variant="outline"
                            className="tw-border-none tw-bg-slate-700 tw-text-white hover:tw-bg-slate-600"
                        >
                            REVIEW LATER
                        </Button>

                        <Button
                            onClick={handleNext}
                            variant="outline"
                            className="tw-border-none tw-bg-orange-700 tw-text-white hover:tw-bg-orange-600"
                            disabled={currentQuestionIndex === questions.length - 1}
                        >
                            Next
                        </Button>
                    </div>
                </Card>
            </div>

            <div className="tw-space-y-4 md:tw-w-1/3">
                <Timer timeRemaining={timeRemaining} setTimeRemaining={setTimeRemaining} />
                <QuestionsList
                    questions={questions}
                    currentIndex={currentQuestionIndex}
                    answeredQuestions={answeredQuestions}
                    setCurrentQuestionIndex={setCurrentQuestionIndex}
                />

                {isCorrect === false && !showCorrectAnswer && (
                    <Button
                        onClick={handleShowAnswer}
                        className="tw-mt-4 tw-w-full tw-bg-teal-700 tw-text-white hover:tw-bg-teal-600"
                    >
                        Show Correct Answer
                    </Button>
                )}
            </div>
        </div>
    );
}
