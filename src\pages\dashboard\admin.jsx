import { Label } from "@/components/ui/label";

import { tanstack<PERSON>pi } from "@/react-query/api";
import { useGetCourses } from "@/react-query/courses";
import { fetchCoursesList } from "@/redux/course/action";
import { fetchDashStatsReq } from "@/redux/dashboard/dash/action";
import { Button } from "antd";
import { Book, IdCard, PackageOpen, Route, User, UsersRound } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Link } from "react-router-dom";
import TrainerScheduleDash from "./user/trainerSchdeule";

const DashboardAdmin = () => {
    const dispatch = useDispatch();
    const [dashData, setDashData] = useState(null);
    const [dashboard, setDashboard] = useState([
        {
            name: "Total Users",
            icon: User,
            suffix: "users",
            value: 0,
            api_key: "users",
        },
        {
            name: "Total Trainers",
            icon: IdCard,
            suffix: "trainers",
            value: 0,
            api_key: "trainers",
        },
        {
            name: "Available Courses",
            icon: Book,
            suffix: "courses",
            value: 0,
            api_key: "courses",
        },
        {
            name: "Total Groups / Classes",
            icon: UsersRound,
            suffix: "groups",
            value: 0,
            api_key: "groups",
        },
        {
            name: "Learning Paths",
            icon: Route,
            suffix: "paths",
            value: 0,
            api_key: "learningPaths",
        },
        {
            name: "Course bundles",
            icon: PackageOpen,
            suffix: "bundles",
            value: 0,
            api_key: "bundles",
        },
    ]);
    const [reportsData, setReportsData] = useState([]);
    const courses = useGetCourses();
    useEffect(() => {
        if (dashData !== null) {
            let dash = dashboard?.map((dash, idx) => {
                return {
                    ...dash,
                    value: dashData[dash?.api_key],
                };
            });
            setDashboard(dash);
        }
    }, [dashData]);

    const getDashData = async (payload) => {
        await tanstackApi
            .get("dashboard")
            .then((res) => {
                setDashData(res?.data?.data);
            })
            .catch((err) => {
                setDashData(null);
            });
    };

    const getReports = async (payload) => {
        await tanstackApi
            .get("reports/get-leraner-reports-user")
            .then((res) => {
                setReportsData(res?.data?.data);
            })
            .catch((err) => {
                setReportsData([]);
            });
    };

    useEffect(() => {
        dispatch(fetchCoursesList());
        const fetchData = async () => {
            // Delay the API call by 3 seconds
            setTimeout(async () => {
                dispatch(fetchDashStatsReq());
            }, 1000);
        };
        fetchData();
    }, []);

    const level = localStorage.getItem("level");
    useEffect(() => {
        if (level === "levelThree") {
            getReports();
        } else {
            getDashData();
        }
    }, [level]);

    return (
        <div>
            <div className="tw-flex tw-flex-col tw-justify-between tw-gap-3">
                <div>
                    <h1 className="tw-font-mono tw-text-xl tw-font-semibold">Welcome back, LMS Dashboard 👋</h1>
                    <p className="tw-text-sm tw-text-slate-600">
                        There is the latest update for the of below mentioned modules.
                    </p>
                </div>
            </div>
            <div className="tw-mt-5 tw-flex tw-flex-wrap tw-gap-3">
                {dashboard?.map((card, index) => (
                    <DashKPICard key={index} card={card} index={index} />
                ))}
            </div>
            <div>
                <div className="tw-mt-10">
                    {level !== "levelThree" && courses.data?.data?.length > 0 && (
                        <UserCoursesDash list={courses.data?.data || []} />
                    )}
                    <br />
                    <TrainerScheduleDash />
                    {/* {level === "levelThree" && (
                        <section>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-[300px]">Course</TableHead>
                                        <TableHead>Start Date</TableHead>
                                        <TableHead>Completion Date</TableHead>
                                        <TableHead>Progress</TableHead>
                                        <TableHead>Certificate</TableHead>
                                        <TableHead>Certificate Issue Date</TableHead>
                                        <TableHead>Certificate Expiry Date</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {reportsData?.map((report, idx) => (
                                        <TableRow key={idx}>
                                            <TableCell>
                                                <p className="tw-line-clamp-3">
                                                    {report?.course_details?.course_title}
                                                </p>
                                            </TableCell>
                                            <TableCell>
                                                {report?.start_date !== "0000-00-00" && report?.start_date !== null
                                                    ? report?.start_date
                                                    : "-"}
                                            </TableCell>
                                            <TableCell>
                                                {report?.end_date !== "0000-00-00" && report?.end_date !== null
                                                    ? report?.end_date
                                                    : "-"}
                                            </TableCell>
                                            <TableCell>{report?.progress}%</TableCell>
                                            <TableCell>
                                                {report?.certificate !== null ? (
                                                    <Button>
                                                        <a
                                                            href={report?.certificate}
                                                            target="_blank"
                                                            download="Certificate"
                                                        >
                                                            Download
                                                        </a>
                                                    </Button>
                                                ) : (
                                                    "No Certificate"
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                {report?.certificate_issue_date !== "0000-00-00" &&
                                                report?.certificate_issue_date !== null
                                                    ? report?.certificate_issue_date
                                                    : "-"}
                                            </TableCell>
                                            <TableCell>
                                                {report?.certificate_validity !== "0000-00-00" &&
                                                report?.certificate_validity !== null
                                                    ? report?.certificate_validity
                                                    : "-"}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </section>
                    )} */}
                </div>
            </div>
        </div>
    );
};

const DashKPICard = ({ card }) => {
    const ICON = card?.icon;
    return (
        <div className="p-3 tw-w-[280px] tw-cursor-pointer tw-rounded-2xl tw-border-[1px] hover:tw-bg-teal-50">
            <div className="tw-mb-2 tw-flex tw-justify-end">
                <Label className="tw-text-slate-600">{card?.name}</Label>
            </div>
            <div className="tw-grid tw-grid-cols-[50px_auto] tw-items-center tw-gap-2">
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="p-2 tw-rounded-xl tw-bg-gray-100">
                        <ICON className="tw-text-teal-500" size={30} />
                    </div>
                </div>
                <div className="tw-flex tw-items-baseline tw-gap-2">
                    <h1 className="tw-text-3xl tw-font-bold tw-text-slate-800">{card?.value}</h1>
                    <small className="tw-text-[16px] tw-font-normal tw-capitalize tw-text-slate-500">
                        {card?.suffix}
                    </small>
                </div>
            </div>
        </div>
    );
};

const UserCoursesDash = ({ list }) => {
    return (
        <div className="tw-font-lexend">
            <div className="tw-flex tw-items-center tw-justify-between">
                <div>
                    <h1 className="tw-text-xl">All Course</h1>
                </div>
                <div>
                    <Button variant="link" asChild>
                        <Link to="/dashboard/my-courses">View All</Link>
                    </Button>
                </div>
            </div>
            <div className="tw-my-4 tw-grid tw-grid-cols-1 tw-gap-4 tw-font-lexend md:tw-grid-cols-2 lg:tw-grid-cols-4">
                {list?.slice(0, 8)?.map((course, idx) => (
                    <Link to={`/dashboard/view-course/${course.id}`} key={idx} className="tw-block">
                        <div className="tw-relative tw-aspect-video tw-w-full">
                            <img
                                className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw-rounded-lg tw-object-cover"
                                src={course?.course_banner_url ? course?.course_banner_url : "/assets/placeholder.jpg"}
                                alt=""
                            />
                        </div>
                        <div className="tw-space-y-2">
                            <h1 className="tw-pt-2 tw-font-semibold">{course?.course_title}</h1>
                            <div className="tw-space-y-1">
                                <p className="tw-text-sm tw-font-medium">
                                    {course?.lms_course_category?.category_name}
                                </p>
                                <p className="tw-text-xs">
                                    Expired in : <b>{course?.lms_course_settings[0]?.expiration_days} Days</b>
                                </p>
                            </div>
                            <p className="tw-line-clamp-3 tw-text-sm tw-text-gray-500">{course?.course_description}</p>
                        </div>
                    </Link>
                ))}
            </div>
        </div>
    );
};

export default DashboardAdmin;
