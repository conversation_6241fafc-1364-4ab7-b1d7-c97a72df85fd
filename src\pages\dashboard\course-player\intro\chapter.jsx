import { colorsList } from "@/data/colors";

export default function ChapterIntro({ details, template }) {
    return (
        <div
            className="tw-relative tw-h-full tw-rounded-xl"
            style={{
                backgroundImage: `url(${template?.background})`,
                "--primary-color": template?.elements?.color?.primary?.background,
                "--primary-text-color": template?.elements?.color?.primary?.text,
                "--secondary-color": template?.elements?.color?.secondary?.background,
                "--secondary-text-color": template?.elements?.color?.secondary?.text,
            }}
        >
            <div
                className="tw-absolute tw-inset-0 tw-z-0 tw-size-full tw-bg-cover tw-bg-no-repeat"
                style={{
                    backgroundImage: `url(${template?.ui_elements?.intro_bg})`,
                }}
            />
            <div className="tw-relative tw-z-10 tw-mx-auto tw-flex tw-h-full tw-max-w-[80%] tw-items-center tw-justify-center tw-gap-y-5 tw-py-5">
                <div className="tw-grid tw-h-full tw-grid-cols-2 tw-place-items-center tw-gap-3 tw-overflow-hidden">
                    <div className="tw-flex tw-size-full tw-items-center">
                        <div>
                            <h1
                                className="tw-font-lexend tw-leading-[1.25]"
                                style={{
                                    fontFamily: template?.font_family?.subtitle?.font_family,
                                    fontSize: template?.font_family?.subtitle?.font_size ?? "32px",
                                    fontWeight: template?.font_family?.subtitle?.font_weight ?? "700",
                                    color: template?.font_family?.subtitle?.color ?? "#333333",
                                }}
                            >
                                {details?.chapter_title}
                            </h1>
                            <p
                                className="tw-mt-1 tw-line-clamp-5 tw-w-[80%] tw-font-lexend tw-leading-[1.25]"
                                style={{
                                    fontFamily: template?.font_family?.body?.font_family,
                                    fontSize: template?.font_family?.body?.font_size ?? "20px",
                                    fontWeight: template?.font_family?.body?.font_weight ?? "400",
                                    color: template?.font_family?.body?.color ?? "#333333",
                                }}
                            >
                                {details?.chapter_discription}
                            </p>
                        </div>
                    </div>
                    <div className="tw-flex tw-flex-col tw-gap-5 tw-overflow-y-auto">
                        {details?.lms_course_chapter_contents?.map((chapter, c_idx) => (
                            <div
                                key={c_idx}
                                className="tw-grid tw-min-h-fit tw-grid-cols-4 tw-gap-x-2 tw-overflow-hidden tw-rounded-md tw-border tw-bg-background tw-font-lexend lg:tw-gap-x-4"
                            >
                                <div
                                    style={{
                                        fontFamily: template?.font_family?.title?.font_family,
                                        color: template?.font_family?.body?.color ?? "#333333",
                                        backgroundColor: colorsList[c_idx % colorsList.length],
                                    }}
                                    className="tw-flex tw-items-center tw-justify-center tw-text-xl tw-font-semibold tw-text-black lg:tw-text-2xl"
                                >
                                    {c_idx + 1 > 9 ? c_idx + 1 : `0${c_idx + 1}`}
                                </div>
                                <div
                                    style={{
                                        fontFamily: template?.font_family?.body?.font_family,
                                        fontWeight: template?.font_family?.body?.font_weight ?? "600",
                                        color: template?.font_family?.body?.color ?? "#333333",
                                    }}
                                    className="tw-col-span-3 tw-flex tw-items-center tw-py-2 tw-pr-2"
                                >
                                    <p className="tw-text-xl tw-font-bold">{chapter?.lecture_title}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}
