export const countryLanguageMapping = {
    USA: {
        languages: ["en-US"],
        currency: { code: "USD", symbol: "$" },
    },
    GBR: {
        languages: ["en-GB"],
        currency: { code: "GBP", symbol: "£" },
    },
    IND: {
        languages: ["en-IN", "hi-IN", "mr-IN", "te-IN", "ta-IN", "gu-IN", "kn-IN", "ml-IN", "or-IN", "as-IN"],
        currency: { code: "INR", symbol: "₹" },
    },
    AUS: {
        languages: ["en-AU"],
        currency: { code: "AUD", symbol: "A$" },
    },
    CAN: {
        languages: ["en-CA", "fr-CA"],
        currency: { code: "CAD", symbol: "C$" },
    },
    FRA: {
        languages: ["fr-FR"],
        currency: { code: "EUR", symbol: "€" },
    },
    ESP: {
        languages: ["es-ES"],
        currency: { code: "EUR", symbol: "€" },
    },
    MEX: {
        languages: ["es-MX"],
        currency: { code: "MXN", symbol: "$" },
    },
    DEU: {
        languages: ["de-DE"],
        currency: { code: "EUR", symbol: "€" },
    },
    ITA: {
        languages: ["it-IT"],
        currency: { code: "EUR", symbol: "€" },
    },
    PRT: {
        languages: ["pt-PT"],
        currency: { code: "EUR", symbol: "€" },
    },
    BRA: {
        languages: ["pt-BR"],
        currency: { code: "BRL", symbol: "R$" },
    },
    CHN: {
        languages: ["zh-CN"],
        currency: { code: "CNY", symbol: "¥" },
    },
    TWN: {
        languages: ["zh-TW"],
        currency: { code: "TWD", symbol: "NT$" },
    },
    JPN: {
        languages: ["ja-JP"],
        currency: { code: "JPY", symbol: "¥" },
    },
    KOR: {
        languages: ["ko-KR"],
        currency: { code: "KRW", symbol: "₩" },
    },
    RUS: {
        languages: ["ru-RU"],
        currency: { code: "RUB", symbol: "₽" },
    },
    SAU: {
        languages: ["ar-SA"],
        currency: { code: "SAR", symbol: "ر.س" },
    },
    TUR: {
        languages: ["tr-TR"],
        currency: { code: "TRY", symbol: "₺" },
    },
    VNM: {
        languages: ["vi-VN"],
        currency: { code: "VND", symbol: "₫" },
    },
    THA: {
        languages: ["th-TH"],
        currency: { code: "THB", symbol: "฿" },
    },
    IDN: {
        languages: ["id-ID", "jw-ID"],
        currency: { code: "IDR", symbol: "Rp" },
    },
    POL: {
        languages: ["pl-PL"],
        currency: { code: "PLN", symbol: "zł" },
    },
    NLD: {
        languages: ["nl-NL"],
        currency: { code: "EUR", symbol: "€" },
    },
    SWE: {
        languages: ["sv-SE"],
        currency: { code: "SEK", symbol: "kr" },
    },
    DNK: {
        languages: ["da-DK"],
        currency: { code: "DKK", symbol: "kr" },
    },
    FIN: {
        languages: ["fi-FI"],
        currency: { code: "EUR", symbol: "€" },
    },
    NOR: {
        languages: ["no-NO"],
        currency: { code: "NOK", symbol: "kr" },
    },
    HUN: {
        languages: ["hu-HU"],
        currency: { code: "HUF", symbol: "Ft" },
    },
    CZE: {
        languages: ["cs-CZ"],
        currency: { code: "CZK", symbol: "Kč" },
    },
    ROU: {
        languages: ["ro-RO"],
        currency: { code: "RON", symbol: "lei" },
    },
    SVK: {
        languages: ["sk-SK"],
        currency: { code: "EUR", symbol: "€" },
    },
    BGR: {
        languages: ["bg-BG"],
        currency: { code: "BGN", symbol: "лв" },
    },
    SVN: {
        languages: ["sl-SI"],
        currency: { code: "EUR", symbol: "€" },
    },
    EST: {
        languages: ["et-EE"],
        currency: { code: "EUR", symbol: "€" },
    },
    LVA: {
        languages: ["lv-LV"],
        currency: { code: "EUR", symbol: "€" },
    },
    LTU: {
        languages: ["lt-LT"],
        currency: { code: "EUR", symbol: "€" },
    },
    ISL: {
        languages: ["is-IS"],
        currency: { code: "ISK", symbol: "kr" },
    },
    MLT: {
        languages: ["mt-MT"],
        currency: { code: "EUR", symbol: "€" },
    },
    IRL: {
        languages: ["ga-IE"],
        currency: { code: "EUR", symbol: "€" },
    },
    ZAF: {
        languages: ["xh-ZA", "zu-ZA"],
        currency: { code: "ZAR", symbol: "R" },
    },
    KEN: {
        languages: ["sw-KE"],
        currency: { code: "KES", symbol: "KSh" },
    },
    PHL: {
        languages: ["tl-PH"],
        currency: { code: "PHP", symbol: "₱" },
    },
    NPL: {
        languages: ["ne-NP"],
        currency: { code: "NPR", symbol: "Rs" },
    },
    PAK: {
        languages: ["pa-PK"],
        currency: { code: "PKR", symbol: "₨" },
    },
    BGD: {
        languages: ["bn-BD"],
        currency: { code: "BDT", symbol: "৳" },
    },
    LKA: {
        languages: ["si-LK"],
        currency: { code: "LKR", symbol: "Rs" },
    },
    KHM: {
        languages: ["km-KH"],
        currency: { code: "KHR", symbol: "៛" },
    },
    MMR: {
        languages: ["my-MM"],
        currency: { code: "MMK", symbol: "K" },
    },
    LAO: {
        languages: ["lo-LA"],
        currency: { code: "LAK", symbol: "₭" },
    },
    MNG: {
        languages: ["mn-MN"],
        currency: { code: "MNT", symbol: "₮" },
    },
    TKM: {
        languages: ["tk-TM"],
        currency: { code: "TMT", symbol: "m" },
    },
    UZB: {
        languages: ["uz-UZ"],
        currency: { code: "UZS", symbol: "so'm" },
    },
    ARM: {
        languages: ["hy-AM"],
        currency: { code: "AMD", symbol: "֏" },
    },
    GEO: {
        languages: ["ka-GE"],
        currency: { code: "GEL", symbol: "₾" },
    },
    SRB: {
        languages: ["sr-RS"],
        currency: { code: "RSD", symbol: "дин." },
    },
    BIH: {
        languages: ["bs-BA"],
        currency: { code: "BAM", symbol: "KM" },
    },
    HRV: {
        languages: ["hr-HR"],
        currency: { code: "HRK", symbol: "kn" },
    },
    MKD: {
        languages: ["mk-MK"],
        currency: { code: "MKD", symbol: "ден." },
    },
    ALB: {
        languages: ["sq-AL"],
        currency: { code: "ALL", symbol: "L" },
    },
    ZWE: {
        languages: ["sn-ZW"],
        currency: { code: "ZWL", symbol: "Z$" },
    },
    SOM: {
        languages: ["so-SO"],
        currency: { code: "SOS", symbol: "S" },
    },
    NGA: {
        languages: ["ha-NG"],
        currency: { code: "NGN", symbol: "₦" },
    },
    ETH: {
        languages: ["am-ET"],
        currency: { code: "ETB", symbol: "Br" },
    },
};

export function findCountryCode(languageCode) {
    for (const [countryCode, data] of Object.entries(countryLanguageMapping)) {
        if (data.languages.includes(languageCode)) {
            return {
                countryCode: countryCode,
                currency: data.currency,
            }; // Return the country code and currency data if found
        }
    }
    return {
        currency: "USD",
        countryCode: {
            languages: ["en-US"],
            currency: { code: "USD", symbol: "$" },
        },
    };
}

export function getUserData() {
    const userData = {
        language: navigator.language || navigator.userLanguage, // User's language
        userAgent: navigator.userAgent, // User's browser user agent
        platform: navigator.platform, // User's platform (OS)
        onlineStatus: navigator.onLine, // Online status
    };

    return userData;
}
