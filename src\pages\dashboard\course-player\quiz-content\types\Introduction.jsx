import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useRef } from "react";

const Introduction = ({ template, AssessmentStart }) => {
    const containerRef = useRef(null);
    const bgVariants = {
        initial: { rotate: 0 },
        animate: { rotate: [360, 0] },
    };
    const quizzTemplate = template?.lms_template;

    return (
        <>
            <div className="tw-relative tw-h-full tw-w-full tw-overflow-hidden tw-rounded-xl tw-bg-cover tw-bg-center tw-bg-no-repeat !tw-font-lazyDog">
                <motion.div
                    variants={bgVariants}
                    initial="initial"
                    transition={{ duration: 10, ease: "easeInOut", repeat: Infinity }}
                    className="tw-absolute tw-inset-0 tw-z-0 tw-size-full tw-overflow-hidden tw-rounded-xl tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{
                        backgroundImage: `url("${quizzTemplate?.background}")`,
                    }}
                />
                <motion.div
                    ref={containerRef}
                    initial={{ x: 0 }}
                    animate={{
                        x: `calc(${0 * -100}% - ${0 * 2}rem)`,
                    }}
                    transition={{ duration: 1.5, ease: "easeInOut" }}
                    className="tw-relative tw-z-10 tw-flex tw-h-full tw-w-full tw-items-center tw-gap-8"
                >
                    <div
                        className={cn(
                            "tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center",
                            "tw-min-w-full tw-flex-grow",
                        )}
                    >
                        <div
                            className={cn(
                                "tw-relative tw-mb-5 tw-h-[90%] tw-w-[90%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-white/20 tw-backdrop-blur",
                            )}
                        >
                            {template?.max_points && (
                                <div
                                    className="tw-absolute tw-left-4 tw-top-4 tw-rounded-lg tw-bg-[#FFD860] tw-p-2 tw-leading-none"
                                    style={{
                                        backgroundColor: quizzTemplate?.elements?.start_button?.background_color,
                                        fontSize: quizzTemplate?.font_family?.body?.font_size ?? "28px",
                                    }}
                                >
                                    Total Score: {template?.max_points}
                                </div>
                            )}
                            {template?.quiz_duration && (
                                <div
                                    className="tw-absolute tw-right-4 tw-top-4"
                                    style={{
                                        backgroundColor: quizzTemplate?.elements?.start_button?.background_color,
                                        fontSize: quizzTemplate?.font_family?.body?.font_size ?? "28px",
                                    }}
                                >
                                    Time: {template.quiz_duration}
                                </div>
                            )}
                            <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center">
                                <div
                                    style={{
                                        fontFamily: quizzTemplate?.font_family?.body?.font_family,
                                        color: quizzTemplate?.font_family?.body?.color,
                                        backgroundColor: quizzTemplate?.font_family?.body?.background_color,
                                        lineHeight: 1,
                                        fontWeight: quizzTemplate?.font_family?.body?.font_weight,
                                        textAlign: quizzTemplate?.font_family?.body?.text_align,
                                        fontSize: quizzTemplate?.font_family?.body?.font_size ?? "52px",
                                    }}
                                    className="tw-pb-2"
                                >
                                    Quiz
                                </div>
                                <h1
                                    style={{
                                        fontFamily: quizzTemplate?.font_family?.title?.font_family,
                                        color: quizzTemplate?.font_family?.title?.color,
                                        backgroundColor: quizzTemplate?.font_family?.title?.background_color,
                                        fontWeight: quizzTemplate?.font_family?.title?.font_weight,
                                        textAlign: quizzTemplate?.font_family?.title?.text_align,
                                        fontSize: quizzTemplate?.font_family?.title?.font_size,
                                    }}
                                    className="tw-mb-3 tw-text-[52px] tw-font-bold tw-leading-[52px] tw-text-[#333333]"
                                >
                                    {template?.title}
                                </h1>
                                <p
                                    style={{
                                        fontFamily: quizzTemplate?.font_family?.body?.font_family,
                                        color: quizzTemplate?.font_family?.body?.color,
                                        backgroundColor: quizzTemplate?.font_family?.body?.background_color,
                                        fontWeight: quizzTemplate?.font_family?.body?.font_weight,
                                        textAlign: quizzTemplate?.font_family?.body?.text_align,
                                        fontSize: quizzTemplate?.font_family?.body?.font_size,
                                    }}
                                    className="tw-mt-3 tw-max-w-[70%] tw-text-center tw-text-[35px] tw-leading-[35px] tw-text-[#333333]"
                                >
                                    {template?.description?.slice(0, 125)}
                                </p>
                                <button
                                    onClick={AssessmentStart}
                                    style={{
                                        backgroundColor: quizzTemplate?.elements?.start_button?.background_color,
                                        color: quizzTemplate?.elements?.start_button?.color,
                                        borderWidth: quizzTemplate?.elements?.start_button?.border_width,
                                        borderColor: quizzTemplate?.elements?.start_button?.border_color,
                                        fontFamily: quizzTemplate?.elements?.start_button?.font_family,
                                        borderStyle: "solid",
                                    }}
                                    className="tw-mt-5 tw-aspect-video tw-h-[60px] tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-text-2xl"
                                >
                                    Start
                                </button>
                            </div>
                        </div>
                    </div>
                </motion.div>
            </div>
        </>
    );
};

export default Introduction;
