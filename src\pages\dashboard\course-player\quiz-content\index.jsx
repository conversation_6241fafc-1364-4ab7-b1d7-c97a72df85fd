import { Button } from "@/components/ui/button";
import { isUrl } from "@/lib/utils";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import SlideWrapper from "@/pages/dashboard/course-player/quiz-content/layouts/SlideWrapper";
import Introduction from "@/pages/dashboard/course-player/quiz-content/types/Introduction";
import QuizzResult from "@/pages/dashboard/course-player/quiz-content/types/result";
import QuizzSummary from "@/pages/dashboard/course-player/quiz-content/types/summary";
import { AddUserPoints } from "@/redux/dashboard/dash/action";
import { SubmitQuizData } from "@/redux/gamification/action";
import { motion, useMotionValue } from "framer-motion";
import { Fullscreen } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";

const QuizContent = ({ content, courseId, Bookmarking }) => {
    const dispatch = useDispatch();
    const ref = useRef(null);
    const [mode, setMode] = useState("instruction");
    const [status, setStatus] = useState("intro");
    const [template, setTemplate] = useState(content?.quiz?.lms_template);
    const [contentFullscreen, setContentFullscreen] = useState(false);
    const onChangeMode = (value) => setMode(value);
    const { fullscreen, currentIndex } = usePlayer();

    useEffect(() => {
        if (content.quiz) setTemplate(content.quiz);
    }, [content]);

    const [components, setComponents] = useState([]);

    useEffect(() => {
        setMode("instruction");
    }, [currentIndex]);

    useEffect(() => {
        if (template !== null && template !== undefined) {
            setComponents(
                template?.components?.map((dt, idx) => {
                    return {
                        index: idx,
                        answerKey: null,
                        points: Number(dt?.points),
                        ...dt,
                    };
                }),
            );
        }
    }, [template]);

    const AssessmentStart = (e) => {
        e.preventDefault();
        onChangeMode("components");
        setStatus("content");
    };

    const AssessmentStop = () => {
        setStatus("completed");
        onChangeMode("results");
    };

    const [slides, setSlides] = useState([]);

    const onUserReponseSave = (e, points, slides) => {
        e.preventDefault();
        AssessmentStop();
        setSlides(slides);
        let payload = {
            quiz_id: template?.id,
            course_id: courseId,
            components: components,
        };

        dispatch(SubmitQuizData(payload));

        dispatch(
            AddUserPoints({
                user_id: localStorage.getItem("userId"),
                event: "Complete quiz",
                points: points,
                entity_id: `${template?.id}`,
            }),
        );
    };

    useEffect(() => {
        if (mode == "components") toggleFullScreen();
    }, [mode]);

    const toggleFullScreen = () => {
        const elem = ref.current;
        if (elem && !fullscreen) {
            if (!document.fullscreenElement) {
                elem.requestFullscreen().catch((err) => {
                    toast.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
                });
                setContentFullscreen(!contentFullscreen);
            } else {
                document.exitFullscreen();
                setContentFullscreen(!contentFullscreen);
            }
        }
    };

    return (
        <div ref={ref} className="tw-relative tw-grid tw-size-full tw-grid-cols-1 tw-grid-rows-1">
            {mode == "instruction" && <Introduction template={template} AssessmentStart={AssessmentStart} />}
            {mode == "components" && (
                <SlideWrapper
                    template={template}
                    components={components}
                    isStarted={status}
                    onUserReponseSave={onUserReponseSave}
                />
            )}
            {mode == "results" && (
                <QuizzResult template={template} status={status} slides={slides} onChangeMode={onChangeMode} />
            )}
            {mode == "summary" && (
                <QuizzSummary template={template} status={status} slides={slides} onChangeMode={onChangeMode} />
            )}
            <Button
                onClick={toggleFullScreen}
                variant="secondary"
                size="icon"
                className="tw-absolute tw-inset-0 tw-left-0 tw-top-0 tw-z-50"
            >
                <Fullscreen />
            </Button>
            {Object.entries(template?.lms_template?.ui_elements ?? {}).map(([key, value]) => {
                if (!isUrl(value.src)) return null;
                return <UiElements key={key} constraintsRef={ref} options={value} />;
            })}
        </div>
    );
};

export default QuizContent;

function UiElements({ constraintsRef, options }) {
    const x = useMotionValue(0);
    const y = useMotionValue(0);

    useEffect(() => {
        const updatePosition = () => {
            x.set(((options.position.x ?? 0) * (constraintsRef.current?.clientWidth ?? 1)) / 100);
            y.set(((options.position.y ?? 0) * (constraintsRef.current?.clientHeight ?? 1)) / 100);
        };

        updatePosition();
        window.addEventListener("resize", updatePosition);
        return () => {
            window.removeEventListener("resize", updatePosition);
        };
    }, [constraintsRef, options.position.x, options.position.y, x, y]);

    return (
        <motion.img
            className="tw-pointer-events-none tw-absolute tw-inset-0 tw-z-50 tw-size-20"
            dragConstraints={constraintsRef}
            src={options.src}
            style={{ x, y }}
            transition={{ duration: 3, ease: "easeInOut", repeat: Infinity }}
            drag={false}
            alt="ui elements"
        />
    );
}
