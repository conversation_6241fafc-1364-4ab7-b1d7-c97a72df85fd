import Logo from "@/components/logo";
import { administrator, learner, trainer, user } from "@/components/side-bar/options";
import Topbar from "@/components/top-bar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarProvider,
} from "@/components/ui/sidebar";
import { siteConfig } from "@/lib/site.config";
import { createRouteMatcher } from "@/lib/utils";
import { useLocalStorage } from "@uidotdev/usehooks";
import { ChevronDown } from "lucide-react";
import * as React from "react";
import { useSelector } from "react-redux";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";

const stripTrailingSlash = (str) => {
    if (str.substr(-1) === "/") {
        return str.substr(0, str.length - 1);
    }
    return str;
};

export default function DashboardLayout() {
    const location = useLocation();
    const navigate = useNavigate();
    const [mapOption, setMapOption] = React.useState([]);
    const appHeight = useSelector((state) => state.AppReducer.height);
    const url = stripTrailingSlash("/dashboard");
    const role = localStorage.getItem("role_category");
    const is_trainer = localStorage.getItem("is_trainer");
    const level = localStorage.getItem("level");
    const lgnRole = localStorage.getItem("login_role");
    const tkn = localStorage.getItem("login_token");
    const [permission, setPermission] = useLocalStorage("permissions", {});

    const tknRole = atob(tkn.split(".")[1]);
    const tknObj = JSON.parse(tknRole);

    const levelTwoRoles = ["company", "university", "organization"];

    React.useEffect(() => {
        if (role === "administrator" && level === "levelOne" && lgnRole === "super-admin" && tknObj?.role === lgnRole) {
            setMapOption(administrator);
        } else if (
            role === "user" &&
            level === "levelTwo" &&
            levelTwoRoles.includes(lgnRole.toLowerCase()) &&
            tknObj?.role === lgnRole
        ) {
            setMapOption(user);
        } else if (role === "sub-user" && level === "levelThree" && is_trainer == "false" && tknObj?.role === lgnRole) {
            setMapOption(learner);
        } else if (role === "sub-user" && level === "levelThree" && is_trainer == "true" && tknObj?.role === lgnRole) {
            setMapOption(trainer);
        } else {
            window.localStorage.clear();
            navigate("/");
        }
    }, [role, level, lgnRole, tknObj, is_trainer]);

    console.log(permission.filter((item) => item.permissions.length > 0));
    console.log(permission.filter((item) => item.sub_modules.length > 0));

    return (
        <SidebarProvider
            style={{
                "--sidebar-width-icon": "5rem",
            }}
        >
            <Sidebar collapsible="icon">
                <SidebarHeader className="tw-flex tw-h-[65px] tw-items-center tw-justify-center tw-bg-slate-900">
                    <Logo />
                </SidebarHeader>
                <SidebarContent className="tw-bg-[#2d3446] tw-p-4 tw-text-slate-300">
                    <SidebarGroup>
                        <SidebarGroupContent>
                            <SidebarMenu className="tw-gap-y-3 group-data-[collapsible=icon]:tw-items-center">
                                {mapOption.map((item) => {
                                    let matcherArray = [];
                                    if (item.key !== "") {
                                        matcherArray.push(`/dashboard/${item.key}(.*)`);
                                    }
                                    const isMatcher = createRouteMatcher([matcherArray]);
                                    const isActive = location.pathname !== "dashboard" && isMatcher(location.pathname);
                                    if (item.children) {
                                        return (
                                            <Collapsible key={item.key}>
                                                <SidebarMenuButton asChild>
                                                    <CollapsibleTrigger className="tw-flex tw-items-center tw-justify-between">
                                                        <div className="tw-flex tw-items-center tw-gap-2">
                                                            {item.icon}
                                                            <span>{item.label}</span>
                                                        </div>
                                                        <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                                                    </CollapsibleTrigger>
                                                </SidebarMenuButton>
                                                <CollapsibleContent className="tw-flex tw-flex-col tw-gap-y-1">
                                                    {item?.children.map((child) => {
                                                        const linkTo = child.withoutDashboard
                                                            ? `/${child.key}`
                                                            : `${url}/${child.key}`;
                                                        return (
                                                            <SidebarMenuItem key={child.key}>
                                                                <SidebarMenuButton
                                                                    isActive={
                                                                        stripTrailingSlash(location.pathname) ==
                                                                        stripTrailingSlash(linkTo)
                                                                    }
                                                                    asChild
                                                                >
                                                                    <Link to={linkTo} className="tw-pl-8">
                                                                        {child.icon}
                                                                        <span>{child.label}</span>
                                                                    </Link>
                                                                </SidebarMenuButton>
                                                            </SidebarMenuItem>
                                                        );
                                                    })}
                                                </CollapsibleContent>
                                            </Collapsible>
                                        );
                                    }
                                    return (
                                        <SidebarMenuItem key={item.label}>
                                            <SidebarMenuButton
                                                isActive={
                                                    item.key == ""
                                                        ? stripTrailingSlash(location.pathname) ===
                                                          stripTrailingSlash(`${url}/`)
                                                        : isActive
                                                }
                                                asChild
                                            >
                                                <Link to={`${url}/${item.key}`}>
                                                    {item.icon}
                                                    <span>{item.label}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    );
                                })}
                            </SidebarMenu>
                        </SidebarGroupContent>
                    </SidebarGroup>
                </SidebarContent>
                <SidebarFooter className="tw-bg-[#2d3446] tw-p-4 tw-text-center tw-text-xs tw-text-slate-300">
                    <p className="group-data-[collapsible=icon]:tw-hidden">{siteConfig.footerText}</p>
                    <p className="tw-hidden group-data-[collapsible=icon]:tw-block">@</p>
                </SidebarFooter>
            </Sidebar>
            <main className="tw-w-full tw-font-lexend">
                <div
                    style={{ height: appHeight }}
                    className="tw-relative tw-grid tw-h-full tw-w-full tw-grid-rows-[65px_1fr]"
                >
                    <Topbar />
                    <div className="tw-flex tw-h-full tw-w-[100%] tw-flex-col tw-overflow-y-auto tw-p-3">
                        <Outlet />
                    </div>
                </div>
            </main>
        </SidebarProvider>
    );
}
