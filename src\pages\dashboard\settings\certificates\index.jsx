import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useGetCertificate } from "@/react-query/certificate";
import moment from "moment";
import { useMemo, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import PreviewFile from "./PreviewFile";
import Pagination from "@/components/table/pagination";

const ITEMS_PER_PAGE = 7;

const CertificateMaster = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const navigate = useNavigate();
    const initialTab = searchParams.get("tab") || "course-completion";
    const [certificateType, setCertificateType] = useState(initialTab);
    const [recordsPerPage, setRecordsPerPage] = useState(ITEMS_PER_PAGE);
    const [searchTerm, setSearchTerm] = useState("");
    const [appliedFilter, setAppliedFilter] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [open, setOpen] = useState(searchParams.get("type") === "create");
    const [editData, setEditData] = useState(null);

    // Get certificate data using tanstack query.
    const { data, isLoading } = useGetCertificate(certificateType);

    const allCertificates = useMemo(() => {
        const list = data?.data?.certificateList || [];
        return [...list].reverse();
    }, [data]);

    const filteredCertificates = useMemo(() => {
        if (!appliedFilter) return allCertificates;
        return allCertificates.filter((cert) => cert.title.toLowerCase().includes(appliedFilter.toLowerCase()));
    }, [allCertificates, appliedFilter]);

    const totalPages = Math.ceil(filteredCertificates.length / recordsPerPage);

    const paginatedCertificates = useMemo(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        return filteredCertificates.slice(startIndex, startIndex + recordsPerPage);
    }, [filteredCertificates, currentPage, recordsPerPage]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const handleSearch = () => {
        setAppliedFilter(searchTerm);
        setCurrentPage(1);
    };

    const handleClear = () => {
        setSearchTerm("");
        setAppliedFilter("");
        setCurrentPage(1);
    };

    const handleTabChange = (value) => {
        setCertificateType(value);
        const params = Object.fromEntries(searchParams.entries());
        setSearchParams({ ...params, tab: value });
        setSearchTerm("");
        setAppliedFilter("");
        setCurrentPage(1);
    };

    const onPreview = (data) => {
        setEditData(data);
        setOpen(true);
    };

    return (
        <div>
            <PreviewFile open={open} setOpen={setOpen} editData={editData} />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Certificates</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button
                    className="tw-px-2 tw-py-1"
                    onClick={() => navigate(`/dashboard/certificate-create/${certificateType}`)}
                >
                    <i className="fa-solid fa-plus"></i> New Certificate
                </Button>
            </div>
            <Tabs value={certificateType} onValueChange={handleTabChange}>
                <TabsList className="tw-grid tw-w-[450px] tw-grid-cols-2">
                    <TabsTrigger value="course-completion" className="tw-gap-2">
                        <i className="fa-solid fa-book"></i> Course Completion
                    </TabsTrigger>
                    <TabsTrigger value="learning-path" className="tw-gap-2">
                        <i className="fa-solid fa-signs-post"></i> Learning Path
                    </TabsTrigger>
                </TabsList>
            </Tabs>

            {/* Search Filters */}
            <div className="tw-mt-4 tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-gap-2">
                    <input
                        type="text"
                        placeholder="Search by certificate title..."
                        className="tw-rounded tw-border tw-border-gray-300 tw-px-2 tw-py-1"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button onClick={handleSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </Button>
                    <Button variant="secondary" onClick={handleClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                </div>
            </div>
            <div className="tw-h-4"></div>

            {/* Table */}
            {isLoading ? (
                <div className="tw-flex tw-h-40 tw-items-center tw-justify-center tw-rounded-md tw-border tw-text-center">
                    Loading...
                </div>
            ) : paginatedCertificates.length > 0 ? (
                <div className="custom_table tw-mt-4">
                    <table>
                        <thead>
                            <tr>
                                <th>Certificate Title</th>
                                <th>Type</th>
                                <th>Expiration Date</th>
                                <th>Created On</th>
                                <th>Preview</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody className="tw-font-lexend">
                            {paginatedCertificates.map((row, idx) => (
                                <tr key={idx}>
                                    <td>{row?.title}</td>
                                    <td>
                                        {row?.is_system === 1 ? (
                                            <Badge>System</Badge>
                                        ) : (
                                            <Badge variant="outline">Normal</Badge>
                                        )}
                                    </td>
                                    <td>{moment(row?.preview_strings?.expiry_date).format("LL")}</td>
                                    <td>{moment(row?.createdAt).format("LL")}</td>
                                    <td>
                                        <button className="selected_btn" onClick={() => onPreview(row)}>
                                            <i className="fa-solid fa-eye"></i>
                                        </button>
                                    </td>
                                    <td>
                                        {localStorage.getItem("level") === "levelTwo" && row?.is_system === 0 && (
                                            <button
                                                className="selected_btn"
                                                onClick={() =>
                                                    navigate(
                                                        `/dashboard/certificate-create/${certificateType}/${row?.id}`,
                                                    )
                                                }
                                            >
                                                <i className="fa-regular fa-edit"></i> Edit
                                            </button>
                                        )}
                                        {localStorage.getItem("level") === "levelOne" && row?.is_system === 1 && (
                                            <button
                                                className="selected_btn"
                                                onClick={() =>
                                                    navigate(
                                                        `/dashboard/certificate-create/${certificateType}/${row?.id}`,
                                                    )
                                                }
                                            >
                                                <i className="fa-regular fa-edit"></i> Edit
                                            </button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            ) : (
                <div className="tw-flex tw-h-40 tw-items-center tw-justify-center tw-rounded-md tw-border tw-text-center tw-text-slate-500">
                    No certificates found.
                </div>
            )}

            <div>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                    itemsPerPage={ITEMS_PER_PAGE}
                />
            </div>
        </div>
    );
};

export default CertificateMaster;
