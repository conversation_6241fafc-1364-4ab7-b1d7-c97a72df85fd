import { isUrl } from "@/lib/utils";
import { useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import Draggable from "react-draggable";
import ContentSlideLayout from "../layouts/ContentSlideLayout";

const HotspotDNDImage = ({
    handleNextSlide,
    handlePrevSlide,
    className,
    data,
    sequence,
    onComponentAnswer,
    template,
}) => {
    const containerRef = useRef(null);
    const [containerSize, setContainerSize] = useState({ width: 600, height: 350 });
    const [items, setItems] = useState([]);
    const [zones, setZones] = useState([]);
    const inView = useInView(containerRef);
    const [answer, setAnswer] = useState([]);

    useEffect(() => {
        if (data) {
            setItems(data?.hotspots);
            setZones(data?.hotspotDropzone);
        }
    }, [data]);

    const updateContainerSize = () => {
        if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            setContainerSize({ width: rect.width, height: rect.height });
        }
    };

    useEffect(() => {
        setTimeout(() => {
            updateContainerSize();
        }, 1000);
    }, [inView]);

    useEffect(() => {
        window.addEventListener("resize", updateContainerSize);
        return () => {
            window.removeEventListener("resize", updateContainerSize);
        };
    }, []);

    useEffect(() => {
        let ans = answer.filter((dt) => dt.isCorrect);
        let ppq = data?.points / data?.hotspots?.length;
        let points = Math.round(ans?.length * ppq);
        onComponentAnswer(sequence, answer, points);
    }, [items, answer]);

    const getAbsolutePosition = (relativeX, relativeY) => {
        const rect = containerRef.current.getBoundingClientRect();
        return {
            x: relativeX * rect.width,
            y: relativeY * rect.height,
        };
    };

    const isWithinZone = (itemPosition, zone) => {
        const tolerance = 50;
        const isXWithin = Math.abs(itemPosition.x - zone.x) <= tolerance;
        const isYWithin = Math.abs(itemPosition.y - zone.y) <= tolerance;
        return isXWithin && isYWithin;
    };

    const handleStop = (e, data, index) => {
        const updatedItems = [...items];
        updatedItems[index].x = data.x / containerSize.width;
        updatedItems[index].y = data.y / containerSize.height;

        const itemAbsolutePos = { x: data.x, y: data.y };

        const nearestZone = zones
            .map((zone) => ({
                ...zone,
                ...getAbsolutePosition(zone.x, zone.y),
            }))
            .find((zone) => isWithinZone(itemAbsolutePos, zone));

        if (nearestZone) {
            const isCorrect = updatedItems[index].correct === nearestZone.zone;
            const exist = answer.find((item) => item.name === updatedItems[index].name);

            if (exist) {
                const oldItems = [...answer];
                const findIndex = answer.findIndex((item) => item.name === updatedItems[index].name);
                oldItems[findIndex].isCorrect = isCorrect;
                oldItems[findIndex].zone = nearestZone.zone;
                oldItems[findIndex].name = updatedItems[index].name;
                setAnswer(oldItems);
            } else {
                setAnswer([...answer, { name: updatedItems[index].name, zone: nearestZone.zone, isCorrect }]);
            }
        } else {
            const removeItemsList = answer.filter((item) => item.name === updatedItems[index].name);
            setAnswer(removeItemsList);
        }

        setItems(updatedItems);
    };

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data: data?.name ?? "Complete the gaps using the appropriate words from the box.",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div
                ref={containerRef}
                className="tw-relative tw-grid tw-w-[650px] tw-grid-cols-1 tw-grid-rows-1 tw-bg-cover tw-bg-center tw-bg-no-repeat"
                style={{
                    aspectRatio: "12 / 7",
                    backgroundImage: `url(${data?.question_thumbnail})`,
                }}
            >
                {zones?.map((zone, index) => {
                    const { x, y } = getAbsolutePosition(zone.x, zone.y);
                    return (
                        <div
                            key={index}
                            className="tw-line-height-[50px] tw-absolute tw-flex tw-size-[60px] tw-items-center tw-justify-center tw-rounded-md tw-border-2 tw-border-dashed tw-border-[#000] tw-bg-[#f0f0f0] tw-text-center"
                            style={{
                                left: x,
                                top: y,
                                width: zone.width > 0 && zone.width ? zone.width : 50,
                                height: zone.height > 0 && zone.height ? zone.height : 50,
                            }}
                        >
                            <div className="tw-absolute tw-z-0 tw-size-full tw-p-1">
                                <img src="/assets/hotspot_dnd_media.png" className="tw-size-full tw-opacity-50" />
                            </div>
                            {isUrl(zone?.src) ? (
                                <img
                                    src={zone?.src}
                                    alt=""
                                    className="tw-absolute tw-inset-0 tw-size-full tw-select-none tw-object-contain"
                                />
                            ) : (
                                <p
                                    style={{
                                        color: data?.styles?.answer?.color,
                                        fontFamily: data?.styles?.answer?.fontFamily,
                                        fontSize: data?.styles?.answer?.fontSize,
                                    }}
                                    className="tw-text-xs tw-font-medium"
                                >
                                    {zone.zone}
                                </p>
                            )}
                        </div>
                    );
                })}

                {items?.map((item, index) => {
                    const { x, y } = getAbsolutePosition(item.x, item.y);
                    return (
                        <Draggable key={index} position={{ x, y }} onStop={(e, data) => handleStop(e, data, index)}>
                            <div className="tw-absolute tw-flex tw-h-[60px] tw-w-[70px] tw-cursor-pointer tw-flex-col tw-items-center tw-justify-center tw-rounded-xl tw-bg-[#add8e6] tw-p-1">
                                {item.src && item.src !== "" && (
                                    <img
                                        className="tw-pointer-events-none tw-aspect-square tw-h-[40%] tw-w-[40%] tw-select-none tw-object-contain"
                                        src={item.src}
                                        alt={item.name}
                                    />
                                )}
                                <p
                                    style={{
                                        color: data?.styles?.answer?.color,
                                        fontFamily: data?.styles?.answer?.fontFamily,
                                        fontSize: data?.styles?.answer?.fontSize,
                                    }}
                                >
                                    {item.name}
                                </p>
                            </div>
                        </Draggable>
                    );
                })}
            </div>
        </ContentSlideLayout>
    );
};

export default HotspotDNDImage;
