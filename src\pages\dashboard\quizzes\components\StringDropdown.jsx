import { cn, isUrl } from "@/lib/utils";
import QuizContainer from "@/pages/dashboard/quizzes/components/container";
import { motion, useInView } from "framer-motion";
import { useRef, useState } from "react";

const StringDropdown = ({ ...props }) => {
    const { content } = props;
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { scale: 0.5 },
        inView: { scale: 1 },
    };
    const [tempSelectedValues, setTempSelectedValues] = useState(Array(content?.dropdown_options?.length).fill(""));

    const handleTempChange = (index, event) => {
        const newTempValues = [...tempSelectedValues];
        newTempValues[index] = event.target.value;
        setTempSelectedValues(newTempValues);
    };

    const renderTemplate = () => {
        const parts = content?.question?.split(/{(\d+)}/g);
        return parts?.map((part, index) => {
            if (index % 2 === 1) {
                const dropdownIndex = parseInt(part, 10);
                const dt = content?.dropdown_options[dropdownIndex];
                const randomString = Math.random().toString(36).substring(2, 15);
                const isSelected = tempSelectedValues[dropdownIndex] == "" ? "default" : "selected";
                return (
                    <motion.label
                        key={index}
                        variants={variants}
                        initial="initial"
                        whileInView={inView ? "inView" : "initial"}
                        transition={{
                            duration: 0.5,
                        }}
                        htmlFor={`option-${randomString}`}
                        className="tw-inline-flex tw-rounded-md tw-border tw-border-gray-500 tw-p-1 tw-transition-all"
                        style={{
                            color: content?.styles?.answer?.color,
                            fontFamily: content?.styles?.answer?.fontFamily,
                            fontSize: content?.styles?.answer?.fontSize,
                            lineHeight: 1,
                            backgroundColor: content?.styles?.[isSelected]?.backgroundColor,
                            borderWidth: content?.styles?.[isSelected]?.borderWidth,
                            borderColor: content?.styles?.[isSelected]?.borderColor,
                            borderStyle: content?.styles?.[isSelected]?.borderStyle,
                        }}
                    >
                        <select
                            key={index}
                            value={tempSelectedValues[dropdownIndex]}
                            onChange={(event) => handleTempChange(dropdownIndex, event)}
                            id={`option-${randomString}`}
                            className="tw-w-full tw-bg-transparent tw-p-0 tw-outline-none"
                        >
                            <option value="">- Select -</option>
                            {dt?.options?.map((option, idx) => (
                                <option key={idx} value={option}>
                                    {option}
                                </option>
                            ))}
                        </select>
                    </motion.label>
                );
            } else {
                return part;
            }
        });
    };

    return (
        <QuizContainer {...props} type="string_dropdown" empty={content?.dropdown_options?.length === 0}>
            <div className="tw-px-10 tw-py-7">
                <div className="tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden">
                    <div
                        className={cn(
                            "tw-grid tw-h-1/2 tw-w-full tw-flex-wrap tw-gap-[1rem]",
                            isUrl(content?.question_thumbnail) ? "tw-grid-cols-2" : "tw-grid-cols-1",
                        )}
                        ref={ref}
                    >
                        {isUrl(content?.question_thumbnail) && (
                            <div className="tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
                                <div className="tw-relative tw-mt-7 tw-h-[300px] tw-w-[420px]">
                                    <img
                                        src={content?.question_thumbnail}
                                        alt=""
                                        className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw--rotate-3 tw-rounded-xl tw-object-cover"
                                    />
                                    <motion.img
                                        initial={{ scale: 1, rotate: 1 }}
                                        animate={{
                                            scale: 1.1,
                                        }}
                                        transition={{
                                            duration: 1,
                                            repeat: Infinity,
                                            repeatType: "reverse",
                                        }}
                                        src={"/quiz/spark, sparkle, 26.png"}
                                        alt=""
                                        className="tw-absolute tw-right-[-50px] tw-top-[-110px] tw-z-0 tw-aspect-[1/1.25] tw-w-[150px]"
                                    />
                                    <motion.img
                                        initial={{ rotate: 10 }}
                                        animate={{
                                            rotate: -10,
                                            transition: {
                                                duration: 2,
                                                repeat: Infinity,
                                                repeatType: "reverse",
                                            },
                                        }}
                                        src="/quiz/Question Mark.png"
                                        alt=""
                                        className="tw-absolute tw-bottom-[-3%] tw-right-[-5%] tw-z-0 tw-aspect-[1/1.25] tw-w-[80px]"
                                    />
                                </div>
                            </div>
                        )}
                        <motion.div className="tw-inline-flex tw-items-center tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[37px] tw-leading-[60px] tw-text-black">
                            <p
                                className="tw-font-mono"
                                style={{
                                    backgroundColor: content?.styles?.question?.backgroundColor,
                                    color: content?.styles?.question?.color,
                                    fontFamily: content?.styles?.question?.fontFamily,
                                    fontSize: content?.styles?.question?.fontSize,
                                    lineHeight: 1.5,
                                }}
                            >
                                {content !== null && renderTemplate()}
                            </p>
                        </motion.div>
                    </div>
                </div>
            </div>
        </QuizContainer>
    );
};

export default StringDropdown;
