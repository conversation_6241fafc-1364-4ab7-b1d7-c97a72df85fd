import { colorsList } from "@/data/colors";
import { cn, isUrl } from "@/lib/utils";
import { motion, useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { ReactSortable } from "react-sortablejs";
import ContentSlideLayout from "../layouts/ContentSlideLayout";

const SequenceArrange = ({
    handleNextSlide,
    handlePrevSlide,
    className,
    data,
    sequence,
    onComponentAnswer,
    template,
}) => {
    const newList = data?.matchOptions?.map((dt, idx) => ({ ...dt, id: idx }));
    const [state, setState] = useState(newList);

    useEffect(() => {
        const ans = state.filter((dt, idx) => dt?.correctIndex == idx + 1);
        let ppq = data?.points / data?.matchOptions?.length;
        let points = Math.round(ans?.length * ppq);

        onComponentAnswer(sequence, state, points);
    }, [state]);

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div className="tw-relative tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center tw-overflow-hidden">
                <div
                    style={{
                        color: data?.styles?.question?.color,
                        fontFamily: data?.styles?.question?.fontFamily,
                        fontSize: data?.styles?.question?.fontSize,
                    }}
                    className="tw-mb-5 tw-flex tw-items-start tw-gap-4 tw-text-[40px] tw-leading-[40px]"
                >
                    <p>{sequence}. </p>
                    <p>{data?.name}</p>
                </div>
                <ListItem content={data} items={state} setItems={setState} />
            </div>
        </ContentSlideLayout>
    );
};

export default SequenceArrange;

function ListItem({ content, items, setItems }) {
    const ref = useRef(null);
    const inView = useInView(ref);
    const variants = {
        initial: { x: "-50%", scaleX: 0.5 },
        inView: { x: 0, scaleX: 1 },
    };

    return (
        <div ref={ref} className="tw-w-full">
            <div className={cn("tw-grid", isUrl(content?.question_thumbnail) ? "tw-grid-cols-2" : "tw-grid-cols-1")}>
                {isUrl(content?.question_thumbnail) && (
                    <div className="tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center">
                        <div className="tw-relative tw-mt-7 tw-h-[300px] tw-w-[420px]">
                            <img
                                src={content?.question_thumbnail}
                                alt=""
                                className="tw-absolute tw-inset-0 tw-h-full tw-w-full tw--rotate-3 tw-rounded-xl tw-object-cover"
                            />
                            <motion.img
                                initial={{ scale: 1, rotate: 1 }}
                                animate={{
                                    scale: 1.1,
                                }}
                                transition={{
                                    duration: 1,
                                    repeat: Infinity,
                                    repeatType: "reverse",
                                }}
                                src={"/quiz/spark, sparkle, 26.png"}
                                alt=""
                                className="tw-absolute tw-right-[-50px] tw-top-[-110px] tw-z-0 tw-aspect-[1/1.25] tw-w-[150px]"
                            />
                            <motion.img
                                initial={{ rotate: 10 }}
                                animate={{
                                    rotate: -10,
                                    transition: {
                                        duration: 2,
                                        repeat: Infinity,
                                        repeatType: "reverse",
                                    },
                                }}
                                src="/quiz/Question Mark.png"
                                alt=""
                                className="tw-absolute tw-bottom-[-3%] tw-right-[-5%] tw-z-0 tw-aspect-[1/1.25] tw-w-[80px]"
                            />
                        </div>
                    </div>
                )}
                <div className="tw-grid tw-w-full tw-grid-cols-1 tw-gap-[1rem]">
                    <ReactSortable list={items} setList={setItems} className="tw-flex tw-flex-col tw-gap-4">
                        {items.map((card, index) => {
                            return (
                                <motion.div
                                    variants={variants}
                                    initial="initial"
                                    whileInView={inView ? "inView" : "initial"}
                                    transition={{
                                        duration: 0.5,
                                        delay: index * 0.2,
                                    }}
                                    key={card.id}
                                    className="tw-w-full tw-cursor-grab tw-rounded-xl tw-p-1"
                                    style={{
                                        backgroundColor: colorsList[card.id || 0],
                                        color: content?.styles?.answer?.color,
                                        fontFamily: content?.styles?.answer?.fontFamily,
                                        fontSize: content?.styles?.answer?.fontSize,
                                        borderWidth: content?.styles?.answer?.borderWidth,
                                        borderColor: content?.styles?.answer?.borderColor,
                                        borderStyle: content?.styles?.answer?.borderStyle,
                                    }}
                                >
                                    <div id={card.id} className="tw-flex tw-items-center tw-gap-2 tw-p-4 tw-text-3xl">
                                        {card?.imageText ? (
                                            <img
                                                src={card?.imageText}
                                                className="tw-aspect-[1/1] tw-w-[50px] tw-rounded-[0.5rem] tw-bg-white tw-object-cover"
                                            />
                                        ) : (
                                            <p className="tw-pl-3">{index + 1}. </p>
                                        )}
                                        <p>{card.text}</p>
                                    </div>
                                </motion.div>
                            );
                        })}
                    </ReactSortable>
                </div>
            </div>
        </div>
    );
}
