import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { tanstackApi } from "@/react-query/api";
import { useGetUserCourse } from "@/react-query/courses";
import { useGetScheduledChapters } from "@/react-query/users/schedule-chapter";
import Pagination from "@mui/material/Pagination";
import { DateCalendar, PickersDay } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs from "dayjs";
import { Clock, MapPinHouse, Timer, Video } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const UserScheduleDash = () => {
    const navigate = useNavigate();
    const courses = useGetUserCourse();
    const assignCourses = courses.data?.data.map((course) => course.id) ?? [];
    const [recordsCountOnTable, setRecordsCountOnTable] = useState(3);
    const [pageCount, setPageCount] = useState(0);
    const [selectedChapter, setSelectedChapter] = useState(null);
    const [tableData, setTableData] = useState([]);
    const [selectedDate, setSelectedDate] = useState(null);
    const [highlightDays, setHighlightDays] = useState([]);
    const [allChapters, setAllChapters] = useState([]);
    const [editData, setEditData] = useState(null);
    const [openAlert, setOpenAlert] = useState(false);
    const [startDate, setStartDate] = useState(dayjs().startOf("month").format("YYYY-MM-DD"));
    const [endDate, setEndDate] = useState(dayjs().endOf("month").format("YYYY-MM-DD"));
    const scheduleChapter = useGetScheduledChapters({ params: { startDate, endDate } });

    useEffect(() => {
        if (scheduleChapter.isSuccess) {
            if (!scheduleChapter.data.data || scheduleChapter.data?.data.length === 0 || assignCourses.length === 0) {
                setHighlightDays([]);
                setPageCount(0);
                setTableData([]);
                return;
            }
            const allChapters = scheduleChapter.data.data;
            const modulas = allChapters.length % recordsCountOnTable;
            const count = (allChapters.length - modulas) / recordsCountOnTable;
            setPageCount(modulas !== 0 ? count + 1 : count);
            const data = allChapters?.slice(0, recordsCountOnTable * 1);
            setTableData(data);
            setAllChapters(allChapters);
            setHighlightDays([...new Set(allChapters?.map((t) => moment(t.schedule_date).format("YYYY-MM-DD")))]);
        }
    }, [scheduleChapter.status, startDate, endDate]);

    const handleDateChange = (date) => {
        date = date.format("YYYY-MM-DD");
        setSelectedDate(date ? date : "");
        const options = allChapters?.filter((dt) => moment(dt.schedule_date).format("YYYY-MM-DD") == date);
        const modulas = options?.length % recordsCountOnTable;
        const count = (options?.length - modulas) / recordsCountOnTable;
        setPageCount(modulas !== 0 ? count + 1 : count);
        setTableData(options?.slice(0, recordsCountOnTable * 1));
    };

    const handleMonthChange = (data) => {
        setStartDate(data.startOf("month").format("YYYY-MM-DD"));
        setEndDate(data.endOf("month").format("YYYY-MM-DD"));
        setSelectedDate(null);
    };

    const onPageChange = (e, number) => {
        const data = [...allChapters].slice(recordsCountOnTable * (number - 1), recordsCountOnTable * number);
        setTableData(data);
    };

    const onRedirectToCourse = (chapter) => {
        const courseId = chapter?.course_id;
        const chapterId = chapter?.chapter_id;
        const isScorm = chapter?.course?.is_scorm;

        if (localStorage.getItem("level") === "levelTwo") {
            navigate(`/dashboard/course-details/${courseId}`);
        } else {
            navigate(
                isScorm
                    ? `/dashboard/course-details/play-scorm/${courseId}`
                    : `/course-details/view/${courseId}/${chapterId}/0`,
            );
        }
    };

    const onJoinZoomClass = (chapter) => {
        setSelectedChapter(chapter);
    };

    const onJoinClass = async () => {
        await tanstackApi.get(`class-attendance/mark-my-attendance/${editData?.id}`);
        punchTimelineLog({
            user_id: localStorage.getItem("parent_user_id"),
            event: "sessions",
            log: `${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} attended ${editData?.topic} successfully.`,
        });
        window.open(editData?.location, "_blank");
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    function renderSelectedChapter() {
        return (
            <div>
                <div className="tw-flex tw-justify-between">
                    <h1 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                        #. {selectedChapter?.chapter?.title}
                    </h1>
                    <Button onClick={() => setSelectedChapter(null)} variant="outline">
                        Back
                    </Button>
                </div>
                <div className="tw-grid tw-grid-cols-3 tw-items-start tw-gap-3">
                    {selectedChapter?.chapter?.classes?.map((group, index) => (
                        <ClassCard key={index} group={group} setEditData={setEditData} setOpenAlert={setOpenAlert} />
                    ))}
                </div>
            </div>
        );
    }

    function renderScheduledChapters() {
        return (
            <div className="scheduled_chapters">
                {tableData.map((chapter, idx) => (
                    <ChapterCard
                        key={idx}
                        chapter={chapter}
                        onJoinZoomClass={onJoinZoomClass}
                        onRedirectToCourse={onRedirectToCourse}
                    />
                ))}
            </div>
        );
    }

    return (
        <div className="schedule_view">
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you ready to join the class?</AlertDialogTitle>
                        <AlertDialogDescription>Click on join button to continue. Thank you!</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onJoinClass}>Yes Join!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="schedule_header">
                <img src="/assets/robo-2.png" alt="" />
                <div>
                    <h1>Time Table</h1>
                    <p>Explore your scheduled classes and chapters.</p>
                </div>
            </div>
            <div className="main_details">
                <div className="calendar_info">
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DateCalendar
                            date={selectedDate ? dayjs(selectedDate) : null}
                            slots={{
                                day: (props) => <HighlightDays {...props} highlightedDays={highlightDays} />,
                            }}
                            onChange={handleDateChange}
                            onMonthChange={handleMonthChange}
                        />
                    </LocalizationProvider>
                </div>
                {selectedChapter !== null ? renderSelectedChapter() : renderScheduledChapters()}
            </div>
            <div className="schedule_bottom">
                <Pagination count={pageCount} onChange={onPageChange} variant="outlined" shape="rounded" />
            </div>
        </div>
    );
};

const ClassCard = ({ group, setEditData, setOpenAlert }) => (
    <div className="tw-rounded-xl tw-border-[1px] tw-p-1">
        <div className="tw-p-2">
            <div className="tw-flex tw-items-center tw-gap-2">
                <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2">{group?.topic}</h2>
            </div>
            <p className="tw-mt-2 tw-line-clamp-3">{group?.chapter_discription}</p>
            <ClassDetails group={group} setEditData={setEditData} setOpenAlert={setOpenAlert} />
        </div>
    </div>
);

const ClassDetails = ({ group, setEditData, setOpenAlert }) => (
    <div className="tw-mt-1 tw-space-y-2">
        <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
            {group?.meet_type === "zoom" ? (
                <Badge className={"tw-flex tw-items-center tw-gap-2"} variant={""}>
                    <Video size={16} /> Zoom Meet
                </Badge>
            ) : group?.meet_type === "google-meet" ? (
                <Badge className={"tw-flex tw-items-center tw-gap-2"} variant={""}>
                    <Video size={16} /> Google Meet
                </Badge>
            ) : (
                <Badge className={"tw-flex tw-items-center tw-gap-2"} variant={"outline"}>
                    <MapPinHouse size={16} /> Offline
                </Badge>
            )}
        </span>
        <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
            <Calendar size={16} /> {moment(group?.date).format("LL")}
        </span>
        <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
            <Clock size={16} />{" "}
            {`${moment(group?.start_time, "HH:mm:ss").format("h:mm A")} - ${moment(group?.end_time, "HH:mm:ss").format("h:mm A")}`}
        </span>
        <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
            <Timer size={16} /> {group?.duration} minutes
        </span>
        <JoinClassButton group={group} setEditData={setEditData} setOpenAlert={setOpenAlert} />
    </div>
);

const JoinClassButton = ({ group, setEditData, setOpenAlert }) => (
    <div className="tw-mt-3 tw-grid tw-grid-cols-1 tw-gap-2">
        {group?.type === "online" && (
            <a
                onClick={() => {
                    setEditData(group);
                    setOpenAlert(true);
                }}
                className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
            >
                <Video size={16} strokeWidth={2} aria-hidden="true" />
                Join Class
            </a>
        )}
    </div>
);

const ChapterCard = ({ chapter, onJoinZoomClass, onRedirectToCourse }) => (
    <div className="chapter_card">
        <div className="card_head">
            <h1>{chapter?.chapter?.title}</h1>
            <div className="tw-flex tw-gap-2">
                <button onClick={() => onRedirectToCourse(chapter)}>See Chapter</button>
            </div>
        </div>
        <div className="card_details">
            <p>
                Timing: <b>{moment(chapter?.schedule_date).format("hh:mm:A")}</b>
            </p>
            <p>
                Start Date: <b>{moment(chapter?.schedule_date).format("LL")}</b>
            </p>
        </div>
        <div className="tw-mt-2 tw-flex tw-items-center tw-justify-between">
            <Badge variant={"outline"} className="tw-flex tw-gap-2">
                <i className="fa-solid fa-book"></i> {chapter?.course?.title}
            </Badge>
            {chapter?.chapter?.classes?.length > 0 && (
                <Button className="tw-rounded-xl" variant={"outline"} onClick={() => onJoinZoomClass(chapter)}>
                    <i className="fa-solid fa-video"></i> View Classes
                </Button>
            )}
        </div>
    </div>
);

function HighlightDays(props) {
    const { highlightedDays = [], day, outsideCurrentMonth, ...other } = props;
    const formattedDay = day.format("YYYY-MM-DD");
    const isSelected = !outsideCurrentMonth && highlightedDays.indexOf(formattedDay) >= 0;
    return (
        <div key={day} style={{ position: "relative" }}>
            <PickersDay {...other} outsideCurrentMonth={outsideCurrentMonth} day={day} />
            {isSelected && (
                <div
                    style={{
                        position: "absolute",
                        top: "75%",
                        right: "30%",
                        backgroundColor: "rgb(0, 185, 174)",
                        color: "white",
                        borderRadius: "2rem",
                        width: "15px",
                        height: "2px",
                    }}
                />
            )}
        </div>
    );
}

export default UserScheduleDash;
