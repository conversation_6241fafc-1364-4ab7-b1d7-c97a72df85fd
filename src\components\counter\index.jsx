import { useEffect, useRef } from "react";

export default function Counter(props) {
    const { value, direction = "up" } = props;
    const ref = useRef(null);

    useEffect(() => {
        let start = direction === "down" ? value : 0;
        const end = direction === "down" ? 0 : value;
        const duration = 2000; // animation duration in milliseconds
        const frameRate = 60; // frames per second
        const totalFrames = Math.round((duration / 1000) * frameRate);
        let frame = 0;

        const counter = setInterval(() => {
            frame++;
            const progress = frame / totalFrames;
            const current = start + (end - start) * progress;

            if (ref.current) {
                ref.current.textContent = Intl.NumberFormat("en-US").format(Math.round(current));
            }

            if (frame === totalFrames) clearInterval(counter);
        }, 1000 / frameRate);

        return () => clearInterval(counter); // cleanup on component unmount
    }, [value, direction]);

    return <span ref={ref} />;
}
