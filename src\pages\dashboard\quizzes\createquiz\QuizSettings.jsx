import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";

const QuizSettings = ({ onChangeHandle, quizData, setQuizData, trainersList }) => {
    const params = useParams();
    const loginToken = localStorage.getItem("login_token");
    const AnswerTypes = ["show-only-right-wrong", "show-correct-answer"];

    const [courseList, setCourseList] = useState([]);
    const [chapterList, setChapterList] = useState([]);

    useEffect(() => {
        getCourses();
    }, []);

    useEffect(() => {
        if (quizData?.course_id) {
            getChapters({ course_id: quizData?.course_id });
        }
    }, [quizData?.course_id]);

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getChapters = async (payload) => {
        await tanstackApi
            .post("course/view-course", { ...payload })
            .then((res) => {
                setChapterList(res?.data?.data?.courseChapters);
            })
            .catch((err) => {
                setChapterList([]);
            });
    };

    return (
        <Card>
            <CardHeader>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-space-y-2">
                        <CardTitle>Quiz settings</CardTitle>
                        <CardDescription>
                            Define quiz schedule date, duration & marks here. bind it with some course or chapter &
                            assign it to any trainer. Click save when you&apos;re done.
                        </CardDescription>
                    </div>
                    <div>
                        {params?.quiz_id !== undefined && (
                            <Link
                                to={`https://lms-course-builder.vercel.app/dashboard/quiz-config/${params?.quiz_id}?token=${loginToken}`}
                                target="_blank"
                            >
                                <Button variant="outline" className="tw-rounded-xl">
                                    <i className="fa-solid fa-plus"></i> Add Questions
                                </Button>
                            </Link>
                        )}
                    </div>
                </div>
            </CardHeader>
            <CardContent className="tw-grid tw-grid-cols-2 tw-gap-10 tw-space-y-2">
                <div className="tw-w-full tw-space-y-5">
                    <div className="tw-grid tw-grid-cols-3 tw-gap-2">
                        <div className="tw-space-y-1">
                            <Label htmlFor="schedule_date">Schedule On</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.schedule_date}
                                name="schedule_date"
                                id="schedule_date"
                                type="date"
                                min={new Date().toISOString().split("T")[0]} // Validation to not accept less than today
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="start_time">Start Time</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.start_time}
                                name="start_time"
                                id="start_time"
                                type="time"
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="end_time">End Time</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.end_time}
                                name="end_time"
                                id="end_time"
                                type="time"
                            />
                        </div>
                    </div>
                    <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                        <div className="tw-space-y-1">
                            <Label htmlFor="max_points">Max Points</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.max_points}
                                name="max_points"
                                id="max_points"
                                type="number"
                                placeholder="Define max points"
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="passing_marks">Passing Marks</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.passing_marks}
                                name="passing_marks"
                                id="passing_marks"
                                type="number"
                                placeholder="Define passing marks"
                            />
                        </div>
                    </div>
                    <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                        <div className="tw-space-y-1">
                            <Label htmlFor="attempts_allowed">Attempts allowed for quiz</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.attempts_allowed}
                                name="attempts_allowed"
                                id="attempts_allowed"
                                type="number"
                                placeholder="Enter allowed attempts for quiz"
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="attempts_per_question">Attempts per Question</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.attempts_per_question}
                                name="attempts_per_question"
                                id="attempts_per_question"
                                type="number"
                                placeholder="Enter allowed attempts for question"
                            />
                        </div>
                    </div>
                    <div className="tw-grid tw-grid-cols-1 tw-items-end tw-gap-10">
                        <div className="tw-space-y-3">
                            <h1 className="tw-text-xl tw-font-semibold">Show Answer Type</h1>
                            <div className="tw-flex-col-2 tw-mt-3 tw-flex tw-gap-10">
                                <RadioGroup
                                    value={quizData?.show_answer_type}
                                    onValueChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "show_answer_type" } })
                                    }
                                    className="tw-flex"
                                >
                                    <div className="tw-ml-3 tw-mt-2 tw-grid tw-grid-cols-1 tw-gap-4">
                                        {AnswerTypes?.map((data, idx) => (
                                            <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                                <RadioGroupItem
                                                    value={data}
                                                    id={data}
                                                    checked={quizData?.show_answer_type == data}
                                                />
                                                <Label
                                                    htmlFor={data}
                                                    className="tw-cursor-pointer tw-font-normal tw-capitalize"
                                                >
                                                    {data?.replaceAll("-", " ")}
                                                </Label>
                                            </div>
                                        ))}
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="tw-w-full tw-space-y-5">
                    <div className="tw-grid tw-grid-cols-1 tw-gap-2">
                        <div className="tw-w-full tw-space-y-1">
                            <Label htmlFor="course_id">Course</Label>
                            <Select
                                onValueChange={(e) => onChangeHandle({ target: { value: e, name: "course_id" } })}
                                value={quizData?.course_id}
                                name="course_id"
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Choose course" />
                                </SelectTrigger>
                                <SelectContent>
                                    {courseList?.map((data, idx) => (
                                        <SelectItem key={idx} value={data?.id}>
                                            {data?.course_title}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                        <div className="tw-space-y-1">
                            <Label htmlFor="chapter_id">Chapter</Label>
                            <Select
                                onValueChange={(e) => onChangeHandle({ target: { value: e, name: "chapter_id" } })}
                                value={quizData?.chapter_id}
                                name="chapter_id"
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Choose chapter" />
                                </SelectTrigger>
                                <SelectContent>
                                    {chapterList?.map((data, idx) => (
                                        <SelectItem key={idx} value={data?.id}>
                                            {data?.chapter_title}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="trainer_id">Trainer</Label>
                            <Select
                                onValueChange={(e) => onChangeHandle({ target: { value: e, name: "trainer_id" } })}
                                value={quizData?.trainer_id}
                                name="trainer_id"
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Choose trainer" />
                                </SelectTrigger>
                                <SelectContent>
                                    {trainersList?.map((data, idx) => (
                                        <SelectItem key={idx} value={data?.id}>
                                            {`${data?.first_name} ${data?.last_name}`}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="tw-grid tw-grid-cols-1">
                        <div className="tw-flex-grid-1 tw-mt-3 tw-grid tw-gap-5">
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox
                                    id="is_graded"
                                    checked={quizData?.is_graded}
                                    onCheckedChange={(e) => onChangeHandle({ target: { value: e, name: "is_graded" } })}
                                    name="is_graded"
                                />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="is_graded"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Is Graded
                                    </label>
                                </div>
                            </div>
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox
                                    id="on_next_answer_show"
                                    checked={quizData?.on_next_answer_show}
                                    onCheckedChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "on_next_answer_show" } })
                                    }
                                />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="on_next_answer_show"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        On Next - Answer Show
                                    </label>
                                </div>
                            </div>
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox
                                    id="show_introduction"
                                    checked={quizData?.show_introduction}
                                    onCheckedChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "show_introduction" } })
                                    }
                                    name="show_introduction"
                                />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="show_introduction"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Show Introduction
                                    </label>
                                </div>
                            </div>
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox
                                    id="show_result"
                                    checked={quizData?.show_result}
                                    onCheckedChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "show_result" } })
                                    }
                                />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="show_result"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Show Result
                                    </label>
                                </div>
                            </div>
                            <div className="tw-items-top tw-flex tw-space-x-2">
                                <Checkbox
                                    id="move_next_on_right_answer"
                                    checked={quizData?.move_next_on_right_answer}
                                    onCheckedChange={(e) =>
                                        onChangeHandle({ target: { value: e, name: "move_next_on_right_answer" } })
                                    }
                                />
                                <div className="tw-grid tw-gap-1.5 tw-leading-none">
                                    <label
                                        htmlFor="move_next_on_right_answer"
                                        className="tw-peer-disabled:tw-cursor-not-allowed tw-peer-disabled:tw-opacity-70 tw-cursor-pointer tw-text-sm tw-font-medium tw-leading-none"
                                    >
                                        Move next if Answer is right
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default QuizSettings;
