import { Badge } from "@/components/ui/badge";
import S<PERSON><PERSON>eader, { AddContentFunction } from "@/pages/dashboard/createcourse/chaptercontent/slides/common";
import { useInView } from "framer-motion";
import { CircleFadingPlus } from "lucide-react";
import moment from "moment";
import { useEffect, useRef } from "react";

const Homework = ({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    onSetDelete,
    setCurrentId,
    setContentArray,
    contentArray,
    setOpenIconDialog,
}) => {
    const containerRef = useRef(null);
    const inView = useInView(containerRef, { amount: 0.8 });
    useEffect(() => {
        if (inView) setCurrentId(`#slide_${index}`);
    }, [inView]);
    return (
        <div
            ref={containerRef}
            className="tw-grid tw-min-h-[60vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${template?.background || content?.background})` }}
        >
            <SlideHeader
                index={index}
                content={content}
                contentArray={contentArray}
                onSlideEdit={onSlideEdit}
                onSetDelete={onSetDelete}
                onRemoveSlide={onRemoveSlide}
                setOpenIconDialog={setOpenIconDialog}
                setContentArray={setContentArray}
            />
            {content?.homework == null ? (
                <AddContentFunction onSlideEdit={onSlideEdit} content={content} index={index} />
            ) : (
                <div className="tw-px-10 tw-py-7">
                    <div>
                        <h1 className="tw-text-3xl tw-font-bold">
                            <i className="fa-solid fa-laptop-file"></i> {content?.homework?.homework_title}
                        </h1>
                        <p className="my-2 tw-text-lg">{content?.homework?.description}</p>
                    </div>
                    <div className="tw-mt-5 tw-flex tw-gap-10 tw-rounded-xl tw-border-[1px] tw-p-5">
                        <div className="tw-space-y-2">
                            <p className="tw-text-lg tw-font-semibold tw-text-gray-500">
                                <i className="fa-solid fa-dice-d6"></i> Points
                            </p>
                            <h1 className="tw-text-xl tw-font-semibold">{content?.homework?.homework_points}</h1>
                        </div>
                        <div className="tw-space-y-2">
                            <p className="tw-text-lg tw-font-semibold tw-text-gray-500">
                                <i className="fa-solid fa-clock"></i> Submission
                            </p>
                            <h1 className="tw-text-xl tw-font-semibold">
                                {moment(content?.homework?.submission_date).format("LLL")}
                            </h1>
                        </div>
                        <div className="tw-space-y-2">
                            <p className="tw-text-lg tw-font-semibold tw-text-gray-500">
                                <i className="fa-solid fa-paperclip"></i> Attachment
                            </p>
                            <Badge className="tw-flex tw-cursor-pointer tw-gap-2 tw-p-1">
                                <i className="fa-solid fa-download"></i> Download
                            </Badge>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Homework;
