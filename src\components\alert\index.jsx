import { AlertSnackInfo } from "@/redux/alert/alert-action";
import Alert from "@mui/material/Alert";
import Snackbar from "@mui/material/Snackbar";
import * as React from "react";
import { useDispatch } from "react-redux";

const MuiAlert = React.forwardRef(function Alert2(props, ref) {
    return <Alert elevation={6} ref={ref} variant="filled" {...props} />;
});

const AlertSnackbar = ({ AlertInfo: alertInfo }) => {
    const dispatch = useDispatch();
    const [open, setOpen] = React.useState(false);

    const [type, setType] = React.useState("");
    const [message, setMessage] = React.useState("");

    React.useEffect(() => {
        if (Object.keys(alertInfo ?? {}).length > 0) {
            handleClick();
            setMessage(alertInfo.message);
            setType(alertInfo.result == true ? "success" : "error");
        }
    }, [alertInfo]);

    const handleClick = () => setOpen(true);

    const handleClose = (event, reason) => {
        if (reason === "clickaway") return;
        setOpen(false);
        dispatch(AlertSnackInfo({}));
    };
    return (
        <Snackbar open={open} autoHideDuration={6000} onClose={handleClose}>
            <MuiAlert onClose={handleClose} severity={type} sx={{ width: "100%" }}>
                {message}
            </MuiAlert>
        </Snackbar>
    );
};

export default AlertSnackbar;
