import { ADD_NEW_ROLE, DELETE_ROLE, FETCH_ROLES_REQ, GET_ROLES, UPDATE_ROLE } from "@/redux-types";

export const fetchRoleReq = () => {
    return {
        type: FETCH_ROLES_REQ,
    };
};
export const getAllRoles = (rolesList) => {
    return {
        type: GET_ROLES,
        payload: rolesList,
    };
};
export const addRole = (roleData, onClose) => {
    return {
        type: ADD_NEW_ROLE,
        payload: roleData,
        onClose: onClose,
    };
};
export const updateRole = (roleData, onClose) => {
    return {
        type: UPDATE_ROLE,
        payload: roleData,
        onClose: onClose,
    };
};
export const deleteRole = (deleteID) => {
    return {
        type: DELETE_ROLE,
        payload: deleteID,
    };
};
