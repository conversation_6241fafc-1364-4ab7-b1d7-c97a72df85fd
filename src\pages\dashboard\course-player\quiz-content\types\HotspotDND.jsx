import { colorsList } from "@/data/colors";
import { cn, isUrl } from "@/lib/utils";
import { useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import Draggable from "react-draggable";
import ContentSlideLayout from "../layouts/ContentSlideLayout";

const HotspotDND = ({ handleNextSlide, handlePrevSlide, className, data, sequence, onComponentAnswer, template }) => {
    const containerRef = useRef(null); // Reference to the container
    const inView = useInView(containerRef);
    const [containerSize, setContainerSize] = useState({ width: 600, height: 350 });
    const [items, setItems] = useState([]);
    const [zones, setZones] = useState([]);
    const [answer, setAnswer] = useState([]);

    useEffect(() => {
        if (data) {
            setItems(data?.hotspots);
            setZones(data?.hotspotDropzone);
        }
    }, [data]);

    const updateContainerSize = () => {
        if (containerRef.current) {
            const rect = containerRef.current.getBoundingClientRect();
            setContainerSize({ width: rect.width, height: rect.height });
        }
    };

    useEffect(() => {
        setTimeout(() => {
            updateContainerSize();
        }, 1000);
    }, [inView]);

    useEffect(() => {
        window.addEventListener("resize", updateContainerSize);
        return () => {
            window.removeEventListener("resize", updateContainerSize);
        };
    }, []);

    // Convert relative positions to absolute positions
    const getAbsolutePosition = (relativeX, relativeY) => {
        return {
            x: relativeX * containerSize.width,
            y: relativeY * containerSize.height,
        };
    };

    // Check if the item is within a drop zone
    const isWithinZone = (itemPosition, zone) => {
        const tolerance = 50; // Tolerance for snapping
        const isXWithin = Math.abs(itemPosition.x - zone.x) <= tolerance;
        const isYWithin = Math.abs(itemPosition.y - zone.y) <= tolerance;
        return isXWithin && isYWithin;
    };

    useEffect(() => {
        let ans = answer.filter((dt) => dt.isCorrect);
        let ppq = data?.points / data?.hotspots?.length;
        let points = Math.round(ans?.length * ppq);
        onComponentAnswer(sequence, answer, points);
    }, [items, answer]);

    const handleStop = (e, data, index) => {
        const updatedItems = [...items];
        updatedItems[index].x = data.x / containerSize.width; // Save relative x
        updatedItems[index].y = data.y / containerSize.height; // Save relative y

        const itemAbsolutePos = { x: data.x, y: data.y };

        // Find the nearest zone (if any)
        const nearestZone = zones
            .map((zone) => ({
                ...zone,
                ...getAbsolutePosition(zone.x, zone.y), // Convert zone to absolute position
            }))
            .find((zone) => isWithinZone(itemAbsolutePos, zone));

        if (nearestZone) {
            const isCorrect = updatedItems[index].correct === nearestZone.zone;
            const exist = answer.find((item) => item.name === updatedItems[index].name);
            if (exist) {
                const oldItems = [...answer];
                const findIndex = answer.findIndex((item) => item.name === updatedItems[index].name);
                oldItems[findIndex].isCorrect = isCorrect;
                oldItems[findIndex].zone = nearestZone.zone;
                oldItems[findIndex].name = updatedItems[index].name;
                setAnswer(oldItems);
            } else {
                setAnswer([...answer, { name: updatedItems[index].name, zone: nearestZone.zone, isCorrect }]);
            }
        } else {
            const removeItemsList = answer.filter((item) => item.name === updatedItems[index].name);
            setAnswer(removeItemsList);
        }

        setItems(updatedItems);
    };

    return (
        <ContentSlideLayout
            handleNextSlide={handleNextSlide}
            handlePrevSlide={handlePrevSlide}
            className={className}
            data={data}
            template={template}
            question={{
                index: sequence,
                data: data?.name ?? "Complete the gaps using the appropriate words from the box.",
                specialInstruction:
                    data?.specialInstruction ?? "Complete the gaps using the appropriate words from the box.",
            }}
        >
            <div
                ref={containerRef}
                className="tw-relative tw-grid tw-w-[650px] tw-grid-cols-1 tw-grid-rows-1 tw-bg-cover tw-bg-center tw-bg-no-repeat"
                style={{
                    // height: "100%",
                    aspectRatio: "12 / 7",
                    backgroundImage: `url(${data?.question_thumbnail})`,
                }}
            >
                {zones.map((zone, index) => {
                    const { x, y } = getAbsolutePosition(zone.x, zone.y);
                    return (
                        <Draggable key={index} position={{ x, y }} disabled>
                            <div
                                className={cn(
                                    "tw-absolute tw-z-0 tw-flex tw-cursor-grab tw-items-center tw-justify-center tw-overflow-auto tw-rounded-md tw-border-[2px] tw-border-dashed tw-border-red-500 tw-bg-gray-100/50 tw-p-2 tw-text-xl",
                                )}
                                style={{ width: "max-content" }}
                            >
                                <img
                                    src="/assets/hotspot_dnd.png"
                                    className="tw-absolute tw-z-0 tw-size-12 tw-opacity-50"
                                />
                                {zone.zone && (
                                    <p
                                        className="tw-font-mono tw-font-medium"
                                        style={{
                                            width: zone.width > 0 || zone.width ? zone.width : 50,
                                            height: zone.height > 0 || zone.height ? zone.height : 50,
                                        }}
                                    >
                                        {zone.zone}
                                    </p>
                                )}
                                {isUrl(zone?.src) && (
                                    <img
                                        src={zone?.src}
                                        alt=""
                                        className="tw-size-[50px] tw-select-none tw-object-contain"
                                    />
                                )}
                            </div>
                        </Draggable>
                    );
                })}

                {items.map((item, index) => {
                    const { x, y } = getAbsolutePosition(item.x, item.y);
                    return (
                        <Draggable key={index} position={{ x, y }} onStop={(e, data) => handleStop(e, data, index)}>
                            <div
                                className="tw-absolute tw-flex tw-h-fit tw-w-fit tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-[5px] tw-bg-[#add8e6] tw-px-5 tw-py-2 tw-text-center tw-text-4xl tw-leading-none"
                                style={{
                                    backgroundColor: colorsList[index + 2],
                                    color: data?.styles?.answer?.color,
                                    fontFamily: data?.styles?.answer?.fontFamily,
                                    fontSize: data?.styles?.answer?.fontSize,
                                }}
                            >
                                {item.name}
                            </div>
                        </Draggable>
                    );
                })}
            </div>
        </ContentSlideLayout>
    );
};

export default HotspotDND;
