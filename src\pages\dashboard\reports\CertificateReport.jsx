import {
    <PERSON><PERSON><PERSON>rumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
// import QuestionTypeForm from "./QuestionTypeForm";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { utils, writeFileXLSX } from "xlsx";
import PreviewFile from "./PreviewFile";

const CertificateReport = () => {
    const navigate = useNavigate();
    const [courseList, setCourseList] = useState([]);
    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(10);

    const [open, setOpen] = useState(false);
    const [editData, setEditData] = useState(null);

    const [filterState, setFilterState] = useState({
        search: "",
        course_title: "",
        certificate_issued_at: "",
        validity: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesname = filterState?.search
                ? `${item?.first_name} ${item?.last_name}`?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const matchesTitle = filterState?.course_title ? item?.course_title === filterState?.course_title : true; // Allow all items if no subCategory filter

            const issueDate = filterState?.certificate_issued_at
                ? moment(item.certificate_issued_at).format("DD/MMM/YYYY") ===
                  moment(filterState?.certificate_issued_at).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            const expiration = filterState?.validity
                ? moment(item.validity).format("DD/MMM/YYYY") === moment(filterState?.validity).format("DD/MMM/YYYY")
                : true; // Allow all items if no subCategory filter

            return matchesname && matchesTitle && issueDate && expiration; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            course_title: "",
            certificate_issued_at: "",
            validity: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getReports();
    }, []);

    const getReports = async () => {
        await tanstackApi
            .post("reports/get-certificate-reports", {})
            .then((res) => {
                setDataList(remapData(res?.data?.data));
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    useEffect(() => {
        getCourses();
    }, []);

    const getCourses = async () => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    function remapData(data) {
        // Initialize an empty array to store the remapped data
        const remappedData = [];

        // Loop through the data array
        data.forEach((item) => {
            // For each item, loop through its userData array
            item.userData.forEach((user) => {
                // Create a new object for each user, merging the user data with the parent item data
                remappedData.push({
                    first_name: user.first_name,
                    last_name: user.last_name,
                    email: user.email,
                    certificate_url: user.certificate_url,
                    download_certificate_url: user.download_certificate_url,
                    validity: user.validity,
                    certificate_issued_at: user.certificate_issued_at,
                    id: item.id + user.email + item.course_title, // Certificate id from the parent object
                    certificate_title: item.certificate_title, // Certificate title from the parent object
                    course_title: item.course_title, // Course title from the parent object
                });
            });
        });

        // Return the remapped data array
        return remappedData;
    }

    function excelConverter() {
        var finalData = filteredData?.map((row, index) => {
            return {
                Course: row?.course_title,
                "User Name": `${row?.first_name} ${row?.last_name}`,
                Email: row?.email,
                Certificate: row?.certificate_title,
                "File URl": row?.certificate_url,
                "Certificate Issue Date": row?.certificate_issued_at,
                "Certificate Expiry Date": row?.validity,
            };
        });
        const wb = utils.book_new();
        utils.book_append_sheet(wb, utils.json_to_sheet(finalData));
        writeFileXLSX(wb, "Certificate Reports.xlsx");
    }

    const onDownload = (row) => {
        const url = row?.certificate_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `Certificate : ${`${row?.first_name} ${row?.last_name}`}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    const onPreview = (data) => {
        setEditData(data);
        setOpen(true);
    };

    return (
        <>
            <PreviewFile open={open} setOpen={setOpen} editData={editData} />
            <div>
                <div className="tw-flex tw-justify-between">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink href="#">Reports</BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Certificate Reports</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <Button className="tw-px-2 tw-py-1" onClick={excelConverter}>
                        <i className="fa-solid fa-download"></i> Export Data
                    </Button>
                </div>
                {/* <br /> */}
                <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                    <div className="input_group">
                        <label htmlFor="">Search</label>
                        <input
                            type="text"
                            placeholder="Username or email ..."
                            onChange={onFilterChange}
                            value={filterState?.search}
                            name="search"
                            className="tw-text-sm"
                        />
                    </div>

                    <div className="input_group">
                        <label htmlFor="">Course</label>
                        <select
                            className="tw-text-sm"
                            onChange={onFilterChange}
                            value={filterState?.course_title}
                            name="course_title"
                        >
                            <option value=""> - Choose Course - </option>
                            {courseList?.map((status, idx) => (
                                <option key={idx} value={status?.course_title}>
                                    {status?.course_title}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="input_group">
                        <label htmlFor="">Issue date</label>
                        <input
                            className="tw-text-sm"
                            type="date"
                            onChange={onFilterChange}
                            value={filterState?.certificate_issued_at}
                            name="certificate_issued_at"
                        />
                    </div>
                    <div className="input_group">
                        <label htmlFor="">Expiration date</label>
                        <input
                            className="tw-text-sm"
                            type="date"
                            onChange={onFilterChange}
                            value={filterState?.validity}
                            name="validity"
                        />
                    </div>
                    <div className="filter_controls">
                        <button className="search_btn" onClick={onSearch}>
                            <i className="fa-solid fa-magnifying-glass"></i> Search
                        </button>
                        <button className="clear_btn" onClick={onClear}>
                            <i className="fa-solid fa-xmark"></i> Clear
                        </button>
                    </div>
                </div>
                <div className="custom_table tw-font-lexend">
                    <table>
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>User Name</th>
                                <th>Email</th>
                                <th>Certificate Name</th>
                                <th>Download</th>
                                <th>View</th>
                                <th>Certificate Issue Date</th>
                                <th>Certificate Expiry Date</th>
                            </tr>
                        </thead>
                        <tbody className="tw-font-lexend">
                            {tableData?.map((row, idx) => (
                                <tr key={idx}>
                                    <td>{row?.course_title}</td>
                                    <td>{`${row?.first_name} ${row?.last_name}`}</td>
                                    <td>{row?.email}</td>
                                    <td>{row?.certificate_title}</td>
                                    <td>
                                        {row?.certificate_url ? (
                                            <Button variant="outline" onClick={() => onDownload(row)}>
                                                Download
                                            </Button>
                                        ) : (
                                            "-"
                                        )}
                                    </td>
                                    <td>
                                        {row?.certificate_url ? (
                                            <Button variant="outline" onClick={() => onPreview(row)}>
                                                View
                                            </Button>
                                        ) : (
                                            "-"
                                        )}
                                    </td>
                                    <td>
                                        {row?.certificate_issued_at
                                            ? moment(row?.certificate_issued_at).format("LL")
                                            : "-"}
                                    </td>
                                    <td>
                                        {row?.validity && row?.validity != "0000-00-00"
                                            ? moment(row?.validity).format("LL")
                                            : moment(row?.certificate_issued_at).add(1, "year").format("LL")}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                        <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                        <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                            <SelectTrigger>
                                <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={7}>7</SelectItem>
                                <SelectItem value={10}>10</SelectItem>
                                <SelectItem value={20}>20</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Pagination className="">
                            <PaginationContent>
                                {/* Previous Button */}
                                <PaginationItem>
                                    <PaginationPrevious
                                        href="#"
                                        onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                        disabled={currentPage === 1}
                                    />
                                </PaginationItem>

                                {/* Page Numbers with Ellipses */}
                                {currentPage > 3 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                                1
                                            </PaginationLink>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    </>
                                )}

                                {getVisiblePages().map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href="#"
                                            isActive={page === currentPage}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {currentPage < totalPages - 2 && (
                                    <>
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                                {totalPages}
                                            </PaginationLink>
                                        </PaginationItem>
                                    </>
                                )}

                                {/* Next Button */}
                                <PaginationItem>
                                    <PaginationNext
                                        href="#"
                                        onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            </div>
        </>
    );
};

export default CertificateReport;
