import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useGetTemplate } from "@/react-query/quizz/template";
import { Pencil } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const QuizTemplates = ({ onChangeHandle, quizData, setQuizData }) => {
    const [templateList, setTemplateList] = useState([]);
    const [selectedTemplate, setSelectedTemplate] = useState(quizData?.template_id || null);
    const loginToken = localStorage.getItem("login_token");

    const templates = useGetTemplate({ limit: 10, offset: 0 });

    useEffect(() => {
        if (templates.isSuccess) {
            setTemplateList(templates.data?.data?.filter((dt) => dt?.template_type == "quiz"));
        }
    }, [templates.status]);

    useEffect(() => {
        if (selectedTemplate !== null) {
            setQuizData({ ...quizData, template_id: selectedTemplate });
        }
    }, [selectedTemplate]);

    return (
        <Card>
            <CardHeader>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-space-y-2">
                        <CardTitle>Quiz template</CardTitle>
                        <CardDescription>
                            Choose UI UX template for the quiz. Click save when you&apos;re done.
                        </CardDescription>
                    </div>
                    <div>
                        <Button className="tw-rounded-xl" variant="outline" asChild>
                            <Link
                                to={`https://lms-course-builder.vercel.app/dashboard/templates/create?type=quiz&token=${loginToken}`}
                                target="_blank"
                            >
                                <i className="fa-solid fa-plus"></i> Create Template
                            </Link>
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[1.5fr_2fr] tw-gap-8">
                    <div
                        className="tw-aspect-[1.53] tw-overflow-hidden tw-rounded-[1.2vw] tw-bg-cover tw-bg-center tw-bg-no-repeat tw-shadow-md"
                        style={{
                            backgroundImage: `url(${(templateList?.find((dt) => dt?.id == selectedTemplate)?.thumbnail ?? templateList?.find((dt) => dt?.id == selectedTemplate)?.font_family?.thumbnail) || "/assets/thumbnail.png"})`,
                        }}
                    ></div>
                    <div className="tw-grid tw-grid-cols-3 tw-gap-3">
                        {templateList?.map((template, idx) => (
                            <div
                                key={idx}
                                onClick={() => setSelectedTemplate(template?.id)}
                                className={`${selectedTemplate == template?.id && "active_template"} tw-flex tw-h-[10.3vw] tw-cursor-pointer tw-flex-col tw-items-center tw-justify-between tw-gap-2 tw-rounded-2xl tw-border-[1px] tw-p-1 tw-shadow-sm hover:tw-bg-slate-100`}
                            >
                                <div className="tw-aspect-[1.65] tw-overflow-hidden tw-rounded-xl">
                                    <img
                                        src={
                                            (template?.thumbnail ?? template?.font_family?.thumbnail) ||
                                            "/assets/thumbnail.png"
                                        }
                                        alt=""
                                        className="tw-h-full tw-w-full tw-object-cover"
                                    />
                                </div>
                                <div className="tw-flex tw-w-full tw-items-center tw-justify-between tw-gap-2 tw-px-2">
                                    <Label className="tw-pb-1">{template?.template_title}</Label>
                                    <Pencil size={15} />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default QuizTemplates;
