import { useEffect } from "react";
import { Card } from "@/components/ui/card";

export default function Timer({ timeRemaining, setTimeRemaining }) {
    useEffect(() => {
        const timer = setInterval(() => {
            setTimeRemaining((prev) => {
                if (prev <= 0) {
                    clearInterval(timer);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [setTimeRemaining]);

    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;

    // Calculate the progress percentage
    const progress = (timeRemaining / 60) * 100;

    return (
        <Card className="tw-border-none tw-bg-orange-800/80 tw-p-4 tw-text-center tw-text-white tw-shadow-lg">
            <div className="tw-relative tw-mx-auto tw-h-32 tw-w-32">
                {/* Background circle */}
                <div className="tw-absolute tw-inset-0 tw-rounded-full tw-bg-slate-700"></div>

                {/* Progress circle - using conic gradient for the timer */}
                <div
                    className="tw-absolute tw-inset-0 tw-rounded-full"
                    style={{
                        background: `conic-gradient(#2D9D78 ${progress}%, transparent ${progress}%)`,
                    }}
                ></div>

                {/* Inner circle with time */}
                <div className="tw-absolute tw-inset-2 tw-flex tw-items-center tw-justify-center tw-rounded-full tw-bg-orange-800">
                    <span className="tw-text-3xl tw-font-bold">
                        {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
                    </span>
                </div>
            </div>
            <p className="tw-mt-2 tw-text-sm">Time Remaining</p>
        </Card>
    );
}
