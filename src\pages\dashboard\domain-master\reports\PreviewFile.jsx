import { Dialog, DialogContent } from "@/components/ui/dialog";

const PreviewFile = ({ open, setOpen, editData }) => {
    return (
        <>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-h-[700px] tw-w-full tw-max-w-[1000px]">
                    <div className="tw-flex tw-items-center tw-justify-center">
                        <embed
                            src={`${editData?.certificate_url}#toolbar=0`}
                            width="842px"
                            height="595px"
                            type="application/pdf"
                        />
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default PreviewFile;
