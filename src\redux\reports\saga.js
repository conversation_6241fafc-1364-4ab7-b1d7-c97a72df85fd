import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { getREPORTS, getTimelineLogs } from "@/redux/reports/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function remapData(data) {
    // Initialize an empty array to store the remapped data
    const remappedData = [];

    // Loop through the data array
    data.forEach((item) => {
        // For each item, loop through its userData array
        item.userData.forEach((user) => {
            // Create a new object for each user, merging the user data with the parent item data
            remappedData.push({
                first_name: user.first_name,
                last_name: user.last_name,
                email: user.email,
                certificate_url: user.certificate_url,
                download_certificate_url: user.download_certificate_url,
                validity: user.validity,
                certificate_issued_at: user.certificate_issued_at,
                id: item.id + user.email + item.course_title, // Certificate id from the parent object
                certificate_title: item.certificate_title, // Certificate title from the parent object
                course_title: item.course_title, // Course title from the parent object
            });
        });
    });

    // Return the remapped data array
    return remappedData;
}

function* getReports(reportData) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `reports/${reportData.payload}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.data?.reverse();
            return response;
        })
        .catch((err) => {
            var errMsg = [];
            return errMsg;
        });

    if (data) {
        yield put(getREPORTS(data));
    } else {
        yield put(getREPORTS([]));
    }
}

function* getCertificateReports(reportData) {
    const data = yield axios
        .post(CONSTANTS.getAPI() + `reports/get-certificate-reports`, reportData?.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.data?.filter((dt) => dt?.certificate_completions !== 0)?.reverse();
            return remapData(response);
        })
        .catch((err) => {
            var errMsg = [];
            return errMsg;
        });

    if (data) {
        yield put(getREPORTS(data));
    } else {
        yield put(getREPORTS([]));
    }
}

function* getTimelinelogs(reportData) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `users/get-timeline-log`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.data?.timeline_data;
            return response;
        })
        .catch((err) => {
            var errMsg = [];
            return errMsg;
        });

    if (data) {
        yield put(getTimelineLogs(data));
    } else {
        yield put(getTimelineLogs([]));
    }
}

function* AddTimelinelog(Data) {
    const logData = yield axios
        .post(CONSTANTS.getAPI() + "users/add-timeline-log", Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token") || localStorage.getItem("auth_signup_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response;
            return errMsg;
        });
}

export function* ReportsWatcher() {
    yield takeEvery(actions.FETCH_REPORTS_REQ, getReports);
    yield takeEvery(actions.FETCH_CERTIFICATE_REPORTS_REQ, getCertificateReports);
    yield takeEvery(actions.FETCH_TIMELINE_LOGS_REQ, getTimelinelogs);
    yield takeEvery(actions.ADD_TIMELINE_LOG, AddTimelinelog);
}

export default function* rootSaga() {
    yield all([fork(ReportsWatcher)]);
}
