import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const defaultValue = {
    language_id: "",
    name: "",
    user_restriction: "",
    is_active: "Active",
};

const Details = ({ groupTeamData, getGroupTeams }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [languages, setLanguages] = useState([]);
    const [Trainers, setTrainers] = useState([]);
    const [selectedTrainer, setSelectedTrainer] = useState("");
    const [groupTeamDetails, setGroupTeamDetails] = useState(defaultValue);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setGroupTeamDetails({ ...groupTeamDetails, [name]: value });
    };

    useEffect(() => {
        setData(groupTeamData);
    }, [groupTeamData]);

    useEffect(() => {
        getLanguages();
        getTrainers();
    }, []);

    const getLanguages = async () => {
        await tanstackApi
            .get("language/list")
            .then((res) => {
                setLanguages(res?.data?.data);
            })
            .catch((err) => {
                setLanguages([]);
            });
    };

    const getTrainers = async () => {
        await tanstackApi
            .get("users/get-users")
            .then((res) => {
                setTrainers(res?.data?.data?.filter((dt) => dt?.is_trainer == 1));
            })
            .catch((err) => {
                setTrainers([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (!groupTeamDetails?.name) {
            toast?.warning("Team Name", {
                description: "Team name is required",
            });
            return false;
        } else if (!groupTeamDetails?.language_id) {
            toast?.warning("Language", {
                description: "Team Language selection is required",
            });
            return false;
        } else if (!groupTeamDetails?.user_restriction) {
            toast?.warning("User restriction", {
                description: "Define user restriction in team",
            });
            return false;
        }

        if (params?.subgroup_id !== undefined) {
            const payload = {
                subgroup_id: params?.subgroup_id,
                language_id: groupTeamDetails?.language_id,
                name: groupTeamDetails?.name,
                user_restriction: groupTeamDetails?.user_restriction,
                is_active: groupTeamDetails?.is_active == "Active" ? true : false,
            };

            await tanstackApi
                .put("user-subgroup/update", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "teams",
                        log: `${groupTeamDetails?.name} updated under ${params?.group_name} successfully.`,
                    });
                    toast.success("Updated Successfully");
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                group_id: params?.group_id,
                subGroups: [
                    {
                        language_id: groupTeamDetails?.language_id,
                        name: groupTeamDetails?.name,
                        user_restriction: groupTeamDetails?.user_restriction,
                        is_active: groupTeamDetails?.is_active == "Active" ? true : false,
                    },
                ],
            };

            await tanstackApi
                .post("user-subgroup/create", { ...payload })
                .then((res) => {
                    punchTimelineLog({
                        user_id: localStorage.getItem("userId"),
                        event: "teams",
                        log: `${groupTeamDetails?.name} created under ${params?.group_name} successfully.`,
                    });
                    toast.success("Saved Successfully");
                    navigate(`/dashboard/user-subgroup-master/${params?.group_id}/${params?.group_name}`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    const onAssignTrainer = async (e) => {
        e.preventDefault();
        if (!selectedTrainer) {
            toast?.warning("Trainer of Team", {
                description: "Please select an trainer to assign.",
            });
            return false;
        }
        let trainer = Trainers?.find((dt) => dt?.id == Number(selectedTrainer));
        await tanstackApi
            .post("user-subgroup/assign-trainer", { subgroup_id: params?.subgroup_id, trainer_id: selectedTrainer })
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("userId"),
                    event: "teams",
                    log: `${groupTeamDetails?.name} assigned to Trainer - ${`${trainer?.first_name} ${trainer?.last_name}`} successfully.`,
                });
                toast.success("Trainer Assigned", {
                    description: `Trainer has been assigned successfully`,
                });

                // getCourses({ course_id: params?.course_id });
                // navigate(`/dashboard/user-subgroup-master/${params?.group_id}/${params?.group_name}`);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    const setData = (data) => {
        if (data) {
            setGroupTeamDetails({
                language_id: data?.language_id,
                name: data?.name,
                user_restriction: data?.user_restriction,
                is_active: data?.is_active ? "Active" : "Inactive",
            });
            setSelectedTrainer(data?.lms_trainer_subgroups_mappings?.[0]?.lms_user?.id);
        } else {
            setGroupTeamDetails(defaultValue);
            setSelectedTrainer("");
        }
    };

    const onClear = () => {
        setData(groupTeamData);
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Team basic details</CardTitle>
                <CardDescription>
                    Add bundle title, user restriction & language etc here. Click save when you&apos;re done.
                </CardDescription>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[700px_auto] tw-gap-0">
                    <div className="tw-grid tw-grid-cols-1 tw-gap-3">
                        <div className="tw-space-y-3">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Team Name</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={groupTeamDetails?.name}
                                    name="name"
                                    id="name"
                                    placeholder="Enter team name here"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-3 tw-gap-4">
                            <div className="tw-space-y-1">
                                <Label htmlFor="language_id">Language</Label>
                                <SelectNative
                                    onChange={onChangeHandle}
                                    value={groupTeamDetails?.language_id}
                                    name="language_id"
                                >
                                    <option value=""> - Choose Language - </option>
                                    {languages?.map((lang, idx) => (
                                        <option value={lang?.id} key={idx}>
                                            {lang?.name}
                                        </option>
                                    ))}
                                </SelectNative>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="user_restriction">Max Members</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={groupTeamDetails?.user_restriction}
                                    name="user_restriction"
                                    id="user_restriction"
                                    type="number"
                                    placeholder="Max members limit here"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="is_active">Status</Label>
                                <SelectNative
                                    onChange={onChangeHandle}
                                    value={groupTeamDetails?.is_active}
                                    name="is_active"
                                >
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </SelectNative>
                            </div>
                        </div>
                        {params?.subgroup_id !== undefined && (
                            <div className="tw-grid tw-grid-cols-[1fr_150px] tw-gap-4">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="trainer_id">Trainers</Label>
                                    <SelectNative
                                        onChange={(e) => setSelectedTrainer(e.target.value)}
                                        value={selectedTrainer}
                                        id="trainer_id"
                                    >
                                        <option value=""> - Select Trainer - </option>
                                        {Trainers?.map((user, idx) => (
                                            <option value={user?.id} key={idx}>
                                                {`${user?.first_name} ${user?.last_name}`}
                                            </option>
                                        ))}
                                    </SelectNative>
                                </div>
                                <div className="tw-flex tw-w-full tw-items-end tw-justify-end">
                                    <Button onClick={onAssignTrainer} variant="outline">
                                        <i className="fa-solid fa-check"></i> Assign Trainer
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" onClick={onClear} variant="outline">
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> Save Details
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default Details;
