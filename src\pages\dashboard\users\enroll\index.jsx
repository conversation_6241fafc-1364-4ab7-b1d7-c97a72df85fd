import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/pages/dashboard/users/enroll/data-table";
import { useGetUsersEnrollApprove, useGetUsersEnrollListing } from "@/react-query/users/enroll";
import AddTaskIcon from "@mui/icons-material/AddTask";
import ReplyAllIcon from "@mui/icons-material/ReplyAll";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { toast } from "sonner";

export default function UserEnrollPage() {
    const params = useParams();
    const assignmentList = useGetUsersEnrollListing({ userId: params.user_code.split("_")[1] });
    const approveEnroll = useGetUsersEnrollApprove();
    const [enrollmentIds, setEnrollments] = useState([]);

    const onSelectEnroll = (id) => {
        setEnrollments((prevEnrollments) => {
            const newEnrollments = new Set(prevEnrollments);
            newEnrollments.has(id) ? newEnrollments.delete(id) : newEnrollments.add(id);
            return Array.from(newEnrollments);
        });
    };

    const columns = [
        {
            accessorKey: "id",
            header: "Select",
            cell: ({ row }) =>
                new Date().getTime() >=
                    new Date(row.getValue("lms_course_lms_course_settings")[0]?.expiration_date).getTime() && (
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={(value) => {
                            row.toggleSelected(!!value);
                            onSelectEnroll(row.getValue("id"));
                        }}
                        aria-label="Select row"
                    />
                ),
        },
        {
            accessorKey: "lms_course.course_banner_url",
            header: "Banner",
            cell: ({ row }) => {
                return (
                    <Avatar className="!tw-aspect-video !tw-rounded-xl">
                        <AvatarImage className="!tw-aspect-video" src={row.getValue("lms_course_course_banner_url")} />
                    </Avatar>
                );
            },
        },
        {
            accessorKey: "lms_course.course_title",
            header: "Course Name",
        },
        {
            accessorKey: "lms_user.first_name",
            header: "Student Name",
            cell: ({ row }) => {
                return row.getValue("lms_user_first_name") + " " + row.getValue("lms_user_last_name");
            },
        },
        {
            accessorKey: "lms_user.last_name",
        },
        {
            accessorKey: "enrollment_status",
            header: "Enrollment Status",
        },
        {
            accessorKey: "enrollment_count",
            header: "Enrollment Count",
        },
        {
            accessorKey: "lms_course.lms_course_settings",
            header: "Expiry Date",
            cell: ({ row }) => {
                const settings = row.getValue("lms_course_lms_course_settings") || [];
                const expirationDate = settings.length > 0 ? settings[0].expiration_date : "N/A";
                return <div className="text-right font-medium">{expirationDate && format(expirationDate, "PP")}</div>;
            },
        },
    ];

    useEffect(() => {
        if (approveEnroll.status === "success" || approveEnroll.status === "error") {
            const message =
                approveEnroll.status === "success"
                    ? { title: "Updated successfully", description: approveEnroll.data.message }
                    : { title: "Something went wrong", description: approveEnroll.error.response.data.message };
            toast[approveEnroll.status](message.title, { description: message.description });
        }
    }, [approveEnroll.status]);

    if (assignmentList.isLoading) return <div>Loading...</div>;

    return (
        <div>
            <div className="tw-mb-5 tw-flex tw-items-center tw-justify-between tw-gap-4">
                <p className="tw-font-lexend tw-text-xl tw-font-bold">Enrollment Requests</p>
                <div className="tw-flex tw-justify-end tw-gap-3">
                    <Button asChild variant="outline">
                        <Link to="/dashboard/users">
                            <ReplyAllIcon />
                            Back
                        </Link>
                    </Button>
                    <Button
                        onClick={() => {
                            approveEnroll.mutate({ enrollmentIds });
                        }}
                        variant="success-outline"
                    >
                        <AddTaskIcon />
                        Approve
                    </Button>
                </div>
            </div>
            <DataTable
                columns={columns}
                data={assignmentList.data?.courses.filter((data) => data.enrollment_status !== "Allowed") ?? []}
            />
        </div>
    );
}
