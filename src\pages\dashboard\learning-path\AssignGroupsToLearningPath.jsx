import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const AssignGroupsToLearningPath = ({ pathData, getLearningPath }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [groupList, setGroupsList] = useState([]);
    const [groupUsersList, setGroupUsersList] = useState([]);
    const [openAlert, setOpenAlert] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [languages, setLanguages] = useState([]);

    useEffect(() => {
        getGroups();
        getLanguages();
    }, []);

    const getGroups = async (payload) => {
        await tanstackApi
            .post("user-groups/list")
            .then((res) => {
                setGroupsList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setGroupsList([]);
            });
    };

    const getLanguages = async () => {
        await tanstackApi
            .get("language/list")
            .then((res) => {
                setLanguages(res?.data?.data);
            })
            .catch((err) => {
                setLanguages([]);
            });
    };

    useEffect(() => {
        if (pathData?.usersGroups !== undefined) {
            setSelectedUsers(pathData?.usersGroups?.map((dt) => dt?.group_id) || []);
        }
    }, [pathData]);

    const onCourseSelection = (item) => {
        if (selectedUsers?.includes(item)) {
            setSelectedUsers(selectedUsers?.filter((dt) => dt !== item));
        } else {
            setSelectedUsers([...selectedUsers, item]);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        if (selectedUsers?.length == 0) {
            toast?.warning("Users", {
                description: "Please select some users for group.",
            });
            return false;
        }

        if (params?.path_id !== undefined) {
            const payload = {
                learning_path_id: params?.path_id,
                data: selectedUsers?.map((dt) => {
                    return {
                        group_id: dt,
                        user_id: null,
                    };
                }),
            };

            await tanstackApi
                .post("learning-path/assign-users", { ...payload })
                .then((res) => {
                    toast.success("Groups Assigned Successfully", {
                        description: res?.data?.message,
                    });
                    setOpenAlert(false);
                    getLearningPath(params?.path_id);
                    // navigate(`/dashboard/learning-path-view/${params?.path_id}`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action will assign this learning path to selected users.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <Card>
                <CardHeader>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div className="tw-space-y-2">
                            <CardTitle>Assign User Groups to Learning path</CardTitle>
                            <CardDescription>
                                Click on select button to select any user group for learning path. Click Add groups when
                                you&apos;re done.
                            </CardDescription>
                        </div>
                        <div className="">
                            <p className="tw-font-mono tw-text-sm">{selectedUsers?.length} User Selected</p>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="tw-space-y-2">
                    <div className="custom_scrollbar tw-h-[55vh] tw-overflow-auto">
                        <table>
                            <thead>
                                <tr>
                                    <th>Group Name</th>
                                    <th>Trainers</th>
                                    <th>Language</th>
                                    <th>Users</th>
                                    <th>Created On</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {groupList?.map((row, idx) => (
                                    <tr key={idx}>
                                        <td>{row?.name}</td>
                                        <td>
                                            {row?.lms_trainer_group_mappings?.length > 0
                                                ? row?.lms_trainer_group_mappings?.map((trainer, idx) => (
                                                      <>
                                                          {trainer?.lms_user?.first_name} {trainer?.lms_user?.last_name}
                                                          ,{"  "}
                                                      </>
                                                  ))
                                                : "No trainers assigned Yet!"}
                                        </td>
                                        <td>{languages?.find((dt) => dt?.id == row?.language_id)?.name}</td>
                                        <td>
                                            {row?.user_count}/{row?.user_restriction}
                                        </td>
                                        <td>{moment(row?.createdAt).format("LL")}</td>
                                        <td>
                                            {selectedUsers?.includes(row?.id) ? (
                                                <Button onClick={() => onCourseSelection(row?.id)}>Selected</Button>
                                            ) : (
                                                <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                                    Choose
                                                </Button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </CardContent>
                <CardFooter>
                    <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                        {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                            <i className="fa-solid fa-xmark"></i> Clear
                        </Button> */}
                        {selectedUsers?.length > 0 && (
                            <Button onClick={() => setOpenAlert(true)}>
                                <i className="fa-solid fa-floppy-disk"></i> Add Groups
                            </Button>
                        )}
                    </div>
                </CardFooter>
            </Card>
        </>
    );
};

export default AssignGroupsToLearningPath;
