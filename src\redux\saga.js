import AuthSaga from "@/redux/auth/saga";
import Category<PERSON>aga from "@/redux/category/saga";
import Certificate<PERSON>aga from "@/redux/certificate/saga";
import Course<PERSON>aga from "@/redux/course/saga";
import <PERSON><PERSON><PERSON> from "@/redux/dashboard/dash/saga";
import Define<PERSON><PERSON><PERSON>aga from "@/redux/define-role/saga";
import Gami<PERSON><PERSON><PERSON> from "@/redux/gamification/saga";
import ImageUpload<PERSON>aga from "@/redux/image-upload/saga";
import LearningPathSaga from "@/redux/learning-path/saga";
import <PERSON>dulesSaga from "@/redux/module/saga";
import NotificationSaga from "@/redux/notification/saga";
import Permission<PERSON>aga from "@/redux/permission/saga";
import <PERSON><PERSON><PERSON> from "@/redux/roles/saga";
import <PERSON>Saga from "@/redux/users/saga";
import { all, fork } from "redux-saga/effects";

export default function* root_saga(getState) {
    yield all([fork(AuthSaga)]);
    yield all([fork(DashSaga)]);
    yield all([fork(NotificationSaga)]);
    yield all([fork(GamificationSaga)]);
    yield all([fork(CourseSaga)]);
    yield all([fork(UsersSaga)]);
    yield all([fork(RoleSaga)]);
    yield all([fork(PermissionSaga)]);
    yield all([fork(ModulesSaga)]);
    yield all([fork(ImageUploadSaga)]);
    yield all([fork(CertificateSaga)]);
    yield all([fork(LearningPathSaga)]);
    yield all([fork(DefineRoleSaga)]);
    // yield all([fork(LMSHelpingAPISaga)]);
    yield all([fork(CategorySaga)]);
    // yield all([fork(orginazationSettingsSaga)]);
    // yield all([fork(ReportSaga)]);
    // // LEVEL TWO SAGA'S -> STARTS
    // yield all([fork(SubRoleSaga)]);
    // yield all([fork(courseBundleSaga)]);
    // yield all([fork(UserGroupSaga)]);
    // yield all([fork(TermsSaga)]);
    // yield all([fork(LanguageSaga)]);
    // yield all([fork(LearnerPermissionSaga)]);
    // LEVEL TWO SAGA'S -> ENDS
}
