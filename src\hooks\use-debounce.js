import { useEffect, useRef, useState } from "react";

/**
 * Custom hook to debounce object data
 * @param {Object} value - The object data to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Object} - Debounced object data
 */
const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = useState(value);
    const previousValue = useRef(value);

    useEffect(() => {
        if (JSON.stringify(previousValue.current) !== JSON.stringify(value)) {
            previousValue.current = value;

            const handler = setTimeout(() => {
                setDebouncedValue(value);
            }, delay);

            return () => clearTimeout(handler);
        }
    }, [value, delay]);

    return debouncedValue;
};

export default useDebounce;
