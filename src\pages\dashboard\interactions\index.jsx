import Pagination from "@/components/table/pagination";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useDeleteInteraction, useGetAllInteractions } from "@/react-query/interactions";
import { Trash } from "lucide-react";
import { Loader2 } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const ITEMS_PER_PAGE = 10;

export default function Interactions() {
    const [totalPages, setTotalPages] = useState(0);
    const [searchParams] = useSearchParams();
    const loginToken = localStorage.getItem("login_token");
    const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get("page") || "1", 10));
    const offset = (currentPage - 1) * ITEMS_PER_PAGE;
    const limit = ITEMS_PER_PAGE;
    const interactions = useGetAllInteractions({
        limit,
        offset,
        is_public: localStorage.getItem("level") == "levelOne" ? true : false,
    });

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (interactions.isSuccess) {
            setTotalPages(Math.ceil(interactions.data.pagination.total / ITEMS_PER_PAGE));
        }
    }, [interactions]);

    return (
        <>
            <div className="tw-mb-3 tw-flex tw-items-center tw-justify-between tw-gap-2">
                <h4 className="tw-text-xl tw-font-bold">
                    <i className="fa-solid fa-book"></i> Interactions
                </h4>
                <Button asChild className="tw-px-2 tw-py-1">
                    <Link
                        to={`https://lms-course-builder.vercel.app/dashboard/interactions/create?token=${loginToken}`}
                        target="_blank"
                    >
                        <i className="fa-solid fa-plus"></i> Create New
                    </Link>
                </Button>
            </div>

            <div className="custom_table tw-mt-2 tw-h-full tw-font-lexend">
                <table>
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Category</th>
                            <th>Creation Date</th>
                            <th>Updated Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {interactions.isLoading && (
                            <tr>
                                <td colSpan={6}> Loading...</td>
                            </tr>
                        )}
                        {interactions.data?.data?.map((row, idx) => (
                            <tr key={idx}>
                                <td>
                                    <p className="tw-font-lexend tw-font-medium">{row?.title}</p>
                                    <p className="tw-line-clamp-2 tw-font-lexend">{row?.description}</p>
                                </td>
                                <td className="tw-font-lexend">{row?.type}</td>
                                <td className="tw-font-lexend">{row?.category}</td>
                                <td className="tw-font-lexend">{moment(row?.createdAt).format("LL")}</td>
                                <td className="tw-font-lexend">{moment(row?.updatedAt).format("LL")}</td>
                                <td className="">
                                    <div className="tw-flex tw-gap-2">
                                        <Link
                                            to={`https://lms-course-builder.vercel.app/dashboard/interactions/${row?.id}?token=${loginToken}`}
                                            target="_blank"
                                        >
                                            <button className="selected_btn_alpha tw-font-lexend">
                                                <i className="fa-solid fa-pen-to-square"></i> Edit
                                            </button>
                                        </Link>
                                        <DeleteInteraction id={row?.id} />
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                    itemsPerPage={ITEMS_PER_PAGE}
                />
            </div>
        </>
    );
}

function DeleteInteraction({ id }) {
    const deleteFn = useDeleteInteraction();
    const Icon = deleteFn.isPending ? Loader2 : Trash;
    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <button className="selected_btn tw-flex tw-items-center tw-gap-1 tw-font-lexend">
                    <Trash className="tw-size-4" /> Delete
                </button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                        disabled={deleteFn.isPending}
                        onClick={() =>
                            deleteFn.mutate(
                                { id },
                                {
                                    onSuccess: (data) => {
                                        toast.success(data.message ?? "Template deleted successfully");
                                    },
                                    onError: (e) => {
                                        toast.error(e.response.data.message ?? "Something went wrong");
                                    },
                                },
                            )
                        }
                    >
                        <Icon className={cn("tw-size-4", deleteFn.isPending && "tw-animate-spin")} /> Delete
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
