import { Badge } from "@/components/ui/badge";
import {
    Bread<PERSON>rumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { BrainCircuit, Clock, Shapes } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import QuestionImport from "./QuestionImport";
import { toast } from "sonner";

const ImportQuestions = () => {
    const navigate = useNavigate();
    const params = useParams();
    const [poolData, setPoolData] = useState(null);
    const [filteredData, setFilteredData] = useState([]);
    const [open, setOpen] = useState(false);
    const [editData, setEditData] = useState(null);
    const [questionList, setQuestionList] = useState([]);
    const [questionTypes, setQuestionTypes] = useState([]);

    const [openAlert, setOpenAlert] = useState(false);

    const [filterState, setFilterState] = useState({
        search: "",
        type: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = poolData?.questions?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.question?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const matchesTag = filterState?.type ? item?.question_type == filterState?.type : true; // Allow all items if no subCategory filter

            return matchesSearch && matchesTag; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(poolData?.questions);
        setFilterState({
            search: "",
            type: "",
        });
    };

    useEffect(() => {
        if (params?.pool_id !== undefined) {
            getPoolData(params?.pool_id);
            getQuestions();
            getQuestionTypes();
        }
    }, [params]);

    useEffect(() => {
        setFilteredData(poolData?.questions || []);
    }, [poolData]);

    const getQuestions = async () => {
        await tanstackApi
            .post("questions/list", {})
            .then((res) => {
                setQuestionList(res?.data?.data);
            })
            .catch((err) => {
                setQuestionList([]);
            });
    };
    const getQuestionTypes = async () => {
        await tanstackApi
            .get("question-type/list")
            .then((res) => {
                setQuestionTypes(res?.data?.data.filter((item) => item?.displayName.trim()));
            })
            .catch((err) => {
                setQuestionTypes([]);
            });
    };

    const getPoolData = async (payload) => {
        await tanstackApi
            .get(`question-pool/get/${payload}`)
            .then((res) => {
                setPoolData(res?.data?.data);
            })
            .catch((err) => {
                setPoolData(null);
            });
    };

    const onQuestionImport = () => {
        setEditData(poolData);
        setOpen(true);
    };

    const onQuestionDelete = (data) => {
        setEditData(data);
        setOpenAlert(true);
    };

    const onDataSubmit = async () => {
        let payload = {
            question_ids: [editData?.id],
            pool_id: poolData?.id,
        };
        await tanstackApi
            .post(`questions/remove-question-pool`, payload)
            .then((res) => {
                getPoolData(params?.pool_id);
                toast.error("Deleted!", {
                    description: res?.data?.message,
                });
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete this question, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible selected questions will be deleted permanantly from this
                            question pool.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDataSubmit}>Yes Delete!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <QuestionImport
                open={open}
                setOpen={setOpen}
                editData={editData}
                getPoolData={getPoolData}
                questionList={questionList}
                questionTypes={questionTypes}
            />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/question-pool">Question Pool</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Import Questions</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <div className="tw-space-x-2">
                    <Button variant="outline" onClick={() => navigate(`/dashboard/question-pool`)}>
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button onClick={onQuestionImport}>
                        <i className="fa-solid fa-file-import"></i> Import Questions
                    </Button>
                </div>
            </div>
            <div className="page_filters tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Question
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState?.search}
                        name="search"
                        className="tw-text-sm"
                        style={{ minWidth: "300px" }}
                        type="text"
                        placeholder="Search by title ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Types
                    </label>
                    <select
                        onChange={onFilterChange}
                        value={filterState?.type}
                        className="tw-text-sm"
                        name="type"
                        id=""
                    >
                        <option value=""> - Select question type - </option>
                        {questionTypes?.map((type, idx) => (
                            <option value={type?.type} key={idx}>
                                {type?.displayName}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <br />
            <div className="tw-flex tw-flex-wrap tw-gap-5">
                {filteredData?.map((question, index) => (
                    <div
                        key={index}
                        className="tw-flex tw-w-[300px] tw-flex-col tw-justify-between tw-rounded-xl tw-border-[1px] tw-p-1"
                    >
                        <div className="tw-p-2">
                            <div className="tw-flex tw-items-center tw-gap-2">
                                <h2 className="tw-leading-2 tw-text-md tw-line-clamp-3 tw-font-lexend tw-font-medium">
                                    {question?.question}
                                </h2>
                            </div>
                        </div>
                        <div className="tw-p-2">
                            <div className="tw-mt-1 tw-space-y-2">
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <BrainCircuit size={18} /> Easy
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Shapes size={18} />{" "}
                                    <Badge variant={"outline"} className={"tw-capitalize"}>
                                        {question?.question_type}
                                    </Badge>
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                    <Clock size={18} /> Added on {moment(question?.createdAt).format("LL")}
                                </span>{" "}
                            </div>
                            <div className="tw-mt-2 tw-grid tw-grid-cols-3 tw-gap-2">
                                <div></div>
                                <div></div>
                                <div
                                    onClick={() => onQuestionDelete(question)}
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <i className="fa-solid fa-trash"></i>
                                    Delete
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default ImportQuestions;
