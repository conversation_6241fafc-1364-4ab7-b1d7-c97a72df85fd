@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 222.2 47.4% 11.2%;
        --primary-foreground: 210 40% 98%;
        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 222.2 84% 4.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
    .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 210 40% 98%;
        --primary-foreground: 222.2 47.4% 11.2%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .ProseMirror {
        @apply tw-z-0 tw-mx-auto tw-max-w-2xl tw-py-16 tw-pl-20 tw-pr-8 tw-caret-black tw-outline-0 dark:tw-caret-white lg:tw-pl-8 lg:tw-pr-8;

        .selection {
            @apply tw-inline;
        }

        .selection,
        *::selection {
            @apply tw-dark:bg-white/20 tw-inline tw-bg-black/10;
        }

        & > .react-renderer {
            @apply tw-first:mt-0 tw-last:mb-0 tw-my-12;
        }

        &.resize-cursor {
            @apply tw-cursor-col-resize;
        }

        .ProseMirror-gapcursor {
            @apply tw-relative tw-mx-auto tw-w-full tw-max-w-2xl;

            &:after {
                @apply -tw-top-[1.5em] tw-left-0 tw-right-0 tw-mx-auto tw-w-full tw-max-w-2xl tw-border-t-black/40 dark:tw-border-t-white/40;
            }
        }

        figure[data-type="imageBlock"] {
            @apply tw-m-0;

            img {
                @apply tw-block tw-w-full tw-rounded;
            }
        }

        /* Block Quote */
        figure[data-type="blockquoteFigure"] {
            @apply tw-my-14 tw-text-black dark:tw-text-white;
        }

        & > blockquote,
        [data-type="blockquoteFigure"] {
            blockquote {
                @apply tw-m-0;

                & > * {
                    @apply tw-first:mt-0 tw-last:mb-0;
                }
            }
        }

        /* Columns */
        [data-type="columns"] {
            @apply tw-mb-12 tw-mt-14 tw-grid tw-gap-4;

            &.layout-sidebar-left {
                grid-template-columns: 40fr 60fr;
            }

            &.layout-sidebar-right {
                grid-template-columns: 60fr 40fr;
            }

            &.layout-two-column {
                grid-template-columns: 1fr 1fr;
            }
        }

        [data-type="column"] {
            @apply tw-overflow-auto;
        }

        /* Details */
        [data-type="details"] {
            @apply tw-mx-auto tw-my-6 tw-flex tw-gap-1 tw-rounded tw-border tw-border-gray-300 tw-p-2;

            summary {
                @apply tw-block tw-font-bold;
            }

            > button {
                @apply tw-flex tw-h-5 tw-w-5 tw-cursor-pointer tw-items-center tw-justify-center tw-rounded tw-border-none tw-bg-transparent tw-text-xs;

                &:hover {
                    @apply tw-bg-gray-300 dark:tw-bg-gray-800;
                }

                &::before {
                    content: "\25B6";
                }
            }

            &.is-open > button::before {
                @apply tw-rotate-90;
            }

            > div {
                @apply tw-flex tw-w-full tw-flex-col tw-gap-4;

                > [data-type="detailsContent"] > :last-child {
                    @apply tw-mb-2;
                }
            }

            [data-type="details"] {
                @apply tw-mx-0 tw-my-2;
            }
        }
    }
}

.tiptap p.is-editor-empty:first-child::before {
    color: #adb5bd;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
}

.tiptap {
    height: 20rem;
    padding: 0.5rem;
    outline: none;
}

@layer base {
    * {
        @apply tw-border-border;
    }
    body {
        @apply tw-bg-background tw-text-foreground;
    }
}
