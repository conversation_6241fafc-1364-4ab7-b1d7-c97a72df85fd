/* Font */

.font_color_global {
    color: var(--input-value-color) !important;
}

.text_transform_capitalize {
    text-transform: capitalize !important;
}

.text_transform_uppercase {
    text-transform: uppercase !important;
}

.text_transform_lowercase {
    text-transform: lowercase !important;
}

.text_transform_none {
    text-transform: none !important;
}

.font_weight_400 {
    font-weight: 400 !important;
}

.font_weight_500 {
    font-weight: 500 !important;
}

.font_weight_600 {
    font-weight: 600 !important;
}

.font_weight_700 {
    font-weight: 700 !important;
}

.font_weight_800 {
    font-weight: 800 !important;
}

.font_weight_900 {
    font-weight: 900 !important;
}

.text_align_right {
    text-align: right;
}

.text_align_left {
    text-align: left;
}

.text_align_center {
    text-align: center;
}

/* Width and height  */

.width_full {
    width: 100vw !important;
}

.width_100 {
    width: 100% !important;
}

.width_max_content {
    width: max-content;
}

.height_full {
    height: 100vw !important;
}

.height_100 {
    height: 100% !important;
}

.height_max_content {
    height: max-content;
}

/* Flexbox  */

.d_flex {
    display: flex !important;
}

.align_items_start {
    align-items: flex-start;
}

.align_items_center {
    align-items: center;
}

.align_items_end {
    align-items: flex-end;
}

.align_self_start {
    align-self: flex-start;
}

.align_self_center {
    align-self: center;
}

.align_self_end {
    align-self: flex-end;
}

.justify_content_center {
    justify-content: center;
}

.justify_content_start {
    justify-content: flex-start;
}

.justify_content_end {
    justify-content: flex-end;
}

.justify_content_between {
    justify-content: space-between;
}

.justify_content_around {
    justify-content: space-around;
}

.justify_content_evenly {
    justify-content: space-evenly;
}

.justify_content_right {
    justify-content: right;
}

.justify_content_left {
    justify-content: left;
}

.flex_col {
    flex-direction: column;
}

.flex_row {
    flex-direction: row;
}

.flex_row_reverse {
    flex-direction: row-reverse;
}

.flex_col_reverse {
    flex-direction: column-reverse;
}

.d_grid {
    display: grid;
}

.gap_x_small {
    gap: 10px;
}

.gap_small {
    gap: 20px;
}

.gap_medium {
    gap: 40px;
}

.gap_large {
    gap: 60px;
}

@media (min-width: 767px) and (max-width: 1279px) {
    .gap_x_small {
        gap: 8px;
    }

    .gap_small {
        gap: 16px;
    }

    .gap_medium {
        gap: 24px;
    }

    .gap_large {
        gap: 32px;
    }
}

@media (max-width: 767px) {
    .gap_x_small {
        gap: 2.5vw;
    }

    .gap_small {
        gap: 5vw;
    }

    .gap_medium {
        gap: 7.5vw;
    }

    .gap_large {
        gap: 10vw;
    }
}

/* Cursors  */

.cursor_pointer {
    cursor: pointer !important;
}

/* Typography  */

/* Header Tags for PC */

.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    color: #10172a;
    font-weight: 700;
}

.h1,
.h2 {
    margin-bottom: 36px !important;
}

.h3,
.h4 {
    margin-bottom: 30px !important;
}

.h5,
.h6 {
    margin-bottom: 24px !important;
}

.h1 {
    font-size: 38px;
    line-height: 45px;
    font-weight: 900;
}

.h2 {
    font-size: 32px;
    line-height: 40px;
}

.h3 {
    font-size: 28px;
    line-height: 35px;
}

.h4 {
    font-size: 24px;
    line-height: 30px;
}

.h5 {
    font-size: 20px;
    line-height: 25px;
}

.h6 {
    font-size: 16px;
    line-height: 20px;
    font-weight: 600;
}

/* Header Tags for tabs */

@media (min-width: 767px) and (max-width: 1279px) {
    .h1,
    .h2 {
        margin-bottom: 32px !important;
    }

    .h3,
    .h4 {
        margin-bottom: 26px !important;
    }

    .h5,
    .h6 {
        margin-bottom: 20px !important;
    }

    .h1 {
        font-size: 32px;
        line-height: 40px;
    }

    .h2 {
        font-size: 28px;
        line-height: 35px;
    }

    .h3 {
        font-size: 24px;
        line-height: 30px;
    }

    .h4 {
        font-size: 20px;
        line-height: 25px;
    }

    .h5 {
        font-size: 16px;
        line-height: 20px;
    }

    .h6 {
        font-size: 14px;
        line-height: 18px;
    }
}

/* Header Tags for mobiles */

@media (max-width: 767px) {
    .h1,
    .h2 {
        margin-bottom: 8.75vw !important;
    }

    .h3,
    .h4 {
        margin-bottom: 7.5vw !important;
    }

    .h5,
    .h6 {
        margin-bottom: 6.25vw !important;
    }

    .h1 {
        font-size: 10vw;
        line-height: 12.5vw;
    }

    .h2 {
        font-size: 8.75vw;
        line-height: 10.94vw;
    }

    .h3 {
        font-size: 7.5vw;
        line-height: 9.38vw;
    }

    .h4 {
        font-size: 6.25vw;
        line-height: 7.81vw;
    }

    .h5 {
        font-size: 5vw;
        line-height: 6.25vw;
    }

    .h6 {
        font-size: 4.38vw;
        line-height: 5.63vw;
    }
}

/* Margins and Paddings for PC */

.margin_all_auto {
    margin: auto !important;
}

.margin_all_large {
    margin: 60px !important;
}

.margin_all_medium {
    margin: 40px !important;
}

.margin_all_smb {
    margin: 30px !important;
}

.margin_all_small {
    margin: 20px !important;
}

.margin_all_xs {
    margin: 10px !important;
}

.margin_all_us {
    margin: 4px !important;
}

.margin_all_zero {
    margin: 0 !important;
}

.margin_top_zero {
    margin-top: 0 !important;
}

.margin_right_zero {
    margin-right: 0 !important;
}

.margin_bottom_zero {
    margin-bottom: 0 !important;
}

.margin_left_zero {
    margin-left: 0 !important;
}

.margin_top_large {
    margin-top: 60px !important;
}

.margin_right_large {
    margin-right: 60px !important;
}

.margin_bottom_large {
    margin-bottom: 60px !important;
}

.margin_left_large {
    margin-left: 60px !important;
}

.margin_top_medium {
    margin-top: 40px !important;
}

.margin_right_medium {
    margin-right: 40px !important;
}

.margin_bottom_medium {
    margin-bottom: 40px !important;
}

.margin_left_medium {
    margin-left: 40px !important;
}

.margin_top_smb {
    margin-top: 30px !important;
}

.margin_right_smb {
    margin-right: 30px !important;
}

.margin_bottom_smb {
    margin-bottom: 30px !important;
}

.margin_left_smb {
    margin-left: 30px !important;
}

.margin_top_small {
    margin-top: 20px !important;
}

.margin_right_small {
    margin-right: 20px !important;
}

.margin_bottom_small {
    margin-bottom: 20px !important;
}

.margin_left_small {
    margin-left: 20px !important;
}

.margin_top_xs {
    margin-top: 10px !important;
}

.margin_right_xs {
    margin-right: 10px !important;
}

.margin_bottom_xs {
    margin-bottom: 10px !important;
}

.margin_left_xs {
    margin-left: 10px !important;
}

.margin_top_us {
    margin-top: 4px !important;
}

.margin_right_us {
    margin-right: 4px !important;
}

.margin_bottom_us {
    margin-bottom: 4px !important;
}

.margin_left_us {
    margin-left: 4px !important;
}

.padding_all_large {
    padding: 60px !important;
}

.padding_all_medium {
    padding: 40px !important;
}

.padding_all_smb {
    padding: 30px !important;
}

.padding_all_small {
    padding: 20px !important;
}

.padding_all_xs {
    padding: 10px !important;
}

.padding_all_us {
    padding: 4px !important;
}

.padding_all_zero {
    padding: 0 !important;
}

.padding_top_zero {
    padding-top: 0 !important;
}

.padding_right_zero {
    padding-right: 0 !important;
}

.padding_bottom_zero {
    padding-bottom: 0 !important;
}

.padding_left_zero {
    padding-left: 0 !important;
}

.padding_top_large {
    padding-top: 60px !important;
}

.padding_right_large {
    padding-right: 60px !important;
}

.padding_bottom_large {
    padding-bottom: 60px !important;
}

.padding_left_large {
    padding-left: 60px !important;
}

.padding_top_medium {
    padding-top: 40px !important;
}

.padding_right_medium {
    padding-right: 40px !important;
}

.padding_bottom_medium {
    padding-bottom: 40px !important;
}

.padding_left_medium {
    padding-left: 40px !important;
}

.padding_top_smb {
    padding-top: 30px !important;
}

.padding_right_smb {
    padding-right: 30px !important;
}

.padding_bottom_smb {
    padding-bottom: 30px !important;
}

.padding_left_smb {
    padding-left: 30px !important;
}

.padding_top_small {
    padding-top: 20px !important;
}

.padding_right_small {
    padding-right: 20px !important;
}

.padding_bottom_small {
    padding-bottom: 20px !important;
}

.padding_left_small {
    padding-left: 20px !important;
}

.padding_top_xs {
    padding-top: 10px !important;
}

.padding_right_xs {
    padding-right: 10px !important;
}

.padding_bottom_xs {
    padding-bottom: 10px !important;
}

.padding_left_xs {
    padding-left: 10px !important;
}

.padding_top_us {
    padding-top: 4px !important;
}

.padding_right_us {
    padding-right: 4px !important;
}

.padding_bottom_us {
    padding-bottom: 4px !important;
}

.padding_left_us {
    padding-left: 4px !important;
}

/* Margins and Paddings for Tabs */

@media (min-width: 767px) and (max-width: 1279px) {
    /* Margins  */

    .margin_all_large {
        margin: 48px !important;
    }

    .margin_all_medium {
        margin: 32px !important;
    }

    .margin_all_smb {
        margin: 24px !important;
    }

    .margin_all_small {
        margin: 16px !important;
    }

    .margin_all_xs {
        margin: 8px !important;
    }

    .margin_all_us {
        margin: 2.5px !important;
    }

    .margin_top_large {
        margin-top: 48px !important;
    }

    .margin_right_large {
        margin-right: 48px !important;
    }

    .margin_bottom_large {
        margin-bottom: 48px !important;
    }

    .margin_left_large {
        margin-left: 48px !important;
    }

    .margin_top_medium {
        margin-top: 32px !important;
    }

    .margin_right_medium {
        margin-right: 32px !important;
    }

    .margin_bottom_medium {
        margin-bottom: 32px !important;
    }

    .margin_left_medium {
        margin-left: 32px !important;
    }

    .margin_top_smb {
        margin-top: 24px !important;
    }

    .margin_right_smb {
        margin-right: 24px !important;
    }

    .margin_bottom_smb {
        margin-bottom: 24px !important;
    }

    .margin_left_smb {
        margin-left: 24px !important;
    }

    .margin_top_small {
        margin-top: 16px !important;
    }

    .margin_right_small {
        margin-right: 16px !important;
    }

    .margin_bottom_small {
        margin-bottom: 16px !important;
    }

    .margin_left_small {
        margin-left: 16px !important;
    }

    .margin_top_xs {
        margin-top: 10px !important;
    }

    .margin_right_xs {
        margin-right: 10px !important;
    }

    .margin_bottom_xs {
        margin-bottom: 10px !important;
    }

    .margin_left_xs {
        margin-left: 10px !important;
    }

    .margin_top_us {
        margin-top: 2.5px !important;
    }

    .margin_right_us {
        margin-right: 2.5px !important;
    }

    .margin_bottom_us {
        margin-bottom: 2.5px !important;
    }

    .margin_left_us {
        margin-left: 2.5px !important;
    }

    /* Paddings  */

    .padding_all_large {
        padding: 48px !important;
    }

    .padding_all_medium {
        padding: 32px !important;
    }

    .padding_all_smb {
        padding: 24px !important;
    }

    .padding_all_small {
        padding: 16px !important;
    }

    .padding_all_xs {
        padding: 8px !important;
    }

    .padding_all_us {
        padding: 2.5px !important;
    }

    .padding_top_large {
        padding-top: 48px !important;
    }

    .padding_right_large {
        padding-right: 48px !important;
    }

    .padding_bottom_large {
        padding-bottom: 48px !important;
    }

    .padding_left_large {
        padding-left: 48px !important;
    }

    .padding_top_medium {
        padding-top: 32px !important;
    }

    .padding_right_medium {
        padding-right: 32px !important;
    }

    .padding_bottom_medium {
        padding-bottom: 32px !important;
    }

    .padding_left_medium {
        padding-left: 32px !important;
    }

    .padding_top_smb {
        padding-top: 24px !important;
    }

    .padding_right_smb {
        padding-right: 24px !important;
    }

    .padding_bottom_smb {
        padding-bottom: 24px !important;
    }

    .padding_left_smb {
        padding-left: 24px !important;
    }

    .padding_top_small {
        padding-top: 16px !important;
    }

    .padding_right_small {
        padding-right: 16px !important;
    }

    .padding_bottom_small {
        padding-bottom: 16px !important;
    }

    .padding_left_small {
        padding-left: 16px !important;
    }

    .padding_top_xs {
        padding-top: 8px !important;
    }

    .padding_right_xs {
        padding-right: 8px !important;
    }

    .padding_bottom_xs {
        padding-bottom: 8px !important;
    }

    .padding_left_xs {
        padding-left: 8px !important;
    }

    .padding_top_us {
        padding-top: 2.5px !important;
    }

    .padding_right_us {
        padding-right: 2.5px !important;
    }

    .padding_bottom_us {
        padding-bottom: 2.5px !important;
    }

    .padding_left_us {
        padding-left: 2.5px !important;
    }
}

@media (max-width: 767px) {
    /* Margins  */

    .margin_all_large {
        margin: 10vw !important;
    }

    .margin_all_medium {
        margin: 7.5vw !important;
    }

    .margin_all_smb {
        margin: 6.25vw !important;
    }

    .margin_all_small {
        margin: 5vw !important;
    }

    .margin_all_xs {
        margin: 2.5vw !important;
    }

    .margin_all_us {
        margin: 1vw !important;
    }

    .margin_top_large {
        margin-top: 10vw !important;
    }

    .margin_right_large {
        margin-right: 10vw !important;
    }

    .margin_bottom_large {
        margin-bottom: 10vw !important;
    }

    .margin_left_large {
        margin-left: 10vw !important;
    }

    .margin_top_medium {
        margin-top: 7.5vw !important;
    }

    .margin_right_medium {
        margin-right: 7.5vw !important;
    }

    .margin_bottom_medium {
        margin-bottom: 7.5vw !important;
    }

    .margin_left_medium {
        margin-left: 7.5vw !important;
    }

    .margin_top_smb {
        margin-top: 6.25 !important;
    }

    .margin_right_smb {
        margin-right: 6.25 !important;
    }

    .margin_bottom_smb {
        margin-bottom: 6.25 !important;
    }

    .margin_left_smb {
        margin-left: 6.25 !important;
    }

    .margin_top_small {
        margin-top: 5vw !important;
    }

    .margin_right_small {
        margin-right: 5vw !important;
    }

    .margin_bottom_small {
        margin-bottom: 5vw !important;
    }

    .margin_left_small {
        margin-left: 5vw !important;
    }

    .margin_top_xs {
        margin-top: 2.5vw !important;
    }

    .margin_right_xs {
        margin-right: 2.5vw !important;
    }

    .margin_bottom_xs {
        margin-bottom: 2.5vw !important;
    }

    .margin_left_xs {
        margin-left: 2.5vw !important;
    }

    .margin_top_us {
        margin-top: 1vw !important;
    }

    .margin_right_us {
        margin-right: 1vw !important;
    }

    .margin_bottom_us {
        margin-bottom: 1vw !important;
    }

    .margin_left_us {
        margin-left: 1vw !important;
    }

    /* Paddings  */

    .padding_all_large {
        padding: 10vw !important;
    }

    .padding_all_medium {
        padding: 7.5vw !important;
    }

    .padding_all_smb {
        padding: 6.25vw !important;
    }

    .padding_all_small {
        padding: 5vw !important;
    }

    .padding_all_xs {
        padding: 2.5vw !important;
    }

    .padding_all_us {
        padding: 1vw !important;
    }

    .padding_top_large {
        padding-top: 10vw !important;
    }

    .padding_right_large {
        padding-right: 10vw !important;
    }

    .padding_bottom_large {
        padding-bottom: 10vw !important;
    }

    .padding_left_large {
        padding-left: 10vw !important;
    }

    .padding_top_medium {
        padding-top: 7.5vw !important;
    }

    .padding_right_medium {
        padding-right: 7.5vw !important;
    }

    .padding_bottom_medium {
        padding-bottom: 7.5vw !important;
    }

    .padding_left_medium {
        padding-left: 7.5vw !important;
    }

    .padding_top_smb {
        padding-top: 6.25vw !important;
    }

    .padding_right_smb {
        padding-right: 6.25vw !important;
    }

    .padding_bottom_smb {
        padding-bottom: 6.25vw !important;
    }

    .padding_left_smb {
        padding-left: 6.25vw !important;
    }

    .padding_top_small {
        padding-top: 5vw !important;
    }

    .padding_right_small {
        padding-right: 5vw !important;
    }

    .padding_bottom_small {
        padding-bottom: 5vw !important;
    }

    .padding_left_small {
        padding-left: 5vw !important;
    }

    .padding_top_xs {
        padding-top: 2.5vw !important;
    }

    .padding_right_xs {
        padding-right: 2.5vw !important;
    }

    .padding_bottom_xs {
        padding-bottom: 2.5vw !important;
    }

    .padding_left_xs {
        padding-left: 2.5vw !important;
    }

    .padding_top_xs {
        padding-top: 1vw !important;
    }

    .padding_right_xs {
        padding-right: 1vw !important;
    }

    .padding_bottom_xs {
        padding-bottom: 1vw !important;
    }

    .padding_left_xs {
        padding-left: 1vw !important;
    }
}

/* ----------------------- Buttons ----------------------- */

/* Button 1 */

.button-all-dev {
    grid-template-columns: repeat(5, auto);
}

.button-all-dev > button {
    width: max-content;
}

.btn_1 {
    align-items: center;
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 0.25rem;
    box-shadow: rgba(0, 0, 0, 0.02) 0 1px 3px 0;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    display: inline-flex;
    font-family:
        system-ui,
        -apple-system,
        system-ui,
        "Helvetica Neue",
        Helvetica,
        Arial,
        sans-serif;
    font-size: 16px;
    font-weight: 600;
    justify-content: center;
    line-height: 1.25;
    margin: 0;
    min-height: 2rem;
    padding: calc(0.875rem - 7px) calc(1.5rem - 7px);
    position: relative;
    text-decoration: none;
    transition: all 250ms;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    vertical-align: baseline;
    width: auto;
    letter-spacing: 0.1rem;
}

.btn_1:hover,
.btn_1:focus {
    border-color: rgba(0, 0, 0, 0.5);
    box-shadow: rgba(0, 0, 0, 0.1) 0 4px 12px;
    color: rgba(0, 0, 0, 0.65);
}

.btn_1:hover {
    transform: translateY(-1px);
}

.btn_1:active {
    background-color: #f0f0f1;
    border-color: rgba(0, 0, 0, 0.15);
    box-shadow: rgba(0, 0, 0, 0.06) 0 2px 4px;
    color: rgba(0, 0, 0, 0.65);
    transform: translateY(0);
}

/* Button 2 */

.btn_2 {
    background: var(--input-primary-color);
    border: 1px solid var(--input-primary-color);
    border-radius: 6px;
    box-shadow: rgba(0, 0, 0, 0.1) 1px 2px 4px;
    box-sizing: border-box;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-family: nunito, roboto, proxima-nova, "proxima nova", sans-serif;
    font-size: 16px;
    font-weight: 800;
    line-height: 16px;
    min-height: 40px;
    outline: 0;
    padding: 12px 14px;
    text-align: center;
    text-rendering: geometricprecision;
    text-transform: none;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    vertical-align: middle;
    letter-spacing: 0.12rem;
}

.btn_2:hover,
.btn_2:active {
    background-color: initial;
    background-position: 0 0;
    color: var(--input-primary-color);
}

.btn_2:active {
    opacity: 0.5;
}

@media (max-width: 767px) {
    .btn_2 {
        border-radius: 1.88vw;
        box-shadow: rgba(0, 0, 0, 0.1) 1px 2px 4px;
        font-size: 4.06vw;
        line-height: 4.69vw;
        min-height: unset;
        padding: 2.81vw 3.44vw;
        letter-spacing: 0.12rem;
    }
}

/* Button 2 inversion  */

.btn_2_inv {
    background-color: initial;
    background-position: 0 0;
    color: var(--input-primary-color);
}

.btn_2_inv:hover,
.btn_2_inv:active {
    background: var(--input-primary-color);
    color: #ffffff;
}

/* Button 3 */

.btn_3 {
    align-items: center;
    appearance: none;
    background-color: #fcfcfd;
    border-radius: 4px;
    border-width: 0;
    box-shadow:
        rgba(45, 35, 66, 0.4) 0 2px 4px,
        rgba(45, 35, 66, 0.3) 0 7px 13px -3px,
        #d6d6e7 0 -3px 0 inset;
    box-sizing: border-box;
    color: #36395a;
    cursor: pointer;
    display: inline-flex;
    font-family: "JetBrains Mono", monospace;
    height: 40px;
    justify-content: center;
    line-height: 1;
    list-style: none;
    overflow: hidden;
    padding-left: 16px;
    padding-right: 16px;
    position: relative;
    text-align: left;
    text-decoration: none;
    transition:
        box-shadow 0.15s,
        transform 0.15s;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    white-space: nowrap;
    will-change: box-shadow, transform;
    font-size: 18px;
    letter-spacing: 0.08rem;
}

.btn_3:focus {
    box-shadow:
        #d6d6e7 0 0 0 1.5px inset,
        rgba(45, 35, 66, 0.4) 0 2px 4px,
        rgba(45, 35, 66, 0.3) 0 7px 13px -3px,
        #d6d6e7 0 -3px 0 inset;
}

.btn_3:hover {
    box-shadow:
        rgba(45, 35, 66, 0.4) 0 4px 8px,
        rgba(45, 35, 66, 0.3) 0 7px 13px -3px,
        #d6d6e7 0 -3px 0 inset;
    transform: translateY(-2px);
}

.btn_3:active {
    box-shadow: #d6d6e7 0 3px 7px inset;
    transform: translateY(2px);
}

/* Button 4 */

.btn_4 {
    background-color: #13aa52;
    border: 1px solid #13aa52;
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.1) 0 2px 4px 0;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    font-family:
        "Akzidenz Grotesk BQ Medium",
        -apple-system,
        BlinkMacSystemFont,
        sans-serif;
    font-size: 16px;
    font-weight: 500;
    outline: none;
    outline: 0;
    padding: 10px 25px;
    text-align: center;
    transform: translateY(0);
    transition:
        transform 150ms,
        box-shadow 150ms;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    letter-spacing: 0.15rem;
}

.btn_4:hover {
    box-shadow: rgba(0, 0, 0, 0.15) 0 3px 9px 0;
    transform: translateY(-2px);
}

@media (min-width: 768px) {
    .btn_4 {
        padding: 10px 30px;
    }
}

/* Button 5 */

.btn_5 {
    background-color: #ea4c89;
    border-radius: 8px;
    border-style: none;
    box-sizing: border-box;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-family: "Haas Grot Text R Web", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 16px;
    font-weight: 700;
    height: 48px;
    line-height: 20px;
    list-style: none;
    margin: 0;
    outline: none;
    padding: 10px 16px;
    position: relative;
    text-align: center;
    text-decoration: none;
    transition: color 100ms;
    vertical-align: baseline;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    letter-spacing: 0.1rem;
}

.btn_5:hover,
.btn_5:focus {
    background-color: #f082ac;
}

/* Button 6 */

.btn_6 {
    background-image: linear-gradient(-180deg, #37aee2 0%, #1e96c8 100%);
    border-radius: 0.5rem;
    box-sizing: border-box;
    color: #ffffff;
    display: flex;
    font-size: 16px;
    justify-content: center;
    align-items: center;
    padding: 0.6rem 1rem;
    font-weight: 700;
    text-decoration: none;
    width: 100%;
    border: 0;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    letter-spacing: 0.08rem;
}

.btn_6:hover {
    background-image: linear-gradient(-180deg, #1d95c9 0%, #17759c 100%);
}

/* Button 7 */

.btn_7 {
    align-self: center;
    background-color: #fff;
    background-image: none;
    background-position: 0 90%;
    background-repeat: repeat no-repeat;
    background-size: 4px 3px;
    border-radius: 15px 225px 255px 15px 15px 255px 225px 15px;
    border-style: solid;
    border-width: 2px;
    box-shadow: rgba(0, 0, 0, 0.2) 15px 28px 25px -18px;
    box-sizing: border-box;
    color: #41403e;
    cursor: pointer;
    display: inline-block;
    font-family: Neucha, sans-serif;
    font-size: 1rem;
    line-height: 23px;
    outline: none;
    padding: 0.75rem;
    text-decoration: none;
    transition: all 235ms ease-in-out;
    border-bottom-left-radius: 15px 255px;
    border-bottom-right-radius: 225px 15px;
    border-top-left-radius: 255px 15px;
    border-top-right-radius: 15px 225px;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    letter-spacing: 0.1rem;
    font-weight: 500;
}

.btn_7:hover {
    box-shadow: rgba(0, 0, 0, 0.3) 2px 8px 8px -5px;
    transform: translate3d(0, 2px, 0);
}

.btn_7:focus {
    box-shadow: rgba(0, 0, 0, 0.3) 2px 8px 4px -6px;
}

/* Button 8 */

.btn_8 {
    background: linear-gradient(to bottom right, #ef4765, #ff9a5a);
    border: 0;
    border-radius: 12px;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-family: -apple-system, system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: 2.5;
    outline: transparent;
    padding: 0 1rem;
    text-align: center;
    text-decoration: none;
    transition: box-shadow 0.2s ease-in-out;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    white-space: nowrap;
}

.btn_8:not([disabled]):focus {
    box-shadow:
        0 0 0.25rem rgba(0, 0, 0, 0.5),
        -0.125rem -0.125rem 1rem rgba(239, 71, 101, 0.5),
        0.125rem 0.125rem 1rem rgba(255, 154, 90, 0.5);
}

.btn_8:not([disabled]):hover {
    box-shadow:
        0 0 0.25rem rgba(0, 0, 0, 0.5),
        -0.125rem -0.125rem 1rem rgba(239, 71, 101, 0.5),
        0.125rem 0.125rem 1rem rgba(255, 154, 90, 0.5);
}

/* Button 9 */

.btn_9 {
    background: linear-gradient(to right, #fa7415 0%, #d946ef 51%, #fa7415 100%);
    border-radius: 50px;
    padding: 10px 32px;
    gap: 10px;
    border: none;
    height: 48px;
    font-weight: 600;
    letter-spacing: 0.08rem;
    color: #fff;
    font-size: 20px;
    line-height: 24px;
    outline: none;
    cursor: pointer;
    transition: all 300ms ease-in;
    transition: 0.5s;
    background-size: 200% auto;
}

.btn_9:hover {
    opacity: 0.75;
    background-position: right center;
    transition: 0.5s;
}

/* Button 10 */

.btn_10 {
    appearance: none;
    background-color: #ffffff;
    border-width: 0;
    box-sizing: border-box;
    color: #000000;
    cursor: pointer;
    display: inline-block;
    font-family: Clarkson, Helvetica, sans-serif;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 1em;
    margin: 0;
    opacity: 1;
    outline: 0;
    padding: 1.5em 2.2em;
    position: relative;
    text-align: center;
    text-decoration: none;
    text-rendering: geometricprecision;
    text-transform: uppercase;
    transition:
        opacity 300ms cubic-bezier(0.694, 0, 0.335, 1),
        background-color 100ms cubic-bezier(0.694, 0, 0.335, 1),
        color 100ms cubic-bezier(0.694, 0, 0.335, 1);
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    vertical-align: baseline;
    white-space: nowrap;
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    box-sizing: border-box;
}

.btn_10:before {
    animation: opacityFallbackOut 0.5s step-end forwards;
    backface-visibility: hidden;
    background-color: #ebebeb;
    clip-path: polygon(-1% 0, 0 0, -25% 100%, -1% 100%);
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    transform: translateZ(0);
    transition:
        clip-path 0.5s cubic-bezier(0.165, 0.84, 0.44, 1),
        -webkit-clip-path 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    width: 100%;
    border: 1px solid transparent;
    border-radius: 6px;
    box-sizing: border-box;
}

.btn_10:hover:before {
    animation: opacityFallbackIn 0s step-start forwards;
    clip-path: polygon(0 0, 101% 0, 101% 101%, 0 101%);
    box-sizing: border-box;
}

.btn_10:after {
    background-color: #ffffff;
    box-sizing: border-box;
}

.btn_10:hover {
    border: 1px solid #ebebeb;
    box-sizing: border-box;
}

.btn_10 span {
    z-index: 1;
    position: relative;
}

/* ------------------------- Inputs , Textareas and Selects -------------------------  */

.input_custom,
.select_custom,
.textarea_custom {
    position: relative;
}

.select_custom,
.input_custom {
    flex-shrink: 0;
    height: 40px;
}

.input_custom input,
.textarea_custom textarea,
.select_custom select {
    width: 100%;
    height: 100%;
    outline: none;
    border: 1px solid var(--input-primary-color);
    padding: 10px 14px 6px 15px;
    font-size: 15px;
    font-weight: 600;
    color: var(--input-value-color);
    letter-spacing: 0.1rem;
    box-shadow: rgba(0, 0, 0, 0.1) 4px 3px 4px;
}

.input_custom span,
.textarea_custom span,
.select_custom span,
.select_custom select:disabled span {
    position: absolute;
    pointer-events: none;
    left: 14px;
    top: 10px;
    font-size: 15px;
    color: var(--input-label-color);
    opacity: 0.8;
    letter-spacing: 0.12rem;
    transition: all 0.3s ease-in-out;
    padding-right: 15px;
}

.input_custom input:valid ~ span,
.input_custom input:focus ~ span,
.input_custom .valid_value ~ span,
.textarea_custom .valid_value ~ span,
.textarea_custom textarea:focus ~ span,
.select_custom .valid_value ~ span,
.select_custom select:focus ~ span {
    left: 22px;
    top: -5px;
    font-size: 11px;
    line-height: 13px;
    background: var(--input-primary-color);
    opacity: 1;
    color: white;
    letter-spacing: 0.1rem;
    font-weight: 500;
    display: block;
    border-radius: 3px;
    padding: 0px 6px;
}

.input_custom_1 input:valid ~ span,
.input_custom_1 input:focus ~ span,
.input_custom_1 .valid_value ~ span,
.textarea_custom_1 .valid_value ~ span,
.textarea_custom_1 textarea:focus ~ span,
.select_custom_1 .valid_value ~ span,
.select_custom_1 select:focus ~ span {
    background: white;
    border: 1px solid var(--input-primary-color);
    color: var(--input-primary-color);
}

.input_custom_2 input:valid ~ span,
.input_custom_2 input:focus ~ span,
.input_custom_2 .valid_value ~ span,
.textarea_custom_2 .valid_value ~ span,
.textarea_custom_2 textarea:focus ~ span,
.select_custom_2 .valid_value ~ span,
.select_custom_2 select:focus ~ span {
    background: white;
    border-left: 1px solid var(--input-primary-color);
    border-right: 1px solid var(--input-primary-color);
    border-radius: 0px;
    color: var(--input-primary-color);
}

.textarea_custom textarea::-webkit-scrollbar,
.select_custom select::-webkit-scrollbar {
    width: 12px;
    background: #e8e9ed;
    border-radius: 8px;
}

.textarea_custom textarea::-webkit-scrollbar-thumb,
.select_custom select::-webkit-scrollbar-thumb {
    background-color: var(--input-primary-color-70);
    border: 2px solid rgba(0, 0, 0, 0);
    border-radius: 8px;
    background-clip: content-box !important;
}

.select_custom select {
    margin: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;

    background-image: linear-gradient(45deg, transparent 50%, var(--input-primary-color-70) 50%),
        linear-gradient(135deg, var(--input-primary-color-70) 50%, transparent 50%),
        linear-gradient(to right, var(--input-primary-color-70), var(--input-primary-color-70));
    background-position:
        calc(100% - 20px) 17px,
        calc(100% - 14px) 17px,
        calc(100% - 40px) 9px;
    background-size:
        6px 6px,
        6px 6px,
        1px 20px;
    background-repeat: no-repeat;
}

.select_custom select:focus {
    background-image: linear-gradient(45deg, var(--input-primary-color-70) 50%, transparent 50%),
        linear-gradient(135deg, transparent 50%, var(--input-primary-color-70) 50%),
        linear-gradient(to right, var(--input-primary-color-70), var(--input-primary-color-70));
    background-position:
        calc(100% - 14px) 15px,
        calc(100% - 20px) 15px,
        calc(100% - 40px) 9px;
    background-size:
        6px 6px,
        6px 6px,
        1px 20px;
    background-repeat: no-repeat;
}

.select_custom select option:not(:first-child) {
    background-color: rgba(0, 0, 0, 0.05);
}

.select_custom select option:first-child {
    background-color: rgba(0, 0, 0, 0.2);
}

/* Date Input  */

.date_input input[type="date"] {
    width: 250px;
}

.date_input input[type="date"]::-webkit-datetime-edit-fields-wrapper {
    opacity: 0;
    text-transform: uppercase;
}

.date_input input[type="date"]:focus::-webkit-datetime-edit-fields-wrapper,
.date_input input[type="date"]:valid::-webkit-datetime-edit-fields-wrapper {
    opacity: 1;
    color: var(--input-value-color);
    transition: all 0.3s 0.3s ease-in-out;
}

.date_input input[type="date"]:invalid:-webkit-datetime-edit-fields-wrapper {
    transition-duration: 0s;
    transition-delay: 0s;
    opacity: 0;
}

.date_input input[type="date"]:focus::-webkit-datetime-edit-month-field:focus,
.date_input input[type="date"]:focus::-webkit-datetime-edit-day-field:focus,
.date_input input[type="date"]:focus::-webkit-datetime-edit-year-field:focus {
    background-color: transparent;
    color: var(--input-value-color);
}

.date_input input[type="date"]::-webkit-calendar-picker-indicator {
    width: 26px;
    height: 26px;
    margin-top: -5px;
    opacity: 0.5;
    transition: all 0.3s ease-in-out;
}

.date_input input[type="date"]:focus::-webkit-calendar-picker-indicator,
.date_input input[type="date"]:valid::-webkit-calendar-picker-indicator {
    width: 22px;
    height: 22px;
    margin-top: 0;
    opacity: 1;
}

/* Number Input */

.number_input input[type="number"]::-webkit-inner-spin-button,
.number_input input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}

/* File Type Input */

.file_input input[type="file"]::-webkit-file-upload-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
    display: none;
}

.sample_for_width {
    width: 450px;
    margin: auto;
}
