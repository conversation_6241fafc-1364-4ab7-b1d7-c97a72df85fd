import { ADD_DEFINE_ROLES, ADD_GOALS, ADD_USER_ABOUT, USER_SIGN_UP } from "@/redux-types";

const initialState = {
    defineRoleData: {},
    userAboutData: {},
    signUpData: {},
    goalsData: {},
};

const DefineRoleReducer = (state = initialState, action) => {
    switch (action.type) {
        case ADD_DEFINE_ROLES:
            return {
                ...state,
                defineRoleData: action.payload,
            };
        case ADD_USER_ABOUT:
            return {
                ...state,
                userAboutData: action.payload,
            };
        case ADD_GOALS:
            return {
                ...state,
                goalsData: action.payload,
            };
        case USER_SIGN_UP:
            return {
                ...state,
                signUpData: action.payload,
            };
        default:
            return state;
    }
};

export default DefineRoleReducer;
