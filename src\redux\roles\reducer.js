import { ADD_NEW_ROLE, FETCH_ROLES_REQ, GET_ROLES, UPDATE_ROLE } from "@/redux-types";

const initialState = {
    rolesList: [],
    isLoading: true,
    error: null,
    addRoleData: {},
    updateRoleData: {},
};

const RoleReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_ROLES_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_ROLES:
            return {
                ...state,
                rolesList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case ADD_NEW_ROLE:
            return {
                addRoleData: action.payload,
            };
        case UPDATE_ROLE:
            return {
                updateRoleData: action.payload,
            };
        default:
            return state;
    }
};

export default RoleReducer;
