import { colorsList } from "@/data/colors";
import { cn } from "@/lib/utils";
import QuizContainer from "@/pages/dashboard/quizzes/components/container";
import { DndContext, useDraggable, useDroppable } from "@dnd-kit/core";
import { motion } from "framer-motion";
import { useState } from "react";

const MatchTheFollowing = (props) => {
    const content = props.content;
    const [optionList, setOptionList] = useState(content?.matchOptions);
    const [answerList, setAnswerList] = useState(content?.answerKey ?? Array(content?.matchOptions.length).fill({}));

    function handleDragEnd(event) {
        const { active, over } = event;
        if (!over) return;
        const value = active.data.current.label;
        const container = over.id;
        const option = content?.matchOptions?.find((dt, i) => (value ? dt.text == value : active.id == i));

        if (container === "option") {
            const exists = optionList.find((arr) => arr?.text === option.text);
            if (exists) return;
            const newDraggableOptions = [...optionList, option];
            setOptionList(newDraggableOptions);
            setAnswerList(answerList?.filter((dt) => dt?.text !== option.text));
            return;
        }

        const exists = answerList.find((arr) => arr?.text === option.text);
        const newIndex = Number(container.split("-")[1]);
        const newBlanks = [...answerList];
        if (exists) {
            const findIndex = answerList.findIndex((arr) => arr?.text === option.text);
            newBlanks[newIndex] = exists;
            newBlanks[findIndex] = answerList[newIndex];
        } else {
            newBlanks[newIndex] = option;
        }
        setAnswerList(newBlanks);
        setOptionList(optionList?.filter((dt) => dt?.text !== option.text));
    }

    return (
        <QuizContainer {...props} type="match_the_following">
            <div className="tw-px-10 tw-py-7">
                <div className="tw-grid tw-h-full tw-min-h-[20rem] tw-w-full tw-grid-cols-1 tw-gap-6">
                    <DndContext onDragEnd={handleDragEnd}>
                        <div className="tw-grid tw-grid-cols-4 tw-grid-rows-[1fr_minmax(60px,_auto)] tw-gap-x-6">
                            {content?.matchOptions?.map((option, index) => (
                                <motion.div
                                    key={index}
                                    initial={{
                                        opacity: 0.5,
                                        scale: 0.2,
                                    }}
                                    whileInView={{
                                        scale: 1,
                                        opacity: 1,
                                    }}
                                    transition={{
                                        duration: 0.25,
                                        delay: index * 0.1,
                                        ease: "easeInOut",
                                    }}
                                    className="tw-h-full tw-rounded-lg tw-bg-teal-200 tw-p-2 tw-text-center tw-font-mono tw-text-[40px] tw-leading-[40px] tw-text-white"
                                    style={{
                                        color: content?.styles?.question?.color,
                                        fontFamily: content?.styles?.question?.fontFamily,
                                        fontSize: content?.styles?.question?.fontSize,
                                        lineHeight: 1,
                                        backgroundColor: content?.styles?.question?.backgroundColor,
                                        borderColor: content?.styles?.question?.borderColor,
                                        borderWidth: content?.styles?.question?.borderWidth,
                                        borderStyle: content?.styles?.question?.borderStyle,
                                    }}
                                >
                                    {option?.imageLabel && (
                                        <img
                                            src={option?.imageLabel}
                                            alt=""
                                            className="tw-h-40 tw-w-full tw-object-contain"
                                        />
                                    )}
                                    {option?.label}
                                </motion.div>
                            ))}
                            {content?.matchOptions.map((ans, idx) => {
                                return (
                                    <DropZone key={idx} current={idx} blanks={answerList}>
                                        <div
                                            className="tw-h-full tw-w-full tw-rounded-lg tw-border tw-border-dashed tw-border-gray-600"
                                            style={{
                                                backgroundColor: colorsList[idx],
                                                color: content?.styles?.selected?.color,
                                                fontSize: content?.styles?.selected?.fontSize,
                                            }}
                                        />
                                    </DropZone>
                                );
                            })}
                        </div>
                        <div className="tw-flex tw-min-w-16 tw-items-center tw-justify-center tw-gap-2">
                            <OptionDropZone>
                                {optionList.map((opt, idx) => {
                                    return (
                                        <DropItem key={idx} index={`option-${idx}`} label={opt?.text}>
                                            <div
                                                style={{
                                                    color: content?.styles?.answer?.color,
                                                    fontFamily: content?.styles?.answer?.fontFamily,
                                                    fontSize: content?.styles?.answer?.fontSize,
                                                    lineHeight: 1,
                                                    borderColor: content?.styles?.answer?.borderColor,
                                                    borderWidth: content?.styles?.answer?.borderWidth,
                                                    borderStyle: content?.styles?.answer?.borderStyle,
                                                    backgroundColor: colorsList[Number(opt.id) - 1],
                                                }}
                                                className="tw-flex tw-h-fit tw-w-fit tw-items-center tw-justify-center tw-rounded-lg tw-border-gray-600 tw-p-2 tw-font-mono tw-text-[20px] tw-leading-[20px]"
                                            >
                                                {opt?.imageText ? (
                                                    <img
                                                        src={opt?.imageText}
                                                        alt=""
                                                        className="tw-h-20 tw-w-full tw-select-none tw-object-contain"
                                                    />
                                                ) : (
                                                    opt?.text
                                                )}
                                                {/* {opt?.text} */}
                                            </div>
                                        </DropItem>
                                    );
                                })}
                            </OptionDropZone>
                        </div>
                    </DndContext>
                </div>
            </div>
        </QuizContainer>
    );
};

export default MatchTheFollowing;

function OptionDropZone({ children }) {
    const { setNodeRef } = useDroppable({
        id: "option",
    });

    return (
        <div
            ref={setNodeRef}
            className="tw-relative tw-z-50 tw-flex tw-min-h-[3rem] tw-w-full tw-min-w-16 tw-cursor-grab tw-items-center tw-justify-center tw-gap-6 tw-rounded-xl tw-border tw-p-1"
        >
            {children}
        </div>
    );
}

function DropItem({ index, label, children }) {
    const { attributes, listeners, setNodeRef, transform } = useDraggable({
        id: index,
        data: { label },
    });

    return (
        <motion.div
            initial={{
                opacity: 0.5,
                scale: 0.2,
            }}
            whileInView={{
                scale: 1,
                opacity: 1,
            }}
            transition={{
                duration: 0.25,
                delay: index * 0.1,
                ease: "easeInOut",
            }}
            ref={setNodeRef}
            style={{
                x: transform?.x,
                y: transform?.y,
            }}
            {...listeners}
            {...attributes}
        >
            {children}
        </motion.div>
    );
}

function DropZone({ blanks, current }) {
    const { setNodeRef } = useDroppable({
        id: `answer-${current}`,
    });

    return (
        <div
            ref={setNodeRef}
            className="tw-mr-1 tw-inline-block tw-min-h-8 tw-min-w-20 tw-rounded-md tw-border-2 tw-border-dashed tw-border-black tw-px-3 tw-py-0.5 !tw-text-[40px] !tw-leading-[40px]"
        >
            {blanks[Number(current)]?.text ? (
                <DropItem index={Number(current)} label={blanks[Number(current)]?.text}>
                    <div
                        className={cn(
                            "tw-flex tw-h-fit tw-w-full tw-cursor-grab tw-items-center tw-justify-center tw-rounded-lg tw-border-gray-600 tw-font-mono tw-text-[20px] tw-leading-[20px]",
                            blanks[Number(current)]?.imageText ? "tw-p-2" : "tw-px-7 tw-py-2",
                        )}
                        style={{ backgroundColor: colorsList[Number(blanks[Number(current)].id) - 1] }}
                    >
                        {blanks[Number(current)]?.imageText ? (
                            <img
                                src={blanks[Number(current)]?.imageText}
                                alt=""
                                className="tw-h-20 tw-w-full tw-select-none tw-object-contain"
                            />
                        ) : (
                            blanks[Number(current)]?.text
                        )}
                    </div>
                </DropItem>
            ) : (
                blanks[Number(current)]?.text
            )}
        </div>
    );
}
