import {
    FETCH_LEARNING_PATH_REQ,
    GET_LEARNING_PATH,
    VIEW_LEARNING_PATH_DATA,
    VIEW_LEARNING_PATH_REQ,
} from "@/redux-types";

const initialState = {
    LearningPaths: [],
    isLoading: true,
    viewData: {},
    error: null,
};

const LearningPathReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_LEARNING_PATH_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_LEARNING_PATH:
            return {
                ...state,
                LearningPaths: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case VIEW_LEARNING_PATH_REQ:
            return {
                ...state,
                payload: action.payload,
                isLoading: true,
            };
        case VIEW_LEARNING_PATH_DATA:
            return {
                ...state,
                viewData: action.payload,
                isLoading: false,
                error: action.payload,
            };
        default:
            return state;
    }
};

export default LearningPathReducer;
