import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { Calendar, Clock, Edit, MapPinHouse, Timer, Trash, Video } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import CreateNewZoomClass from "./CreateNewZoomClass";
import { toast } from "sonner";

const ChapterZoomClasses = () => {
    const navigate = useNavigate();
    const params = useParams();
    const [dataList, setDataList] = useState([]);
    const [editData, setEditData] = useState(null);
    const [open, setOpen] = useState(false);
    const [openAlert, setOpenAlert] = useState(false);

    useEffect(() => {
        getSchdedules();
    }, []);

    const getSchdedules = async () => {
        if (localStorage.getItem("is_trainer") == "true") {
            await tanstackApi
                .get("chapter-class/list-trainer")
                .then((res) => {
                    setDataList(res?.data?.data?.filter((dt) => dt?.chapter_id == Number(params?.chapter_id)));
                })
                .catch((err) => {
                    setDataList([]);
                });
        } else {
            await tanstackApi
                .get("chapter-class/list")
                .then((res) => {
                    setDataList(res?.data?.data?.filter((dt) => dt?.chapter_id == Number(params?.chapter_id)));
                })
                .catch((err) => {
                    setDataList([]);
                });
        }
    };

    const onAddClassSchedule = () => {
        setEditData(null);
        setOpen(true);
    };

    const onEditClassSchedule = (data) => {
        setEditData(data);
        setOpen(true);
    };

    const onDelete = (data) => {
        setEditData(data);
        setOpenAlert(true);
    };

    const onDeleteSchedule = async () => {
        await tanstackApi
            .delete(`chapter-class/delete/${editData?.id}`)
            .then((res) => {
                getSchdedules();
                toast.success("Schedule Deleted", {
                    description: res?.data?.message,
                });
            })
            .catch((err) => {
                setDataList([]);
                toast.error("Something went wrong!", {
                    description: err?.response?.data?.message,
                });
            });
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Schedule, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible. Selected chapter schedule will be deleted permanantly.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDeleteSchedule}>Yes Delete!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <CreateNewZoomClass
                open={open}
                setOpen={setOpen}
                editData={editData}
                getSchdedules={getSchdedules}
                params={params}
            />
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href={`/dashboard/view-course/${params?.course_id}`}>
                                View Course
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{params?.chapter_title}</BreadcrumbPage>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Online Classes</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <div className="tw-space-x-2">
                    <Button
                        onClick={() => navigate(`/dashboard/view-course/${params?.course_id}`)}
                        variant="outline"
                        className="tw-px-2 tw-py-1"
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button className="tw-px-2 tw-py-1" onClick={onAddClassSchedule}>
                        <i className="fa-solid fa-plus"></i> New Online Class
                    </Button>
                </div>
            </div>
            <div className="tw-mt-5 tw-grid tw-grid-cols-4 tw-items-start tw-gap-5">
                {dataList.length == 0 ? (
                    <div className="tw-col-span-4 tw-flex tw-h-40 tw-items-center tw-justify-center tw-rounded-xl tw-border tw-p-1">
                        No Online Classes Found
                    </div>
                ) : (
                    dataList?.map((schedule, index) => (
                        <div key={index} className="tw-rounded-xl tw-border-[1px] tw-p-1">
                            <div className="tw-p-2">
                                <div className="tw-flex tw-items-center tw-gap-2">
                                    <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2">{schedule?.topic}</h2>
                                </div>
                                <p className="tw-mt-2 tw-line-clamp-3">{schedule?.chapter_discription}</p>
                                <div className="tw-mt-1 tw-space-y-2">
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        {schedule?.meet_type == "zoom" ? (
                                            <Badge className={"tw-flex tw-items-center tw-gap-2"} variant={""}>
                                                <Video size={16} /> Zoom Meet
                                            </Badge>
                                        ) : schedule?.meet_type == "google-meet" ? (
                                            <Badge className={"tw-flex tw-items-center tw-gap-2"} variant={""}>
                                                <Video size={16} /> Google Meet
                                            </Badge>
                                        ) : (
                                            <Badge className={"tw-flex tw-items-center tw-gap-2"} variant={"outline"}>
                                                <MapPinHouse size={16} /> Offline
                                            </Badge>
                                        )}
                                    </span>{" "}
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Calendar size={16} /> {moment(schedule?.date).format("LL")}
                                    </span>{" "}
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Clock size={16} />{" "}
                                        {`${moment(schedule?.start_time, "HH:mm:ss").format("h:mm A")} - ${moment(
                                            schedule?.end_time,
                                            "HH:mm:ss",
                                        ).format("h:mm A")}`}
                                    </span>{" "}
                                    <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                        <Timer size={16} /> {schedule?.duration} minutes
                                    </span>{" "}
                                </div>
                                <div className="tw-mt-3 tw-grid tw-grid-cols-3 tw-gap-2">
                                    {schedule?.type == "online" ? (
                                        <a
                                            href={schedule?.location}
                                            target="_blank"
                                            className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-2 tw-text-sm hover:tw-bg-slate-100"
                                        >
                                            <Video size={16} strokeWidth={2} aria-hidden="true" />
                                            Join Class
                                        </a>
                                    ) : (
                                        <div></div>
                                    )}
                                    <div
                                        onClick={() => onDelete(schedule)}
                                        className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-2 tw-text-sm hover:tw-bg-slate-100"
                                    >
                                        <Trash size={16} strokeWidth={2} aria-hidden="true" />
                                        Delete
                                    </div>
                                    <div
                                        onClick={() => onEditClassSchedule(schedule)}
                                        className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-2 tw-text-sm hover:tw-bg-slate-100"
                                    >
                                        <Edit size={16} strokeWidth={2} aria-hidden="true" />
                                        Edit
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default ChapterZoomClasses;
