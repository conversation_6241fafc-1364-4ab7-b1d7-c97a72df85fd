import * as Actions from "@/redux-types";
import { imageResponse, imageUpload } from "@/redux/image-upload/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* uploadFile(data) {
    const imageRequestPost = yield axios
        .post("https://lms.infoware.xyz/api/common/upload-course-file", data.payload, {
            headers: {
                "Content-Type": "multipart/form-data",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (imageRequestPost) {
        if (imageRequestPost?.success) {
            yield put(imageUpload(imageRequestPost));
            yield put(imageResponse(imageRequestPost));
        }
    }
}

export function* ImageUploadWatcher() {
    yield takeEvery(Actions.UPLOAD_FILE_CONTAINER, uploadFile);
}

export default function* ImageUploadSaga() {
    yield all([fork(ImageUploadWatcher)]);
}
