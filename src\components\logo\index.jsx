import { useSidebar } from "@/components/ui/sidebar";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { HiBookOpen } from "react-icons/hi";
import { Link } from "react-router-dom";

export default function Logo() {
    const { open } = useSidebar();
    const [orgData, setOrgData] = useState(null);
    const [showLogo, setShowLogo] = useState(false);
    useEffect(() => {
        if (localStorage.getItem("org_data") !== undefined) {
            setOrgData(JSON.parse(localStorage.getItem("org_data")));
        }
    }, []);
    return (
        <div className="isoLogoWrapper">
            {!open ? (
                <div>
                    <Link to="/dashboard">
                        <img
                            style={{ height: "30px" }}
                            src={orgData?.about?.company_logo || "/assets/company-logo.png"}
                        />
                    </Link>
                </div>
            ) : (
                <motion.div
                    className="org_logo"
                    initial={{ x: "-50%", opacity: 0.5 }}
                    animate={{ x: "0%", opacity: 1 }}
                    transition={{
                        duration: 0.5,
                        ease: "linear",
                        type: "spring",
                        stiffness: 100,
                    }}
                >
                    <div className="img">
                        {showLogo === false ? (
                            <>
                                <img
                                    src={orgData?.about?.company_logo || "/assets/company-logo.png"}
                                    alt=""
                                    style={{
                                        height: "40px",
                                    }}
                                    onError={() => {
                                        setShowLogo(true);
                                    }}
                                />
                            </>
                        ) : (
                            <>
                                <HiBookOpen color="white" size={50} />
                            </>
                        )}
                    </div>
                </motion.div>
            )}
        </div>
    );
}
