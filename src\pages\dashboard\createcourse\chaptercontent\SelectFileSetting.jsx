import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useGetHomeworkList } from "@/react-query/homework";
import { useGetAllInteractions } from "@/react-query/interactions";
import { useGetQuizList } from "@/react-query/quizz";
import { CalendarClock, CircleHelp, Layers2, Package, Radar, UserRoundPen } from "lucide-react";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { Link } from "react-router-dom";

const SelectFileSetting = ({ setContentData, contentData }) => {
    const level = localStorage.getItem("level");
    const loginToken = localStorage.getItem("login_token");

    const quizListData = useGetQuizList();
    const homeworkListData = useGetHomeworkList({
        is_assignment: false,
        is_public: level == "levelOne",
    });
    const assignmentListData = useGetHomeworkList({
        is_assignment: true,
        is_public: level == "levelOne",
    });
    const interactionListData = useGetAllInteractions({ limit: 10, offset: 0 });

    const [selectedHomwork, setSelectedHomwork] = useState(contentData?.homework);
    const [selectedQuiz, setSelectedQuiz] = useState(contentData?.quiz);
    const [selectedInteraction, setSelectedInteraction] = useState(contentData?.interaction);

    const quizList = useMemo(() => {
        return quizListData.isSuccess && contentData?.content_type == "QUIZ" ? quizListData?.data?.data : [];
    }, [quizListData, contentData?.content_type]);
    const homeworkList = useMemo(() => {
        if (contentData?.content_type == "HOMEWORK" && homeworkListData.isSuccess) {
            return homeworkListData?.data?.data;
        }
        if (contentData?.content_type == "ASSIGNMENT" && assignmentListData.isSuccess) {
            return assignmentListData?.data?.data;
        }
        return [];
    }, [homeworkListData, assignmentListData, contentData?.content_type]);
    const interactionsList = useMemo(() => {
        return interactionListData.isSuccess && contentData?.content_type == "INTERACTIONS"
            ? interactionListData?.data?.data
            : [];
    }, [interactionListData, contentData?.content_type]);

    useEffect(() => {
        if (contentData?.content_type == "ASSIGNMENT" || contentData?.content_type == "HOMEWORK") {
            setContentData({
                ...contentData,
                homework: selectedHomwork,
                homework_id: selectedHomwork?.id,
            });
        }
    }, [selectedHomwork, contentData?.content_type]);

    useEffect(() => {
        if (contentData?.content_type == "QUIZ") {
            setContentData({
                ...contentData,
                quiz: selectedQuiz,
                quiz_id: selectedQuiz?.id,
            });
        }
    }, [selectedQuiz, contentData?.content_type]);

    useEffect(() => {
        if (contentData?.content_type == "INTERACTIONS") {
            setContentData({
                ...contentData,
                interaction: selectedInteraction,
                interaction_id: selectedInteraction?.id,
            });
        }
    }, [selectedInteraction, contentData?.content_type]);

    return (
        <div className="tw-mt-4">
            {contentData?.content_type == "QUIZ" && (
                <div>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div>
                            <Label>Quiz List :-</Label>
                            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">Select an quiz from below list</p>
                        </div>
                        <div>
                            <Button variant="add" className="tw-rounded-xl" asChild>
                                <Link to="/dashboard/quiz-create">
                                    <i className="fa-solid fa-plus"></i> Create quiz
                                </Link>
                            </Button>
                        </div>
                    </div>
                    <div className="custom_scrollbar tw-mt-2 tw-grid tw-max-h-[30vw] tw-grid-cols-3 tw-gap-3 tw-overflow-y-auto tw-pr-2">
                        {quizList?.map((quiz, idx) => (
                            <div
                                key={idx}
                                className={`tw-flex tw-cursor-pointer tw-flex-col tw-gap-2 tw-rounded-xl tw-border-[1px] tw-p-[5px] ${selectedQuiz?.id == quiz?.id && "selected_quiz"}`}
                                onClick={() => setSelectedQuiz(quiz)}
                            >
                                <div className="tw-h-[110px] tw-overflow-hidden tw-rounded-lg tw-shadow-sm">
                                    <img
                                        src={quiz?.thumbnail_img}
                                        className="tw-h-full tw-w-full tw-object-cover"
                                        alt=""
                                    />
                                </div>
                                <div className="tw-p-1">
                                    <Label className="tw-flex tw-items-center tw-gap-1 tw-font-normal">
                                        {quiz?.title}
                                    </Label>
                                    <div className="tw-mt-3 tw-items-center tw-space-y-1">
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                            <Package size={16} /> {quiz?.max_points} Points
                                        </span>
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                            <UserRoundPen size={16} /> {quiz?.trainer_name}
                                        </span>
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                            <CircleHelp size={16} /> {quiz?.components?.length} Questions
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
            {(contentData?.content_type == "ASSIGNMENT" || contentData?.content_type == "HOMEWORK") && (
                <div>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div>
                            <Label>{contentData?.content_type} List :-</Label>
                            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">
                                Select an {contentData?.content_type} from below list
                            </p>
                        </div>
                        <div>
                            <Button asChild variant="add" className="tw-rounded-xl">
                                <Link
                                    to={`/dashboard/${contentData?.content_type == "ASSIGNMENT" ? "project-assignment" : "homework"}?type=create`}
                                >
                                    <i className="fa-solid fa-plus"></i> Create{" "}
                                    {contentData?.content_type?.toLowerCase()}
                                </Link>
                            </Button>
                        </div>
                    </div>
                    <div className="custom_scrollbar tw-mt-2 tw-grid tw-max-h-[30vw] tw-grid-cols-3 tw-gap-3 tw-overflow-y-auto tw-pr-2">
                        {homeworkList?.map((homwrk, idx) => (
                            <div
                                key={idx}
                                className={`tw-flex tw-cursor-pointer tw-flex-col tw-gap-2 tw-rounded-xl tw-border-[1px] tw-p-[5px] tw-transition-all ${selectedHomwork?.id == homwrk?.id && "selected_homework"}`}
                                onClick={() => setSelectedHomwork(homwrk)}
                            >
                                <div className="tw-p-1">
                                    <h1 className="tw-text-md tw-mb-2 tw-font-semibold">{homwrk?.homework_title}</h1>
                                    <p className="tw-line-clamp-3 tw-text-sm tw-font-normal">{homwrk?.description}</p>
                                    <div className="tw-mt-3 tw-items-center tw-space-y-1">
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                            <Package size={16} /> {homwrk?.homework_points} Points
                                        </span>
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                            <CalendarClock size={16} /> {moment(homwrk?.submission_date).format("LL")}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
            {contentData?.content_type == "INTERACTIONS" && (
                <div>
                    <div className="tw-flex tw-items-center tw-justify-between">
                        <div>
                            <Label>{contentData?.content_type} List :-</Label>
                            <p className="tw-mb-4 tw-text-sm tw-text-slate-400">
                                Select an {contentData?.content_type} from below list
                            </p>
                        </div>
                        <div>
                            <Button variant="add" className="tw-rounded-xl" asChild>
                                <Link
                                    to={`https://lms-course-builder.vercel.app/dashboard/interactions/create?token=${loginToken}`}
                                    target="_blank"
                                >
                                    <i className="fa-solid fa-plus"></i> Create interaction
                                </Link>
                            </Button>
                        </div>
                    </div>
                    <div className="custom_scrollbar tw-mt-2 tw-grid tw-max-h-[30vw] tw-grid-cols-3 tw-gap-3 tw-overflow-y-auto tw-pr-2">
                        {interactionsList?.map((interctn, idx) => (
                            <div
                                key={idx}
                                className={`tw-flex tw-cursor-pointer tw-flex-col tw-gap-2 tw-rounded-xl tw-border-[1px] tw-p-[5px] tw-transition-all ${selectedInteraction?.id == interctn?.id && "selected_homework"}`}
                                onClick={() => setSelectedInteraction(interctn)}
                            >
                                <div className="tw-p-1">
                                    <h1 className="tw-text-md tw-mb-2 tw-font-semibold">{interctn?.title}</h1>
                                    <p className="tw-line-clamp-3 tw-text-sm tw-font-normal">{interctn?.description}</p>
                                    <div className="tw-mt-3 tw-items-center tw-space-y-1">
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                            <Layers2 size={16} /> {interctn?.category}
                                        </span>
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-xs tw-font-normal">
                                            <Radar size={16} /> {interctn?.type}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SelectFileSetting;
