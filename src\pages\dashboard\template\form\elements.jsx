import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import ColorInput from "@/pages/dashboard/template/form/color-input";
import { useState } from "react";

const elementStyles = [
    {
        type: "color",
        name: "color",
    },
    {
        type: "color",
        name: "background_color",
    },
    {
        type: "select",
        name: "font_family",
        options: ["Lazy Dog", "Love Ya Like A Sister", "Lexend"],
    },
    {
        type: "color",
        name: "border_color",
    },
    {
        type: "text",
        name: "border_width",
    },
    {
        type: "select",
        name: "border_style",
        options: ["solid", "dotted", "dashed", "double"],
    },
];

export default function ElementsForm({ setTemplate, template }) {
    const [activeTab, setActiveTab] = useState("info_button");

    const setData = ({ name, value, element }) => {
        setTemplate((prev) => {
            const oldData = { ...prev };
            if (!oldData.elements) oldData.elements = {};
            if (!oldData.elements[element]) {
                oldData.elements[element] = elementStyles.reduce((acc, style) => {
                    acc[style.name] = "";
                    return acc;
                }, {});
            }
            oldData.elements[element][name] = value;
            return oldData;
        });
    };

    const handleChange = (e, element) => {
        const { name, value } = e.target;
        setData({ name, value, element });
    };

    return (
        <div>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="tw-h-auto tw-flex-wrap">
                    {["info_button", "next_button", "prev_button", "start_button"].map((key) => {
                        return (
                            <TabsTrigger key={key} value={key} className="tw-capitalize">
                                {key.replaceAll("_", " ")}
                            </TabsTrigger>
                        );
                    })}
                </TabsList>
                {["info_button", "next_button", "prev_button", "start_button"].map((key) => {
                    return (
                        <TabsContent key={key} value={key} className="tw-space-y-3">
                            {elementStyles.map(({ name, type, options }) => {
                                return (
                                    <div key={name} className="tw-space-y-1">
                                        <Label className="tw-capitalize">{name.replaceAll("_", " ")}</Label>

                                        {type !== "select" &&
                                            (type == "color" ? (
                                                <ColorInput
                                                    onHandleChange={(value) => setData({ name, value, element: key })}
                                                    value={template?.elements?.[key]?.[name]}
                                                    name={name}
                                                />
                                            ) : (
                                                <Input
                                                    value={template?.elements?.[key]?.[name]}
                                                    name={name}
                                                    onChange={(e) => handleChange(e, key)}
                                                    type={type}
                                                />
                                            ))}
                                        {type == "select" && (
                                            <SelectNative name={name} onChange={(e) => handleChange(e, key)}>
                                                <option value="">Select Options</option>
                                                {options.map((v) => (
                                                    <option
                                                        selected={v == template?.elements?.[key]?.[name]}
                                                        key={v}
                                                        value={v}
                                                    >
                                                        {v}
                                                    </option>
                                                ))}
                                            </SelectNative>
                                        )}
                                    </div>
                                );
                            })}
                        </TabsContent>
                    );
                })}
            </Tabs>
        </div>
    );
}
