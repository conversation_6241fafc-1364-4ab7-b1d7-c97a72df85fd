import { Button } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { addTimelineLog } from "@/redux/reports/action";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useParams } from "react-router-dom";

const GenerateCertificate = () => {
    const dispatch = useDispatch();
    const params = useParams();
    const [courseDetails, setCourseDetails] = useState(null);
    const [bookmarkingData, setBookmarkingData] = useState(null);
    const [certificateList, setCertificateList] = useState(null);
    const [certificate, setCertificate] = useState(null);
    const [UpdateBookmarking, setUpdateBookmarking] = useState(null);
    const [Loading, setLoading] = useState(false);

    useEffect(() => {
        if (params?.course_id !== undefined && params?.bookmarking_id !== undefined) {
            onFetchBookmarking();
            getCourseDetails();
            getCertificates("course-completion");
        }
    }, [params]);

    useEffect(() => {
        if (courseDetails && certificateList) {
            const data = certificateList?.find((dt) => dt?.id == courseDetails?.courseSettings?.course_certificate_id);
            setCertificate(data);
        }
    }, [courseDetails, certificateList]);

    useEffect(() => {
        if (bookmarkingData) {
            const bookmark = {
                user_id: bookmarkingData?.user_id,
                course_id: bookmarkingData?.course_id,
                course_status: bookmarkingData?.course_status,
                course_seen: bookmarkingData?.course_seen,
                course_start_date: bookmarkingData?.course_start_date,
                course_end_date: "",
                status: true,
                last_activity: bookmarkingData?.last_activity,
                course_bookmarking_details: bookmarkingData?.bookmarkingData,
            };
            onUpdateBookmarking(bookmark);
        }
    }, [bookmarkingData]);

    useEffect(() => {
        if (certificate && UpdateBookmarking && bookmarkingData) {
            AutoGenerate();
        }
    }, [certificate, UpdateBookmarking, bookmarkingData]);

    const AutoGenerate = () => {
        const orgData = JSON.parse(localStorage.getItem("org_data"));
        const companyName = orgData?.about?.company_name;
        let userImage = localStorage.getItem("user_image");
        if (userImage == "null") userImage = "https://lms-node20.vercel.app/assets/profile_placeholder.jpg";

        const data = certificate?.certificate
            .replace(`"<%=background_url%>"`, `'${certificate?.background_image_url}'`)
            .replace(`<%=student_name%>`, `${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`)
            .replace(`<%=course_title%>`, `${courseDetails?.course_title}`)
            .replace(`<%=picture_url%>`, userImage)
            .replace(`<%=mentor_name%>`, UpdateBookmarking?.Mentor_name)
            .replace(`<%=start_date%>`, moment(UpdateBookmarking?.course_start_date).format("DD MMMM YYYY"))
            .replace(
                `<%=completion_date%>`,
                moment(UpdateBookmarking?.course_end_date ?? moment().add(1, "year")).format("DD MMMM YYYY"),
            )
            .replace(`<%=certificate_id%>`, UpdateBookmarking?.unique_code)
            .replace(`<%=created_date%>`, moment(new Date()).format("DD MMMM YYYY"))
            .replace(`<%=admin_name%>`, companyName)
            .replace(
                `<%=expiry_date%>`,
                moment(UpdateBookmarking?.certificate_expiry_date || moment().add(1, "years")).format("DD MMMM YYYY"),
            )
            .replace(`538px`, `799.748px`)
            .replace(`768px`, `1133.858px`);
        onCertificateDownload(data);
    };

    const onCertificateDownload = (data) => {
        const payload = {
            pdf: {
                printBackground: true,
                scale: 1,
                landscape: true,
                width: "1150px",
                height: "815px",
            },
            source: { html: data },
            wait: {
                for: "navigation",
                timeout: 1500,
                waitUntil: "load",
            },
        };

        if (bookmarkingData?.certificate_url == null) {
            onGenerateCertificate(payload);
        }
    };

    const onGenerateCertificate = async (payload) => {
        setLoading(true);

        await tanstackApi
            .post("course/bookmark/generate-certificates-url", payload)
            .then((res) => {
                const response = res.data;
                const payload = {
                    bookmark_id: bookmarkingData?.id,
                    certificate_issue_date: moment().format("YYYY-MM-DD"),
                    certificate_url: response?.data?.Location,
                    download_certificate_url: response?.data?.downloadUrl,
                };
                onUploadToBookmarking(payload);
            })
            .catch((err) => {
                var errMsg = err.response.data;
                return errMsg;
            });
    };

    const onUploadToBookmarking = async (payload) => {
        await tanstackApi
            .post("course/bookmark/upload-certificate", payload)
            .then((res) => {
                const response = res.data;
                dispatch(
                    addTimelineLog({
                        user_id: localStorage.getItem("parent_user_id"),
                        event: "certificate",
                        log: `${certificate?.title} generated successfully for ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} in order to complete the course: ${courseDetails?.course_title}`,
                    }),
                );
                onFetchBookmarking();
                setLoading(false);
            })
            .catch((err) => {
                var errMsg = err.response.data;
                return errMsg;
            });
    };

    const onFetchBookmarking = async () => {
        await tanstackApi
            .get(`course/bookmark/get-course-bookmarking/${params?.course_id}`)
            .then((res) => {
                setBookmarkingData(res?.data?.data);
            })
            .catch((err) => {
                setBookmarkingData(null);
            });
    };

    const onUpdateBookmarking = async (payload) => {
        await tanstackApi
            .post(`course/bookmark/update-course-bookmarking`, payload)
            .then((res) => {
                setUpdateBookmarking(res?.data?.data);
            })
            .catch((err) => {
                setUpdateBookmarking(null);
            });
    };

    const getCourseDetails = async () => {
        await tanstackApi
            .post("course/view-course", {
                course_id: params?.course_id,
            })
            .then((res) => {
                setCourseDetails(res?.data?.data);
            })
            .catch((err) => {
                setCourseDetails(null);
            });
    };

    const getCertificates = async (type) => {
        await tanstackApi
            .get(`certificate/get-certificates/${type}`)
            .then((res) => {
                setCertificateList(res?.data?.data?.certificateList);
            })
            .catch((err) => {
                setCertificateList([]);
            });
    };

    const onDownload = () => {
        const url = bookmarkingData?.certificate_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `Certificate of ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    return (
        <div className="tw-relative tw-flex tw-h-[100vh] tw-flex-col tw-items-center tw-justify-center tw-gap-4 tw-border-[1px]">
            <div className="tw-relative tw-mx-auto tw-flex tw-h-[85%] tw-w-[90%] tw-items-center tw-justify-center tw-overflow-hidden tw-rounded-2xl tw-shadow-md">
                <div
                    className="tw-absolute tw-inset-0 tw-size-full tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{ backgroundImage: `url(${courseDetails?.course_banner_url})` }}
                ></div>
                <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/60 tw-bg-cover tw-bg-center tw-bg-no-repeat"></div>

                <div className="tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center tw-rounded-2xl tw-bg-white tw-p-20">
                    <span className="tw-mb-6 tw-text-8xl tw-leading-none">🎉</span>
                    <h1 className="tw-mb-10 tw-font-mono tw-text-5xl tw-font-bold tw-text-green-500">
                        Congratulations!
                    </h1>
                    <p className="tw-text-center tw-font-mono tw-text-2xl tw-font-medium">
                        You’ve successfully completed the
                    </p>
                    <h1 className="tw-text-center tw-font-mono tw-text-2xl tw-font-bold">
                        {courseDetails?.course_title}!
                    </h1>
                    <div className="tw-space-x-4">
                        <Button variant="outline" asChild>
                            <Link to={`/dashboard/course`}>
                                <i className="fa-solid fa-left-long"></i> Back to Courses
                            </Link>
                        </Button>
                        <Button variant="success-outline" className="tw-mt-5" onClick={onDownload}>
                            {Loading ? (
                                <>
                                    <i className="fa-solid fa-spinner"></i> Loading Please wait
                                </>
                            ) : (
                                <>
                                    <i className="fa-solid fa-cloud-arrow-down"></i> Download Certificate
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default GenerateCertificate;
