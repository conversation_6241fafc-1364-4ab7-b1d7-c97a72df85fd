import { tanstackApi } from "@/react-query/api";
import { useGetTrainerScheduledChapters } from "@/react-query/users/schedule-chapter";
import Pagination from "@mui/material/Pagination";
import { DateCalendar, PickersDay } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs from "dayjs"; // Import dayjs
import { Calendar, Clock, MapPinHouse, Timer, UsersRound, Video } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const TrainerScheduleDash = () => {
    const [recordsCountOnTable, setRecordsCountOnTable] = useState(2);
    const [pageCount, setPageCount] = useState(0);
    const [tableData, setTableData] = useState([]);
    const [selectedDate, setSelectedDate] = useState(null);
    const [highlightDays, setHighlightDays] = useState([]);
    const [allChapters, setAllChapters] = useState([]);
    const [startDate, setStartDate] = useState(dayjs().startOf("month").format("YYYY-MM-DD"));
    const [endDate, setEndDate] = useState(dayjs().endOf("month").format("YYYY-MM-DD"));
    const scheduleChapter = useGetTrainerScheduledChapters({
        params: { type: "custom", start_date: startDate, end_date: endDate },
    });

    useEffect(() => {
        if (scheduleChapter.isSuccess) {
            if (!scheduleChapter.data.data || scheduleChapter.data?.data?.length == 0) {
                setHighlightDays([]);
                setPageCount(0);
                setTableData([]);
                return;
            }
            const allChapters = scheduleChapter.data.data;
            const modulas = allChapters.length % recordsCountOnTable;
            const count = (allChapters.length - modulas) / recordsCountOnTable;
            setPageCount(modulas !== 0 ? count + 1 : count);
            const data = allChapters?.slice(0, recordsCountOnTable * 1);
            setTableData(data);
            setAllChapters(allChapters);
            setHighlightDays(allChapters?.map((t) => moment(t.date).format("YYYY-MM-DD")));
        }
    }, [startDate, endDate, scheduleChapter?.status]);

    useEffect(() => {
        if (selectedDate) {
            const options = allChapters?.filter((dt) => dt?.date == selectedDate);
            const modulas = options?.length % recordsCountOnTable;
            const count = (options?.length - modulas) / recordsCountOnTable;
            setPageCount(modulas !== 0 ? count + 1 : count);
            setTableData(options?.slice(0, recordsCountOnTable * 1));
        } else {
            setTableData(allChapters?.slice(0, recordsCountOnTable * 1));
        }
    }, [selectedDate]);

    const handleDateChange = (date) => {
        setSelectedDate(date ? date.format("YYYY-MM-DD") : "");
    };

    const handleMonthChange = (data) => {
        setStartDate(data.startOf("month").format("YYYY-MM-DD"));
        setEndDate(data.endOf("month").format("YYYY-MM-DD"));
        setSelectedDate(null);
    };

    const onPageChange = (e, number) => {
        const data = [...allChapters]?.slice(recordsCountOnTable * (number - 1), recordsCountOnTable * number);
        setTableData(data);
    };

    const onJoinClass = (data) => {
        punchTimelineLog({
            user_id: localStorage.getItem("parent_user_id"),
            event: "sessions",
            log: `Trainer: ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} attended ${data?.topic} successfully.`,
        });
        window.open(data?.location, "_blank");
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi.post("users/add-timeline-log", payload);
    };

    return (
        <div className="schedule_view">
            <div className="schedule_header">
                <img src="/assets/robo-2.png" alt="" />
                <div>
                    <h1>Time Table</h1>
                    <p>Explore your scheduled classes and chapters.</p>
                </div>
            </div>
            <div className="main_details">
                <div className="calendar_info">
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DateCalendar
                            date={selectedDate ? dayjs(selectedDate) : null}
                            slots={{
                                day: (props) => <HighlightDays {...props} highlightedDays={highlightDays} />,
                            }}
                            onChange={handleDateChange}
                            onMonthChange={handleMonthChange}
                        />
                    </LocalizationProvider>
                </div>
                <div>
                    <div className="tw-grid tw-grid-cols-1 tw-items-start tw-gap-3">
                        {tableData?.map((group, index) => (
                            <div
                                key={index}
                                className="tw-grid tw-grid-cols-[1fr_250px] tw-rounded-xl tw-border-[1px] tw-p-1"
                            >
                                <div className="tw-p-2">
                                    <div className="tw-flex tw-flex-col tw-gap-1">
                                        <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                            {group?.topic}
                                        </h2>
                                        <span className="tw-line-clamp-2">{group?.overview}</span>
                                        <p className="tw-text-md tw-line-clamp-1 tw-font-mono tw-text-sm tw-text-slate-500">
                                            Course : {group?.chapter?.lms_course?.course_title} /{" "}
                                            {group?.chapter?.chapter_title}
                                        </p>
                                    </div>
                                    <p className="tw-mt-2 tw-line-clamp-3">{group?.chapter_discription}</p>
                                    <div className="tw-mt-1 tw-space-y-2">
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                            <Calendar size={16} /> {moment(group?.date).format("LL")}
                                        </span>{" "}
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                            <Clock size={16} />{" "}
                                            {`${moment(group?.start_time, "HH:mm:ss").format("h:mm A")} - ${moment(
                                                group?.end_time,
                                                "HH:mm:ss",
                                            ).format("h:mm A")}`}
                                        </span>{" "}
                                        <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                            <Timer size={16} /> {group?.duration} minutes
                                        </span>{" "}
                                    </div>
                                </div>
                                <div>
                                    <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-gap-4">
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            <img
                                                className="tw-h-8"
                                                src={
                                                    group?.meet_type == "zoom"
                                                        ? "/assets/zoom.png"
                                                        : group?.meet_type == "google-meet"
                                                          ? "/assets/google.png"
                                                          : ""
                                                }
                                                alt=""
                                            />

                                            {group?.meet_type == "zoom" ? (
                                                <p className="tw-font-lexend tw-text-xl tw-font-semibold tw-text-slate-500">
                                                    Zoom
                                                </p>
                                            ) : group?.meet_type == "google-meet" ? (
                                                <p className="tw-font-lexend tw-text-xl tw-font-semibold tw-text-slate-500">
                                                    Google
                                                </p>
                                            ) : (
                                                <p className="tw-flex tw-items-center tw-gap-2 tw-font-lexend tw-text-xl tw-font-semibold tw-text-slate-500">
                                                    <MapPinHouse size={30} /> Offline
                                                </p>
                                            )}
                                        </div>
                                        <div className="tw-flex tw-items-center tw-gap-2">
                                            {group?.type == "offline" ? (
                                                <Link
                                                    to={`/dashboard/trainer-classes-learners/${group?.id}`}
                                                    target="_blank"
                                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-2 tw-px-3 tw-text-sm hover:tw-bg-slate-100"
                                                >
                                                    <UsersRound size={16} strokeWidth={2} aria-hidden="true" />
                                                    Learners
                                                </Link>
                                            ) : (
                                                <div
                                                    onClick={() => onJoinClass(group)}
                                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-2 tw-px-3 tw-text-sm hover:tw-bg-slate-100"
                                                >
                                                    <Video size={16} strokeWidth={2} aria-hidden="true" />
                                                    Join Class
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div className="schedule_bottom">
                <Pagination count={pageCount} onChange={onPageChange} variant="outlined" shape="rounded" />
            </div>
        </div>
    );
};

export default TrainerScheduleDash;

function HighlightDays(props) {
    const { highlightedDays = [], day, outsideCurrentMonth, ...other } = props;
    const formattedDay = day.format("YYYY-MM-DD");
    const isSelected = !outsideCurrentMonth && highlightedDays.indexOf(formattedDay) >= 0;
    return (
        <div key={day} style={{ position: "relative" }}>
            <PickersDay {...other} outsideCurrentMonth={outsideCurrentMonth} day={day} />
            {isSelected && (
                <div
                    style={{
                        position: "absolute",
                        top: "75%",
                        right: "30%",
                        backgroundColor: "rgb(0, 185, 174)",
                        color: "white",
                        borderRadius: "2rem",
                        width: "15px",
                        height: "2px",
                    }}
                />
            )}
        </div>
    );
}
