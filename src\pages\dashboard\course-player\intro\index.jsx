import { colorsList } from "@/data/colors";

const IntroPage = ({ details, template }) => {
    return (
        <div
            className="tw-relative tw-h-full tw-rounded-xl"
            style={{
                backgroundImage: `url(${template?.background})`,
                "--primary-color": template?.elements?.color?.primary?.background,
                "--primary-text-color": template?.elements?.color?.primary?.text,
                "--secondary-color": template?.elements?.color?.secondary?.background,
                "--secondary-text-color": template?.elements?.color?.secondary?.text,
            }}
        >
            <div
                className="tw-absolute tw-inset-0 tw-z-0 tw-size-full tw-bg-cover tw-bg-no-repeat"
                style={{
                    backgroundImage: `url(${template?.ui_elements?.intro_bg})`,
                }}
            />
            <div className="tw-relative tw-z-10 tw-mx-auto tw-grid tw-h-full tw-max-w-[80%] tw-grid-rows-[min-content_1fr] tw-gap-y-5 tw-py-10">
                <div>
                    <h1
                        className="tw-text-center tw-font-lexend tw-leading-[1.25]"
                        style={{
                            fontFamily: template?.font_family?.subtitle?.font_family,
                            fontSize: template?.font_family?.subtitle?.font_size ?? "32px",
                            fontWeight: template?.font_family?.subtitle?.font_weight ?? "700",
                            color: template?.font_family?.subtitle?.color ?? "#333333",
                        }}
                    >
                        {details?.course_title}
                    </h1>
                    <p
                        className="tw-mx-auto tw-mt-1 tw-line-clamp-3 tw-w-[80%] tw-text-center tw-font-lexend tw-leading-[1.25]"
                        style={{
                            fontFamily: template?.font_family?.body?.font_family,
                            fontSize: template?.font_family?.body?.font_size ?? "20px",
                            fontWeight: template?.font_family?.body?.font_weight ?? "400",
                            color: template?.font_family?.body?.color ?? "#333333",
                        }}
                    >
                        {details?.course_description}
                    </p>
                </div>
                <div className="tw-h-full tw-max-h-full tw-overflow-hidden">
                    <div className="tw-grid tw-grid-cols-3 tw-gap-5">
                        {details?.courseChapters?.map((chapter, c_idx) => (
                            <div
                                key={c_idx}
                                className="tw-grid tw-grid-cols-4 tw-gap-x-2 tw-overflow-hidden tw-rounded-md tw-border tw-bg-background tw-font-lexend lg:tw-gap-x-4"
                            >
                                <div
                                    style={{
                                        fontFamily: template?.font_family?.title?.font_family,
                                        color: template?.font_family?.body?.color ?? "#333333",
                                        backgroundColor: colorsList[c_idx % colorsList.length],
                                    }}
                                    className="tw-flex tw-items-center tw-justify-center tw-text-xl tw-font-semibold tw-text-black lg:tw-text-2xl"
                                >
                                    {c_idx + 1 > 9 ? c_idx + 1 : `0${c_idx + 1}`}
                                </div>
                                <div
                                    style={{
                                        fontFamily: template?.font_family?.body?.font_family,
                                        fontWeight: template?.font_family?.body?.font_weight ?? "600",
                                        color: template?.font_family?.body?.color ?? "#333333",
                                    }}
                                    className="tw-col-span-3 tw-py-4 tw-pr-2"
                                >
                                    <p className="tw-text-xl tw-font-bold lg:tw-text-2xl">{chapter?.chapter_title}</p>
                                    <p className="tw-mt-0.5 tw-line-clamp-3 tw-text-sm tw-font-normal tw-leading-[1.25] lg:tw-text-base lg:tw-leading-[1.25]">
                                        {chapter?.chapter_discription}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default IntroPage;
