import { Button } from "@/components/ui/button";
import { DataTable } from "@/pages/dashboard/roles/permission/data-table";
import { fetchModulesList } from "@/redux/module/action";
import { format } from "date-fns";
import { Pencil, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ModulesForm from "./form";

export default function ModulePage() {
    const dispatch = useDispatch();
    const [open, setOpen] = useState(false);
    const [selected, setSelected] = useState(null);

    const modulesList = useSelector((state) => state.ModulesReducer.modulesList)?.data;

    useEffect(() => {
        dispatch(fetchModulesList());
    }, []);

    const handleUpdate = (id) => {
        const data = modulesList?.find((dt) => dt.id == id);
        setSelected(data);
        setOpen(true);
    };

    const columns = [
        {
            accessorKey: "id",
        },
        {
            accessorKey: "display_name",
            header: "Module Name",
        },
        {
            accessorKey: "possible_permissions",
            header: "Possible Permissions",
            cell: ({ row }) => {
                return (
                    <div className="text-right font-medium">
                        {row?.getValue("possible_permissions")?.map((dt) => (
                            <>{dt}, </>
                        ))}
                    </div>
                );
            },
        },
        {
            accessorKey: "is_active",
            header: "Status",
            cell: ({ row }) => {
                const is_active = Boolean(row.getValue("is_active"));
                return <div className="text-right font-medium">{is_active ? "Active" : "In Active"}</div>;
            },
        },
        {
            accessorKey: "createdAt",
            header: "Create On",
            cell: ({ row }) => {
                return <div className="text-right font-medium">{format(row.getValue("createdAt"), "PP")}</div>;
            },
        },
        // {
        //     accessorKey: "createdAt",
        //     header: "Sub Category ",
        //     cell: ({ row }) => {
        //         return <Link to={`/dashboard/sub-category/${row.getValue("id")}` }><Eye className="tw-text-blue-400" /></Link>;
        //     },
        // },
        {
            accessorKey: "created_by",
            header: "Actions",
            cell: ({ row }) => {
                return (
                    <div
                        className="text-right font-medium"
                        onClick={() => {
                            handleUpdate(row.getValue("id"));
                        }}
                    >
                        <Pencil className="tw-text-blue-400" />
                    </div>
                );
            },
        },
    ];
    return (
        <div>
            <div className="tw-mb-3 tw-flex tw-items-center tw-justify-between">
                <div className="tw-font-lexend tw-text-2xl tw-font-medium">Modules</div>
                <Button
                    onClick={() => {
                        setSelected(null);
                        setOpen(true);
                    }}
                    variant="outline"
                >
                    <Plus />
                    Create Module
                </Button>
            </div>
            <DataTable columns={columns} data={modulesList ? modulesList : []} />
            <ModulesForm open={open} setOpen={setOpen} selected={selected} />
        </div>
    );
}
