import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

import { fetchModulesList, getAllModules, getAllSubModules } from "@/redux/module/action";

function* getModules() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "modules/listing-modules", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;

            return response;
        })
        .catch((err) => {
            var errMsg = err.response;
            return errMsg;
        });
    if (data) {
        yield put(getAllModules(data));
    } else {
        yield null;
    }
}

function* getSubModules(subModuleId) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `modules/listing-sub-modules/${subModuleId.payload}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response;
            alert("errMsg", errMsg);
            return errMsg;
        });
    if (data) {
        yield put(getAllSubModules(data));
    } else {
        yield null;
    }
    // alert("Saga HIT")
}

function* addModule(moduleData, handleClose) {
    try {
        const data = yield axios
            .post(CONSTANTS.getAPI() + "modules/create-module", moduleData.payload, {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("login_token")}`,
                },
            })
            .then((res) => {
                const response = res.data;
                return response;
            })
            .catch((err) => {
                var errMsg = err.response?.data?.message;
                alert(errMsg);
                return errMsg;
            });
        if (data) {
            if (data?.success) {
                yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
            }
            yield put(fetchModulesList());
        } else {
            yield null;
            yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
        }
    } catch (error) {
        yield put(AlertSnackInfo({ message: error?.message, result: false }));
    }
}

function* deleteModule(moduleData) {
    const data = yield axios
        .post(
            CONSTANTS.getAPI() + `modules/delete-module/${moduleData.payload}`,
            {},
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("login_token")}`,
                },
            },
        )
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const msg = err?.response?.data?.error;
            if (msg) {
                return msg;
            }
            const errMsg = err?.message;
            return errMsg;
        });

    if (data) {
        if (data?.success) {
            yield put(fetchModulesList());
            yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
        } else {
            yield put(AlertSnackInfo({ message: data, result: false }));
        }
    } else {
        yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
        yield null;
    }
}

function* updateModule(moduleData) {
    const data = yield axios
        .put(CONSTANTS.getAPI() + "modules/update-module", moduleData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response?.data?.message;
            return errMsg;
        });
    if (data) {
        yield put(fetchModulesList());
        yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
    } else {
        yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
        yield null;
    }
}

function* addSubModule(subModuleData) {
    const data = yield axios
        .post(CONSTANTS.getAPI() + "modules/create-sub-module", subModuleData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response?.data?.message;
            return errMsg;
        });
    if (data) {
        yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
    } else {
        yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
        yield null;
    }
}

function* updateSubModuleSaga(subModuleData) {
    const data = yield axios
        .put(CONSTANTS.getAPI() + "modules/update-sub-module", subModuleData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response?.data?.message;
            return errMsg;
        });
    if (data) {
        yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
    } else {
        yield put(AlertSnackInfo({ message: data?.message, result: data?.success }));
        yield null;
    }
}

export function* ModulesWatcher() {
    yield takeEvery(actions.GET_MODULES_REQ, getModules);
    yield takeEvery(actions.SUB_MODULE_REQ, getSubModules);
    yield takeEvery(actions.ADD_MODULE, addModule);
    yield takeEvery(actions.DELETE_MODULE, deleteModule);
    yield takeEvery(actions.UPDATE_MODULE, updateModule);
    yield takeEvery(actions.ADD_SUB_MODULE, addSubModule);
    yield takeEvery(actions.UPDATE_SUB_MODULE, updateSubModuleSaga);
}

export default function* ModuleSaga() {
    yield all([fork(ModulesWatcher)]);
}
