import {
    CLEAR_USER_DATA,
    DOMAIN_HOLDER_DATA,
    DOMAIN_NAME_SAVE,
    LOGIN_DATA,
    LOGIN_REQUEST,
    PERMISSION_DATA,
    USER_PERSONAL_DETAILS_HOLDER,
    USER_PERSONAL_DETAILS_SAVE,
} from "@/redux-types";

const initialState = {
    loginDetaills: {},
    onUserData: {},
    domainHolder: {},
    permissions: localStorage.getItem("permissions"),
};

const AuthReducer = (state = initialState, action) => {
    switch (action.type) {
        case LOGIN_REQUEST:
            return {
                ...state,
                loginDetaills: action.payload,
            };
        case LOGIN_DATA:
            return {
                ...state,
                onUserData: action.payload,
            };
        case DOMAIN_HOLDER_DATA:
            return {
                ...state,
                domainHolder: action.payload,
            };
        case DOMAIN_NAME_SAVE:
            return {
                ...state,
                domainHolder: action.payload,
            };
        case CLEAR_USER_DATA:
            return {
                ...state,
                onUserData: {},
            };
        case USER_PERSONAL_DETAILS_SAVE:
            return {
                ...state,
                onUserData: { ...state.onUserData, ...action.payload },
            };
        case USER_PERSONAL_DETAILS_HOLDER:
            return {
                ...state,
                onUserData: { ...state.onUserData, ...action.payload },
            };
        case PERMISSION_DATA:
            return {
                ...state,
                permissions: action.payload,
            };

        default:
            return state;
    }
};
export default AuthReducer;
