import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SelectNative } from "@/components/ui/select-native";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

const formSchema = z.object({
    first_name: z
        .string({ required_error: "First name required" })
        .trim("First name cannot have leading or trailing spaces")
        .min(1, "First name cannot be empty")
        .regex(/^[A-Za-z]+$/, "First name must contain only letters"),
    last_name: z
        .string({ required_error: "Last name required" })
        .trim("Last name cannot have leading or trailing spaces")
        .min(1, "Last name cannot be empty")
        .regex(/^[A-Za-z]+$/, "Last name must contain only letters"),
    email: z
        .string({ required_error: "Email required" })
        .trim("Email cannot have leading or trailing spaces")
        .email("Invalid email address"),
    password: z
        .string({ required_error: "Password required" })
        .trim("Password cannot have leading or trailing spaces")
        .min(1, "Password cannot be empty")
        .min(8, "Password must be at least 8 characters"),
    user_type_code: z.string({ required_error: "User type code required" }),
    domain_type: z.string({ required_error: "Domain type required" }),
    domain_name: z
        .string({ required_error: "Domain name required" })
        .trim("Domain name cannot have leading or trailing spaces")
        .min(1, "Domain name cannot be empty"),
});

const defaultValue = {
    first_name: "",
    last_name: "",
    email: "",
    password: "",
    confirm_password: "",
    user_type_code: "",
    domain_type: "",
    domain_name: "",
};

const Details = () => {
    const params = useParams();
    const navigate = useNavigate();
    const [roleList, setRoleList] = useState([]);
    const [domainVerified, setDomainVerified] = useState(false);
    const [passwordShow, setPasswordShow] = useState(false);
    const [confirmPasswordShow, setConfirmPasswordShow] = useState(false);
    const [domainData, setDomainData] = useState(null);
    const [domainTypes, setDomainTypes] = useState([]);
    const [domainUser, setDomainUser] = useState(defaultValue);

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setDomainUser({ ...domainUser, [name]: value });
        if (name == "domain_name") {
            setDomainVerified(false);
        }
    };

    useEffect(() => {
        if (params?.domain_id !== undefined) {
            getDomainData();
        }
    }, [params]);

    const getDomainData = async () => {
        await tanstackApi
            .get("auth/get-domains")
            .then((res) => {
                setDomainData(res?.data?.data?.find((dt) => dt?.id == Number(params?.domain_id)));
            })
            .catch((err) => {
                setDomainData(null);
            });
    };

    useEffect(() => {
        if (domainData !== null) {
            setDomainUser({
                first_name: domainData?.first_name,
                last_name: domainData?.last_name,
                email: domainData?.email,
                user_type_code: domainData?.role,
                domain_type: domainData?.domain_type,
                domain_name: domainData?.domain_name,
            });
            setDomainVerified(true);
        } else {
            setDomainUser(defaultValue);
        }
    }, [domainData]);

    useEffect(() => {
        getLanguages();
        getDomainTypes();
    }, []);

    const getLanguages = async () => {
        await tanstackApi
            .get("role/listing-role")
            .then((res) => {
                setRoleList(res?.data?.data?.filter((dt) => dt?.is_active == 1));
            })
            .catch((err) => {
                setRoleList([]);
            });
    };

    const getDomainTypes = async () => {
        await tanstackApi
            .get("domain-type/all")
            .then((res) => {
                setDomainTypes(res?.data?.data);
            })
            .catch((err) => {
                setDomainTypes([]);
            });
    };

    const VerifyDomain = async (e) => {
        e.preventDefault();

        if (!domainUser?.domain_name) {
            toast?.warning("Domain Name", {
                description: "Domain name is required",
            });
            return false;
        }

        let payload = {
            name: domainUser?.domain_name,
        };
        await tanstackApi
            .post("signup/check-name", { ...payload })
            .then((res) => {
                if (res?.data?.validity) {
                    setDomainVerified(true);
                    toast.success("Successful", {
                        description: res?.data?.message,
                    });
                } else {
                    toast.warning("Error", {
                        description: res?.data?.message,
                    });
                    setDomainVerified(false);
                }
            })
            .catch((err) => {
                setDomainVerified(false);
            });
    };

    const handleClear = () => {
        if (domainData !== null) {
            setDomainUser({
                first_name: domainData?.first_name,
                last_name: domainData?.last_name,
                email: domainData?.email,
                user_type_code: domainData?.role,
                domain_type: domainData?.domain_type,
                domain_name: domainData?.domain_name,
            });
            setDomainVerified(true);
        } else {
            setDomainUser(defaultValue);
        }
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();

        const validate = formSchema.safeParse(domainUser);
        if (!validate.success) {
            toast?.warning("Invalid Data", {
                description: validate.error.errors[0].message,
            });
            return false;
        }

        if (!domainVerified) {
            toast?.warning("Verify Domain", {
                description: "domain verification is required",
            });
            return false;
        }

        if (params?.domain_id !== undefined) {
            const payload = {
                id: params?.domain_id,
                domain_name: domainUser?.domain_name,
                first_name: domainUser?.first_name,
                last_name: domainUser?.last_name,
                email: domainUser?.email,
                password: domainUser?.password || undefined,
                user_type_code: "organization",
                user_lang: "en",
                status: "active",
                domain_type: domainUser?.domain_type,
                is_domain: true,
                is_trainer: false,
            };

            await tanstackApi
                .put("users/update-user", { ...payload })
                .then((res) => {
                    toast.success("Updated Successfully", {
                        description: res?.data?.message,
                    });
                    // navigate(`/dashboard/domain-users`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        } else {
            const payload = {
                domain_name: domainUser?.domain_name,
                first_name: domainUser?.first_name,
                last_name: domainUser?.last_name,
                email: domainUser?.email,
                password: domainUser?.password,
                user_type_code: "organization",
                user_lang: "en",
                status: "active",
                domain_type: domainUser?.domain_type,
                is_domain: true,
                is_trainer: false,
            };

            await tanstackApi
                .post("users/add-user", { ...payload })
                .then((res) => {
                    toast.success("Created Successfully", {
                        description: res?.data?.message,
                    });
                    navigate(`/dashboard/domain-users`);
                })
                .catch((err) => {
                    toast.error("Something went wrong", {
                        description: err?.response?.data?.message,
                    });
                });
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Domain basic details</CardTitle>
                <CardDescription>
                    Add domain name, domain type, role & contact person details. domain verification is necessary Click
                    save when you&apos;re done.
                </CardDescription>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-2 tw-gap-10">
                    <div className="tw-space-y-4">
                        <div className="tw-grid tw-grid-cols-[auto_120px] tw-items-end tw-gap-2">
                            <div className="tw-space-y-1">
                                <Label htmlFor="name">Domain Name *</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={domainUser?.domain_name}
                                    name="domain_name"
                                    id="domain_name"
                                    placeholder="Enter domain name here"
                                />
                            </div>
                            <div>
                                {domainVerified ? (
                                    <Button
                                        variant="outline"
                                        className="tw-w-full tw-border-emerald-400 tw-bg-emerald-50 tw-text-emerald-400"
                                    >
                                        Verified <i className="fa-regular fa-circle-check"></i>
                                    </Button>
                                ) : (
                                    <Button variant="outline" className="tw-w-full" onClick={VerifyDomain}>
                                        <i className="fa-solid fa-globe"></i>Verify
                                    </Button>
                                )}
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-[1fr_2fr] tw-items-end tw-gap-2">
                            <div className="tw-space-y-1">
                                <Label htmlFor="domain_type">Domain Type *</Label>
                                <SelectNative
                                    onChange={onChangeHandle}
                                    value={domainUser?.domain_type}
                                    name="domain_type"
                                    className="tw-capitalize"
                                    disabled={params?.domain_id !== undefined}
                                >
                                    <option value=""> - Choose Domain Type - </option>
                                    {domainTypes?.map((type, idx) => (
                                        <option className="tw-capitalize" value={type?.type} key={idx}>
                                            {type?.display_name}
                                        </option>
                                    ))}
                                </SelectNative>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="email">E-mail *</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={domainUser?.email}
                                    name="email"
                                    id="email"
                                    type="email"
                                    placeholder="eg: <EMAIL>"
                                />
                            </div>
                        </div>
                        {/* <div className="tw-grid tw-grid-cols-2 tw-gap-4">
                            <div className="tw-space-y-1">
                                <Label htmlFor="user_type_code">Role *</Label>
                                <SelectNative
                                    onChange={onChangeHandle}
                                    value={domainUser?.user_type_code}
                                    name="user_type_code"
                                >
                                    <option value=""> - Choose Role - </option>
                                    {roleList?.map((lang, idx) => (
                                        <option value={lang?.role_code} key={idx}>
                                            {lang?.display_name}
                                        </option>
                                    ))}
                                </SelectNative>
                            </div>
                        </div> */}
                    </div>
                    <div className="tw-space-y-4">
                        <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                            <div className="tw-space-y-1">
                                <Label htmlFor="first_name">First Name *</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={domainUser?.first_name}
                                    name="first_name"
                                    id="first_name"
                                    placeholder="eg: John"
                                />
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="last_name">Last Name *</Label>
                                <Input
                                    onChange={onChangeHandle}
                                    value={domainUser?.last_name}
                                    name="last_name"
                                    id="last_name"
                                    placeholder="eg: Doe"
                                />
                            </div>
                        </div>
                        <div className="tw-grid tw-grid-cols-2 tw-gap-2">
                            <div className="tw-space-y-1">
                                <Label htmlFor="password">
                                    Password {params?.domain_id ? <small>(Optional)</small> : <>*</>}
                                </Label>
                                <div className="tw-relative tw-flex tw-rounded-md tw-border">
                                    <Input
                                        className="tw-border-none"
                                        onChange={onChangeHandle}
                                        value={domainUser?.password}
                                        name="password"
                                        id="password"
                                        type={passwordShow ? "text" : "password"}
                                        placeholder="eg: 123@32$#SD"
                                        autocomplete="off"
                                    />
                                    <div className="tw-absolute tw-right-2 tw-top-2 tw-cursor-pointer">
                                        <i
                                            className={`fa-solid ${passwordShow ? "fa-eye" : "fa-eye-slash"}`}
                                            onClick={() => setPasswordShow(!passwordShow)}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="confirm_password">
                                    Confirm Password {params?.domain_id ? <small>(Optional)</small> : <>*</>}
                                </Label>
                                <div className="tw-relative tw-flex tw-rounded-md tw-border">
                                    <Input
                                        className="tw-border-none"
                                        onChange={onChangeHandle}
                                        value={domainUser?.confirm_password}
                                        name="confirm_password"
                                        id="confirm_password"
                                        type={confirmPasswordShow ? "text" : "password"}
                                        placeholder="eg: 123@32$#SD"
                                        autocomplete="off"
                                    />
                                    <div className="tw-absolute tw-right-2 tw-top-2 tw-cursor-pointer">
                                        <i
                                            className={`fa-solid ${confirmPasswordShow ? "fa-eye" : "fa-eye-slash"}`}
                                            onClick={() => setConfirmPasswordShow(!confirmPasswordShow)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
            <CardFooter>
                <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                    <Button className="aspect-square max-sm:p-0" onClick={handleClear} variant="outline">
                        <i className="fa-solid fa-xmark"></i> Clear
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> {params?.domain_id ? "Update" : "Create"} Domain
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
};

export default Details;
