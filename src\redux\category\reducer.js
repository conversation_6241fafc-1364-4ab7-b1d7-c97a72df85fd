import {
    ADD_NEW_CATEGORY,
    ADD_NEW_SUB_CATEGORY,
    FETCH_CATEGORY_LIST_REQ,
    FETCH_SUB_CATEGORY_REQ,
    GET_CATEGORY_LIST,
    GET_SUB_CATEGORY_LIST,
    UPDATE_CATEGORY,
    UPDATE_SUB_CATEGORY,
} from "@/redux-types";

const initialState = {
    categoryList: [],
    isLoading: true,
    error: null,
    addCategoryData: {},
    updateCategoryData: {},
    subCategoryList: [],
    addSubCategoryData: {},
    updateSubCategoryData: {},
};

const CategoryReducers = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_CATEGORY_LIST_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_CATEGORY_LIST:
            return {
                ...state,
                isLoading: false,
                categoryList: action.payload,
                error: action.payload,
            };
        case ADD_NEW_CATEGORY:
            return {
                addCategoryData: action.payload,
                onClose: action.onClose,
            };
        case UPDATE_CATEGORY:
            return {
                updateCategoryData: action.payload,
                onClose: action.onClose,
            };
        case FETCH_SUB_CATEGORY_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_SUB_CATEGORY_LIST:
            return {
                ...state,
                isLoading: false,
                subCategoryList: action.payload,
                error: action.payload,
            };
        case ADD_NEW_SUB_CATEGORY:
            return {
                addSubCategoryData: action.payload,
                onClose: action.onClose,
            };
        case UPDATE_SUB_CATEGORY:
            return {
                updateSubCategoryData: action.payload,
                onClose: action.onClose,
            };
        default:
            return state;
    }
};

export default CategoryReducers;
