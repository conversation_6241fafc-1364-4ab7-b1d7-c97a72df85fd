<html>
<head>
 <script src = "https://appapi.amieyaa.com/scorm2.rte.js"></script>
 <script src = "https://appapi.amieyaa.com/control.rte.js"></script>
 <script>
      function createIframe() {
         var Body = document.getElementsByTagName('body');
          if(Body) {
            Body= Body[0];
          }
          var iframeElement = document.createElement("iframe");
          iframeElement.setAttribute("id", "IFRAME_MAIN");
          Body.appendChild(iframeElement);
          return iframeElement;
      }
      var  openedW, iFrame, basePath, scoIdLinear , currentIndex, 
        LDC = (data, basepath, opentype='popup', width=window.innerWidth, height=window.innerHeight, bookmark=null) => {
        scoIdLinear = Object.keys(data)
        if(!bookmark) {
          bookmark = scoIdLinear[0];
        }
        currentIndex = scoIdLinear.indexOf(bookmark);
        loadDataControl(data, bookmark);
        basePath = basepath;
        var launch = data[bookmark].activity;
        var URL = basepath + '/' + launch.sco_url;
        if(opentype == 'popup') {          
          openedW = window.open(basepath + '/' + launch.sco_url, launch.title, `width=${width},height=${height},scrollbars=yes,resizable=yes,titlebar=yes`); 
	  openedW.moveTo(0,0);
	  openedW.focus();
        }
        else {
          iFrame = createIframe();
          iFrame.setAttribute("src", basepath + '/' + launch.sco_url);
          iFrame.setAttribute("width", "100%");
          iFrame.setAttribute("height", "100%");
          iFrame.setAttribute("style", "border-style:none");
          iFrame.setAttribute("frameborder", "0");
          iFrame.setAttribute("border", "0");
        }
        return true;
      },
      goNext = () => {
        if(iFrame && scoIdLinear.length > 1 && currentIndex <=  scoIdLinear.length-2) {
          navigateSCO(scoIdLinear[currentIndex + 1]);
          currentIndex += 1;
          iFrame.setAttribute("src", basePath + '/' + getURL() );
        }
      },
      goPrevious = () => {
        if(iFrame && scoIdLinear.length > 1 && currentIndex >= 1) {
          navigateSCO(scoIdLinear[currentIndex - 1]);
          currentIndex -= 1;
          iFrame.setAttribute("src", basePath + '/' + getURL() );
        }
      },
      goSCO = (scoId) => {
        if(iFrame && (scoIdLinear.indexOf(scoId) != -1)) {
          navigateSCO(scoId);
          currentIndex = scoIdLinear.indexOf(scoId);
          iFrame.setAttribute("src", basePath + '/' + getURL() );
        }
      },
      getFrameURL = () => {
        return iFrame.getAttribute("src");
      };
      
      
</script>
</head>
<body>
</body>
</html>
