import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { cn, isUrl } from "@/lib/utils";
import { useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import { useFileUpload } from "@/react-query/common";
import { motion } from "framer-motion";
import { Upload, X } from "lucide-react";
import React, { useCallback } from "react";

export function Expandable({ template }) {
    const { tabList } = useEditInteraction();
    const [open, setOpen] = React.useState(0);

    const handlePrevSlide = () => {
        if (open === 0) return;
        setOpen((prev) => prev - 1);
    };

    const handleNextSlide = () => {
        if (tabList.length - 1 === open) return;
        setOpen((prev) => prev + 1);
    };

    return (
        <>
            <div className="tw-flex tw-h-[420px] tw-w-full tw-gap-5">
                {tabList.map((item, index) => {
                    return <TabItem key={index} index={index} setOpen={setOpen} open={open} item={item} />;
                })}
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    disabled={0 === open}
                    onClick={handlePrevSlide}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    disabled={tabList.length - 1 === open}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px] disabled:tw-opacity-70",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}

function TabItem({ setOpen, open, item, index }) {
    const { handleTabChange } = useEditInteraction();
    const [titleEditable, setTitleEditable] = React.useState(false);
    const [descriptionEditable, setDescriptionEditable] = React.useState(false);
    const titleRef = React.useRef(null);
    const descriptionRef = React.useRef(null);
    const upload = useFileUpload();

    const handleTitleEditable = useCallback(() => {
        setTitleEditable((prev) => !prev);
    }, []);

    const handleDescriptionEditable = useCallback(() => {
        setDescriptionEditable((prev) => !prev);
    }, []);

    const handleImageChange = useCallback(async (e) => {
        let imageUrl = "";
        try {
            if (e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                imageUrl = response?.fileUrl;
            }
        } catch (error) {
            imageUrl = "";
        } finally {
            handleTabChange("image_src", imageUrl, index);
        }
    }, []);

    const removeImage = useCallback(() => {
        handleTabChange("image_src", "", index);
    }, []);

    const handleTitleBlur = useCallback(() => {
        handleTabChange("label", titleRef?.current?.innerText, index);
    }, []);

    const handleDescriptionBlur = useCallback(() => {
        handleTabChange("description", descriptionRef?.current?.innerText, index);
    }, []);

    const circleVariants = {
        initial: {
            bottom: "2.5rem",
            left: "1.25rem",
        },
        open: {
            bottom: "4.5rem",
            left: "1rem",
        },
    };

    const variants = {
        initial: {
            opacity: 0,
            scale: 0,
        },
        open: {
            opacity: 1,
            scale: 1,
        },
    };
    return (
        <div
            className={cn(
                "tw-relative tw-flex tw-w-[80px] tw-items-center tw-justify-center tw-overflow-hidden tw-rounded-2xl tw-transition-all",
                index === open && "tw-flex-grow",
            )}
            onClick={() => setOpen(index)}
        >
            {index == open && (
                <div className="tw-absolute tw-right-0 tw-top-0 tw-z-20">
                    {isUrl(item.image_src) ? (
                        <Button variant="outline" className="aspect-square max-sm:p-0" onClick={removeImage}>
                            <X className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                            <Label className="max-sm:sr-only">Remove</Label>
                        </Button>
                    ) : (
                        <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10">
                            <Upload
                                className="opacity-60 sm:-ms-1 sm:me-2"
                                size={16}
                                strokeWidth={2}
                                aria-hidden="true"
                            />
                            <input
                                onChange={handleImageChange}
                                type="file"
                                style={{ display: "none" }}
                                id="bg_image"
                                accept="image/*"
                            />
                            <Label htmlFor="bg_image" className="max-sm:sr-only">
                                {upload.isPending ? "Uploading..." : "Upload Image"}
                            </Label>
                        </Button>
                    )}
                </div>
            )}
            <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/80">
                {isUrl(item.image_src) && (
                    <img
                        src={item.image_src}
                        className="tw-absolute tw-inset-0 tw-z-0 tw-size-full tw-object-cover"
                        alt=""
                    />
                )}
            </div>
            <motion.div
                variants={circleVariants}
                initial="initial"
                animate={open === index ? "open" : "initial"}
                className="tw-absolute tw-z-20 tw-size-10 tw-shrink-0 tw-rounded-full tw-bg-white"
            />
            <motion.div
                variants={variants}
                initial="initial"
                animate={open === index ? "open" : "initial"}
                className="tw-relative tw-z-10 tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center tw-bg-black/40 tw-pb-4"
            >
                <div className="tw-mt-auto tw-flex tw-w-full tw-items-start tw-gap-4 tw-pl-16 tw-text-white">
                    <div className="tw-flex tw-h-24 tw-flex-col tw-gap-1 tw-text-base">
                        <h1
                            ref={titleRef}
                            contentEditable={titleEditable}
                            onDoubleClick={handleTitleEditable}
                            onBlur={handleTitleBlur}
                            className="tw-text-lg"
                        >
                            {item.label}
                        </h1>
                        <p
                            ref={descriptionRef}
                            contentEditable={descriptionEditable}
                            onDoubleClick={handleDescriptionEditable}
                            onBlur={handleDescriptionBlur}
                            className="tw-text-xs"
                        >
                            {item.description}
                        </p>
                    </div>
                </div>
            </motion.div>
        </div>
    );
}

export function ExpandablePreview() {
    const list = [0, 1, 2, 3];
    return (
        <div className="tw-flex tw-size-full tw-items-end tw-justify-center tw-gap-2">
            {list.map((item) => (
                <div
                    key={item}
                    className={cn(
                        "tw-relative tw-flex tw-h-[90%] tw-w-[20px] tw-items-center tw-justify-center tw-overflow-hidden tw-rounded-2xl tw-border tw-bg-slate-300 tw-transition-all",
                        2 === item && "tw-flex-grow",
                    )}
                />
            ))}
        </div>
    );
}
