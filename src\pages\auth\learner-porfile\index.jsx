import { CONSTANTS } from "@/constants";
import { onUserLogin } from "@/redux/auth/action";
import { addTimelineLog } from "@/redux/reports/action";
import axios from "axios";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const LearnerProfile = () => {
    const navigate = useNavigate();
    const [imgURL, setImgURL] = useState("/assets/learner.png");
    const dispatch = useDispatch();
    const [fileURL, setFileURL] = useState("");
    const user = useSelector((state) => state.AuthReducer?.onUserData);

    useEffect(() => {
        if (Object.keys(user).length > 0) {
            if (user?.role_category === "sub-user") {
                navigate(user?.picture_url ? "/dashboard" : "/learner-profile");
            } else if (user?.role_category === "user") {
                navigate(user?.organizationDetails?.signup_completed === true ? "/dashboard/" : "/signup-steps");
            } else if (user?.role_category === "administrator") {
                navigate("/dashboard/");
            }
        }
    }, [user]);

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const formData = new FormData();
            var file = e.target.files[0];
            setImgURL(URL.createObjectURL(file));
            formData.append("file", file);
            formData.append("category", "USER-PROFILE");
            ImageUpload(formData);
        }
    };

    const ImageUpload = (data) => {
        axios
            .post(`${CONSTANTS.getAPI()}common/upload-file`, data, {
                headers: {
                    "Content-Type": "multipart/form-data",
                    Authorization: `Bearer ${localStorage.getItem("auth_signup_token") || localStorage.getItem("login_token")}`,
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setFileURL(res?.data?.fileUrl);
                }
            })
            .catch((err) => {});
    };

    const onSubmitData = (e) => {
        e.preventDefault();
        var data = {
            id: parseInt(localStorage.getItem("id") || localStorage.getItem("userId")),
            picture_url: fileURL,
        };

        axios
            .put(`${CONSTANTS.getAPI()}users/update-user`, data, {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("auth_signup_token") || localStorage.getItem("login_token")}`,
                },
            })
            .then((res) => {
                dispatch(
                    addTimelineLog({
                        user_id: localStorage.getItem("parent_user_id"),
                        event: "signup",
                        log: `${localStorage.getItem("first_name")} ${localStorage.getItem("last_name")} Sign up successfully `,
                    }),
                );
                alert(res.data.message);
                let payload = {
                    email: localStorage.getItem("QL_email"),
                    password: localStorage.getItem("QL_password"),
                };
                dispatch(onUserLogin(payload));

                // window.location.href = '/'
            })
            .catch((err) => {});
    };

    return (
        <section>
            <header className="SecondaryHeader">
                <div className="Logo">
                    <img src="assets/lmsLogo.png" alt="" />
                    <h1>LMS</h1>
                </div>
            </header>
            <div className="Registration_Container">
                <div className="reg_section">
                    <div className="imageUploadSec">
                        <h5>#. User profile</h5>
                        <small>Upload your profile picture to complete your registration</small>
                        <div className="ProfileContent">
                            <div className="imageDiv">
                                <img src={imgURL} alt="" />

                                <label htmlFor="Image_prof">
                                    <i className="fa-solid fa-image"></i> Select Image
                                </label>
                                <input onChange={onImageChange} type="file" accept="image/*" id="Image_prof" />
                            </div>
                            <div className="profileInfo">
                                <p>
                                    <b>Name :</b>{" "}
                                    {`${localStorage.getItem("first_name") || localStorage.getItem("lms_fName")} ${
                                        localStorage.getItem("last_name") || localStorage.getItem("lms_lName")
                                    }`}
                                </p>
                                <p>
                                    <b>Email :</b> {localStorage.getItem("email") || localStorage.getItem("user_email")}
                                </p>
                                <p>
                                    <b>Status :</b> {localStorage.getItem("status")}
                                </p>
                            </div>
                        </div>
                        <div className="continueButton">
                            <button onClick={onSubmitData}>
                                <i className="fa-solid fa-check-double"></i> Done
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default LearnerProfile;
