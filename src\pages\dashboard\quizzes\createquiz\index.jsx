import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { tanstackApi } from "@/react-query/api";
import { useGetQuizDetails, useQuizCreate, useQuizUpdate } from "@/react-query/quizz";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import QuizDetails from "./QuizDetails";
import QuizSettings from "./QuizSettings";
import QuizTemplates from "./QuizTemplates";
import { z } from "zod";

function calculateTimeDuration(startTime, endTime) {
    // Parse the start and end times
    const [startHours, startMinutes] = startTime.split(":").map(Number);
    const [endHours, endMinutes] = endTime.split(":").map(Number);

    // Convert start and end times to minutes from the beginning of the day
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    // Calculate duration in minutes, accounting for cases that span midnight
    const durationMinutes =
        endTotalMinutes >= startTotalMinutes
            ? endTotalMinutes - startTotalMinutes
            : 24 * 60 - (startTotalMinutes - endTotalMinutes);

    // Convert the duration back to hours and minutes
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;

    // Format the result as HH:mm
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");

    return `${formattedHours}:${formattedMinutes}:00`;
}

function formatTimeToSeconds(time) {
    // Check if the input matches the HH:mm:ss format
    if (/^\d{2}:\d{2}:\d{2}$/.test(time)) {
        return time; // Already in the correct format, return as is
    }
    // Check if the input matches the HH:mm format
    else if (/^\d{2}:\d{2}$/.test(time)) {
        return `${time}:00`; // Append ":00" to match HH:mm:ss format
    } else {
        throw new Error("Invalid time format. Please provide time in HH:mm or HH:mm:ss format.");
    }
}

const formSchema = z.object({
    title: z.string({ required_error: "Quiz title is required" }).trim().min(1, "Quiz title is required"),
    description: z
        .string({ required_error: "Quiz description is required" })
        .trim()
        .min(1, "Quiz description is required"),
    type: z.string({ required_error: "Quiz type is required" }).trim().min(1, "Quiz type is required"),
    thumbnail_img: z
        .string({ required_error: "Quiz thumbnail image is required" })
        .trim()
        .min(1, "Quiz thumbnail image is required")
        .url(),
});

const CreateQuizWrapper = () => {
    const params = useParams();
    const navigate = useNavigate();
    const loginToken = localStorage.getItem("login_token");

    const [trainersList, setTrainersList] = useState([]);
    const [quiz_details, setQuiz_details] = useState(null);
    const [quizData, setQuizData] = useState({
        course_id: null,
        chapter_id: null,
        title: "",
        type: "Mixed",
        schedule_date: "",
        start_time: "",
        end_time: "",
        trainer_id: "",
        trainer_name: "",
        thumbnail_img:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1733999076_quizz.jpg",
        description: "",
        max_points: null,
        status: true,
        background_image:
            "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1733129388_thumbnail.png",
        components: [],
        quiz_duration: "",
        is_graded: false,
        on_next_answer_show: false,
        show_answer_type: "",
        passing_marks: null,
        template_id: null,
        attempts_allowed: null,
        attempts_per_question: null,
        move_next_on_right_answer: false,
    });
    const particularQuizData = useGetQuizDetails(params?.quiz_id);
    const createQuiz = useQuizCreate();
    const updateQuiz = useQuizUpdate();

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        const allowedChars = /^[a-zA-Z0-9 ]*$/;
        if (name == "is_graded") {
            setQuizData({ ...quizData, [name]: !quizData?.is_graded });
        } else if (name == "on_next_answer_show") {
            setQuizData({ ...quizData, [name]: !quizData?.on_next_answer_show });
        } else if (name == "title") {
            if (!allowedChars.test(value)) {
                e.preventDefault();
            } else {
                setQuizData({ ...quizData, [name]: value });
            }
        } else {
            setQuizData({ ...quizData, [name]: value });
        }
    };

    useEffect(() => {
        if (quiz_details !== null) {
            setQuizData({
                course_id: quiz_details?.course_id,
                chapter_id: quiz_details?.chapter_id,
                title: quiz_details?.title,
                type: quiz_details?.type,
                schedule_date: quiz_details?.schedule_date,
                start_time: quiz_details?.start_time,
                end_time: quiz_details?.end_time,
                trainer_id: Number(quiz_details?.trainer_id),
                trainer_name: quiz_details?.trainer_name,
                thumbnail_img: quiz_details?.thumbnail_img,
                description: quiz_details?.description,
                max_points: quiz_details?.max_points,
                background_image: quiz_details?.background_image,
                quiz_duration: quiz_details?.quiz_duration,
                is_graded: quiz_details?.is_graded,
                on_next_answer_show: quiz_details?.on_next_answer_show,
                show_answer_type: quiz_details?.show_answer_type,
                passing_marks: quiz_details?.passing_marks,
                template_id: quiz_details?.template_id,
                show_result: quiz_details?.show_result,
                show_introduction: quiz_details?.show_introduction,
                attempts_allowed: quiz_details?.attempts_allowed,
                attempts_per_question: quiz_details?.attempts_per_question,
                move_next_on_right_answer: quiz_details?.move_next_on_right_answer,
            });
        } else {
            setQuizData({
                course_id: null,
                chapter_id: null,
                title: "",
                type: "Mixed",
                schedule_date: "",
                start_time: "",
                end_time: "",
                trainer_id: "",
                trainer_name: "",
                thumbnail_img:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1733999076_quizz.jpg",
                description: "",
                max_points: null,
                status: true,
                background_image:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1733129388_thumbnail.png",
                components: [],
                quiz_duration: "",
                is_graded: false,
                on_next_answer_show: false,
                show_answer_type: "",
                passing_marks: null,
                template_id: null,
                show_result: false,
                show_introduction: false,
                attempts_allowed: null,
                attempts_per_question: null,
                move_next_on_right_answer: false,
            });
        }
    }, [quiz_details]);

    useEffect(() => {
        if (particularQuizData.isSuccess) setQuiz_details(particularQuizData.data?.data?.[0]);
    }, [particularQuizData.status]);

    useEffect(() => {
        getTrainers();
    }, []);

    const getTrainers = async () => {
        await tanstackApi
            .get("users/get-users")
            .then((res) => {
                setTrainersList(res?.data?.data?.filter((dt) => dt?.is_trainer == true));
            })
            .catch((err) => {
                setTrainersList([]);
            });
    };

    const onDataSubmit = async (e) => {
        e.preventDefault();
        let trainer = trainersList?.find((dt) => dt?.id == quizData?.trainer_id);

        const data = formSchema.safeParse(quizData);
        if (!data.success) return toast.error(data.error.issues[0].message);

        if (params?.quiz_id !== undefined) {
            const scheduleDate = new Date(quizData.schedule_date);
            const currentDate = new Date();
            currentDate.setHours(0, 0, 0, 0); // Set current date time to 00:00:00 for comparison

            if (scheduleDate <= currentDate) {
                return toast.warning("Schedule date", {
                    description: "Schedule date should be in future",
                });
            }

            const payload = {
                id: params?.quiz_id,
                course_id: quizData?.course_id || undefined,
                chapter_id: quizData?.chapter_id || undefined,
                title: quizData?.title,
                type: quizData?.type,
                schedule_date: quizData?.schedule_date || undefined,
                start_time: quizData?.start_time ? formatTimeToSeconds(quizData?.start_time) : undefined,
                end_time: quizData?.end_time ? formatTimeToSeconds(quizData?.end_time) : undefined,
                trainer_id: quizData?.trainer_id ? quizData?.trainer_id?.toString() : undefined,
                trainer_name: trainer ? `${trainer?.first_name} ${trainer?.last_name}` : undefined,
                thumbnail_img: quizData?.thumbnail_img,
                description: quizData?.description,
                max_points: quizData?.max_points || undefined,
                background_image:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1733129388_thumbnail.png",
                components: quiz_details?.components,
                quiz_duration:
                    quizData?.start_time && quizData?.end_time
                        ? calculateTimeDuration(quizData?.start_time, quizData?.end_time)
                        : undefined,
                is_graded: quizData?.is_graded,
                on_next_answer_show: quizData?.on_next_answer_show,
                show_answer_type: quizData?.show_answer_type || undefined,
                passing_marks: quizData?.passing_marks || undefined,
                template_id: quizData?.template_id || undefined,
                show_result: quizData?.show_result,
                show_introduction: quizData?.show_introduction,
                attempts_allowed: quizData?.attempts_allowed || undefined,
                attempts_per_question: quizData?.attempts_per_question || undefined,
                move_next_on_right_answer: quizData?.move_next_on_right_answer,
            };

            updateQuiz.mutate(payload, {
                onSuccess: (data) => {
                    toast.success("Quiz has been updated Successfully", {
                        description: data?.message,
                    });
                    navigate(`/dashboard/quiz-tests`);
                },
                onError: (error) => {
                    toast.error("Something went wrong", {
                        description: error?.data?.data?.details?.[0]?.message,
                    });
                },
            });
        } else {
            const payload = {
                title: quizData?.title,
                type: quizData?.type,
                thumbnail_img: quizData?.thumbnail_img,
                description: quizData?.description,
                background_image:
                    "https://s3.ap-south-1.amazonaws.com/infowarelms/company/LMS2246/course-images/1733129388_thumbnail.png",
                components: [],
                is_public: localStorage.getItem("level") == "levelOne" ? true : false,
            };

            createQuiz.mutate(payload, {
                onSuccess: (data) => {
                    toast.success("Quiz has been created Successfully", {
                        description: data?.message,
                    });
                    window.open(
                        `https://lms-course-builder.vercel.app/dashboard/quiz-config/${data.data.id}?token=${loginToken}`,
                        "_blank",
                    );
                },
                onError: (error) => {
                    toast.error("Something went wrong", {
                        description: error?.data?.data?.details?.[0]?.message,
                    });
                },
            });
        }
    };

    const onRedirect = () => {
        if (localStorage.getItem("is_trainer") == "true") {
            navigate(`/dashboard/assigned-quizzes`);
        } else {
            navigate(`/dashboard/quiz-tests`);
        }
    };

    return (
        <div className="">
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-square-pen"></i> {params?.quiz_id !== undefined ? "Update" : "Create New"}{" "}
                    Quiz
                </h4>
                <Button className="tw-px-2 tw-py-1" onClick={onRedirect}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
            </div>
            <Tabs defaultValue="quiz_details" className="">
                {params?.quiz_id !== undefined && (
                    <TabsList className="tw-grid tw-w-[450px] tw-grid-cols-3">
                        <TabsTrigger value="quiz_details" className="tw-gap-2">
                            <i className="fa-solid fa-circle-info"></i> Details
                        </TabsTrigger>
                        <TabsTrigger value="Settings" className="tw-gap-2">
                            <i className="fa-solid fa-gear"></i> Settings
                        </TabsTrigger>
                        <TabsTrigger value="template" className="tw-gap-2">
                            <i className="fa-solid fa-brush"></i> Templates
                        </TabsTrigger>
                    </TabsList>
                )}
                <TabsContent value="quiz_details">
                    <QuizDetails
                        onChangeHandle={onChangeHandle}
                        quizData={quizData}
                        setQuizData={setQuizData}
                        trainersList={trainersList}
                    />
                </TabsContent>
                <TabsContent value="Settings">
                    <QuizSettings
                        onChangeHandle={onChangeHandle}
                        quizData={quizData}
                        setQuizData={setQuizData}
                        trainersList={trainersList}
                    />
                </TabsContent>
                <TabsContent value="template">
                    <QuizTemplates onChangeHandle={onChangeHandle} quizData={quizData} setQuizData={setQuizData} />
                </TabsContent>
            </Tabs>
            <br />
            <div className="tw-flex tw-w-full tw-justify-end tw-gap-2">
                {/* <Button className="aspect-square max-sm:p-0" variant="outline">
                    <i className="fa-solid fa-xmark"></i> Clear
                </Button> */}
                <Button onClick={onDataSubmit}>
                    <i className="fa-solid fa-floppy-disk"></i> Save
                </Button>
            </div>
        </div>
    );
};

export default CreateQuizWrapper;
