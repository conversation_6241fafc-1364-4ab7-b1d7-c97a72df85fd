import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alogClose, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { tanstackApiFormdata } from "@/react-query/api";
import { AddNewBadge, UpdateBadge } from "@/redux/gamification/action";
import { Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";

export default function BadgesForm({ open, setOpen, editData }) {
    const dispatch = useDispatch();
    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    const [questionType, setQuestionType] = useState({
        name: "",
        level: null,
        description: "",
        points_required: null,
        points_upper_limit: null,
        icon_url: "",
    });

    const RemoveImage = () => {
        setQuestionType({ ...questionType, icon_url: "" });
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setQuestionType({ ...questionType, [name]: value });
    };

    const onDataClose = () => setOpen(false);

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "ORGANIZATION-PROFILE");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setQuestionType({ ...questionType, icon_url: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setQuestionType({ ...questionType, icon_url: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setQuestionType({ ...questionType, icon_url: "" });
            });
    };
    useEffect(() => {
        if (editData !== null) {
            setQuestionType({
                name: editData?.name,
                level: Number(editData?.level),
                description: editData?.description,
                points_required: Number(editData?.points_required),
                points_upper_limit: Number(editData?.points_upper_limit),
                icon_url: editData?.icon_url,
                id: editData.id,
            });
        } else {
            setQuestionType({
                name: "",
                level: null,
                description: "",
                points_required: null,
                points_upper_limit: null,
                icon_url: "",
            });
        }
    }, [editData]);

    const onDataSubmit = (e) => {
        e.preventDefault();

        if (!questionType?.name) {
            toast?.warning("Name", {
                description: "name is required",
            });
            return false;
        } else if (!questionType?.points_required) {
            toast?.warning("Points Required", {
                description: "Points is required",
            });
            return false;
        } else if (!questionType?.description) {
            toast?.warning("Description", {
                description: "description is required",
            });
            return false;
        }
        if (editData === null) {
            const payload = {
                badges: [
                    {
                        name: questionType?.name,
                        icon_url: questionType?.icon_url,
                        level: questionType?.level,
                        description: questionType?.description,
                        points_required: questionType?.points_required,
                        points_upper_limit: questionType?.points_upper_limit,
                    },
                ],
            };
            dispatch(AddNewBadge(payload));
            setOpen(false);
        } else {
            const payload = {
                id: editData?.id,
                name: questionType?.name,
                icon_url: questionType?.icon_url,
                level: questionType?.level,
                description: questionType?.description,
                points_required: questionType?.points_required,
                points_upper_limit: questionType?.points_upper_limit,
            };
            dispatch(UpdateBadge(payload));
            setOpen(false);
        }
    };

    return (
        <div>
            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="tw-w-full tw-max-w-[800px]">
                    <DialogHeader>
                        <DialogTitle>
                            <h1 className="tw-text-2xl">{editData !== null ? "Update" : "Create a new"} Badge!</h1>
                        </DialogTitle>
                    </DialogHeader>
                    <div className="tw-grid tw-grid-cols-[250px_1fr] tw-gap-5">
                        <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                            <div className="tw-aspect-[2/1] tw-w-[100%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                                <img
                                    className="tw-aspect-[1/1] tw-w-full tw-rounded-md tw-object-contain"
                                    src={questionType?.icon_url || "/assets/thumbnail-alpha.png"}
                                />
                            </div>
                            <div className="tw-flex tw-gap-2">
                                {!questionType?.icon_url && (
                                    <Button
                                        htmlFor="display_icon"
                                        className={cn(
                                            buttonVariants({ variant: "outline" }),
                                            "aspect-square max-sm:p-0 tw-rounded-xl",
                                        )}
                                    >
                                        <Upload
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <input
                                            onChange={onImageChange}
                                            type="file"
                                            style={{ display: "none" }}
                                            id="display_icon"
                                            accept="image/*"
                                        />
                                        <Label htmlFor="display_icon" className="max-sm:sr-only">
                                            {load ? "Uploading" : "Upload Icon"} {load ? `${uploaded}%` : null}
                                        </Label>
                                    </Button>
                                )}
                                {/* {questionType?.icon_url && (
                                    <Button
                                        variant="outline"
                                        className="aspect-square max-sm:p-0"
                                        onClick={RemoveImage}
                                    >
                                        <X
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <Label className="max-sm:sr-only">Remove Icon</Label>
                                    </Button>
                                )} */}
                            </div>
                        </div>
                        <div className="tw-flex tw-flex-col tw-gap-1">
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="displayName">Badge Name</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={questionType?.name}
                                        name="name"
                                        id="displayName"
                                        placeholder="Enter name here"
                                    />
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="component_type">Level</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={questionType?.level}
                                        name="level"
                                        id="component_type"
                                        type="number"
                                        placeholder="Ex, 1 "
                                    />
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="points_required">Points Required</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={questionType?.points_required}
                                        name="points_required"
                                        id="points_required"
                                        type="number"
                                        placeholder="Ex, 100 "
                                    />
                                </div>
                            </div>
                            <div className="tw-grid tw-grid-cols-[1fr] tw-gap-3">
                                <div className="tw-space-y-1">
                                    <Label htmlFor="points_upper_limit">Points Uper Limit</Label>
                                    <Input
                                        onChange={onChangeHandle}
                                        value={questionType?.points_upper_limit}
                                        name="points_upper_limit"
                                        id="points_upper_limit"
                                        type="number"
                                        placeholder="Ex, 1000 "
                                    />
                                </div>
                            </div>
                            <div className="tw-space-y-1">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    onChange={onChangeHandle}
                                    value={questionType?.description}
                                    name="description"
                                    className="tw-text-sm"
                                    placeholder="Enter description here."
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                            <Button type="button" variant="secondary" onClick={onDataClose}>
                                <i className="fa-solid fa-xmark"></i> Close
                            </Button>
                        </DialogClose>
                        <Button type="submit" onClick={onDataSubmit}>
                            <i className="fa-solid fa-floppy-disk"></i> Save
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
