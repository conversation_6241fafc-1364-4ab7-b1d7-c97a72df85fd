import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import { ExpandablePreview } from "@/pages/dashboard/interactions/preview/expandable";
import { ProcessPreview } from "@/pages/dashboard/interactions/preview/process";
import { RevealPreview } from "@/pages/dashboard/interactions/preview/reveal";
import { StepsPreview } from "@/pages/dashboard/interactions/preview/steps";
import { TimelinePreview } from "@/pages/dashboard/interactions/preview/timeline";
import { useGetTemplate } from "@/react-query/quizz/template";
import { Check, MonitorCog } from "lucide-react";
import { useEffect, useState } from "react";

const previewMapping = {
    Accordion: ExpandablePreview,
    Process: ProcessPreview,
    Steps: StepsPreview,
    Timeline: TimelinePreview,
    Reveal: RevealPreview,
};

export default function InteractionSetting({ open, setOpen }) {
    const { content, updateChanges } = useEditInteraction();
    const [defaultSlide, setDefaultSlide] = useState("General");
    const [localData, setLocalData] = useState(content);
    const templates = useGetTemplate({ limit: 10, offset: 0 });

    useEffect(() => {
        if (content?.template_id) setLocalData((prev) => ({ ...prev, template_id: content.template_id }));
        setLocalData(content);
    }, [content]);

    const handleTypeChange = ({ type, category }) => {
        setLocalData((prev) => ({ ...prev, category, type }));
    };

    const handleTemplateChange = (template_id) => {
        setLocalData((prev) => ({ ...prev, template_id }));
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setLocalData((prev) => ({ ...prev, [name]: value }));
    };

    const interactionType = [
        { category: "Accordion", type: "Accordion" },
        { category: "Accordion", type: "Expandable" },
        { category: "Cards", type: "FlashCards" },
        { category: "Tabs", type: "Timeline" },
        { category: "Tabs", type: "Process" },
        { category: "Tabs", type: "Steps" },
        { category: "Reveal", type: "Reveal" },
    ];

    return (
        <Sheet open={open} onOpenChange={setOpen}>
            <SheetContent className="tw-w-full !tw-max-w-2xl">
                <SheetHeader>
                    <SheetTitle className="tw-flex tw-items-center tw-gap-3">
                        <MonitorCog /> Interactions Settings
                    </SheetTitle>
                </SheetHeader>
                <br />
                <div className="tw-flex tw-h-[93%] tw-flex-col tw-justify-between">
                    <Tabs value={defaultSlide} className="" onValueChange={(e) => setDefaultSlide(e)}>
                        <TabsList className="tw-grid tw-w-full tw-grid-cols-5">
                            <TabsTrigger className="tw-gap-2" value="General">
                                <i className="fa-solid fa-circle-info"></i> General
                            </TabsTrigger>

                            <TabsTrigger className="tw-gap-2" value="Layouts">
                                <i className="fa-solid fa-table-columns"></i> Layouts
                            </TabsTrigger>
                            <TabsTrigger className="tw-gap-2" value="Template">
                                <i className="fa-solid fa-folder-open"></i> Template
                            </TabsTrigger>
                        </TabsList>
                        <TabsContent value="General">
                            <div className="tw-space-y-2">
                                <div className="grid w-full max-w-sm items-center gap-1.5">
                                    <Label htmlFor="title">Title</Label>
                                    <Input
                                        type="text"
                                        value={localData.title}
                                        onChange={handleChange}
                                        name="title"
                                        id="title"
                                        placeholder="Title"
                                    />
                                </div>
                                <div className="grid w-full gap-1.5">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        value={localData.description}
                                        onChange={handleChange}
                                        name="description"
                                        id="description"
                                        placeholder="Type your description here."
                                    />
                                </div>
                                <div className="grid w-full max-w-sm items-center gap-1.5">
                                    <Label htmlFor="instructions">Instructions</Label>
                                    <Input
                                        type="text"
                                        value={localData.instructions}
                                        onChange={handleChange}
                                        name="instructions"
                                        id="instructions"
                                        placeholder="Instructions"
                                    />
                                </div>
                            </div>
                        </TabsContent>
                        <TabsContent value="Layouts">
                            <div className="tw-flex tw-h-full tw-flex-col tw-gap-10 tw-overflow-y-auto">
                                <div className="tw-grid tw-grid-cols-3 tw-grid-rows-[repeat(2,_150px)] tw-gap-4">
                                    {interactionType?.map(({ type, category }, idx) => {
                                        const Component = previewMapping[type];
                                        return (
                                            Component && (
                                                <div
                                                    onClick={() => handleTypeChange({ type, category })}
                                                    className="tw-relative tw-size-full tw-min-h-36 tw-overflow-hidden tw-rounded-lg tw-border-2 tw-p-2"
                                                    key={idx}
                                                    title={type}
                                                >
                                                    {localData?.type == type && (
                                                        <div className="tw-absolute tw-inset-0 tw-z-50 tw-flex tw-size-full tw-items-center tw-justify-center tw-bg-black/10">
                                                            <div className="tw-aspect-square tw-rounded-full tw-bg-green-500 tw-p-2 tw-text-white">
                                                                <Check />
                                                            </div>
                                                        </div>
                                                    )}
                                                    <Component />
                                                </div>
                                            )
                                        );
                                    })}
                                </div>
                            </div>
                        </TabsContent>
                        <TabsContent value="Template" className="tw-grid tw-grid-cols-3 tw-gap-3">
                            {templates.data?.data?.map((t) => {
                                return (
                                    <div
                                        key={t.id}
                                        onClick={() => handleTemplateChange(t.id)}
                                        className="tw-relative tw-flex tw-aspect-video tw-items-center tw-justify-center tw-rounded-lg tw-border tw-bg-cover tw-bg-no-repeat"
                                        style={{ backgroundImage: `url(${t.background})` }}
                                    >
                                        {t.template_title}

                                        {localData?.template_id == t.id && (
                                            <div className="tw-absolute tw-inset-0 tw-z-50 tw-flex tw-size-full tw-items-center tw-justify-center tw-bg-black/10">
                                                <div className="tw-aspect-square tw-rounded-full tw-bg-green-500 tw-p-2 tw-text-white">
                                                    <Check />
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </TabsContent>
                    </Tabs>
                    <SheetFooter>
                        <Button variant="outline" onClick={() => setOpen(false)}>
                            <i className="fa-solid fa-xmark"></i> Cancel
                        </Button>
                        <Button
                            type="submit"
                            onClick={() => {
                                updateChanges(localData);
                                setOpen(false);
                            }}
                        >
                            <i className="fa-solid fa-save"></i> Save changes
                        </Button>
                    </SheetFooter>
                </div>
            </SheetContent>
        </Sheet>
    );
}
