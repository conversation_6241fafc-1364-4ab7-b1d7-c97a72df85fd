import { Button, buttonVariants } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { useFileUpload } from "@/react-query/common";
import { Image, List, ListOrdered, Minus, Redo, Undo } from "lucide-react";
import { useEffect, useState } from "react";

const typographyList = {
    p: "Paragraph",
    1: "H1",
    2: "H2",
    3: "H3",
    4: "H4",
    5: "H5",
    6: "H6",
};

const fontFamilyList = ["Open Sans", "Lazy Dog", "Inter", "Lexend"];

const MenuBar = ({ editor }) => {
    const [typography, setTypography] = useState("p");
    const [fontFamily, setFontFamily] = useState("Inter");
    const [url, setUrl] = useState("");
    const upload = useFileUpload();

    const onImageChange = async (e) => {
        try {
            if (e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                setUrl(response?.fileUrl);
            }
        } catch (error) {
            setUrl("");
        }
    };

    useEffect(() => {
        if (url !== "") {
            editor.chain().focus().setImage({ src: url }).run();
            setUrl("");
        }
    }, [url]);

    useEffect(() => {
        const setActiveTypography = () => {
            let activeLevel = 0;
            [1, 2, 3, 4, 5, 6].forEach((level) => {
                if (editor.isActive("heading", { level })) {
                    activeLevel = level;
                }
            });
            setTypography(activeLevel !== 0 ? String(activeLevel) : "p");
        };
        setActiveTypography();
    }, [editor.isActive("heading")]);

    useEffect(() => {
        const setActiveTypography = () => {
            let activeLevel = 0;
            [1, 2, 3, 4, 5, 6].forEach((level) => {
                if (editor.isActive("heading", { level })) {
                    activeLevel = level;
                }
            });
            const activeTypography = activeLevel ? activeLevel : "p";
            setTypography(activeTypography);
        };

        setActiveTypography();
    }, [editor]);

    if (!editor) return null;

    return (
        <div className="tw-sticky tw-top-0 tw-z-10 tw-flex tw-flex-wrap tw-items-stretch tw-gap-x-4 tw-gap-y-2 tw-bg-secondary tw-p-2 tw-shadow-sm">
            <div className="tw-flex tw-gap-x-2">
                <Select
                    value={typography}
                    onValueChange={(value) => {
                        setTypography(value);
                        if (value === "p") return editor.chain().focus().setParagraph().run();
                        return editor
                            .chain()
                            .focus()
                            .toggleHeading({ level: Number(value) })
                            .run();
                    }}
                >
                    <SelectTrigger className="!tw-h-8 !tw-w-[100px] [&_svg]:tw-size-3">
                        <SelectValue placeholder="Text" />
                    </SelectTrigger>
                    <SelectContent>
                        {Object.entries(typographyList).map(([key, value]) => (
                            <SelectItem key={key} value={key}>
                                {value}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <Select
                    value={fontFamily}
                    onValueChange={(value) => {
                        setFontFamily(value);
                        editor.chain().focus().setFontFamily(value).run();
                    }}
                >
                    <SelectTrigger className="!tw-h-8 !tw-w-[100px] [&_svg]:tw-size-3">
                        <SelectValue placeholder="Font Family" />
                    </SelectTrigger>
                    <SelectContent>
                        {fontFamilyList.map((value) => (
                            <SelectItem key={value} value={value}>
                                {value}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
            <Separator orientation="vertical" className="!tw-h-8 tw-border-black" />
            <div className="tw-flex tw-gap-x-2">
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().toggleBulletList().run()}
                    variant={editor.isActive("bulletList") ? "secondary" : "outline"}
                    size="icon"
                >
                    <List />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().toggleOrderedList().run()}
                    variant={editor.isActive("orderedList") ? "secondary" : "outline"}
                    size="icon"
                >
                    <ListOrdered />
                </Button>
                <Label
                    className={cn(
                        buttonVariants({ variant: "outline", size: "sm", className: "!tw-size-8 [&_svg]:tw-size-3" }),
                        "aspect-square max-sm:p-0 tw-h-10",
                    )}
                >
                    <Image className="opacity-60 sm:-ms-1 sm:me-2" size={16} strokeWidth={2} aria-hidden="true" />
                    <input
                        onChange={onImageChange}
                        type="file"
                        style={{ display: "none" }}
                        id="bg_image"
                        accept="image/*"
                    />
                </Label>
            </div>
            <Separator orientation="vertical" className="!tw-h-8 tw-border-black" />
            <div className="tw-flex tw-gap-x-2">
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    variant="outline"
                    onClick={() => editor.chain().focus().setHorizontalRule().run()}
                    size="icon"
                >
                    <Minus />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().undo().run()}
                    size="icon"
                >
                    <Undo />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().redo().run()}
                    size="icon"
                >
                    <Redo />
                </Button>
            </div>
        </div>
    );
};

export default MenuBar;
