import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { fetchCATEGORYREQ, fetchSUBCATEGORYREQ, getAllCategories, getSubCategory } from "@/redux/category/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* GetCategories() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "course/categories/get-course-category", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data;
            // console.log("response", response);
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        // console.log("DATA", data);
        yield put(getAllCategories(data));
    } else {
        yield put(getAllCategories([]));
    }
}

function* addCategory(categoryData) {
    const categoryDataPost = yield axios
        .post(CONSTANTS.getAPI() + "course/categories/create-category", categoryData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            // console.log("errmsg", errMsg);
            return errMsg;
        });
    if (categoryDataPost?.success) {
        yield put(fetchCATEGORYREQ());
        yield put(AlertSnackInfo({ message: categoryDataPost?.message, result: categoryDataPost?.success }));
        // console.log("roleData", categoryDataPost);
    } else {
        yield put(AlertSnackInfo({ message: categoryDataPost?.message, result: categoryDataPost?.success }));
        // console.log("roleData", categoryDataPost);
    }
}

function* UpdateCategory(categoryData) {
    const categoryUpdate = yield axios
        .put(CONSTANTS.getAPI() + "course/categories/update-category", categoryData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (categoryUpdate?.success) {
        yield put(AlertSnackInfo({ message: categoryUpdate?.message, result: categoryUpdate?.success }));
        yield put(fetchCATEGORYREQ());
    } else {
        yield put(AlertSnackInfo({ message: categoryUpdate?.message, result: categoryUpdate?.success }));
        // console.log("categoryUpdate", categoryUpdate);
    }
}

function* GetSubCategory(Data) {
    // console.log('DATA',Data);
    const data = yield axios
        .get(CONSTANTS.getAPI() + `course/categories/get-sub-categories/${Data.payload.categoryId}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data;
            // console.log("response", response);
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });
    if (data) {
        // console.log("DATA", data);
        yield put(getSubCategory(data));
    } else {
        yield put(getSubCategory([]));
    }
}

function* addSubCategory(subCategoryData) {
    var data = {
        categoryId: parseInt(subCategoryData.payload.category_id),
    };
    const subCategoryDataPost = yield axios
        .post(CONSTANTS.getAPI() + "course/categories/create-sub-category", subCategoryData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            // console.log("errmsg", errMsg);
            return errMsg;
        });
    if (subCategoryDataPost?.success) {
        yield put(fetchSUBCATEGORYREQ(data));
        yield put(AlertSnackInfo({ message: subCategoryDataPost?.message, result: subCategoryDataPost?.success }));
        // console.log("categeryDtat", subCategoryDataPost);
    } else {
        yield put(AlertSnackInfo({ message: subCategoryDataPost?.message, result: subCategoryDataPost?.success }));
        // console.log("categeryDtat", subCategoryDataPost);
    }
}

function* updateSubCategory(subCategoryData) {
    var data = {
        categoryId: parseInt(subCategoryData.payload.category_id),
    };
    const updateSubCate = yield axios
        .put(CONSTANTS.getAPI() + `course/categories/update-sub-category`, subCategoryData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })

        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            const errMsg = err.message;
            return errMsg;
        });
    if (updateSubCate) {
        yield put(AlertSnackInfo({ message: updateSubCate?.message, result: updateSubCate?.success }));
        yield put(fetchSUBCATEGORYREQ(data));
    } else {
        yield put(AlertSnackInfo({ message: updateSubCate?.message, result: updateSubCate?.success }));
        // console.log("categoryUpdate", updateSubCate);
    }
}

export function* CategoryWatcher() {
    yield takeEvery(actions.FETCH_CATEGORY_LIST_REQ, GetCategories);
    yield takeEvery(actions.ADD_NEW_CATEGORY, addCategory);
    yield takeEvery(actions.UPDATE_CATEGORY, UpdateCategory);
    yield takeEvery(actions.FETCH_SUB_CATEGORY_REQ, GetSubCategory);
    yield takeEvery(actions.ADD_NEW_SUB_CATEGORY, addSubCategory);
    yield takeEvery(actions.UPDATE_SUB_CATEGORY, updateSubCategory);
}

export default function* CategorySaga() {
    yield all([fork(CategoryWatcher)]);
}
