import { cn } from "@/lib/utils";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import DNDImagesAndBox from "@/pages/dashboard/course-player/quiz-content/types/DNDImagesAndBox";
import FillinTheBlank from "@/pages/dashboard/course-player/quiz-content/types/FillinTheBlank";
import HotspotDND from "@/pages/dashboard/course-player/quiz-content/types/HotspotDND";
import HotspotDNDImage from "@/pages/dashboard/course-player/quiz-content/types/HotspotDNDImage";
import LongAnswer from "@/pages/dashboard/course-player/quiz-content/types/LongAnswer";
import MatchTheFollowing from "@/pages/dashboard/course-player/quiz-content/types/MatchTheFollowing";
import SequenceArrange from "@/pages/dashboard/course-player/quiz-content/types/SequenceArrange";
import Singlechoice from "@/pages/dashboard/course-player/quiz-content/types/Singlechoice";
import SinglechoiceMedia from "@/pages/dashboard/course-player/quiz-content/types/SinglechoiceMedia";
import StringDropdown from "@/pages/dashboard/course-player/quiz-content/types/StringDropdown";
import { useFeedbackStore } from "@/store/feedback";
import { motion } from "framer-motion";
import parse from "html-react-parser";
import { useEffect, useState } from "react";

const componentMap = {
    singlechoice: Singlechoice,
    singlechoice_media: SinglechoiceMedia,
    fillin_the_blank: FillinTheBlank,
    dnd_image_box: DNDImagesAndBox,
    sequence_arrange: SequenceArrange,
    string_dropdown: StringDropdown,
    match_the_following: MatchTheFollowing,
    hotspot_dnd: HotspotDND,
    hotspot_dnd_image: HotspotDNDImage,
    long_answer: LongAnswer,
    // hotspot_dnd: HotspotDNDText,
};

const SlideWrapper = ({ components, isStarted, template, onUserReponseSave }) => {
    const [index, setIndex] = useState(0);
    const [start, setStart] = useState(false);
    const feedbacks = useFeedbackStore((state) => state.data);
    const feedbackData = useFeedbackStore((state) => state.setData);
    const status = useFeedbackStore((state) => state.isOpen);
    const actions = useFeedbackStore();
    const bgVariants = {
        initial: { rotate: 0 },
        animate: { rotate: [360, 0] },
    };
    const quizzTemplate = template?.lms_template;
    const { showAnswerType } = usePlayer();

    const [slides, setSlides] = useState([]);

    useEffect(() => {
        if (components?.length > 0) {
            let remapped = components?.map((slide, idx) => {
                const comp = componentMap[slide?.componentTypeId];
                return comp ? { id: idx + 1, data: slide, comp, template: quizzTemplate } : null;
            });
            setSlides(remapped);
        } else {
            setSlides([]);
        }
    }, [components]);

    const handleNextSlide = (e) => {
        if (index === slides.length - 1 && slides[index]?.data.attempted)
            return onUserReponseSave(
                e,
                slides.reduce((sum, item) => sum + (item.data.earnedPoints || 0), 0),
                slides,
            );
        if (index == 0 && start === false) setStart(true);

        const answerData = [...slides];
        const current = answerData[index].data;

        if (slides[index]?.data.attempted) {
            if (status) actions.close();
            return setIndex((prevIndex) => prevIndex + 1);
        }

        const feedbackMapping = {
            success: "correctAns",
            error: "incorrectAns",
            warning: "incorrectAns",
            "show-correct-answer": "rightAnswerToShow",
        };

        const feedbackType =
            current.earnedPoints === current.points ? "success" : current.earnedPoints === 0 ? "error" : "warning";
        const feedbackMessage = feedbackMapping[showAnswerType] || feedbackMapping[feedbackType];
        feedbackData({
            feedback: current.feedbacks[feedbackMessage] || current.feedbacks[feedbackMapping[feedbackType]],
            type: feedbackType,
        });

        if (!current.attempted) {
            actions.open();
            current.attempted = true;
            return setSlides(answerData);
        }
    };

    const handlePrevSlide = () => {
        if (index === 0) return;
        if (index === 1) setStart(false);
        setIndex((prevIndex) => prevIndex - 1);
    };

    const onComponentAnswer = (sequence, answer, points) => {
        const answerData = [...slides];
        const current = answerData[sequence - 1];
        current.data.answerKey = answer;
        current.data.earnedPoints = points;
        setSlides(answerData);
    };

    return (
        <div className="tw-relative tw-h-full tw-w-full tw-overflow-hidden tw-rounded-xl">
            <div
                className={cn(
                    `tw-relative tw-h-full tw-w-full tw-overflow-hidden tw-rounded-[10px] tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat !tw-font-lazyDog`,
                )}
                style={{
                    "--primary-color": quizzTemplate?.elements?.colors?.primary?.background_color,
                    "--primary-text-color": quizzTemplate?.elements?.colors?.primary?.color,
                    "--secondary-color": quizzTemplate?.elements?.colors?.secondary?.background_color,
                    "--secondary-text-color": quizzTemplate?.elements?.colors?.secondary?.color,
                }}
            >
                <motion.div
                    variants={bgVariants}
                    initial="initial"
                    transition={{ duration: 10, ease: "easeInOut", repeat: Infinity }}
                    className="tw-absolute tw-inset-0 tw-z-0 tw-h-[100%] tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{
                        backgroundImage: `url("${quizzTemplate?.background}")`,
                    }}
                ></motion.div>
                <motion.div
                    initial={{ x: 0 }}
                    animate={{
                        x: `calc(${index * -100}% - ${index * 2}rem)`,
                    }}
                    transition={{ duration: 1.5, ease: "easeInOut" }}
                    className="tw-relative tw-z-10 tw-flex tw-h-full tw-w-full tw-items-center tw-gap-8"
                >
                    {(slides || [])?.map((slide, idx) => {
                        return (
                            <SlideContainer
                                key={idx}
                                slide={slide}
                                handleNextSlide={handleNextSlide}
                                handlePrevSlide={handlePrevSlide}
                                onComponentAnswer={onComponentAnswer}
                                template={slide.template}
                                data={slide?.data}
                                sequence={slide?.id}
                            />
                        );
                    })}
                </motion.div>
            </div>

            <motion.div
                initial={{ y: "110%" }}
                animate={{
                    y: Boolean(status) === true ? "-100%" : "110%",
                }}
                transition={{
                    duration: 0.5,
                    ease: "easeInOut",
                }}
                className={cn(
                    "tw-absolute tw-bottom-0 tw-left-0 tw-right-0 tw-z-[1000] tw-flex tw-w-full tw-items-center tw-justify-center !tw-font-lazyDog",
                )}
            >
                <div
                    className={cn(
                        "gap-2 p-3 tw-relative tw-flex tw-min-h-8 tw-w-[90%] tw-items-center tw-justify-between tw-rounded-md tw-text-[40px] tw-leading-[40px] tw-text-white",
                        {
                            "tw-bg-green-500": feedbacks?.type == "success",
                            "tw-bg-red-500": feedbacks?.type == "error",
                            "tw-bg-yellow-500": feedbacks?.type == "warning",
                        },
                    )}
                >
                    {parse(String(feedbacks?.feedback)) ??
                        "Vorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac "}
                    <button
                        onClick={() => actions.action(false)}
                        className="tw-absolute tw--right-2 tw--top-2 tw-flex tw-aspect-square tw-size-8 tw-items-center tw-justify-center tw-rounded-full tw-bg-white tw-text-black"
                    >
                        <i className="fa-solid fa-xmark tw-text-lg tw-leading-none"></i>
                    </button>
                </div>
            </motion.div>
        </div>
    );
};

export default SlideWrapper;

function SlideContainer({ slide, handleNextSlide, handlePrevSlide, data, sequence, onComponentAnswer, template }) {
    return (
        slide?.comp !== undefined && (
            <slide.comp
                className="tw-min-w-full tw-flex-grow"
                handleNextSlide={handleNextSlide}
                handlePrevSlide={handlePrevSlide}
                onComponentAnswer={onComponentAnswer}
                template={template}
                data={data}
                sequence={sequence}
            />
        )
    );
}
