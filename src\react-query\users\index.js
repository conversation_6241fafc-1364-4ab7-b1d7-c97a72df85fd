import { tanstack<PERSON>pi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useAllGetUsersListing = (level) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["users-listing", { level, userId }],
        queryFn: async () => (await tanstackApi.get("users/get-all-users")).data,
        enabled: level == "levelOne",
    });
};

export const useGetUsersListing = (level) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["users-listing", { level, userId }],
        queryFn: async () => (await tanstackApi.get("users/get-users")).data,
        enabled: level == "levelTwo",
    });
};

export const useCreateUser = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("users/add-user", data)).data,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["users-listing"] });
        },
    });
};

export const useUpdateUser = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.put("users/update-user", data)).data,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["users-listing"] });
        },
    });
};

export const useGetUsersLeaderboard = () => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["users-leaderboard", { userId }],
        queryFn: async () => (await tanstackApi.get("users/get-leaderboard")).data,
    });
};
