import { cn } from "@/lib/utils";
import { useEffect } from "react";

export default function MicroQuizPreview({
    onMountSlide,
    formatNumber,
    index,
    title,
    image,
    currentId,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
}) {
    const urlId = `#slide_${index}`;

    useEffect(() => {
        if (urlId == currentId) {
            const element = document.querySelector(`#slide_preview_${index}`);
            element.scrollIntoView({ behavior: "smooth" });
        }
    }, [currentId]);

    return (
        <div
            draggable
            onDragStart={() => handleDragStart(index)}
            onDragOver={() => handleDragOver(index)}
            onDragEnd={handleDragEnd}
            key={index}
            id={`slide_preview_${index}`}
            onClick={() => onMountSlide(index)}
            className="tw-relative tw-grid tw-h-full tw-w-full tw-cursor-pointer tw-gap-1.5 tw-rounded-lg tw-border-[1px] tw-border-gray-200 tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat tw-p-1.5 tw-shadow-sm"
        >
            <div
                className={cn(
                    "tw-absolute tw-inset-0 tw-size-full tw-rounded-lg",
                    urlId == currentId ? "tw-bg-green-100" : "tw-bg-transparent",
                )}
            />
            <div className="tw-relative tw-z-10 tw-flex tw-flex-col tw-items-start tw-gap-2">
                <div className="tw-flex tw-w-full tw-justify-between">
                    <div className="tw-text-md tw-flex tw-h-[30px] tw-w-[30px] tw-items-center tw-justify-center tw-rounded-lg tw-border-[1px] tw-bg-white tw-font-bold tw-text-slate-500 tw-shadow-sm">
                        {formatNumber(index + 1)}
                    </div>
                    <img src={`/assets/${image}`} className="tw-aspect-square tw-w-[30px] tw-shrink-0" alt="" />
                </div>
                <div className="tw-flex tw-flex-col tw-items-center tw-justify-between tw-gap-2">
                    <p className="tw-text-sm tw-font-medium tw-leading-tight">{title}</p>
                </div>
            </div>
        </div>
    );
}
