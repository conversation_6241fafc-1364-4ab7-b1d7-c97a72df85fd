"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { Check, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

export default function ImageLabelQuestion({ question, onAnswerSubmit, showCorrectAnswer, savedAnswer }) {
    const [labels, setLabels] = useState([]);
    const [dropZones, setDropZones] = useState([]);
    const [activeId, setActiveId] = useState(null);
    const [submitted, setSubmitted] = useState(false);
    const [imageLoaded, setImageLoaded] = useState(false);
    const imageContainerRef = useRef(null);

    const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor));

    useEffect(() => {
        // Create draggable labels
        const initialLabels = question.labels.map((label) => ({
            id: label.id,
            text: label.text,
        }));

        // Create drop zones
        const initialDropZones = question.dropZones.map((zone) => ({
            ...zone,
            currentLabelId: null,
        }));

        // If we have a saved answer, populate the drop zones
        if (savedAnswer) {
            const updatedDropZones = initialDropZones.map((zone) => ({
                ...zone,
                currentLabelId: savedAnswer[zone.id] || null,
            }));

            // Filter out the labels that have been used
            const usedLabelIds = Object.values(savedAnswer);
            const updatedLabels = initialLabels.filter((label) => !usedLabelIds.includes(label.id));

            setDropZones(updatedDropZones);
            setLabels(updatedLabels);
            setSubmitted(true);
        } else {
            // Shuffle the labels
            const shuffledLabels = [...initialLabels].sort(() => Math.random() - 0.5);
            setLabels(shuffledLabels);
            setDropZones(initialDropZones);
        }
    }, [question, savedAnswer]);

    function handleDragStart(event) {
        setActiveId(event.active.id);
    }

    function handleDragEnd(event) {
        const { active, over } = event;
        setActiveId(null);

        if (!over) return;

        // If dropping on a drop zone
        if (over.id.startsWith("dropzone-")) {
            const labelIndex = labels.findIndex((l) => l.id === active.id);
            const dropZoneIndex = dropZones.findIndex((d) => d.id === over.id);

            if (labelIndex !== -1 && dropZoneIndex !== -1) {
                // Update the drop zone with the label's id
                const updatedDropZones = [...dropZones];
                updatedDropZones[dropZoneIndex].currentLabelId = labels[labelIndex].id;

                // Remove the label
                const updatedLabels = labels.filter((_, index) => index !== labelIndex);

                setDropZones(updatedDropZones);
                setLabels(updatedLabels);
            }
        }
    }

    function handleDragCancel() {
        setActiveId(null);
    }

    const handleSubmit = () => {
        setSubmitted(true);

        // Extract the answers from the drop zones
        const answers = {};
        dropZones.forEach((zone) => {
            if (zone.currentLabelId) {
                answers[zone.id] = zone.currentLabelId;
            }
        });

        // Check if all zones are filled
        const allFilled = dropZones.every((zone) => zone.currentLabelId !== null);

        if (allFilled) {
            // Check if the answers match the correct answers
            const correct = dropZones.every((zone) => zone.currentLabelId === zone.correctLabelId);
            onAnswerSubmit(answers, correct);
        } else {
            onAnswerSubmit(answers, false);
        }
    };

    const renderImage = () => {
        return (
            <div
                ref={imageContainerRef}
                className="tw-relative tw-mb-6 tw-overflow-hidden tw-rounded-md tw-border-2 tw-border-orange-800"
            >
                <img
                    src={
                        question.imageUrl ||
                        "https://kzmq43wl56a6zir76lhd.lite.vusercontent.net/placeholder.svg?height=400&width=600"
                    }
                    alt={question.question}
                    className="tw-h-auto tw-w-full"
                    onLoad={() => setImageLoaded(true)}
                />

                {imageLoaded &&
                    dropZones.map((zone) => {
                        const label = question.labels.find((l) => l.id === zone.currentLabelId);
                        const isCorrect = zone.currentLabelId === zone.correctLabelId;
                        const showCorrectLabel = showCorrectAnswer && !isCorrect;
                        const correctLabel = question.labels.find((l) => l.id === zone.correctLabelId);

                        return (
                            <div
                                key={zone.id}
                                id={zone.id}
                                className="tw-absolute"
                                style={{
                                    left: `${zone.x}%`,
                                    top: `${zone.y}%`,
                                    width: `${zone.width}%`,
                                    height: `${zone.height}%`,
                                }}
                            >
                                <div
                                    className={`tw-h-full tw-w-full tw-rounded-md tw-border-2 ${
                                        submitted
                                            ? isCorrect
                                                ? "tw-border-green-500 tw-bg-green-500/20"
                                                : "tw-border-red-500 tw-bg-red-500/20"
                                            : "tw-border-orange-400 tw-bg-orange-400/20"
                                    }`}
                                >
                                    {label && (
                                        <div className="tw-absolute tw-left-0 tw-top-full tw-mt-1 tw-whitespace-nowrap tw-rounded tw-bg-slate-800 tw-px-2 tw-py-1 tw-text-sm">
                                            {label.text}
                                            {submitted &&
                                                (isCorrect ? (
                                                    <Check className="tw-ml-1 tw-inline tw-text-green-400" />
                                                ) : (
                                                    <X className="tw-ml-1 tw-inline tw-text-red-400" />
                                                ))}
                                        </div>
                                    )}

                                    {showCorrectLabel && correctLabel && (
                                        <div className="tw-absolute tw-left-0 tw-top-full tw-mt-8 tw-whitespace-nowrap tw-rounded tw-bg-green-700 tw-px-2 tw-py-1 tw-text-sm">
                                            Correct: {correctLabel.text}
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })}
            </div>
        );
    };

    const renderLabels = () => {
        return (
            <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-2">
                {labels.map((label) => (
                    <div
                        key={label.id}
                        id={label.id}
                        className={`tw-cursor-grab tw-rounded-md tw-bg-orange-700 tw-px-3 tw-py-2 ${
                            activeId === label.id ? "tw-opacity-50" : ""
                        }`}
                        draggable
                        onDragStart={(e) => {
                            e.dataTransfer.setData("text/plain", label.id);
                            handleDragStart({ active: { id: label.id } });
                        }}
                    >
                        {label.text}
                    </div>
                ))}
            </div>
        );
    };

    return (
        <div className="tw-space-y-4">
            <p className="tw-mb-2 tw-text-sm tw-text-orange-300">
                Drag the labels to the correct positions on the image
            </p>

            {renderImage()}

            <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                onDragCancel={handleDragCancel}
                modifiers={[restrictToWindowEdges]}
            >
                {!submitted && renderLabels()}
            </DndContext>

            {!submitted && dropZones.some((d) => d.currentLabelId !== null) && (
                <Button onClick={handleSubmit} className="tw-mt-4 tw-bg-teal-700 tw-text-white hover:tw-bg-teal-600">
                    Submit Answer
                </Button>
            )}

            {submitted && (
                <div className="tw-mt-4 tw-flex tw-items-center tw-gap-2 tw-rounded-md tw-bg-slate-700/50 tw-p-3">
                    <span>Your answer is:</span>
                    {dropZones.every((zone) => zone.currentLabelId === zone.correctLabelId) ? (
                        <Check className="tw-text-green-400" />
                    ) : (
                        <X className="tw-text-red-400" />
                    )}
                </div>
            )}
        </div>
    );
}
