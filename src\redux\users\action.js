import {
    ADD_USER_DATA,
    ENROLLMENT_APPROVE,
    FETCH_ENROLL_DATA_REQ,
    FETCH_LEVEL_TWO_USERS__REQ,
    FETCH_SUB_USERS_LIST_REQ,
    FETCH_TRAINER_LIST_REQ,
    FETCH_USERS_LIST_REQ,
    GET_ENROLL_DATA,
    GET_LEVEL_TWO_USERS,
    GET_SUB_USERS_LIST,
    GET_TRAINER_LIST,
    GET_USERS_LIST,
    MAKE_TRAINER,
    RE_ENROLL_REQUEST,
    UPDATE_USER_DATA,
    USER_ALERT_INFO,
} from "@/redux-types";

export const fetchUSERSReq = () => {
    return {
        type: FETCH_USERS_LIST_REQ,
    };
};

export const getAllUSERS = (usersList) => {
    return {
        type: GET_USERS_LIST,
        payload: usersList,
    };
};

export const fetchEnrollDataReq = (data) => {
    return {
        type: FETCH_ENROLL_DATA_REQ,
        payload: data,
    };
};

export const getEnrollData = (enrollData) => {
    return {
        type: GET_ENROLL_DATA,
        payload: enrollData,
    };
};

export const onReEnrollRequest = (data) => {
    return {
        type: RE_ENROLL_REQUEST,
        payload: data,
    };
};

export const ApproveEnrollments = (enrollData) => {
    return {
        type: ENROLLMENT_APPROVE,
        payload: enrollData,
    };
};

export const makeTrainer = (data) => {
    return {
        type: MAKE_TRAINER,
        payload: data,
    };
};

export const fetchTrainersReq = () => {
    return {
        type: FETCH_TRAINER_LIST_REQ,
    };
};

export const getAllTrainers = (usersList) => {
    return {
        type: GET_TRAINER_LIST,
        payload: usersList,
    };
};

export const addUserData = (userData) => {
    return {
        type: ADD_USER_DATA,
        payload: userData,
    };
};

export const UpdateUserData = (userData) => {
    return {
        type: UPDATE_USER_DATA,
        payload: userData,
    };
};

export const UserAlertInfo = (info) => {
    return {
        type: USER_ALERT_INFO,
        payload: info,
    };
};

export const fetchSubUSERSReq = (userId) => {
    return {
        type: FETCH_SUB_USERS_LIST_REQ,
        payload: userId,
    };
};

export const getAllSubUSERS = (subUsersList) => {
    return {
        type: GET_SUB_USERS_LIST,
        payload: subUsersList,
    };
};

export const fetchLevelTwoUSERSReq = (userId) => {
    return {
        type: FETCH_LEVEL_TWO_USERS__REQ,
        payload: userId,
    };
};

export const getLevelTwoUSERS = (userList) => {
    return {
        type: GET_LEVEL_TWO_USERS,
        payload: userList,
    };
};
