import { AiOutlineMergeCells, AiOutlineUsergroupAdd } from "react-icons/ai";
import { BiBookReader } from "react-icons/bi";
import { FaCertificate } from "react-icons/fa";
import { GiBookmark, GiProgression } from "react-icons/gi";
import { HiUserGroup } from "react-icons/hi";
import { ImUserTie } from "react-icons/im";
import { IoMdSchool } from "react-icons/io";
import { TbRoute } from "react-icons/tb";
import { Link } from "react-router-dom";

const StatisticCard = ({ title, statsNo, link }) => {
    return (
        <Link to={`${link}`}>
            <div className="stats-card-mn">
                {title === "Learners" && <ImUserTie id="stats-card-icon" />}
                {(title === "Users" || title === "Trainers") && <HiUserGroup id="stats-card-icon" />}
                {(title === "Total Courses" || title === "Total Scorm Courses") && (
                    <BiBookReader id="stats-card-icon" />
                )}
                {title === "In Progress" && <GiProgression id="stats-card-icon" />}
                {title === "Learning Path" && <TbRoute id="stats-card-icon" />}
                {title === "Groups" && <AiOutlineUsergroupAdd id="stats-card-icon" />}
                {title === "Enrolled Courses" && <IoMdSchool id="stats-card-icon" />}
                {title === "Bundles" && <AiOutlineMergeCells id="stats-card-icon" />}
                {title === "Completed" && <GiBookmark id="stats-card-icon" />}
                {title === "Certificates" && <FaCertificate id="stats-card-icon" />}
                <div>
                    <span>{title}</span>
                    <h5 className="dash-mn-heading-sml">{`${statsNo}`}</h5>
                </div>
            </div>
        </Link>
    );
};

export default StatisticCard;
