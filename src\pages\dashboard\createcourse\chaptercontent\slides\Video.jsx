import SlideHeader, { AddContentFunction } from "@/pages/dashboard/createcourse/chaptercontent/slides/common";
import { useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";

const Video = ({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    onSetDelete,
    contentArray,
    setContentArray,
    setSelectedContent,
    setCurrentId,
    setOpenIconDialog,
}) => {
    const [editable, setEditable] = useState(false);
    const headingRef = useRef(null);
    const containerRef = useRef(null);
    const inView = useInView(containerRef, { amount: 0.8 });
    useEffect(() => {
        if (inView) setCurrentId(`#slide_${index}`);
    }, [inView]);
    return (
        <div
            ref={containerRef}
            className="tw-grid tw-min-h-[60vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${template?.background || content?.background})` }}
        >
            <SlideHeader
                index={index}
                content={content}
                contentArray={contentArray}
                onSlideEdit={onSlideEdit}
                onSetDelete={onSetDelete}
                onRemoveSlide={onRemoveSlide}
                setOpenIconDialog={setOpenIconDialog}
                setContentArray={setContentArray}
            />
            {content?.content_url == null ? (
                <AddContentFunction onSlideEdit={onSlideEdit} content={content} index={index} />
            ) : (
                <div className={`tw-w-full tw-gap-5 tw-rounded-md tw-p-3`}>
                    <div className="tw-h-full tw-rounded-lg tw-border-[2px] tw-border-dashed tw-border-slate-700 tw-p-3 tw-shadow-sm">
                        <video className="tw-aspect-[5.4/2] tw-w-full tw-rounded-lg" controls>
                            <source src={content?.content_url} type="video/mp4" />
                        </video>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Video;
