import { useState } from "react";

export const useDraggable = (id, data, disabled = false) => {
    const [isDragging, setIsDragging] = useState(false);

    const dragHandleProps = {
        draggable: !disabled,
        onDragStart: (e) => {
            if (disabled) return;
            setIsDragging(true);
            e.dataTransfer.setData("application/json", JSON.stringify({ id, ...data }));
            e.dataTransfer.effectAllowed = "move";
        },
        onDragEnd: () => {
            setIsDragging(false);
        },
    };

    return { isDragging, dragHandleProps };
};
