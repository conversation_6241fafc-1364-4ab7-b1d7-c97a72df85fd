"use client";

import { useState, useEffect } from "react";
import { Check, X } from "lucide-react";

export default function SingleChoiceQuestion({ question, onAnswerSubmit, showCorrectAnswer, savedAnswer }) {
    const [selectedOption, setSelectedOption] = useState(null);

    useEffect(() => {
        if (savedAnswer !== undefined) {
            setSelectedOption(savedAnswer);
        }
    }, [savedAnswer]);

    const handleOptionSelect = (optionIndex) => {
        setSelectedOption(optionIndex);
        const isCorrect = optionIndex === question.correctAnswer;
        onAnswerSubmit(optionIndex, isCorrect);
    };

    return (
        <div className="tw-space-y-3">
            {question.options.map((option, index) => {
                const isSelected = selectedOption === index;
                const isCorrect = index === question.correctAnswer;
                const showCorrect = showCorrectAnswer && isCorrect;

                return (
                    <button
                        key={index}
                        onClick={() => handleOptionSelect(index)}
                        className={`tw-flex tw-w-full tw-items-center tw-gap-2 tw-rounded-md tw-p-3 tw-text-left tw-transition-colors ${
                            isSelected
                                ? isCorrect
                                    ? "tw-border-2 tw-border-green-500 tw-bg-green-700/50"
                                    : "tw-border-2 tw-border-red-500 tw-bg-red-700/50"
                                : showCorrect
                                  ? "tw-border-2 tw-border-green-500 tw-bg-green-700/50"
                                  : "tw-border-2 tw-border-orange-800 tw-bg-orange-900/30 hover:tw-bg-orange-800/50"
                        }`}
                        disabled={selectedOption !== null}
                    >
                        <span className="tw-flex tw-h-8 tw-w-8 tw-items-center tw-justify-center tw-rounded-full tw-bg-orange-800 tw-text-white">
                            {index + 1}
                        </span>
                        <span className="tw-flex-1">{option}</span>
                        {isSelected &&
                            (isCorrect ? <Check className="tw-text-green-400" /> : <X className="tw-text-red-400" />)}
                        {showCorrect && !isSelected && <Check className="tw-text-green-400" />}
                    </button>
                );
            })}
        </div>
    );
}
