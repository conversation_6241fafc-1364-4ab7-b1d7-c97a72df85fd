import { cn } from "@/lib/utils";

const MainSlideLayout = ({ handleNextSlide, className, children }) => {
    return (
        <div className={cn("tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center", className)}>
            <div className="tw-relative tw-h-[60%] tw-w-[80%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-[#FFFFFF]">
                <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center">
                    {children}
                    <button
                        onClick={handleNextSlide}
                        className="tw-absolute tw-bottom-[-10%] tw-right-[-2%] tw-aspect-square tw-w-[122px] tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-text-4xl"
                    >
                        Start
                    </button>
                </div>
                <img src="/quiz/Blitz.png" alt="" className="tw-absolute tw-right-[-8%] tw-top-[-22%] tw-z-[5]" />
                <img
                    src="/quiz/Group 59468.png"
                    alt=""
                    className="tw-absolute tw-bottom-[-18%] tw-left-[-11%] tw-z-[5]"
                />
                <img
                    src="/quiz/Frame 1597883374.png"
                    alt=""
                    className="tw-absolute tw-left-[-11.3%] tw-top-[-32.6%] tw-z-[-5]"
                />
                <div className="tw-absolute tw--left-10 tw-top-10 tw-z-[-10] tw-h-[102%] tw-w-[102%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-[#FE5C96]"></div>
            </div>
        </div>
    );
};

export default MainSlideLayout;
