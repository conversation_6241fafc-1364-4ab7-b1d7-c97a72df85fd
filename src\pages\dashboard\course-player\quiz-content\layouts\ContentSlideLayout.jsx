import { cn } from "@/lib/utils";
import { quizContentTypes } from "@/pages/dashboard/quizzes/QuiestionTypes";
import { AnimatePresence, motion, useInView } from "framer-motion";
import { useEffect, useRef, useState } from "react";

const ContentSlideLayout = ({ handleNextSlide, handlePrevSlide, className, data, question, children, template }) => {
    const ref = useRef(null);
    const inView = useInView(ref);
    const slideVariants = {
        initial: { scale: 0.5 },
        animate: { scale: 1 },
        exit: { scale: 0.5 },
    };
    const quizIcon = quizContentTypes.find((content) => content.contentType === data?.componentTypeId);
    const quizzTemplate = template;
    const [isOpen, setIsOpen] = useState(false);
    const [manually, setManually] = useState(false);

    console.log(data?.styles);

    useEffect(() => {
        if (!isOpen && inView) setIsOpen(true);
    }, [inView]);

    useEffect(() => {
        let timer;
        if (inView && isOpen && !manually) {
            timer = setTimeout(() => {
                setIsOpen(false);
            }, 5000);
        }
        return () => clearTimeout(timer);
    }, [inView, isOpen, manually]);

    const popoverVariants = {
        initial: {
            opacity: 0,
            scale: 0,
        },
        open: {
            opacity: 1,
            scale: 1,
        },
    };

    return (
        <AnimatePresence mode="wait">
            <motion.div
                ref={ref}
                variants={slideVariants}
                initial="initial"
                whileInView={inView ? "animate" : "initial"}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                exit="exit"
                className={cn(
                    "tw-relative tw-z-[100] tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center",
                    className,
                )}
            >
                <div className="tw-relative tw-h-[90%] tw-w-[90%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-white">
                    <div
                        style={{
                            backgroundImage: `url(${data?.question_background})`,
                        }}
                        className="tw-flex tw-h-full tw-w-full tw-flex-col tw-items-center tw-justify-center tw-bg-cover tw-bg-no-repeat tw-px-6 tw-pb-12 tw-pt-6"
                    >
                        <div>
                            <button
                                onClick={() => {
                                    setIsOpen(!isOpen);
                                    setManually(true);
                                }}
                                style={{
                                    backgroundColor:
                                        quizzTemplate?.elements?.info_button?.background_color ?? "#FE5C96",
                                    borderColor: quizzTemplate?.elements?.info_button?.border_color ?? "#3D3D3D",
                                    color: quizzTemplate?.elements?.info_button?.color,
                                    fontFamily: quizzTemplate?.elements?.info_button?.font_family,
                                    borderWidth: quizzTemplate?.elements?.info_button?.border_width ?? "7px",
                                }}
                                className="tw-absolute tw-right-2 tw-top-2 tw-z-40 tw-aspect-square tw-w-[60px] tw-rounded-full"
                            >
                                <i
                                    className={cn(
                                        "fa-solid tw-text-[28px] tw-leading-[28px] tw-text-[#333333]",
                                        isOpen ? "fa-close" : "fa-info",
                                    )}
                                />
                            </button>
                        </div>
                        <motion.div
                            variants={popoverVariants}
                            initial="initial"
                            animate={isOpen ? "open" : "initial"}
                            transition={{ duration: 0.25, ease: "easeInOut" }}
                            className="tw-absolute tw-left-4 tw-top-4 tw-z-50 tw-w-[90%] tw-rounded-lg !tw-border-4 !tw-border-[#3D3D3D] tw-bg-white tw-px-8 tw-py-3 !tw-font-lazyDog tw-text-black tw-shadow-sm"
                        >
                            <div className="tw-grid tw-grid-cols-8">
                                <div className="tw-col-span-7">
                                    {question?.data && (
                                        <div
                                            className="tw-flex tw-items-start tw-gap-4 tw-text-[40px] tw-leading-[40px]"
                                            style={{
                                                color: data?.styles?.question?.color,
                                                fontFamily: data?.styles?.question?.fontFamily,
                                                fontSize: data?.styles?.question?.fontSize,
                                            }}
                                        >
                                            <p>{question?.index}. </p>
                                            <p>{question?.data}</p>
                                        </div>
                                    )}
                                    <p
                                        className={cn("tw-text-[28px] tw-leading-[28px]", question?.data && "tw-mt-4")}
                                        style={{
                                            color: data?.styles?.question?.color,
                                            fontFamily: data?.styles?.question?.fontFamily,
                                            fontSize: data?.styles?.question?.noteFontSize,
                                        }}
                                    >
                                        <b>Note: </b>
                                        {question?.specialInstruction}
                                    </p>
                                </div>
                                <div className="tw-flex tw-items-center tw-justify-center">
                                    <div className="tw-size-20 tw-overflow-hidden tw-rounded-full tw-border tw-p-4">
                                        <img src={quizIcon?.icon} className="tw-size-full tw-object-contain" alt="" />
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                        <div className="tw-flex tw-h-full tw-w-[90%] tw-items-center tw-justify-center">{children}</div>
                        <div className="tw-absolute tw-bottom-[-5%] tw-right-[-2%] tw-z-20 tw-flex tw-gap-1">
                            <button
                                onClick={handlePrevSlide}
                                style={{
                                    backgroundColor: quizzTemplate?.elements?.prev_button?.background_color,
                                    borderColor: quizzTemplate?.elements?.prev_button?.border_color,
                                    color: quizzTemplate?.elements?.prev_button?.color,
                                    fontFamily: quizzTemplate?.elements?.prev_button?.font_family,
                                    borderWidth: quizzTemplate?.elements?.prev_button?.border_width,
                                }}
                                className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FFD860]"
                            >
                                <i className="fa-solid fa-arrow-left tw-text-[40px] tw-leading-[40px] tw-text-[#333333]"></i>
                            </button>
                            <button
                                onClick={handleNextSlide}
                                style={{
                                    backgroundColor: quizzTemplate?.elements?.next_button?.background_color,
                                    borderColor: quizzTemplate?.elements?.next_button?.border_color,
                                    color: quizzTemplate?.elements?.next_button?.color,
                                    fontFamily: quizzTemplate?.elements?.next_button?.font_family,
                                    borderWidth: quizzTemplate?.elements?.next_button?.border_width,
                                }}
                                className={cn(
                                    "tw-flex tw-h-[60px] tw-items-center tw-justify-center tw-rounded-full tw-border-[7px] tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-4 tw-text-[40px] tw-leading-[40px]",
                                    data?.attempted ? "tw-w-[60px] tw-flex-shrink-0 tw-px-0" : "tw-w-full",
                                )}
                            >
                                {data?.attempted ? (
                                    <i className="fa-solid fa-arrow-right tw-text-[40px] tw-leading-[40px] tw-text-[#333333]"></i>
                                ) : (
                                    "Submit"
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </motion.div>
        </AnimatePresence>
    );
};

export default ContentSlideLayout;
