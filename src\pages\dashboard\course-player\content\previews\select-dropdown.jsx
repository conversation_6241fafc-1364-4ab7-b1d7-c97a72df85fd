import { useEffect, useState } from "react";

const SelectDropdown = ({ data, index, onComponentAnswer, Disable, correctAns }) => {
    const [compValue, setCompValue] = useState(data?.answerKey);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        let ans = data?.options?.filter((dt) => dt?.name == compValue)?.[0];
        let points = ans?.isCorrect == true ? data?.points : 0;
        onComponentAnswer(index, compValue, points);
    }, [compValue]);

    useEffect(() => {
        setIsMounted(true);
        return () => {
            setIsMounted(false);
        };
    }, []);

    return (
        <div className="comp_control">
            <div className="select_dropdown_input">
                <label htmlFor="">
                    {data?.name || "Component title here"} <b>{data?.preference == "required" && "*"}</b>
                </label>
                <select value={compValue} onChange={(e) => setCompValue(e.target.value)} disabled={Disable}>
                    {data?.placeholder ? (
                        <option value="">{data?.placeholder}</option>
                    ) : (
                        <option value={""}>- Choose -</option>
                    )}
                    {data?.options?.map((option, idx) => (
                        <option key={idx} value={option?.name}>
                            {option?.name}
                        </option>
                    ))}
                </select>
            </div>
            {data?.correctAnswer && correctAns && (
                <div className="correctAnswer">
                    <b>
                        Answer <i className="fa-solid fa-clipboard-check"></i> :-
                    </b>{" "}
                    {data?.correctAnswer}
                </div>
            )}
            {data?.specialInstruction && (
                <p className="special_instruction">
                    <i className="fa-solid fa-circle-info"></i>
                    <small>{data?.specialInstruction}</small>
                </p>
            )}
        </div>
    );
};

export default SelectDropdown;
