import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { tanstackApi } from "@/react-query/api";
import { Clock, FilePenLine, Languages, ShieldCheck, Trash, Users } from "lucide-react";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import { toast } from "sonner";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const UserSubGroups = () => {
    const navigate = useNavigate();
    const params = useParams();
    const [DataList, setDataList] = useState([]);
    const [languages, setLanguages] = useState([]);

    const [openAlert, setOpenAlert] = useState(false);

    const [selectedTeam, setSelectedTeam] = useState(null);

    const [filteredData, setFilteredData] = useState([]);
    const [filterState, setFilterState] = useState({
        search: "",
        language_id: "",
    });

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = DataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? item?.name?.toLowerCase()?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const language = filterState?.language_id ? item?.language_id == filterState?.language_id : true; // Allow all items if no subCategory filter

            return matchesSearch && language; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(DataList);
        setFilterState({
            search: "",
            language_id: "",
        });
    };

    useEffect(() => {
        getLanguages();
        if (params?.group_id !== undefined) {
            getUserSubGroupData(params?.group_id);
        }
    }, [params]);

    useEffect(() => {
        setFilteredData(DataList);
    }, [DataList]);

    const getUserSubGroupData = async (groupID) => {
        await tanstackApi
            .post("user-subgroup/list")
            .then((res) => {
                setDataList(res?.data?.data?.reverse()?.filter((dt) => dt?.group_id == Number(groupID)));
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const getLanguages = async () => {
        await tanstackApi
            .get("language/list")
            .then((res) => {
                setLanguages(res?.data?.data);
            })
            .catch((err) => {
                setLanguages([]);
            });
    };

    const onDelete = async () => {
        await tanstackApi
            .delete("user-subgroup/delete", { data: { subgroup_id: selectedTeam?.id } })
            .then((res) => {
                toast.success("Team Deleted", {
                    description: res?.data?.message,
                });
                getUserSubGroupData(params?.group_id);
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const DeleteTeam = (item) => {
        setSelectedTeam(item);
        setOpenAlert(true);
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Team, Are you sure you?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action action is irreversible, your team will be deleted permanently.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onDelete}>Yes Delete it !</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="tw-flex tw-justify-between">
                <h4 className="tw-text-xl tw-font-semibold">
                    <i className="fa-solid fa-users-line"></i> Teams of {params?.group_name}
                </h4>
                <div className="tw-flex tw-gap-2">
                    <Button
                        variant="outline"
                        className="tw-px-2 tw-py-1"
                        onClick={() => navigate(`/dashboard/user-group-master`)}
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button
                        className="tw-px-2 tw-py-1"
                        onClick={() =>
                            navigate(`/dashboard/user-subgroup-create/${params?.group_id}/${params?.group_name}`)
                        }
                    >
                        <i className="fa-solid fa-plus"></i> New Team
                    </Button>
                </div>
            </div>
            {/* <br /> */}
            <div className="page_filters tw-mt-3 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label htmlFor="">Team Name</label>
                    <input
                        value={filterState?.search}
                        name="search"
                        onChange={onFilterChange}
                        style={{ minWidth: "300px" }}
                        type="text"
                        placeholder="Search by name ..."
                    />
                </div>
                <div className="input_group">
                    <label htmlFor="">Language</label>
                    <select value={filterState?.language_id} name="language_id" onChange={onFilterChange}>
                        <option value="">- All -</option>
                        {languages?.map((lang) => (
                            <option value={lang?.id} key={lang?.id}>
                                {lang?.name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="tw-mt-4 tw-flex tw-flex-wrap tw-gap-5 tw-p-0">
                {filteredData?.map((group, index) => (
                    <div key={index} className="tw-w-[350px] tw-rounded-xl tw-border-[1px] tw-p-1">
                        <div className="tw-p-2">
                            <div className="tw-flex tw-items-center tw-gap-2">
                                <h2 className="tw-leading-2 tw-text-md tw-line-clamp-2 tw-font-semibold">
                                    {group?.name}
                                </h2>
                            </div>
                            <p className="tw-mt-2 tw-line-clamp-3">{group?.chapter_discription}</p>
                            <div className="tw-mt-1 tw-space-y-2">
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Users size={18} /> {group?.user_count}/{group?.user_restriction} Users
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Languages size={18} />{" "}
                                    <Badge variant={"outline"}>
                                        {languages?.find((dt) => dt?.id == group?.language_id)?.name}
                                    </Badge>
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <Clock size={18} /> Created on {moment(group?.createdAt).format("LL")}
                                </span>{" "}
                                <span className="tw-flex tw-items-center tw-gap-1 tw-text-sm tw-font-normal">
                                    <ShieldCheck size={18} />{" "}
                                    {group?.lms_trainer_subgroups_mappings?.length > 0 ? (
                                        group?.lms_trainer_subgroups_mappings?.map((trainer, idx) => (
                                            <>
                                                <a className="tw-font-semibold tw-underline">
                                                    {trainer?.lms_user?.first_name} {trainer?.lms_user?.last_name}
                                                </a>
                                                {"  "}
                                            </>
                                        ))
                                    ) : (
                                        <a className="tw-text-sm tw-text-slate-400">No trainers assigned Yet!</a>
                                    )}
                                </span>{" "}
                            </div>
                            <div className="tw-mt-3 tw-grid tw-grid-cols-2 tw-gap-2">
                                <div
                                    onClick={() => DeleteTeam(group)}
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <Trash size={16} strokeWidth={2} aria-hidden="true" />
                                    Delete Team
                                </div>
                                <div
                                    onClick={() =>
                                        navigate(
                                            `/dashboard/user-subgroup-create/${params?.group_id}/${params?.group_name}/${group?.id}`,
                                        )
                                    }
                                    className="tw-flex tw-cursor-pointer tw-items-center tw-justify-center tw-gap-2 tw-rounded-lg tw-border-[1px] tw-p-1 tw-text-sm hover:tw-bg-slate-100"
                                >
                                    <FilePenLine size={16} strokeWidth={2} aria-hidden="true" />
                                    View Team
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default UserSubGroups;
