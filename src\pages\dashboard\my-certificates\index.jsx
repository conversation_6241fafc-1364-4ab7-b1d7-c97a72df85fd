import { useGetUserCertificate } from "@/react-query/certificate";
import CertificateCards from "./CertificateCards";

const MyCertificates = () => {
    const { data, isLoading, error } = useGetUserCertificate();
    if (isLoading) return <div>Loading certificates...</div>;
    if (error) return <div>Failed to load certificates.</div>;
    return (
        <div className="p-2 tw-flex tw-h-full tw-w-full tw-flex-col tw-items-center tw-overflow-x-hidden">
            <h1 className="tw-text-5xl tw-font-bold">MY CERTIFICATES</h1>
            <h3 className="mt-4 tw-w-[50%] tw-text-center tw-text-gray-600">
                A collection of certifications and achievements that showcase my continuous learning journey and
                expertise in various technologies.
            </h3>
            <div className="tw-mt-6 tw-flex tw-w-full tw-gap-4">
                {data?.data
                    ?.filter((item) => item.userData.length > 0)
                    .map((items, index) => (
                        <CertificateCards
                            key={index}
                            first_name={items.userData[0].first_name}
                            last_name={items.userData[0].last_name}
                            marks={items.userData[0].preview_strings?.marks}
                            admin_name={items.userData[0].preview_strings?.admin_name}
                            title={items.course_title}
                            pdf_url={items.userData[0].certificate_url}
                            issued_at={items.userData[0].certificate_issued_at}
                        />
                    ))}
            </div>
        </div>
    );
};

export default MyCertificates;
