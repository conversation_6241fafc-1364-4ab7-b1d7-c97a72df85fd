import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le } from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import { useFileUpload } from "@/react-query/common";
import { MonitorCog, Upload, X } from "lucide-react";
import { useEffect, useState } from "react";

export default function InteractionTabSetting({ open, setOpen }) {
    const { currentTab, tabList, setContent } = useEditInteraction();
    const [tabData, setTabData] = useState(tabList[currentTab]);

    useEffect(() => {
        if (tabList[currentTab]) setTabData(tabList[currentTab]);
    }, [currentTab, tabList]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setTabData((prev) => ({ ...prev, [name]: value }));
    };

    const handleImageChange = (name, value) => setTabData((prev) => ({ ...prev, [name]: value }));

    const upload = useFileUpload();

    const onImageChange = async (e) => {
        if (e.target.files[0]) {
            const formdata = new FormData();
            const imageData = e.target.files[0];
            formdata.append("file", imageData);
            formdata.append("category", "COURSE-CONTENTS");
            upload.mutate(formdata, {
                onSuccess: (data) => handleImageChange("image_src", data?.fileUrl),
                onError: () => handleImageChange("image_src", ""),
            });
        }
    };

    return (
        <Sheet open={open} onOpenChange={setOpen}>
            <SheetContent className="tw-w-full !tw-max-w-2xl">
                <SheetHeader>
                    <SheetTitle className="tw-flex tw-items-center tw-gap-3">
                        <MonitorCog /> Interactions Tab Settings
                    </SheetTitle>
                </SheetHeader>
                <br />
                <div className="tw-flex tw-h-[93%] tw-flex-col tw-justify-between">
                    <div className="tw-space-y-4">
                        <div className="grid w-full max-w-sm items-center gap-1.5">
                            <Label htmlFor="label">Label</Label>
                            <Input
                                value={tabData?.label ?? tabData?.name}
                                type="text"
                                onChange={handleChange}
                                name="label"
                                id="label"
                                placeholder="Label"
                            />
                        </div>
                        <div className="grid w-full gap-1.5">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                value={tabData?.description ?? tabData?.content}
                                onChange={handleChange}
                                name="description"
                                id="description"
                                placeholder="Type your description here."
                            />
                        </div>
                        <div className="tw-flex tw-w-full tw-flex-col tw-items-start tw-gap-2">
                            <Label htmlFor={`tab_bg_image_${currentTab}`}>Background Image</Label>
                            {tabData?.image_src && (
                                <div className="tw-aspect-video tw-h-36 tw-object-contain">
                                    <img src={tabData.image_src} alt="" className="tw-size-full tw-rounded-md" />
                                </div>
                            )}
                            <div className="tw-flex tw-items-center tw-gap-2">
                                {tabData?.image_src && (
                                    <Button
                                        variant="destructive"
                                        className="aspect-square max-sm:p-0"
                                        onClick={() => handleImageChange("image_src", "")}
                                    >
                                        <X
                                            className="opacity-60 sm:-ms-1 sm:me-2"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <Label className="max-sm:sr-only">Remove</Label>
                                    </Button>
                                )}
                                <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10">
                                    <Upload
                                        className="opacity-60 sm:-ms-1 sm:me-2"
                                        size={16}
                                        strokeWidth={2}
                                        aria-hidden="true"
                                    />
                                    <input
                                        onChange={onImageChange}
                                        type="file"
                                        style={{ display: "none" }}
                                        id={`tab_bg_image_${currentTab}`}
                                        accept="image/*"
                                    />
                                    <Label htmlFor={`tab_bg_image_${currentTab}`} className="max-sm:sr-only">
                                        {upload.isPending ? "Uploading..." : "Upload Background Image"}
                                    </Label>
                                </Button>
                            </div>
                        </div>
                    </div>
                    <SheetFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setTabData(tabList[currentTab]);
                                setOpen(false);
                            }}
                        >
                            <i className="fa-solid fa-xmark"></i> Cancel
                        </Button>
                        <Button
                            type="button"
                            onClick={() => {
                                const prevData = [...tabList];
                                prevData[currentTab] = tabData;
                                setContent((prev) => ({
                                    ...prev,
                                    structure: {
                                        ...prev.structure,
                                        database: prevData,
                                    },
                                }));
                                setOpen(false);
                            }}
                        >
                            <i className="fa-solid fa-save"></i> Save changes
                        </Button>
                    </SheetFooter>
                </div>
            </SheetContent>
        </Sheet>
    );
}
