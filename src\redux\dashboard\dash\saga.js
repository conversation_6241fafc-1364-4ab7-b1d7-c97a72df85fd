import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { dashStatsData, ScheduleChapters, userPointsData } from "@/redux/dashboard/dash/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* getDashStatsDataList() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "dashboard", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(dashStatsData(data));
    }
}

function* getUserPointsData() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "users/list-points", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data?.[0];
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(userPointsData(data));
    }
}

function* getUserScheduledChapters(db) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "course/creation/list-chapter", {
            params: db?.payload,
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.data?.filter((dt) => dt?.user_id == Number(localStorage.getItem("userId")));
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(ScheduleChapters(data));
    }
}

function* addUserPoints(data) {
    const addData = yield axios
        .post(CONSTANTS.getAPI() + `users/add-points`, data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });
}

export function* DashStatsWatcher() {
    yield takeEvery(actions.FETCH_DASHBOARD_STATS_REQ, getDashStatsDataList);
    yield takeEvery(actions.FETCH_USER_POINTS_REQ, getUserPointsData);
    yield takeEvery(actions.ADD_USER_POINTS, addUserPoints);
    yield takeEvery(actions.FETCH_SCHEDULED_CHAPTERS_REQ, getUserScheduledChapters);
}

export default function* DashSaga() {
    yield all([fork(DashStatsWatcher)]);
}
