import { CONSTANTS } from "@/constants";
import * as actions from "@/redux-types";
import { AlertSnackInfo } from "@/redux/alert/alert-action";
import { addTimelineLog } from "@/redux/reports/action";
import {
    fetchLevelTwoUSERSReq,
    fetchUSERSReq,
    getAllSubUSERS,
    getAllTrainers,
    getAllUSERS,
    getEnrollData,
    getLevelTwoUSERS,
} from "@/redux/users/action";
import axios from "axios";
import { all, fork, put, takeEvery } from "redux-saga/effects";

function* GetAllUsers() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "users/get-all-users", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(getAllUSERS(data));
    } else {
        yield put(getAllUSERS([]));
    }
}

function* GetUsers() {
    const data = yield axios
        .get(CONSTANTS.getAPI() + "users/get-users", {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(getLevelTwoUSERS(data));
    } else {
        yield put(getLevelTwoUSERS([]));
    }
}

function* GetSubUsers(userId) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `users/get-user/${userId?.payload}`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.data?.sub_users;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(getAllSubUSERS(data));
    } else {
        yield put(getAllSubUSERS([]));
    }
}

function* AddUser(Data) {
    const userdata = yield axios
        .post(CONSTANTS.getAPI() + "users/add-user", Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response;
            return errMsg;
        });

    if (userdata) {
        yield put(
            addTimelineLog({
                user_id: localStorage.getItem("userId"),
                event: "user",
                log: `${userdata?.data?.first_name} ${userdata?.data?.last_name} created successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
            }),
        );
        if (localStorage.getItem("level") == "levelOne") {
            yield put(fetchUSERSReq());
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        } else if (localStorage.getItem("level") == "levelTwo") {
            yield put(fetchLevelTwoUSERSReq());
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        }
    }
}

function* UpdateUser(Data) {
    const userdata = yield axios
        .put(CONSTANTS.getAPI() + "users/update-user", Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (userdata) {
        yield put(
            addTimelineLog({
                user_id: localStorage.getItem("userId"),
                event: "user",
                log: `${userdata?.data?.first_name} ${userdata?.data?.last_name} updated successfully by ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")}`,
            }),
        );
        if (localStorage.getItem("level") == "levelOne") {
            yield put(fetchUSERSReq());
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        } else if (localStorage.getItem("level") == "levelTwo") {
            yield put(fetchLevelTwoUSERSReq());
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        }
    }
}

function* AssignAsTrainer(Data) {
    const userdata = yield axios
        .post(
            CONSTANTS.getAPI() + `users/update-trainer-status/${Data.payload.userId}/${Data.payload.isTrainer}`,
            {},
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${localStorage.getItem("login_token")}`,
                },
            },
        )
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (userdata) {
        if (localStorage.getItem("level") == "levelOne") {
            yield put(fetchUSERSReq());
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        } else if (localStorage.getItem("level") == "levelTwo") {
            yield put(fetchLevelTwoUSERSReq());
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        }
    }
}

function* GetTrainers(userId) {
    const data = yield axios
        .get(CONSTANTS.getAPI() + `users/get-trainers`, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(getAllTrainers(data));
    } else {
        yield put(getAllTrainers([]));
    }
}

function* GetEnrollments(userData) {
    const data = yield axios
        .post(CONSTANTS.getAPI() + `learner-permission/get-assigned-courses`, userData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.courses;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(getEnrollData(data));
    } else {
        yield put(getEnrollData([]));
    }
}

function* approveEnrolls(Data) {
    const userdata = yield axios
        .post(CONSTANTS.getAPI() + `learner-permission/allow-enrollment`, Data.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res.data;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (userdata) {
        if (localStorage.getItem("level") == "levelOne") {
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        } else if (localStorage.getItem("level") == "levelTwo") {
            yield put(AlertSnackInfo({ message: userdata.message, result: userdata.success }));
        }
    }
}

function* reEnrollRequest(userData) {
    const data = yield axios
        .post(CONSTANTS.getAPI() + `learner-permission/enrollment-request`, userData.payload, {
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("login_token")}`,
            },
        })
        .then((res) => {
            const response = res?.data?.courses;
            return response;
        })
        .catch((err) => {
            var errMsg = err.response.data;
            return errMsg;
        });

    if (data) {
        yield put(AlertSnackInfo({ message: data.message, result: data.success }));
    } else {
        // yield put(getEnrollData([]))
        yield put(AlertSnackInfo({ message: data.message, result: data.success }));
    }
}

export function* UsersWatcher() {
    yield takeEvery(actions.FETCH_USERS_LIST_REQ, GetAllUsers);
    yield takeEvery(actions.FETCH_SUB_USERS_LIST_REQ, GetSubUsers);
    yield takeEvery(actions.FETCH_LEVEL_TWO_USERS__REQ, GetUsers);
    yield takeEvery(actions.ADD_USER_DATA, AddUser);
    yield takeEvery(actions.UPDATE_USER_DATA, UpdateUser);
    yield takeEvery(actions.MAKE_TRAINER, AssignAsTrainer);
    yield takeEvery(actions.FETCH_TRAINER_LIST_REQ, GetTrainers);
    yield takeEvery(actions.FETCH_ENROLL_DATA_REQ, GetEnrollments);
    yield takeEvery(actions.ENROLLMENT_APPROVE, approveEnrolls);
    yield takeEvery(actions.RE_ENROLL_REQUEST, reEnrollRequest);
}

export default function* UsersSaga() {
    yield all([fork(UsersWatcher)]);
}
