import FillInTheBlank from "@/pages/dashboard/course-player/content/previews/fill-in-the-blanks";
import MatchTheFollowing from "@/pages/dashboard/course-player/content/previews/match-the-following";
import MultiChoice from "@/pages/dashboard/course-player/content/previews/multi-choice";
import SelectDropdown from "@/pages/dashboard/course-player/content/previews/select-dropdown";
import SingleChoice from "@/pages/dashboard/course-player/content/previews/single-choice";
import { AddUserPoints } from "@/redux/dashboard/dash/action";
import { SubmitQuizData } from "@/redux/gamification/action";
import { Tooltip } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

const ContentQuizz = ({ content, courseId }) => {
    const dispatch = useDispatch();
    const [activeIndex, setActiveIndex] = useState(0);
    const [mode, setMode] = useState("instruction");
    const [isStarted, setIsStarted] = useState(false);
    const [template, setTemplate] = useState(null);

    useEffect(() => {
        setTemplate(content?.quiz !== null ? content?.quiz : null);
    }, [content]);

    const [components, setComponents] = useState([]);

    useEffect(() => {
        if (template !== null && template !== undefined) {
            setComponents(
                template?.components?.map((dt, idx) => {
                    return {
                        index: idx,
                        answerKey: null,
                        points: Number(dt?.points),
                        ...dt,
                    };
                }),
            );
        }
    }, [template]);

    const onPrev = (e) => {
        e.preventDefault();
        setActiveIndex(activeIndex == 0 ? activeIndex : activeIndex - 1);
    };

    const onNext = (e) => {
        e.preventDefault();
        setActiveIndex(activeIndex == components.length - 1 ? activeIndex : activeIndex + 1);
    };

    const AssessmentStart = (e) => {
        e.preventDefault();
        onChangeMode("components");
        setIsStarted(true);
    };

    const BackAssessment = (e) => {
        e.preventDefault();
        onChangeMode("instruction");
        setIsStarted(false);
    };

    const AssessmentStop = () => {
        setIsStarted(false);
        onChangeMode("results");
    };

    const onChangeMode = (value) => setMode(value);

    const onComponentAnswer = (index, answer, points) => {
        var answerData = [...components];
        answerData[index].answerKey = answer;
        answerData[index].earnedPoints = points;
        setComponents(answerData);
    };

    const onUserReponseSave = (e) => {
        e.preventDefault();
        AssessmentStop();
        let payload = {
            quiz_id: template?.id,
            course_id: courseId,
            components: components,
        };

        dispatch(SubmitQuizData(payload));

        dispatch(
            AddUserPoints({
                user_id: localStorage.getItem("userId"),
                event: "Complete quiz",
                points: components.reduce((sum, item) => sum + (item.earnedPoints || 0), 0),
                entity_id: `${template?.id}`,
            }),
        );
    };

    return (
        <div className="ContentQuizz">
            {mode == "instruction" && (
                <div className="instructions_box">
                    <div className="ins_cards">
                        <div className="ins_card">
                            <small>
                                <i className="fa-solid fa-qrcode"></i> Quizz Details :-
                            </small>
                            <div className="left">
                                <h2>{template?.title}</h2>
                                <p>{template?.description}</p>
                            </div>
                            <div className="right">
                                <img src={template?.thumbnail_img} alt="" />
                            </div>
                        </div>
                    </div>
                    <div className="ins_control">
                        <div>
                            <Tooltip placement="bottom" title="Start Assessment" onClick={AssessmentStart}>
                                <button className="nav_btns_2">
                                    <i className="fa-solid fa-circle-play"></i> <p>Attempt Quizz</p>
                                </button>
                            </Tooltip>
                        </div>
                    </div>
                </div>
            )}
            {mode == "components" && (
                <div className="single_question">
                    <div className="question_box">
                        {components?.slice(activeIndex, activeIndex + 1)?.map((comp, index) => {
                            return comp.type == "select_dropdown" ? (
                                <SelectDropdown
                                    data={comp}
                                    index={comp?.index}
                                    onComponentAnswer={onComponentAnswer}
                                    correctAns={false}
                                />
                            ) : comp.type == "single_choice" ? (
                                <SingleChoice
                                    data={comp}
                                    index={comp?.index}
                                    onComponentAnswer={onComponentAnswer}
                                    correctAns={false}
                                />
                            ) : comp.type == "multi_choice" ? (
                                <MultiChoice
                                    data={comp}
                                    index={comp?.index}
                                    onComponentAnswer={onComponentAnswer}
                                    correctAns={false}
                                />
                            ) : comp.type == "match_the_following" ? (
                                <MatchTheFollowing
                                    data={comp}
                                    index={comp?.index}
                                    onComponentAnswer={onComponentAnswer}
                                    correctAns={false}
                                />
                            ) : comp.type == "fill_in_the_blank" ? (
                                <FillInTheBlank
                                    data={comp}
                                    index={comp?.index}
                                    onComponentAnswer={onComponentAnswer}
                                    correctAns={false}
                                />
                            ) : null;
                        })}
                    </div>
                    <div className="navigation_control">
                        <div>
                            <Tooltip placement="bottom" title="Back to details" onClick={BackAssessment}>
                                <button className="nav_btns_2">
                                    <i className="fa-solid fa-left-long"></i> <p>Back</p>
                                </button>
                            </Tooltip>
                            {activeIndex > 0 && (
                                <Tooltip placement="bottom" title="Previous Question">
                                    <button className="nav_btns_2" onClick={onPrev}>
                                        <i className="fa-solid fa-backward"></i> <p>Prev</p>
                                    </button>
                                </Tooltip>
                            )}
                            {activeIndex == components?.length - 1 ? (
                                <Tooltip placement="bottom" title="Submit Assessment">
                                    <button className="nav_btns_2" onClick={onUserReponseSave}>
                                        <i className="fa-solid fa-clipboard-check"></i> <p>Submit</p>
                                    </button>
                                </Tooltip>
                            ) : (
                                <Tooltip placement="bottom" title="Next Question">
                                    <button className="nav_btns_2" onClick={onNext}>
                                        <p>Next</p> <i className="fa-solid fa-forward"></i>
                                    </button>
                                </Tooltip>
                            )}
                        </div>
                    </div>
                </div>
            )}
            {mode == "results" && (
                <div className="result_screen">
                    <div className="left">
                        <div>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Question</th>
                                        <th>Attempted</th>
                                        <th>Points</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {components?.map((ques, idx) => (
                                        <tr key={idx}>
                                            <td>{ques?.name}</td>
                                            <td style={{ textAlign: "center" }}>
                                                {ques?.earnedPoints ? (
                                                    <i style={{ color: "green" }} className="fa-solid fa-check"></i>
                                                ) : (
                                                    <i style={{ color: "red" }} className="fa-solid fa-xmark"></i>
                                                )}
                                            </td>
                                            <td style={{ textAlign: "center" }}>{ques?.earnedPoints || "-"}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div className="right">
                        <img src="/assets/win.gif" alt="" />
                        <h1>Congratulation!</h1>
                        <h2>You have completed {template?.title}</h2>
                        <div>
                            <small> - Score - </small>
                            <h3>
                                {" "}
                                {components.reduce((sum, item) => sum + (item.earnedPoints || 0), 0)} /{" "}
                                {template?.max_points}
                            </h3>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ContentQuizz;
