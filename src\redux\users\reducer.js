import {
    FETCH_ENROLL_DATA_REQ,
    FETCH_LEVEL_TWO_USERS__REQ,
    FETCH_SUB_USERS_LIST_REQ,
    FETCH_TRAINER_LIST_REQ,
    FETCH_USERS_LIST_REQ,
    GET_ENROLL_DATA,
    GET_LEVEL_TWO_USERS,
    GET_SUB_USERS_LIST,
    GET_TRAINER_LIST,
    GET_USERS_LIST,
} from "@/redux-types";

const initialState = {
    usersList: [],
    EnrollData: [],
    trainerList: [],
    subUsersList: [],
    levelTwoUsersList: [],
    userAlertInfo: {},
    isLoading: true,
    error: null,
};

const UsersReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_USERS_LIST_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_USERS_LIST:
            return {
                ...state,
                usersList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case FETCH_ENROLL_DATA_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_ENROLL_DATA:
            return {
                ...state,
                EnrollData: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case FETCH_TRAINER_LIST_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_TRAINER_LIST:
            return {
                ...state,
                trainerList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case FETCH_SUB_USERS_LIST_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_SUB_USERS_LIST:
            return {
                ...state,
                subUsersList: action.payload,
                isLoading: false,
                error: action.payload,
            };
        case FETCH_LEVEL_TWO_USERS__REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_LEVEL_TWO_USERS:
            return {
                ...state,
                levelTwoUsersList: action.payload,
                isLoading: false,
                error: action.payload,
            };

        default:
            return state;
    }
};

export default UsersReducer;
