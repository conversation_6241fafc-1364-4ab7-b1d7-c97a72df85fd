import {
    ADD_NEW_CATEGORY,
    ADD_NEW_SUB_CATEGORY,
    COURSE_BOOKMARKING,
    COURSE_BOOKMARKING_REQ,
    COURSE_DETAILS,
    COURSE_DETAILS_REQ,
    COURSE_PREREQUISITE_LIST,
    COURSE_PREREQUISITE_REQ,
    FETCH_ALL_COURSES_LIST,
    FETCH_CATEGORY_LIST_REQ,
    FETCH_SUB_CATEGORY_REQ,
    GET_ALL_COURSES_LIST,
    GET_CATEGORY_LIST,
    GET_SUB_CATEGORY_LIST,
    GET_UPDATED_BOOKMARKING,
    INIT_COURSE,
    UPDATE_CATEGORY,
    UPDATE_SUB_CATEGORY,
    UPDTAE_COURSE_STATUS,
} from "@/redux-types";

const initialState = {
    categoryList: [],
    coursesList: [],
    courseBookmarking: {},
    isLoading: false,
    error: null,
    createCourse: {},
    updateCategoryData: {},
    subCategoryList: {},
    addSubCategoryData: {},
    updateSubCategoryData: {},
    courseDetails: {},
    coursePresequite: [],
    updateStatus: {},
    updateBookmarking: {},
};

const CourseReducer = (state = initialState, action) => {
    switch (action.type) {
        case FETCH_CATEGORY_LIST_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case COURSE_BOOKMARKING_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case COURSE_DETAILS_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case COURSE_PREREQUISITE_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_UPDATED_BOOKMARKING:
            return {
                ...state,
                updateBookmarking: action.payload,
                error: action.payload,
            };
        case COURSE_DETAILS:
            return {
                ...state,
                courseDetails: action.payload,
                error: action.payload,
            };
        case COURSE_BOOKMARKING:
            return {
                ...state,
                courseBookmarking: action.payload,
                error: action.payload,
            };
        case COURSE_PREREQUISITE_LIST:
            return {
                ...state,
                coursePresequite: action.payload,
                error: action.payload,
            };
        case FETCH_ALL_COURSES_LIST:
            return {
                ...state,
                isLoading: true,
            };
        case GET_ALL_COURSES_LIST:
            return {
                ...state,
                isLoading: false,
                coursesList: action.payload,
                error: action.payload,
            };
        case GET_CATEGORY_LIST:
            return {
                ...state,
                isLoading: false,
                categoryList: action.payload,
                error: action.payload,
            };
        case INIT_COURSE:
            return {
                ...state,
                isLoading: false,
                createCourse: action.payload,
            };
        case ADD_NEW_CATEGORY:
            return {
                addCategoryData: action.payload,
                onClose: action.onClose,
            };
        case UPDATE_CATEGORY:
            return {
                updateCategoryData: action.payload,
                onClose: action.onClose,
            };
        case FETCH_SUB_CATEGORY_REQ:
            return {
                ...state,
                isLoading: true,
            };
        case GET_SUB_CATEGORY_LIST:
            return {
                ...state,
                isLoading: false,
                subCategoryList: action.payload,
                error: action.payload,
            };
        case ADD_NEW_SUB_CATEGORY:
            return {
                addSubCategoryData: action.payload,
                onClose: action.onClose,
            };
        case UPDATE_SUB_CATEGORY:
            return {
                updateSubCategoryData: action.payload,
                onClose: action.onClose,
            };
        case UPDTAE_COURSE_STATUS:
            return {
                updateStatus: action.payload,
                onClose: action.onClose,
            };
        default:
            return state;
    }
};

export default CourseReducer;
