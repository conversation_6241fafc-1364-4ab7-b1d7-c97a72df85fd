import MyProfileCompanyAbout from "@/pages/dashboard/my-account/role/company";
import MyProfileUniversityAbout from "@/pages/dashboard/my-account/role/university";
import { onDomainSave, onUserPersonalDataSave } from "@/redux/auth/action";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

const MyAccountPage = () => {
    const [domainSettings, setDomainSettings] = useState({
        name: localStorage.getItem("domain_name"),
    });
    const [personalDetailsSettings, setPersonalDetailsSettings] = useState({});
    const dispatch = useDispatch();
    const domainDetails = useSelector((state) => state.AuthReducer).domainHolder;

    useEffect(() => {
        setDomainSettings({ ...domainSettings, ...domainDetails });
    }, [domainDetails]);

    const handleChangeDomainSettings = (e) => {
        const { name, value } = e.target;
        setDomainSettings({ ...domainSettings, [name]: value });
    };

    const handleChangePersonalDetails = (e) => {
        const { name, value } = e.target;
        setPersonalDetailsSettings({ ...personalDetailsSettings, [name]: value });
    };

    const handleDomainSave = () => {
        dispatch(onDomainSave(domainSettings.name));
    };

    const handlePersonalDetailsSave = () => {
        var data = {
            first_name: personalDetailsSettings.first_name,
            last_name: personalDetailsSettings.last_name,
            email: personalDetailsSettings.email,
        };
        dispatch(onUserPersonalDataSave(data));
    };

    return (
        <div>
            <div>
                <div className="MyAccountAlpha">
                    <div className="my_account_header">
                        <h4 className="tw-text-xl tw-font-semibold">My Account</h4>
                    </div>
                    <div className="main_page !tw-mt-3">
                        <div className="section_details">
                            <div className="section_heading">
                                <h6>
                                    <i className="fa-solid fa-building"></i> Domain Details
                                </h6>
                                <button onClick={handleDomainSave}>
                                    <i className="fa-regular fa-circle-check"></i> Verify Domain
                                </button>
                            </div>
                            <div className="input_group !tw-mx-0">
                                <div className="input_text" style={{ flex: 1 }}>
                                    <label htmlFor="">Domain Name</label>
                                    <input
                                        name="name"
                                        type="text"
                                        value={domainSettings.name}
                                        id="name"
                                        onChange={handleChangeDomainSettings}
                                        defaultValue={localStorage.getItem("domain_name")}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="section_details">
                            <div className="section_heading">
                                <h6>
                                    <i className="fa-solid fa-address-card"></i> Personal Details
                                </h6>
                                <button onClick={handlePersonalDetailsSave}>
                                    <i className="fa-solid fa-floppy-disk"></i> Save Details
                                </button>
                            </div>
                            <div className="input_group !tw-mx-0">
                                <div className="input_text" style={{ flex: 0.3 }}>
                                    <label htmlFor="">First Name</label>
                                    <input
                                        type="text"
                                        name="first_name"
                                        value={personalDetailsSettings.first_name}
                                        onChange={handleChangePersonalDetails}
                                        defaultValue={localStorage.getItem("lms_fName")}
                                        placeholder="eg. John"
                                        id="first_name"
                                    />
                                </div>
                                <div className="input_text" style={{ flex: 0.3 }}>
                                    <label htmlFor="">Last Name</label>
                                    <input
                                        type="text"
                                        name="last_name"
                                        value={personalDetailsSettings.last_name}
                                        onChange={handleChangePersonalDetails}
                                        defaultValue={localStorage.getItem("lms_lName")}
                                        placeholder="eg. Dau"
                                        id="last_name"
                                    />
                                </div>
                                <div className="input_text" style={{ flex: 0.6 }}>
                                    <label htmlFor="">E-Mail ID</label>
                                    <input
                                        type="text"
                                        name="email"
                                        value={personalDetailsSettings.email}
                                        onChange={handleChangePersonalDetails}
                                        defaultValue={localStorage.getItem("user_email")}
                                        placeholder="eg. <EMAIL>"
                                        id="email"
                                    />
                                </div>
                            </div>
                        </div>
                        {localStorage.getItem("login_role") == "company" && <MyProfileCompanyAbout />}
                        {localStorage.getItem("login_role") == "university" && <MyProfileUniversityAbout />}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MyAccountPage;
