import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>readcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { AddUserPoints } from "@/redux/dashboard/dash/action";
import { FetchUserHomeworkList } from "@/redux/gamification/action";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const AttemptAssignment = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const params = useParams();

    const userHomworkeList = useSelector((state) => state.GamificationReducer)?.userHomworkeList;
    const [openAlert, setOpenAlert] = useState(false);
    const [database, setDatabase] = useState(null);

    const [percentage, setPercentage] = useState(null);

    const [submissionDetails, setSubmissionDetails] = useState({
        submission: "",
        submission_attachment: "",
    });

    const onHandleChange = (e) => {
        const { name, value } = e.target;
        setSubmissionDetails({ ...submissionDetails, [name]: value });
    };

    useEffect(() => {
        var data = {
            is_assignment: true,
            is_public: localStorage.getItem("level") == "levelOne",
        };
        dispatch(FetchUserHomeworkList(data));
    }, []);

    useEffect(() => {
        if (params?.id !== undefined && userHomworkeList?.length > 0) {
            setDatabase(userHomworkeList?.find((dt) => dt?.id == Number(params?.id)));
        } else {
            setDatabase(null);
        }
    }, [params, userHomworkeList]);

    const onAttachmentChange = (e, index) => {
        if (e.target.files[0]) {
            const formData = new FormData();
            var file = e.target.files[0];

            formData.append("file", file);
            formData.append("category", "COURSE-CONTENTS");

            onAttachmentUpload(formData);
        }
    };

    const onAttachmentUpload = async (formData) => {
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setPercentage(percent);
                },
            })
            .then((res) => {
                setSubmissionDetails({
                    ...submissionDetails,
                    submission_attachment: res.data.fileUrl,
                });
            })
            .catch((err) => {
                setSubmissionDetails({
                    ...submissionDetails,
                    submission_attachment: "",
                });
            });
    };

    const onSubmitHomework = (e) => {
        e.preventDefault();

        const submissionDate = new Date(database.submission_date);
        const currentDate = new Date();

        const submissionDateOnly = new Date(
            submissionDate.getFullYear(),
            submissionDate.getMonth(),
            submissionDate.getDate(),
        );
        const currentDateOnly = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());

        if (currentDateOnly > submissionDateOnly) {
            toast?.warning("Submission date has been passed!", {
                description: "Submission date has already passed! You cannot submit this assignment.",
            });
            setOpenAlert(false);
            return false; // Indicate that submission is not allowed
        }

        if (!submissionDetails?.submission) {
            toast?.warning("Submission summary", {
                description: "Submission summary required",
            });
            return false;
        } else if (!submissionDetails?.submission_attachment) {
            toast?.warning("Attachment", {
                description: "Attachment file is required",
            });
            return false;
        }

        let payload = {
            submission: submissionDetails?.submission,
            homework_id: Number(params?.id),
            submission_attachment: submissionDetails?.submission_attachment,
        };

        SubmitHomework(payload);
    };

    const SubmitHomework = async (payload) => {
        await tanstackApi
            .post("homework/submission/submit", { ...payload })
            .then((res) => {
                setOpenAlert(false);
                toast.success("Submitted", {
                    description: res?.data?.message,
                });

                punchTimelineLog({
                    user_id: localStorage.getItem("parent_user_id"),
                    event: "assignment",
                    log: `${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} submitted ${database?.homework_title} successfully`,
                });

                navigate(`/dashboard/user-assignment`);
            })
            .catch((err) => {
                setOpenAlert(false);
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    const onDownload = () => {
        const url = database?.attachment_url;
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `${database?.attachment_url}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url); // Clean up the object URL
    };

    return (
        <div className="">
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Sumbit Assignment, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible the submission details will pass on to the respective
                            instructure.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onSubmitHomework}>Continue</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="tw-flex tw-items-center tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/user-assignment">Assigned Assignments</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>View Assignment</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Link to={"/dashboard/user-assignment"}>
                    <Button className="">
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                </Link>
            </div>
            <br />
            <div className="content_body_homework">
                <div className="homework_title">
                    <h1>
                        <i className="fa-solid fa-chalkboard"></i> {database?.homework_title}
                    </h1>
                </div>
                <div className="homework_description">
                    <p>{database?.description}</p>
                </div>
                <div className="homework_details">
                    <div>
                        <p>
                            <i className="fa-solid fa-dice-d6"></i> Points
                        </p>
                        <h4>{database?.homework_points}</h4>
                    </div>
                    <div>
                        <p>
                            {" "}
                            <i className="fa-solid fa-clock"></i> Submission
                        </p>
                        <h4>{moment(database?.submission_date).format("LLL")}</h4>
                    </div>
                    <div>
                        <p>
                            <i className="fa-solid fa-paperclip"></i> Attachments
                        </p>
                        <h4>
                            {database?.attachment_url ? (
                                <button onClick={onDownload}>
                                    <i className="fa-solid fa-download"></i> Download
                                </button>
                            ) : (
                                <button className="tw-opacity-[.2]">File not found!</button>
                            )}
                        </h4>
                    </div>
                </div>
                <br />
                {!database?.is_submitted && (
                    <div className="">
                        <div className="tw-space-y-2">
                            <Label>Submission Summary *</Label>
                            <Textarea
                                name="submission"
                                id=""
                                rows={8}
                                placeholder="Type details here ..."
                                onChange={onHandleChange}
                                value={submissionDetails?.submission}
                            ></Textarea>
                        </div>

                        <div className="tw-mt-2">
                            <i className="fa-solid fa-paperclip"></i> Add Attachments :-
                        </div>

                        <input type="file" style={{ display: "none" }} onChange={onAttachmentChange} id="Homeworks" />
                        <Label
                            htmlFor="Homeworks"
                            className="tw-mt-2 tw-cursor-pointer tw-rounded-lg tw-bg-slate-500 tw-p-2 tw-text-white hover:tw-bg-black"
                        >
                            <>
                                <i className="fa-solid fa-upload"></i> Uploaded File{" "}
                                {percentage && <> : {percentage}%</>}
                            </>
                        </Label>
                        <br />
                        <a
                            href={submissionDetails?.submission_attachment}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="tw-mt-5 tw-cursor-pointer tw-text-sm hover:tw-underline"
                        >
                            {submissionDetails?.submission_attachment}
                        </a>
                    </div>
                )}
                <div className="homework_action">
                    {database?.is_submitted ? (
                        <button>
                            <i className="fa-solid fa-circle-check"></i> Already Submitted
                        </button>
                    ) : (
                        <button onClick={() => setOpenAlert(true)}>
                            <i className="fa-solid fa-circle-check"></i> Submit Assignment
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default AttemptAssignment;
