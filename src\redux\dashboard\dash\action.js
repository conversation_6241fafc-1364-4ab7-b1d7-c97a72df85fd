import {
    ADD_USER_POINTS,
    DASHBOARD_STATS_DATA,
    FETCH_DASHBOARD_STATS_REQ,
    FETCH_SCHEDULED_CHAPTERS_REQ,
    FETCH_USER_POINTS_REQ,
    SCHEDULED_CHAPTERS_DATA,
    USER_POINTS_DATA,
} from "@/redux-types";

export const fetchDashStatsReq = () => {
    return {
        type: FETCH_DASHBOARD_STATS_REQ,
    };
};

export const dashStatsData = (data) => {
    return {
        type: DASHBOARD_STATS_DATA,
        payload: data,
    };
};

export const fetchUserPointsReq = (data) => {
    return {
        type: FETCH_USER_POINTS_REQ,
        payload: data,
    };
};

export const AddUserPoints = (data) => {
    return {
        type: ADD_USER_POINTS,
        payload: data,
    };
};

export const userPointsData = (data) => {
    return {
        type: USER_POINTS_DATA,
        payload: data,
    };
};

export const fetchScheduleChaptersReq = (data) => {
    return {
        type: FETCH_SCHEDULED_CHAPTERS_REQ,
        payload: data,
    };
};

export const ScheduleChapters = (data) => {
    return {
        type: SCHEDULED_CHAPTERS_DATA,
        payload: data,
    };
};
