import alertReducer from "@/redux/alert/alert-reducer";
import AppReducer from "@/redux/app/reducer";
import AuthReducer from "@/redux/auth/reducer";
import CategoryReducers from "@/redux/category/reducer";
import CertificateReducer from "@/redux/certificate/reducer";
import CourseReducers from "@/redux/course/reducer";
import DashReducer from "@/redux/dashboard/dash/reducer";
import DefineRoleReducer from "@/redux/define-role/reducer";
import GamificationReducer from "@/redux/gamification/reducer";
import imageUploadReducer from "@/redux/image-upload/reducer";
import LearningPathReducer from "@/redux/learning-path/reducer";
import ModulesReducer from "@/redux/module/reducer";
import NotificationReducer from "@/redux/notification/reducer";
import PermissionReducer from "@/redux/permission/reducer";
import RoleReducer from "@/redux/roles/reducer";
import ThemeSwitcher from "@/redux/theme-switcher/reducer";
import UsersReducer from "@/redux/users/reducer";
import { combineReducers } from "redux";

export default combineReducers({
    AuthReducer,
    AppReducer,
    DashReducer,
    NotificationReducer,
    GamificationReducer,
    ThemeSwitcher,
    CourseReducers,
    UsersReducer,
    RoleReducer,
    PermissionReducer,
    ModulesReducer,
    CategoryReducers,
    // OrginazationSettingsReducer,
    // LanguageSwitcher,
    // Mails,
    // Calendar,
    // Box,
    // Notes,
    // Todos,
    // Contacts,
    // Cards,
    // Chat,
    // DynamicChartComponent,
    // Ecommerce,
    // Invoices,
    // YoutubeSearch,
    // Articles,
    // Investors,
    // scrumBoard,
    // modal,
    // drawer,
    // profile,
    // githubSearch,
    // quiz,
    DefineRoleReducer,
    alertReducer,
    imageUploadReducer,
    CertificateReducer,
    LearningPathReducer,
    // LMSHelpingAPI,
    // courseBundleReducer,
    // ReportReducer,
    // LEVEL TWO REDUCERS -> STARTS
    // SubRoleReducer,
    // UserGroupReducer,
    // LanguageReducer,
    // TermsReducer,
    // LearnerReducer: LearnerPermissionReducer,
    // LEVEL TWO REDUCERS -> ENDS
});
