import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const ITEMS_PER_PAGE = 8;

const ClassLearners = () => {
    const navigate = useNavigate();
    const params = useParams();

    const [openAlert, setOpenAlert] = useState(false);

    const [filterState, setFilterState] = useState({
        search: "",
    });

    const [selectedUsers, setSelectedUsers] = useState([]);

    const onFilterChange = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const [dataList, setDataList] = useState([]);
    const [tableData, setTableData] = useState([]);

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(dataList.length / ITEMS_PER_PAGE);

    // Get data for the current page
    const getPaginatedData = () => {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        return dataList.slice(startIndex, endIndex);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
            const endIndex = startIndex + ITEMS_PER_PAGE;
            let options = dataList.slice(startIndex, endIndex);
            setTableData(options);
        }
    }, [currentPage, dataList]);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (params?.class_id !== undefined) {
            classLearnersList(params?.class_id);
        }
    }, [params]);

    const classLearnersList = async (payload) => {
        await tanstackApi
            .get(`chapter-class/class-learners/${payload}`)
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const onSearch = () => {};

    const onClear = () => {
        setFilterState({
            search: "",
        });
    };

    const onCourseSelection = (userId) => {
        if (selectedUsers?.includes(userId)) {
            setSelectedUsers(selectedUsers?.filter((dt) => dt !== userId));
        } else {
            setSelectedUsers([...selectedUsers, userId]);
        }
    };

    const onDataSubmit = async () => {
        if (selectedUsers?.length == 0) {
            toast.warning("Select Learners", {
                description: "Please select learners before save attendance",
            });
            return false;
        }
        let payload = {
            class_id: Number(params?.class_id),
            user_ids: selectedUsers,
        };
        await tanstackApi
            .post("class-attendance/offline-attendance", payload)
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("parent_user_id"),
                    event: "sessions",
                    log: `Trainer: ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} marked present ${selectedUsers?.length} learners, for ${params?.topic_name}`,
                });

                toast.success("Attendance Saved", {
                    description: res?.data?.message,
                });
            })
            .catch((err) => {
                toast.error("Attendance Saved", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const onRefresh = async () => {
        await tanstackApi
            .get(`class-attendance/refresh-attendance/${params?.class_id}`)
            .then((res) => {
                punchTimelineLog({
                    user_id: localStorage.getItem("parent_user_id"),
                    event: "sessions",
                    log: `Trainer: ${localStorage.getItem("lms_fName")} ${localStorage.getItem("lms_lName")} marked absent learners, for ${params?.topic_name}`,
                });

                toast.success("Attendance Updated", {
                    description: res?.data?.message,
                });
            })
            .catch((err) => {
                toast.error("Something went wrong", {
                    description: err?.response?.data?.message,
                });
            });
    };

    const punchTimelineLog = async (payload) => {
        await tanstackApi
            .post("users/add-timeline-log", payload)
            .then((res) => {})
            .catch((err) => {});
    };

    return (
        <div>
            <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Refresh, Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action is not reversible. The remaining student will be marked as absent.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={onRefresh}>Yes Continue!</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/trainer-zoom-classes">Zoom Classes</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Attendance for - {params?.topic_name}</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>

                <div className="tw-space-x-2">
                    <Button variant="outline" onClick={() => navigate(`/dashboard/trainer-zoom-classes`)}>
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button variant="outline" onClick={() => setOpenAlert(true)}>
                        <i className="fa-solid fa-arrows-rotate"></i> Refresh
                    </Button>
                    <Button onClick={onDataSubmit}>
                        <i className="fa-solid fa-floppy-disk"></i> Save Attendance
                    </Button>
                </div>
            </div>
            <div className="page_filters tw-mt-2 tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Search
                    </label>
                    <input
                        onChange={onFilterChange}
                        value={filterState?.search}
                        name="search"
                        className="tw-text-sm"
                        type="text"
                        placeholder="Search by name ..."
                    />
                </div>

                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table tw-mt-4">
                <table>
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Learner</th>
                            <th>Email</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>#.{row?.user_code}</td>
                                <td>{`${row?.first_name} ${row?.last_name}`}</td>
                                <td>{row?.email}</td>
                                <td>
                                    {selectedUsers?.includes(row?.id) ? (
                                        <Button variant="success" onClick={() => onCourseSelection(row?.id)}>
                                            <i className="fa-solid fa-check-circle"></i> Present
                                        </Button>
                                    ) : (
                                        <Button variant="outline" onClick={() => onCourseSelection(row?.id)}>
                                            <i className="fa-solid fa-ban"></i> Absent
                                        </Button>
                                    )}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <p className="tw-rounded-md tw-border tw-px-2 tw-py-1 tw-font-bold tw-leading-none">7</p>
                </div>
                <div>
                    <Pagination className="tw-mx-0 tw-w-[auto]">
                        <PaginationContent>
                            {/* Previous Button */}
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page Numbers */}
                            {[...Array(totalPages)].map((_, index) => (
                                <PaginationItem key={index}>
                                    <PaginationLink
                                        href="#"
                                        isActive={index + 1 === currentPage}
                                        onClick={() => handlePageChange(index + 1)}
                                    >
                                        {index + 1}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {/* Ellipsis */}
                            {totalPages > 5 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            {/* Next Button */}
                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </div>
    );
};

export default ClassLearners;
