import { tanstackApi } from "@/react-query/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useGetPermissionListing = (role_code) => {
    const userId = localStorage.getItem("userId");
    return useQuery({
        queryKey: ["permission-listing", { role_code, userId }],
        queryFn: async () => (await tanstackApi.get(`module-permission/role-permissions/${role_code}`)).data,
        enabled: !!role_code,
    });
};

export const useUpdatePermission = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (data) => (await tanstackApi.post("module-permission/add-permission", data)).data,
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["permission-listing"] }),
    });
};
