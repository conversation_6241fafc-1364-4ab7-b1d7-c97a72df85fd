import { cn } from "@/lib/utils";
import { usePlayer } from "@/pages/dashboard/course-player/player-context";
import { motion } from "framer-motion";

const QuizzResult = ({ slides, template, onChangeMode, status }) => {
    const bgVariants = {
        initial: { rotate: 0 },
        animate: { rotate: [360, 0] },
    };
    const { handleNext } = usePlayer();

    const points = slides.reduce((sum, item) => sum + (Number(item.data.earnedPoints) || 0), 0);
    const totalPoints = slides.reduce((sum, item) => sum + (Number(item.data.points) || 0), 0);
    const quizzTemplate = template?.lms_template;
    const isPassed = totalPoints / 2 < points;

    return (
        <>
            <div
                style={{
                    "--primary-color": quizzTemplate?.elements?.colors?.primary?.background_color,
                    "--primary-text-color": quizzTemplate?.elements?.colors?.primary?.color,
                    "--secondary-color": quizzTemplate?.elements?.colors?.secondary?.background_color,
                    "--secondary-text-color": quizzTemplate?.elements?.colors?.secondary?.color,
                }}
                className="ContentQuizz tw-relative tw-h-full tw-w-full tw-overflow-hidden tw-rounded-xl tw-bg-cover tw-bg-center tw-bg-no-repeat !tw-font-lazyDog"
            >
                <motion.div
                    variants={bgVariants}
                    initial="initial"
                    transition={{ duration: 10, ease: "easeInOut", repeat: Infinity }}
                    className="tw-absolute tw-inset-0 tw-z-0 tw-h-[100%] tw-bg-cover tw-bg-center tw-bg-no-repeat"
                    style={{
                        backgroundImage: `url("${quizzTemplate?.background}")`,
                    }}
                />
                <motion.div
                    initial={{ x: 0 }}
                    animate={{
                        x: `calc(${0 * -100}% - ${0 * 2}rem)`,
                    }}
                    transition={{ duration: 1.5, ease: "easeInOut" }}
                    className="tw-relative tw-z-10 tw-flex tw-h-full tw-w-full tw-items-center tw-gap-8"
                >
                    <div
                        className={cn(
                            "tw-relative tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center",
                            "tw-min-w-full tw-flex-grow",
                        )}
                    >
                        <div className="tw-relative tw-h-[90%] tw-w-[80%] tw-rounded-xl tw-border-[10px] tw-border-[#3D3D3D] tw-bg-[#FFFFFF]">
                            <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center">
                                <div className="tw-absolute tw--top-6 tw-left-1/2 tw--translate-x-1/2 tw-border-[4px] tw-border-[#3D3D3D] tw-bg-white tw-px-8 tw-py-1 tw-text-[24px] tw-leading-[24px]">
                                    Result
                                </div>
                                <img
                                    src={!isPassed ? "/quiz/Package Failed to Send.gif" : "/quiz/Shipment Approval.gif"}
                                    alt="Trophy"
                                    className="tw-relative tw-z-10 tw-mb-2 tw-w-40 tw-select-none 2xl:tw-w-[160px]"
                                />
                                <h1 className="tw-relative tw-z-10 tw-text-center tw-text-[32px] tw-leading-[32px] tw-text-[#333333] 2xl:tw-text-[50px] 2xl:tw-leading-[50px]">
                                    {isPassed ? (
                                        <>
                                            Congratulation, You have
                                            <span className="tw-text-green-500">{" passed the quiz!"}</span>
                                        </>
                                    ) : (
                                        <>
                                            Sorry, you have
                                            <span className="tw-text-red-500">{" not passed the quiz!"}</span>
                                        </>
                                    )}
                                </h1>
                                <img
                                    src="/quiz/Vector 189 (Stroke).png"
                                    alt=""
                                    className="tw-my-2 tw-h-[10px] tw-w-[300px] tw-select-none 2xl:tw-my-4"
                                />
                                <div className="tw-relative tw-z-10 tw-grid tw-grid-cols-2 tw-gap-x-4 tw-gap-y-2 2xl:tw-gap-y-3">
                                    <div className="tw-text-[20px] tw-leading-[20px] tw-text-[var(--primary-color)] 2xl:tw-text-[32px] 2xl:tw-leading-[40px]">
                                        Your score:
                                    </div>
                                    <div className="tw-text-[20px] tw-leading-[20px] 2xl:tw-text-[32px] 2xl:tw-leading-[40px]">
                                        {((points / totalPoints) * 100).toFixed(2)}% ({points} Points)
                                    </div>
                                    <div className="tw-text-[20px] tw-leading-[20px] tw-text-[var(--primary-color)] 2xl:tw-text-[32px] 2xl:tw-leading-[40px]">
                                        Passing score:
                                    </div>
                                    <div className="tw-text-[20px] tw-leading-[20px] 2xl:tw-text-[32px] 2xl:tw-leading-[40px]">
                                        50% ({totalPoints / 2} Points)
                                    </div>
                                </div>
                                <div className="tw-mt-6 tw-flex tw-gap-2">
                                    <button
                                        style={{
                                            backgroundColor: quizzTemplate?.elements?.start_button?.background_color,
                                            borderColor: quizzTemplate?.elements?.start_button?.border_color,
                                            color: quizzTemplate?.elements?.start_button?.color,
                                            fontFamily: quizzTemplate?.elements?.start_button?.font_family,
                                            borderWidth: quizzTemplate?.elements?.start_button?.border_width,
                                        }}
                                        onClick={() => {
                                            onChangeMode("summary");
                                        }}
                                        className={cn(
                                            "tw-h-[60px] tw-rounded-full tw-border-[4px] tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-8 tw-text-[32px] tw-leading-[32px]",
                                        )}
                                    >
                                        Summary
                                    </button>
                                    <button
                                        style={{
                                            backgroundColor: quizzTemplate?.elements?.start_button?.background_color,
                                            borderColor: quizzTemplate?.elements?.start_button?.border_color,
                                            color: quizzTemplate?.elements?.start_button?.color,
                                            fontFamily: quizzTemplate?.elements?.start_button?.font_family,
                                            borderWidth: quizzTemplate?.elements?.start_button?.border_width,
                                        }}
                                        onClick={() => {
                                            onChangeMode("instructions");
                                            handleNext(status);
                                        }}
                                        className={cn(
                                            "tw-h-[60px] tw-rounded-full tw-border-[4px] tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-8 tw-text-[32px] tw-leading-[32px]",
                                        )}
                                    >
                                        Submit
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>
            </div>
        </>
    );
};

export default QuizzResult;
