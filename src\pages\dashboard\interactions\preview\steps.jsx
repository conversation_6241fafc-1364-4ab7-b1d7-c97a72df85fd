import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn, isUrl } from "@/lib/utils";
import { useEditInteraction } from "@/pages/dashboard/interactions/create/provider";
import { useFileUpload } from "@/react-query/common";
import { motion } from "framer-motion";
import { ArrowRight, Upload, X } from "lucide-react";
import { useCallback, useRef, useState } from "react";

export function Steps({ template }) {
    const { tabList } = useEditInteraction();
    const [index, setIndex] = useState(0);

    const handlePrevSlide = () => {
        if (index === 0) return;
        setIndex((prev) => prev - 1);
    };

    const handleNextSlide = () => {
        if (tabList.length - 1 === index) return;
        setIndex((prev) => prev + 1);
    };

    return (
        <>
            <div className="tw-relative">
                <motion.div className="tw-grid tw-h-full tw-w-full tw-grid-cols-1 tw-gap-[1rem] tw-text-wrap tw-rounded-[0.5rem] tw-text-[40px] tw-text-black">
                    <Tabs
                        value={tabList[index]?.label}
                        onValueChange={(val) => setIndex(tabList.findIndex((dt) => dt?.label == val))}
                        className="w-[400px] tw-grid tw-h-full tw-grid-rows-[80px_1fr]"
                    >
                        <div className="tw-flex tw-items-center tw-justify-center">
                            <TabsList className="tw-h-full tw-w-fit tw-gap-8 tw-bg-transparent tw-px-5 tw-py-0">
                                {tabList.map((step, index) => (
                                    <div key={index} className="tw-flex tw-items-center tw-gap-8">
                                        <div className="tw-flex tw-flex-col tw-gap-0">
                                            <TabsTrigger
                                                value={step.label}
                                                className="tw-flex tw-aspect-square tw-items-center tw-justify-center !tw-rounded-full tw-bg-[#FFEBBA] !tw-p-2 tw-font-lazyDog !tw-text-[22px] tw-leading-none tw-text-[#3B3A3E] tw-shadow-none data-[state=active]:!tw-bg-[#F8CA5C] data-[state=active]:tw-text-[#3B3A3E] data-[state=active]:tw-shadow-none 2xl:!tw-text-[40px]"
                                            >
                                                <div className="w-flex tw-aspect-square tw-size-10 tw-items-center tw-justify-center tw-rounded-full tw-bg-[#f8ca5c] tw-p-2">
                                                    <span className="">{index + 1}</span>
                                                </div>
                                            </TabsTrigger>
                                            <div className="tw-flex tw-items-center tw-justify-center tw-rounded-full tw-text-center tw-text-sm tw-text-black">
                                                {step.label}
                                            </div>
                                        </div>
                                        {tabList.length - 1 !== index && <ArrowRight className="pl-8 mb-3" />}
                                    </div>
                                ))}
                            </TabsList>
                        </div>

                        {tabList.map((step, index) => (
                            <TabItem key={step.label + index} index={index} item={step} />
                        ))}
                    </Tabs>
                </motion.div>
            </div>
            <div className="tw-absolute tw-bottom-4 tw-right-4 tw-z-20 tw-flex tw-gap-1">
                <button
                    onClick={handlePrevSlide}
                    disabled={0 === index}
                    style={{
                        backgroundColor: template?.elements?.prev_button?.background_color,
                        borderColor: template?.elements?.prev_button?.border_color,
                        color: template?.elements?.prev_button?.color,
                        fontFamily: template?.elements?.prev_button?.font_family,
                        borderWidth: template?.elements?.prev_button?.border_width,
                    }}
                    className="tw-aspect-square tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] disabled:tw-opacity-70"
                >
                    <i className="fa-solid fa-arrow-left tw-text-[30px] tw-leading-[40px] tw-text-[#333333]"></i>
                </button>
                <button
                    onClick={handleNextSlide}
                    disabled={tabList.length - 1 === index}
                    style={{
                        backgroundColor: template?.elements?.next_button?.background_color,
                        borderColor: template?.elements?.next_button?.border_color,
                        color: template?.elements?.next_button?.color,
                        fontFamily: template?.elements?.next_button?.font_family,
                        borderWidth: template?.elements?.next_button?.border_width,
                    }}
                    className={cn(
                        "tw-w-[60px] tw-flex-shrink-0 tw-rounded-full tw-border-4 tw-border-[#3D3D3D] tw-bg-[#FFD860] tw-px-0 tw-text-[30px] tw-leading-[30px] disabled:tw-opacity-70",
                    )}
                >
                    <i className="fa-solid fa-arrow-right tw-text-[30px] tw-leading-[30px] tw-text-[#333333]"></i>
                </button>
            </div>
        </>
    );
}

function TabItem({ item, index }) {
    const { handleTabChange } = useEditInteraction();
    const [titleEditable, setTitleEditable] = useState(false);
    const [descriptionEditable, setDescriptionEditable] = useState(false);
    const titleRef = useRef(null);
    const descriptionRef = useRef(null);
    const upload = useFileUpload();

    const handleImageChange = useCallback(async (e) => {
        let imageUrl = "";
        try {
            if (e.target.files[0]) {
                const formdata = new FormData();
                const imageData = e.target.files[0];
                formdata.append("file", imageData);
                formdata.append("category", "COURSE-CONTENTS");
                const response = await upload.mutateAsync(formdata);
                imageUrl = response?.fileUrl;
            }
        } catch (error) {
            imageUrl = "";
        } finally {
            handleTabChange("image_src", imageUrl, index);
        }
    }, []);

    const removeImage = useCallback(() => {
        handleTabChange("image_src", "", index);
    }, []);

    const handleTitleEditable = useCallback(() => {
        setTitleEditable((prev) => !prev);
    }, []);

    const handleDescriptionEditable = useCallback(() => {
        setDescriptionEditable((prev) => !prev);
    }, []);

    const handleTitleBlur = useCallback(() => {
        handleTabChange("label", titleRef?.current?.innerText, index);
    }, []);

    const handleDescriptionBlur = useCallback(() => {
        handleTabChange("description", descriptionRef?.current?.innerText, index);
    }, []);

    return (
        <TabsContent value={item.label}>
            <div
                style={{
                    background: `url(${item.image_src})`,
                }}
                className="tw-relative tw-flex tw-min-h-80 tw-flex-col tw-overflow-hidden tw-rounded-xl tw-p-5 tw-text-white"
            >
                <div className="tw-absolute tw-right-0 tw-top-0 tw-z-20 tw-flex">
                    {isUrl(item.image_src) ? (
                        <Button
                            variant="outline"
                            className="aspect-square max-sm:p-0 tw-text-gray-700"
                            onClick={removeImage}
                        >
                            <X className="opacity-60" size={16} strokeWidth={2} aria-hidden="true" />
                            <Label className="max-sm:sr-only">Remove</Label>
                        </Button>
                    ) : (
                        <Button variant="outline" className="aspect-square max-sm:p-0 tw-h-10 tw-text-gray-700">
                            <Upload className="opacity-60" size={16} strokeWidth={2} aria-hidden="true" />
                            <input
                                onChange={handleImageChange}
                                type="file"
                                style={{ display: "none" }}
                                id="bg_image"
                                accept="image/*"
                            />
                            <Label htmlFor="bg_image" className="max-sm:sr-only">
                                {upload.isPending ? "Uploading..." : "Upload Image"}
                            </Label>
                        </Button>
                    )}
                </div>
                <div className="tw-absolute tw-inset-0 tw-size-full tw-bg-black/40"></div>
                <div className="tw-relative tw-z-10 tw-flex tw-h-full tw-flex-1 tw-flex-col tw-justify-end tw-font-lazyDog">
                    <h2 className="tw-mt-auto tw-text-[20px] tw-leading-none 2xl:tw-text-[30px]">
                        {index + 1}.{" "}
                        <span
                            ref={titleRef}
                            contentEditable={titleEditable}
                            onDoubleClick={handleTitleEditable}
                            onBlur={handleTitleBlur}
                        >
                            {item.label}
                        </span>
                    </h2>
                    <div
                        ref={descriptionRef}
                        contentEditable={descriptionEditable}
                        onDoubleClick={handleDescriptionEditable}
                        onBlur={handleDescriptionBlur}
                        className="tw-text-base"
                        dangerouslySetInnerHTML={{ __html: item.description }}
                    />
                </div>
            </div>
        </TabsContent>
    );
}

export function StepsPreview() {
    const list = [0, 1, 2, 3];
    return (
        <div className="tw-grid tw-size-full tw-grid-cols-1 tw-grid-rows-7 tw-items-center tw-justify-center tw-gap-1">
            <div className="tw-row-span-1 tw-flex tw-items-center tw-justify-center tw-gap-2">
                {list.map((item) => (
                    <div
                        key={item}
                        className={cn(
                            "tw-relative tw-flex tw-size-[20px] tw-items-center tw-justify-center tw-overflow-hidden tw-rounded-2xl tw-border tw-bg-slate-300 tw-transition-all",
                        )}
                    />
                ))}
            </div>
            <div className="tw-row-span-6 tw-h-full tw-rounded-lg tw-bg-slate-300"></div>
        </div>
    );
}
