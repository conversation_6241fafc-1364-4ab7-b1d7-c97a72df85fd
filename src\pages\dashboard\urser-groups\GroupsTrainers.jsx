import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    BreadcrumbItem,
    Bread<PERSON>rumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { tanstackApi } from "@/react-query/api";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import GroupTrainerFrom from "./GroupTrainerFrom";

const GroupsTrainers = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();

    const [groupsList, setGroupsList] = useState([]);

    const [dataList, setDataList] = useState([]);
    const [filteredData, setFilteredData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(10);

    const [open, setOpen] = useState(searchParams.get("type") == "create" ? true : false);
    const [editData, setEditData] = useState(null);

    const [filterState, setFilterState] = useState({
        search: "",
        group_id: "",
    });

    const [currentPage, setCurrentPage] = useState(1);

    // Calculate total pages
    const totalPages = Math.ceil(filteredData.length / recordsPerPage);

    useEffect(() => {
        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        let options = filteredData.slice(startIndex, endIndex);
        setTableData(options);
    }, [currentPage, filteredData]);

    const getVisiblePages = () => {
        const pagesToShow = 3; // Show only 5 page links at a time
        const halfRange = Math.floor(pagesToShow / 2);

        let start = Math.max(1, currentPage - halfRange);
        let end = Math.min(totalPages, currentPage + halfRange);

        if (currentPage <= halfRange) {
            end = Math.min(pagesToShow, totalPages);
        }

        if (currentPage > totalPages - halfRange) {
            start = Math.max(1, totalPages - pagesToShow + 1);
        }

        const pages = [];
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return pages;
    };

    const onChangeHandle = (e) => {
        const { name, value } = e.target;
        setFilterState({ ...filterState, [name]: value });
    };

    const onSearch = () => {
        const data = dataList?.filter((item) => {
            const matchesSearch = filterState?.search
                ? `${item?.lms_user?.first_name} ${item?.lms_user?.last_name}`
                      ?.toLowerCase()
                      ?.includes(filterState?.search?.toLowerCase())
                : true; // Allow all items if no subCategory filter

            const group = filterState?.group_id ? item?.group_id === Number(filterState?.group_id) : true; // Allow all items if no subCategory filter

            return matchesSearch && group; // Both must match
        });
        // setPageNumber(1);
        setFilteredData(data);
    };

    const onClear = () => {
        setFilteredData(dataList);
        setFilterState({
            search: "",
            group_id: "",
        });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    useEffect(() => {
        if (dataList?.length > 0) {
            setFilteredData(dataList);
        } else {
            setFilteredData([]);
        }
    }, [dataList]);

    useEffect(() => {
        getClassListMapping();
        getUserGroups();
    }, []);

    const getClassListMapping = async () => {
        await tanstackApi
            .post("class/list")
            .then((res) => {
                setDataList(res?.data?.data);
            })
            .catch((err) => {
                setDataList([]);
            });
    };

    const onAddHomework = (data) => {
        setEditData(null);
        setOpen(true);
    };

    const getUserGroups = async (payload) => {
        await tanstackApi
            .post("user-groups/list")
            .then((res) => {
                setGroupsList(res?.data?.data?.reverse());
            })
            .catch((err) => {
                setGroupsList([]);
            });
    };

    return (
        <div>
            <GroupTrainerFrom
                open={open}
                setOpen={setOpen}
                editData={editData}
                getClassListMapping={getClassListMapping}
            />
            <div className="tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/user-group-master">User Groups</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Group & Trainer mapping</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <div className="tw-flex tw-justify-between tw-gap-2">
                    <Button
                        variant="outline"
                        className="tw-px-2 tw-py-1"
                        onClick={() => navigate(`/dashboard/user-group-master`)}
                    >
                        <i className="fa-solid fa-left-long"></i> Back
                    </Button>
                    <Button className="tw-px-2 tw-py-1" onClick={onAddHomework}>
                        <i className="fa-solid fa-plus"></i> New Mapping
                    </Button>
                </div>
            </div>
            <div className="page_filters tw-flex tw-flex-wrap">
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Trainer Name
                    </label>
                    <input
                        onChange={onChangeHandle}
                        value={filterState?.search}
                        name="search"
                        className="tw-text-sm"
                        style={{ minWidth: "300px" }}
                        type="text"
                        placeholder="Search by trainer name ..."
                    />
                </div>
                <div className="input_group">
                    <label className="tw-text-sm" htmlFor="">
                        Group
                    </label>
                    <select
                        className="tw-text-sm"
                        onChange={onChangeHandle}
                        value={filterState?.group_id}
                        name="group_id"
                    >
                        <option value=""> - Choose Group - </option>
                        {groupsList?.map((type, idx) => (
                            <option value={type?.id} key={idx}>
                                {type?.name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="filter_controls">
                    <button className="search_btn" onClick={onSearch}>
                        <i className="fa-solid fa-magnifying-glass"></i> Search
                    </button>
                    <button className="clear_btn" onClick={onClear}>
                        <i className="fa-solid fa-xmark"></i> Clear
                    </button>
                </div>
            </div>
            <div className="custom_table">
                <table>
                    <thead>
                        <tr>
                            <th>Group</th>
                            <th>Trainer</th>
                            <th>Trainer Email</th>
                            <th>Assigned On</th>
                            <th>Updated On</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tableData?.map((row, idx) => (
                            <tr key={idx}>
                                <td>{row?.lms_user_group?.name}</td>
                                <td>{`${row?.lms_user?.first_name} ${row?.lms_user?.last_name}`}</td>
                                <td>{row?.lms_user?.email}</td>
                                <td>{moment(row?.createdAt).format("LL")}</td>
                                <td>{moment(row?.updatedAt).format("LL")}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className="tw-flex tw-items-center tw-justify-between">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-3">
                    <p className="tw-whitespace-nowrap tw-text-sm tw-font-medium">Rows per page</p>
                    <Select onValueChange={(e) => setRecordsPerPage(e)} value={recordsPerPage}>
                        <SelectTrigger>
                            <SelectValue placeholder="" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value={7}>7</SelectItem>
                            <SelectItem value={10}>10</SelectItem>
                            <SelectItem value={20}>20</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div>
                    <Pagination className="">
                        <PaginationContent>
                            {/* Previous Button */}
                            <PaginationItem>
                                <PaginationPrevious
                                    href="#"
                                    onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page Numbers with Ellipses */}
                            {currentPage > 3 && (
                                <>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(1)}>
                                            1
                                        </PaginationLink>
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                </>
                            )}

                            {getVisiblePages().map((page) => (
                                <PaginationItem key={page}>
                                    <PaginationLink
                                        href="#"
                                        isActive={page === currentPage}
                                        onClick={() => handlePageChange(page)}
                                    >
                                        {page}
                                    </PaginationLink>
                                </PaginationItem>
                            ))}

                            {currentPage < totalPages - 2 && (
                                <>
                                    <PaginationItem>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                    <PaginationItem>
                                        <PaginationLink href="#" onClick={() => handlePageChange(totalPages)}>
                                            {totalPages}
                                        </PaginationLink>
                                    </PaginationItem>
                                </>
                            )}

                            {/* Next Button */}
                            <PaginationItem>
                                <PaginationNext
                                    href="#"
                                    onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            </div>
        </div>
    );
};

export default GroupsTrainers;
