import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { tanstackApi, tanstackApiFormdata } from "@/react-query/api";
import { Info, Upload, X } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

const QuizDetails = ({ onChangeHandle, quizData, setQuizData, trainersList }) => {
    const params = useParams();

    const AudienceTypes = ["Mixed", "Test", "Quiz", "MCQ"];
    const AnswerTypes = ["show-only-right-wrong", "show-correct-answer"];

    const [courseList, setCourseList] = useState([]);
    const [chapterList, setChapterList] = useState([]);
    const [subCategoryList, setSubCategoryList] = useState([]);

    const [load, setLoad] = useState(false);
    const [uploaded, setUploaded] = useState(null);

    useEffect(() => {
        getCourses();
    }, []);

    useEffect(() => {
        if (quizData?.course_id && courseList?.length > 0) {
            getChapters({ course_id: quizData?.course_id });
        }
    }, [quizData]);

    const RemoveImage = () => {
        setQuizData({ ...quizData, thumbnail_img: "" });
    };

    const onImageChange = (e) => {
        if (e.target.files[0]) {
            const data = new FormData();
            const imageData = e.target.files[0];
            data.append("file", imageData);
            data.append("category", "COURSE-IMAGES");
            onFileUpload(data);
        }
    };

    const onFileUpload = async (formData) => {
        setLoad(true);
        await tanstackApiFormdata
            .post("common/upload-course-file", formData, {
                onUploadProgress: (data) => {
                    var percent = Math.round((data.loaded / data.total) * 100);
                    setUploaded(percent);
                },
            })
            .then((res) => {
                if (res.data.success) {
                    setLoad(false);
                    setQuizData({ ...quizData, thumbnail_img: res.data.fileUrl });
                } else {
                    setLoad(false);
                    setQuizData({ ...quizData, thumbnail_img: "" });
                }
            })
            .catch((err) => {
                setLoad(false);
                setQuizData({ ...quizData, thumbnail_img: "" });
            });
    };

    const getCourses = async (payload) => {
        await tanstackApi
            .get("course/get-courses")
            .then((res) => {
                setCourseList(res?.data?.data);
            })
            .catch((err) => {
                setCourseList([]);
            });
    };

    const getChapters = async (payload) => {
        await tanstackApi
            .post("course/view-course", { ...payload })
            .then((res) => {
                setChapterList(res?.data?.data?.courseChapters);
            })
            .catch((err) => {
                setChapterList([]);
            });
    };

    const loginToken = localStorage.getItem("login_token");

    return (
        <Card>
            <CardHeader>
                <div className="tw-flex tw-items-center tw-justify-between">
                    <div className="tw-space-y-2">
                        <CardTitle>Quiz details</CardTitle>
                        <CardDescription>
                            Add quiz title, banner , decription etc here. Click save when you&apos;re done.
                        </CardDescription>
                    </div>
                    <div>
                        {params?.quiz_id !== undefined && (
                            <Link
                                to={`https://lms-course-builder.vercel.app/dashboard/quiz-config/${params?.quiz_id}?token=${loginToken}`}
                                target="_blank"
                            >
                                <Button variant="outline" className="tw-rounded-xl">
                                    <i className="fa-solid fa-plus"></i> Add Questions
                                </Button>
                            </Link>
                        )}
                    </div>
                </div>
            </CardHeader>
            <CardContent className="tw-space-y-2">
                <div className="tw-grid tw-grid-cols-[1.2fr_2fr] tw-gap-0">
                    <div className="tw-mt-2 tw-flex tw-flex-col tw-items-center tw-gap-2">
                        <div className="tw-aspect-[2/1] tw-w-[85%] tw-rounded-xl tw-border-[2px] tw-border-dashed tw-p-2">
                            <img
                                className="tw-aspect-[2/1] tw-w-full tw-rounded-md tw-object-cover"
                                src={quizData?.thumbnail_img || "/assets/thumbnail.png"}
                            />
                        </div>
                        <div className="tw-flex tw-gap-2">
                            <Label
                                htmlFor="thumbnail_img"
                                className={cn(
                                    buttonVariants({ variant: "outline" }),
                                    "aspect-square max-sm:p-0 hover:tw-text-white",
                                )}
                            >
                                <Upload
                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                                <input
                                    onChange={onImageChange}
                                    type="file"
                                    style={{ display: "none" }}
                                    id="thumbnail_img"
                                    accept="image/*"
                                />
                                <div htmlFor="thumbnail_img" className="max-sm:sr-only">
                                    {load ? "Uploading" : "Upload Quiz thumbnail"} {load ? `${uploaded}%` : null}
                                </div>
                            </Label>
                            <Button variant="outline" className="aspect-square max-sm:p-0" onClick={RemoveImage}>
                                <X
                                    className="opacity-60 sm:-ms-1 sm:me-2"
                                    size={16}
                                    strokeWidth={2}
                                    aria-hidden="true"
                                />
                                <Label className="max-sm:sr-only">Remove</Label>
                            </Button>
                        </div>
                    </div>
                    <div className="tw-space-y-3">
                        <div className="tw-space-y-1">
                            <Label htmlFor="title">Quiz Title *</Label>
                            <Input
                                onChange={onChangeHandle}
                                value={quizData?.title}
                                name="title"
                                id="title"
                                placeholder="Enter quiz title here"
                            />
                        </div>
                        <div className="tw-space-y-1">
                            <Label htmlFor="name">Description *</Label>
                            <Textarea
                                onChange={onChangeHandle}
                                value={quizData?.description}
                                name="description"
                                className="tw-text-sm"
                                placeholder="Define quiz description here."
                                rows="2"
                            />
                        </div>

                        <div className="tw-space-y-3">
                            <h1 className="tw-text-xl tw-font-semibold">Define quiz type *</h1>
                            <div className="tw-flex-col-2 tw-mt-3 tw-flex tw-gap-10">
                                <RadioGroup
                                    value={quizData?.type}
                                    onValueChange={(e) => onChangeHandle({ target: { value: e, name: "type" } })}
                                    className="tw-flex"
                                >
                                    <div className="space-x-6 tw-flex tw-items-center tw-gap-10">
                                        {AudienceTypes?.map((data, idx) => (
                                            <div className="tw-flex tw-items-center tw-space-x-2" key={idx}>
                                                <RadioGroupItem
                                                    value={data}
                                                    id={data}
                                                    checked={quizData?.type == data}
                                                />
                                                <Label
                                                    htmlFor={data}
                                                    className="tw-cursor-pointer tw-font-normal tw-capitalize"
                                                >
                                                    {data}
                                                </Label>
                                                <TooltipProvider>
                                                    <Tooltip delayDuration={100}>
                                                        <TooltipTrigger>
                                                            <Info className="tw-size-4 tw-fill-blue-500 tw-text-white" />
                                                        </TooltipTrigger>
                                                        <TooltipContent className="tw-bg-white">
                                                            <p>Add to library</p>
                                                        </TooltipContent>
                                                    </Tooltip>
                                                </TooltipProvider>
                                            </div>
                                        ))}
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default QuizDetails;
