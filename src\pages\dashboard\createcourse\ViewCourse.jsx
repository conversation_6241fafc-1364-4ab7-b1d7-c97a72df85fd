import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>readcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useDeleteChapter, useViewCourses } from "@/react-query/courses";
import { Edit, Loader2, Plus, Trash, TvMinimalPlay, Video } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import AddNewChapter from "./AddNewChapter";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

function formatNumber(num) {
    return num?.toString()?.padStart(2, "0");
}

const ViewCourse = () => {
    const params = useParams();
    const navigate = useNavigate();
    const viewCourseData = useViewCourses(params?.course_id);
    const loginToken = localStorage.getItem("login_token");
    const [open, setOpen] = useState(false);
    const [editChapter, setEditChapter] = useState(null);
    const [totalPoints, setTotalPoints] = useState(0);
    const [totalTimeline, setTotalTimeline] = useState(0);
    const [CourseData, setCourseData] = useState(null);

    useEffect(() => {
        if (viewCourseData.isSuccess) {
            const data = viewCourseData.data?.data;
            setCourseData(data);
            let totalPoints = 0;
            let totalTimeline = 0;
            data.courseChapters?.forEach((chapter) => {
                totalPoints += chapter?.chapter_points;
                totalTimeline += chapter?.days;
            });
            setTotalPoints(totalPoints);
            setTotalTimeline(totalTimeline);
        }
    }, [viewCourseData]);

    const onChapterAdd = () => {
        setEditChapter(null);
        setOpen(true);
    };

    const onChapterEdit = (data) => {
        setEditChapter(data);
        setOpen(true);
    };

    const onRedirect = () => {
        if (localStorage.getItem("is_trainer") == "true") {
            navigate(`/dashboard/assigned-course`);
        } else {
            navigate(`/dashboard/my-courses`);
        }
    };

    return (
        <div className="">
            <AddNewChapter open={open} setOpen={setOpen} editChapter={editChapter} CourseData={CourseData} />
            <div className="tw-mb-2 tw-flex tw-justify-between">
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/dashboard/my-courses">Courses</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{CourseData?.course_title}</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
                <Button className="tw-px-2 tw-py-1" onClick={onRedirect}>
                    <i className="fa-solid fa-left-long"></i> Back
                </Button>
            </div>
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="tw-w-[50px]">Id</TableHead>
                        <TableHead className="tw-w-40">Thumbnail</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead className="tw-w-32">Type</TableHead>
                        <TableHead className="tw-w-32">Access</TableHead>
                        <TableHead className="tw-w-32">Status</TableHead>
                        <TableHead className="tw-w-32">Total Points</TableHead>
                        <TableHead className="tw-w-32">Timeline</TableHead>
                        <TableHead className="tw-w-48">Action</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow>
                        <TableCell className="font-medium">{CourseData?.id}</TableCell>
                        <TableCell>
                            <img
                                className="tw-aspect-[2/1] tw-w-40 tw-rounded tw-object-cover tw-shadow-sm"
                                src={CourseData?.course_banner_url ?? "/assets/thumbnail.png"}
                            />
                        </TableCell>
                        <TableCell className="tw-text-2xl tw-font-bold">{CourseData?.course_title}</TableCell>
                        <TableCell>{CourseData?.is_scorm ? "SCROM" : "Combined"}</TableCell>
                        <TableCell>{CourseData?.is_public ? "Public" : "Private"}</TableCell>
                        <TableCell>{CourseData?.course_status}</TableCell>
                        <TableCell>{totalPoints}</TableCell>
                        <TableCell>{totalTimeline}</TableCell>
                        <TableCell>
                            <div className="tw-grid tw-grid-cols-3 tw-gap-2">
                                <Button
                                    title="Learner View"
                                    variant="outline"
                                    size="icon"
                                    className="tw-rounded"
                                    asChild
                                >
                                    <Link to={`/course-details/view/${params?.course_id}/0/0`}>
                                        <TvMinimalPlay
                                            className="-tw-ms-1 tw-opacity-60"
                                            size={16}
                                            strokeWidth={2}
                                            aria-hidden="true"
                                        />
                                        <span className="tw-sr-only">Learner View</span>
                                    </Link>
                                </Button>
                                {!CourseData?.is_public ? (
                                    <>
                                        <Button
                                            title="Edit Course"
                                            asChild
                                            variant="outline"
                                            size="icon"
                                            className="tw-rounded"
                                        >
                                            <Link to={`/dashboard/edit-course/${params?.course_id}`}>
                                                <Edit
                                                    className="-tw-ms-1 tw-opacity-60"
                                                    size={16}
                                                    strokeWidth={2}
                                                    aria-hidden="true"
                                                />
                                                <span className="tw-sr-only">Edit Course</span>
                                            </Link>
                                        </Button>
                                    </>
                                ) : (
                                    <></>
                                )}
                            </div>
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
            <div className="tw-mt-10">
                <div className="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-pb-5">
                    <p className="tw-font-medium">Course Content</p>
                    <Button onClick={onChapterAdd}>
                        <Plus size={16} strokeWidth={2} aria-hidden="true" />
                        Add New Chapter
                    </Button>
                </div>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="tw-w-[50px]">Id</TableHead>
                            <TableHead className="tw-w-40">Thumbnail</TableHead>
                            <TableHead className="tw-font-bold">Title</TableHead>
                            <TableHead className="tw-w-32">Type</TableHead>
                            <TableHead className="tw-w-32">Slides</TableHead>
                            <TableHead className="tw-w-32">Status</TableHead>
                            <TableHead className="tw-w-32">Total Points</TableHead>
                            <TableHead className="tw-w-32">Timeline</TableHead>
                            <TableHead className="tw-w-48">Action</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {CourseData?.courseChapters?.map((chapter, index) => (
                            <TableRow key={index}>
                                <TableCell className="tw-w-[50px] tw-font-medium">
                                    {formatNumber(chapter?.chapter_order)}
                                </TableCell>
                                <TableCell>
                                    <img
                                        className="tw-aspect-[2/1] tw-w-40 tw-rounded tw-object-cover tw-shadow-sm"
                                        src={
                                            CourseData?.course_banner_url
                                                ? CourseData?.course_banner_url
                                                : "/assets/thumbnail.png"
                                        }
                                    />
                                </TableCell>
                                <TableCell>{chapter?.chapter_title}</TableCell>
                                <TableCell>Courses</TableCell>
                                <TableCell>{chapter?.lms_course_chapter_contents?.length}</TableCell>
                                <TableCell>{chapter?.is_active ? "Active" : "Inactive"}</TableCell>
                                <TableCell>{chapter?.chapter_points}</TableCell>
                                <TableCell>{chapter?.days}</TableCell>
                                <TableCell>
                                    <div className="tw-flex tw-gap-0.5">
                                        <Button
                                            title="Edit Chapter"
                                            size="icon"
                                            onClick={() => onChapterEdit(chapter)}
                                            variant="outline"
                                        >
                                            <Edit size={16} strokeWidth={2} aria-hidden="true" />
                                            <span className="tw-sr-only">Edit Chapter</span>
                                        </Button>
                                        <Button title="Add Content" asChild size="icon" variant="outline">
                                            <Link
                                                to={`https://lms-course-builder.vercel.app/dashboard/chapter-content/${params?.course_id}/${chapter?.id}?token=${loginToken}`}
                                                target="_blank"
                                            >
                                                <Plus size={16} strokeWidth={2} aria-hidden="true" />
                                                <span className="tw-sr-only">Add Content</span>
                                            </Link>
                                        </Button>
                                        <Button title="Zoom Classes" asChild size="icon" variant="outline">
                                            <Link
                                                to={`/dashboard/zoom-classes/${params?.course_id}/${chapter?.id}/${chapter?.chapter_title}`}
                                            >
                                                <Video size={16} strokeWidth={2} aria-hidden="true" />
                                                <span className="tw-sr-only">Zoom Classes</span>
                                            </Link>
                                        </Button>
                                        <DeleteChapter chapter_id={chapter.id} />
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
};

export default ViewCourse;

function DeleteChapter({ chapter_id }) {
    const deleteChapter = useDeleteChapter();
    const Icon = deleteChapter.isPending ? Loader2 : Trash;
    return (
        <>
            <AlertDialog>
                <AlertDialogTrigger asChild>
                    <Button
                        title="Delete Course"
                        variant="outline"
                        size="icon"
                        className="selected_btn tw-flex tw-items-center tw-gap-1 tw-rounded tw-font-lexend"
                    >
                        <Trash className="-tw-ms-1 tw-text-primary" size={16} strokeWidth={2} aria-hidden="true" />
                        <span className="tw-sr-only">Delete Course</span>
                    </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            disabled={deleteChapter.isPending}
                            onClick={() =>
                                deleteChapter.mutate(
                                    { chapter_id },
                                    {
                                        onSuccess: (data) => {
                                            toast.success(data.message ?? "Chapter deleted successfully");
                                        },
                                        onError: (e) => {
                                            toast.error(e.response.data.message ?? "Something went wrong");
                                        },
                                    },
                                )
                            }
                        >
                            <Icon className={cn("tw-size-4", deleteChapter.isPending && "tw-animate-spin")} /> Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
