import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog";
import QuizConfigButton from "@/pages/dashboard/quizzes/components/button";
import { quizExampleData } from "@/pages/dashboard/quizzes/components/example-data";
import QuizStyle from "@/pages/dashboard/quizzes/components/style";
import { useInView } from "framer-motion";
import { CircleFadingPlus, CircleHelp } from "lucide-react";
import { useEffect, useRef, useState } from "react";

/**
 * This component is used to render the container for a quiz.
 * @param {Object} props - The props for the component.
 * @param {Function} props.onSlideEdit - The function to handle slide editing.
 * @param {Function} props.onRemoveSlide - The function to handle slide removal.
 * @param {number} props.index - The index of the quiz.
 * @param {Object} props.content - The content of the quiz.
 * @param {Object} props.template - The template for the quiz.
 * @param {Array} props.componentsArray - The array of quiz components.
 * @param {Function} props.setComponentsArray - The function to set the quiz components array.
 * @param {Function} props.setCurrentId - The function to set the current id.
 * @param {boolean} props.empty - A flag to indicate if the quiz is empty.
 * @param {keyof typeof quizExampleData} props.type - The type of the quiz, which is a key of quizExampleData.
 * @param {Object} props.children - The children of the quiz.
 */
export default function QuizContainer({
    onSlideEdit,
    onRemoveSlide,
    index,
    content,
    template,
    componentsArray,
    setComponentsArray,
    setCurrentId,
    empty,
    type,
    children,
}) {
    const parentRef = useRef(null);
    const parentInView = useInView(parentRef, { amount: 0.8 });
    useEffect(() => {
        if (parentInView) setCurrentId(`#slide_${index}`);
    }, [parentInView]);
    const data = componentsArray[index];
    const [styleDialogOpen, setStyleDialogOpen] = useState(false);
    return (
        <div
            ref={parentRef}
            className="tw-grid tw-min-h-[60vh] tw-grid-rows-[50px_1fr] tw-rounded-2xl tw-border tw-bg-white tw-bg-cover tw-bg-center tw-bg-no-repeat"
            id={`slide_${index}`}
            style={{ backgroundImage: `url(${template?.background || data?.question_background})` }}
        >
            <div className="tw-flex tw-items-center tw-justify-between tw-gap-3 tw-p-3">
                <div className="tw-flex">
                    <div className="tw-grid tw-grid-cols-[25px_1fr] tw-items-center tw-gap-2">
                        <CircleHelp
                            style={{
                                color: content?.styles?.question?.color,
                            }}
                            size={25}
                        />{" "}
                        <h1
                            className="tw-line-clamp-1 tw-font-mono tw-text-xl tw-font-semibold tw-text-gray-400"
                            style={{
                                color: content?.styles?.question?.color,
                                fontFamily: content?.styles?.question?.fontFamily,
                            }}
                        >
                            {content?.name}
                        </h1>
                    </div>
                </div>
                <div className="tw-flex tw-items-center tw-justify-end tw-gap-2">
                    {!empty && (
                        <>
                            <QuizConfigButton onClick={() => setStyleDialogOpen(true)} icon="design" />
                            <QuizConfigButton
                                onClick={() => {
                                    let options = [...componentsArray];
                                    options[index] = quizExampleData[type];
                                    setComponentsArray(options);
                                    onSlideEdit(quizExampleData[type], index);
                                }}
                                icon="sample"
                            />
                            <QuizConfigButton onClick={() => onSlideEdit(content, index)} icon="edit" />
                        </>
                    )}
                    <QuizConfigButton onClick={() => onRemoveSlide(index)} icon="delete" />
                </div>
            </div>
            <div className="tw-h-full tw-w-full">
                {empty ? (
                    <div className="tw-flex tw-h-full tw-w-full tw-items-center tw-justify-center tw-gap-5 tw-rounded-md tw-p-3">
                        <div
                            onClick={() => onSlideEdit(content, index)}
                            className="tw-m-1 tw-flex tw-w-[200px] tw-cursor-pointer tw-items-center tw-justify-center tw-gap-3 tw-rounded-2xl tw-border-[1px] tw-border-dashed tw-border-gray-400 tw-p-4 tw-transition-all hover:tw-gap-2 hover:tw-bg-white"
                        >
                            <div>
                                <h1 className="tw-leading-0 tw-text-xl tw-font-semibold tw-text-gray-400">
                                    Add Options
                                </h1>
                            </div>
                            <CircleFadingPlus className="tw-text-gray-400" size={30} />
                        </div>
                    </div>
                ) : (
                    children
                )}
            </div>
            <Dialog open={styleDialogOpen} onOpenChange={setStyleDialogOpen}>
                <DialogContent className="tw-h-[60%] tw-overflow-auto">
                    <QuizStyle
                        componentsArray={componentsArray}
                        index={index}
                        setComponentsArray={setComponentsArray}
                        type={type}
                        setStyleDialogOpen={setStyleDialogOpen}
                    />
                </DialogContent>
            </Dialog>
        </div>
    );
}
