import Input1 from "@/components/input/type1";
import { But<PERSON> } from "@/components/ui/button";
import { ColorPicker } from "@/components/ui/color-picker";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Code, Image, List, ListOrdered, Minus, Quote, Redo, Undo, X } from "lucide-react";
import { useEffect, useState } from "react";

const typographyList = {
    p: "Paragraph",
    1: "H1",
    2: "H2",
    3: "H3",
    4: "H4",
    5: "H5",
    6: "H6",
};

const fontFamilyList = ["Open Sans", "Lazy Dog", "Inter", "Lexend"];

const MenuBar = ({ editor }) => {
    const [typography, setTypography] = useState("p");
    const [fontFamily, setFontFamily] = useState("");
    const [url, setUrl] = useState("");

    useEffect(() => {
        if (url) editor.chain().focus().setImage({ src: url }).run();
    }, [url]);

    if (!editor) return null;

    return (
        <div className="tw-sticky tw-top-0 tw-z-10 tw-flex tw-flex-wrap tw-items-stretch tw-gap-x-4 tw-gap-y-2 tw-bg-secondary tw-p-2 tw-shadow-sm">
            <div className="tw-flex tw-gap-x-2">
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().toggleCode().run()}
                    variant={editor.isActive("code") ? "secondary" : "outline"}
                    size="icon"
                >
                    <Code />
                </Button>
            </div>
            <Separator orientation="vertical" className="!tw-h-8 tw-border-black" />
            <div className="tw-flex tw-gap-x-2">
                <ColorPicker
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    value={editor.getAttributes("textStyle").color}
                    onChange={(color) => {
                        editor.chain().focus().setColor(color).run();
                    }}
                />

                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().unsetAllMarks().run()}
                    size="icon"
                >
                    <X />
                </Button>
            </div>
            <Separator orientation="vertical" className="!tw-h-8 tw-border-black" />
            <div className="tw-flex tw-gap-x-2">
                <Select
                    value={typography}
                    onValueChange={(value) => {
                        setTypography(value);
                        if (value === "p") return editor.chain().focus().setParagraph().run();
                        return editor
                            .chain()
                            .focus()
                            .toggleHeading({ level: Number(value) })
                            .run();
                    }}
                >
                    <SelectTrigger className="!tw-h-8 !tw-w-[120px] [&_svg]:tw-size-3">
                        <SelectValue placeholder="Text" />
                    </SelectTrigger>
                    <SelectContent>
                        {Object.entries(typographyList).map(([key, value]) => (
                            <SelectItem key={key} value={key}>
                                {value}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                <Select
                    value={fontFamily}
                    onValueChange={(value) => {
                        setFontFamily(value);
                        editor.chain().focus().setFontFamily(value).run();
                    }}
                >
                    <SelectTrigger className="!tw-h-8 !tw-w-[120px] [&_svg]:tw-size-3">
                        <SelectValue placeholder="Font Family" />
                    </SelectTrigger>
                    <SelectContent>
                        {fontFamilyList.map((value) => (
                            <SelectItem key={value} value={value}>
                                {value}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
            <Separator orientation="vertical" className="!tw-h-8 tw-border-black" />
            <div className="tw-flex tw-gap-x-2">
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().toggleBulletList().run()}
                    variant={editor.isActive("bulletList") ? "secondary" : "outline"}
                    size="icon"
                >
                    <List />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().toggleOrderedList().run()}
                    variant={editor.isActive("orderedList") ? "secondary" : "outline"}
                    size="icon"
                >
                    <ListOrdered />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                    variant={editor.isActive("codeBlock") ? "secondary" : "outline"}
                    size="icon"
                >
                    <Code />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().toggleBlockquote().run()}
                    variant={editor.isActive("blockquote") ? "secondary" : "outline"}
                    size="icon"
                >
                    <Quote />
                </Button>
                <Popover>
                    <PopoverTrigger className="!tw-size-8 [&_svg]:tw-size-3" asChild>
                        <Button variant={editor.isActive("blockquote") ? "secondary" : "outline"} size="icon">
                            <Image />
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent>
                        <Input1 onChange={(value) => setUrl(value)} placeholder="Image URL" />
                    </PopoverContent>
                </Popover>
            </div>
            <Separator orientation="vertical" className="!tw-h-8 tw-border-black" />
            <div className="tw-flex tw-gap-x-2">
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    variant="outline"
                    onClick={() => editor.chain().focus().setHorizontalRule().run()}
                    size="icon"
                >
                    <Minus />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().undo().run()}
                    size="icon"
                >
                    <Undo />
                </Button>
                <Button
                    className="!tw-size-8 [&_svg]:tw-size-3"
                    onClick={() => editor.chain().focus().redo().run()}
                    size="icon"
                >
                    <Redo />
                </Button>
            </div>
        </div>
    );
};

export default MenuBar;
